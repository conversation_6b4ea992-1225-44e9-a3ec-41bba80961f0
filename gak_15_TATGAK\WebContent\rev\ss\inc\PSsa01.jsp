

<%@ page import="com.jast.gakuen.framework.DisplayInfo" %>
<%@ page import="com.jast.gakuen.framework.SystemData" %>
<%@ page import="com.jast.gakuen.framework.util.*"%>
<%@ page import="com.jast.gakuen.framework.util.UtilSystem" %>
<%@ page import="com.jast.gakuen.rev.RevPageCodeBase" %>
	
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	

<% 
	DisplayInfo displayInfo = UtilSystem.getDisplayInfo();
%>
	
<SCRIPT type="text/javascript" language="JavaScript">
var PSsa01_FUNC_ID = "PSsa01";
var subWin = null;

/**
 * 画面ロード時の子画面の起動
 * hidden項目(ID：openSubWinFlg）に１をセットしている場合は、学生検索子画面を起動する
 */
function loadAction(event){
	doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:htmlName');
	if(document.getElementById("form1:openSubWinFlg").value == "1"){
		//矢印ボタン押下時にエラーの場合は、子画面を起動しない。
		if(document.getElementById("form1:message").innerHTML == ''){
			openSubWindow('form1:htmlGakusekiCd');
		}
	}
}

// 学生氏名を取得する
function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	var servlet = "rev/ss/VSsaCsotGakAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);

}



/**
  *　学生検索子画面を起動する
  * ・別機能から起動済みの場合は、起動前にセッション削除
  * ・当画面から未起動の場合は、起動
  * ・起動済みの場合は、起動せず、フォーカスのみ
  * ・子画面起動時に選択ボタン押下で、親画面の選択ボタン（ID：select）を押下する
  *
  */
function openSubWindow(field1) {
  // 矢印ボタン押下後にonLoadイベントで呼び出された場合は、起動フラグを初期化
   var reOpenSubWinFlg = document.getElementById("form1:openSubWinFlg").value;
  document.getElementById("form1:openSubWinFlg").value = "";
  
   // 機能ＩＤ
   var motoFuncId = document.getElementById('form1:htmlFuncId').innerHTML;
  // 当画面からすでに起動済みの場合は、起動せず、フォーカスのみ
  setSubWin();
  if(subWin){
	if(!subWin.closed){
	  	if(subWin.document.getElementById("form1:motoFuncId").value == motoFuncId){
			if(reOpenSubWinFlg != "1"){
				subWin.focus();
	  			return false;
			}
		} else {
			// 起動元の画面が異なる場合は、いったん子画面をクローズしておく
			subWin.close();
		}
	} else {
		// 起動もとのＩＤを変更したいが閉じているためできない
	}
  }	
  
	// 別機能から起動済みの場合は、起動前にセッションから削除しておく
	var pageCodeClass = "com.jast.gakuen.rev.ss.PSsa0101";
			 
  // 当画面から未起動の場合は、起動
  var url="${pageContext.request.contextPath}/faces/rev/ss/pSsa0101.jsp"
        + "?retFieldName=" + field1 + "&selectBtnId=form1:select2" + "&funcId=" + motoFuncId + "&pSelectedFlgItemId=form1:pSelectedFlg";
 
  
	removeSessionRev("PSsa01" ,pageCodeClass ,motoFuncId ,reOpenSubWinFlg
			,url, "PSsa0101", "<%=com.jast.gakuen.rev.ss.PSsa0101.getWindowOpenOption() %>");

  return false;
}


/**
  *　学生検索子画面（モーダル）を起動する
  * ・別機能から起動済みの場合は、起動前にセッション削除
  *
  */
function openModalSubWindow(field1) { 
   // 機能ＩＤ
   var motoFuncId = document.getElementById('form1:htmlFuncId').innerHTML;

  // 起動元の画面が異なる場合は、いったん子画面をクローズしておく
  setSubWin();
  if(subWin){
	if(!subWin.closed){
		subWin.close();
	}
  }	

  // 別機能から起動済みの場合は、起動前にセッションから削除しておく
  var pageCodeClass = "com.jast.gakuen.rev.ss.PSsa0101";
			 
  // 当画面から未起動の場合は、起動
  var url="${pageContext.request.contextPath}/faces/rev/ss/pSsa0101.jsp"
        + "?retFieldName=" + field1 + "&selectBtnId=" + "&funcId=" + motoFuncId + "&pSelectedFlgItemId=";
 
  
	removeSessionRevAndOpenModal("PSsa01" ,pageCodeClass ,motoFuncId 
			,url, "PSsa0101", "<%=com.jast.gakuen.rev.ss.PSsa0101.getWindowOpenOption() %>");
  return false;
}

function openWindow2(pUrl, pName, pOption) {
var win;
	if (pOption) {
		win = window.open(pUrl, pName, pOption);
	} else {
		win = window.open(pUrl, pName, "status=yes,toolbar=yes,menubar=yes,location=yes,resizable=yes");
	}
	win.focus();
	try{
		opener.setWindowObject(win, pName);
	}catch(e){}
}

// 学生検索子画面のオブジェクト取得
function setSubWin(){
	// IE8対応 2009-11-26 ->
	//windowList = opener.windowList;
	// IE8対応 2009-11-26 <-
	windowList = opener.getOpenWins();
	for(i=0;i<windowList.length;i++) {
		tmpWin = windowList[i];
		if(tmpWin) {
			if (!tmpWin.winObj.closed) {
				if (tmpWin.funcId == PSsa01_FUNC_ID){
				    subWin = tmpWin.winObj;
				    return true;
				}
			}
		}
	}
	return false;
}

/**
 * 矢印ボタン押下時の処理
 * 子画面が起動している場合は、学生変更後に子画面を再描画するためのフラグをセットする。
 */
function changeGakuseki(){
   // 機能ＩＤ
   var motoFuncId = document.getElementById('form1:htmlFuncId').innerHTML;
   
	setSubWin();
	// 子画面を１回以上起動済み
	if(subWin){
		//if(subWin.document.getElementById("motoFuncId").value == motoFuncId){
		//subWin.close();
		if((!subWin.closed)){
			// 起動元画面が当機能の場合
			if(subWin.document.getElementById("form1:motoFuncId").value == motoFuncId){
				document.getElementById("form1:openSubWinFlg").value = "1";
			}
		}
	// 子画面未起動
	} else {
		// ありえない（検索画面で選択された場合のみ矢印ボタンを活性化するため）
		//alert("学生検索子画面で学生を検索してください。");
		//return false;
	}
	return true;
}
/**	
 *  motoFuncIdから起動された子画面でない場合は、セッションを消去後、子画面を開きます。
 *  RemoveFromSessionAjaxでは、子画面別の処理を行う（この関数を他子画面起動に使う場合に、処理の追加が必要）
 * 
 *  funcId：起動する子画面の機能ＩＤ
 *  cls：起動する子画面のページコードクラス（例　com.jast.gakuen.rev.ss.PSsa0101）
 *  motoFuncId：起動元の機能ＩＤ
 *  motoFuncFocusFlg：起動元の機能にフォーカスする場合、１
 *  url,name,opt：openWindow関数の引数
 */	
function removeSessionRev(funcId ,cls ,motoFuncId ,reOpenSubWinFlg ,url,name,opt) {
	var ajaxServlet = "rev/co/RemoveFromSessionAjax";
	var args = new Array();
		args['pcClass'] = cls;
		args['motoFuncId'] = motoFuncId;
	var engine      = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) { 
			var windowPointer = openWindow(url,name,opt);
			opener.setWindowObject(windowPointer, funcId);
			if(reOpenSubWinFlg=="1"){
				focus();
			}
		}
	);
	engine.send(ajaxServlet,null,args);
}
/**	
 *  motoFuncIdから起動された子画面でない場合は、セッションを消去後、子画面をモーダルで開きます。
 *  RemoveFromSessionAjaxでは、子画面別の処理を行う（この関数を他子画面起動に使う場合に、処理の追加が必要）
 * 
 *  funcId：起動する子画面の機能ＩＤ
 *  cls：起動する子画面のページコードクラス（例　com.jast.gakuen.rev.ss.PSsa0101）
 *  motoFuncId：起動元の機能ＩＤ
 *  motoFuncFocusFlg：起動元の機能にフォーカスする場合、１
 *  url,name,opt：openWindow関数の引数
 */	
function removeSessionRevAndOpenModal(funcId ,cls ,motoFuncId  ,url,name,opt) {
	var ajaxServlet = "rev/co/RemoveFromSessionAjax";
	var args = new Array();
		args['pcClass'] = cls;
		args['motoFuncId'] = motoFuncId;
	var engine      = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) { 
			openModalWindow(url,name,opt);
			opener.setWindowObject(_modalWin, funcId);
		}
	);
	engine.send(ajaxServlet,null,args);
}

</SCRIPT>