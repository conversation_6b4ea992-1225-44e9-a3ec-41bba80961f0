<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm01402.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xrm01402.jsp</TITLE>
<SCRIPT type="text/javascript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrm01402.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrm01402.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrm01402.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrm01402.screenName}"></h:outputText>
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>

				<!--↓CONTENT↓-->
				<DIV class="head_button_area" >
					<hx:commandExButton type="submit" value="戻る"
						styleClass="commandExButton_etc" id="returnDisp"
						action="#{pc_Xrm01402.doReturnDispAction}">
					</hx:commandExButton>
				</DIV>
				<DIV id="content" class="column" align="center">
					<DIV class="column" align="center">

						<TABLE width="900px" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" width="100%" cellpadding="0" cellspacing="0"
											class="table">
											<TBODY>
												<TR>
													<TH align="center" nowrap class="v_a" width="100">
														<h:outputText styleClass="outputText" id="lblGhYear"
															value="#{pc_Xrm01402.propGhYear.labelName}"
															style="#{pc_Xrm01402.propGhYear.style}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="150">
														<h:outputText styleClass="outputText" id="htmlGhYear"
															value="#{pc_Xrm01402.propGhYear.value}"
															style="#{pc_Xrm01402.propGhYear.style}">
														</h:outputText>
													</TD>
												
													<TH align="center" nowrap class="v_b" width="150px">
														<h:outputText styleClass="outputText"
															id="lblGakusekiCd"
															value="#{pc_Xrm01402.propGakusekiCd.labelName}"
															style="#{pc_Xrm01402.propGakusekiCd.style}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="150px">
														<h:outputText styleClass="outputText" id="htmlGakusekiCd"
															value="#{pc_Xrm01402.propGakusekiCd.value}"
															style="#{pc_Xrm01402.propGakusekiCd.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_c" width="150px">
														<h:outputText styleClass="outputText" id="lblName"
															value="#{pc_Xrm01402.propName.labelName}"
															style="#{pc_Xrm01402.propName.style}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="150px">
														<h:outputText styleClass="outputText" id="htmlName"
															value="#{pc_Xrm01402.propName.value}"
															style="#{pc_Xrm01402.propName.style}">
														</h:outputText>
													</TD>

													<TH align="center" nowrap class="v_d" width="150px">
														<h:outputText styleClass="outputText" id="lblNameKana"
															value="#{pc_Xrm01402.propNameKana.labelName}"
															style="#{pc_Xrm01402.propNameKana.style}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="150px">
														<h:outputText styleClass="outputText" id="htmlNameKana"
															value="#{pc_Xrm01402.propNameKana.value}"
															style="#{pc_Xrm01402.propNameKana.style}">
														</h:outputText>
													</TD>

												</TR>
												<TR>
													<TH align="center" nowrap class="v_e" width="150px">
														<h:outputText styleClass="outputText" id="lblSeibetsu"
															value="#{pc_Xrm01402.propSeibetsu.labelName}"
															style="#{pc_Xrm01402.propSeibetsu.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap  width="150px">
														<h:outputText styleClass="outputText" id="htmlSeibetsu"
															style="#{pc_Xrm01402.propSeibetsu.style}"
															value="#{pc_Xrm01402.propSeibetsu.value}">
														</h:outputText>
													</TD>

													<TH align="center" nowrap class="v_f" width="150px">
														<h:outputText styleClass="outputText" id="lblGaknen"
															value="#{pc_Xrm01402.propGaknen.labelName}"
															style="#{pc_Xrm01402.propGaknen.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap  width="150px">
														<h:outputText styleClass="outputText" id="htmlGaknen"
															style="#{pc_Xrm01402.propGaknen.style}"
															value="#{pc_Xrm01402.propGaknen.value}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_g" width="150px">
														<h:outputText styleClass="outputText" id="lblZaisekiLimit"
															value="#{pc_Xrm01402.propZaisekiLimit.labelName}"
															style="#{pc_Xrm01402.propZaisekiLimit.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap  width="150px">
														<h:outputText styleClass="outputText" id="htmlZaisekiLimit"
															style="#{pc_Xrm01402.propZaisekiLimit.style}"
															value="#{pc_Xrm01402.propZaisekiLimit.value}">
														</h:outputText>
													</TD>

													<TH align="center" nowrap class="v_h" width="150px">
														<h:outputText styleClass="outputText" id="lblGakuhiLimit"
															value="#{pc_Xrm01402.propGakuhiLimit.labelName}"
															style="#{pc_Xrm01402.propGakuhiLimit.labelStyle}">
														</h:outputText>
													</TH>
													<TD nowrap  width="150px">
														<h:outputText styleClass="outputText" id="htmlGakuhiLimit"
															style="#{pc_Xrm01402.propGakuhiLimit.style}"
															value="#{pc_Xrm01402.propGakuhiLimit.value}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH align="center" nowrap class="v_i" width="150px">
														<h:outputText styleClass="outputText" id="lblIdo"
															value="#{pc_Xrm01402.propIdo.labelName}"
															style="#{pc_Xrm01402.propIdo.labelStyle}">
														</h:outputText>
													</TH>
													<TD colspan="3" width="150px">
														<h:outputText styleClass="likeOutput" id="htmlIdo"
															style="#{pc_Xrm01402.propIdo.style}"
															value="#{pc_Xrm01402.propIdo.value}">
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="5px">
									</TD>
								</TR>
						
								<TR align="center" >
									<TD width="100%">
									<TABLE class="table" width="900px">
										<TBODY>
											<TR>
												<TH nowrap class="v_a" width="220px">
												<!-- 年度(請求年度) -->
													<h:outputText styleClass="outputText" id="lblNendo"
													value="#{pc_Xrm01402.propNendo.labelName}"
													style="#{pc_Xrm01402.propNendo.labelStyle}"></h:outputText></TH>
												<TD valign="middle" width="250px">
													<h:inputText styleClass="inputText" id="htmlNendo"
														size="4" value="#{pc_Xrm01402.propNendo.dateValue}"
														style="#{pc_Xrm01402.propNendo.style}"
														>
														<hx:inputHelperAssist imeMode="inactive"
															errorClass="inputText_Error" promptCharacter="_" />
														<f:convertDateTime pattern="yyyy" />
													</h:inputText>
												</TD>
												<TD width="140px" valign="middle" style=" border-right-style:none;">
													<hx:commandExButton type="submit" value="選択"
														styleClass="cmdBtn_dat_s" id="search"
														action="#{pc_Xrm01402.doSearchAction}" tabindex="21"></hx:commandExButton>
												</TD>

												<TD width="365px" style=" border-left-style:none;">
												</TD>
											</TR>

										</TBODY>
									</TABLE>
									</TD>
								</TR>

								<TR>
									<TD height="10px">
									</TD>
								</TR>
						
							</TBODY>
						</TABLE>

						<TABLE width="900px" border="0" cellpadding="0" cellspacing="0">
							<TBODY>

								<%-- 現納付状況一覧(上段)--%>
								<TR>
									<TD>
										<TABLE border="0" width="100%" cellpadding="0" cellspacing="0" class="table1">
											<TBODY>
												<TR>
													<!-- (現在)現在 -->
													<TH align="left">
														<h:outputText styleClass="outputText" id="lblNowDate"
															value="#{pc_Xrm01402.propNowDate.labelName}"
															style="#{pc_Xrm01402.propNowDate.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="150px">
														<h:outputText styleClass="outputText" id="htmlNowDate"
															style="#{pc_Xrm01402.propNowDate.style}"
															value="#{pc_Xrm01402.propNowDate.value}">
														</h:outputText>
													</TD>
													<!-- (現在)納付金パターン名称 -->
													<TH align="left">
														<h:outputText styleClass="outputText" id="lblNowNofuPatternNm"
															value="#{pc_Xrm01402.propNowNofuPatternNm.labelName}"
															style="#{pc_Xrm01402.propNowNofuPatternNm.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="350px">
														<h:outputText styleClass="outputText" id="htmlNowNofuPatternNm"
															style="#{pc_Xrm01402.propNowNofuPatternNm.style}"
															value="#{pc_Xrm01402.propNowNofuPatternNm.value}">
														</h:outputText>
													</TD>
													<!-- (現在)振替依頼人コード -->
													<TH align="left">
														<h:outputText styleClass="outputText" id="lblNowFurikomiIraiCd"
															value="#{pc_Xrm01402.propNowFurikomiIraiCd.labelName}"
															style="#{pc_Xrm01402.propNowFurikomiIraiCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="100px">
														<h:outputText styleClass="outputText" id="htmlNowFurikomiIraiCd"
															style="#{pc_Xrm01402.propNowFurikomiIraiCd.style}"
															value="#{pc_Xrm01402.propNowFurikomiIraiCd.value}">
														</h:outputText>
													</TD>

												</TR>
											</TBODY>
										</TABLE>

									</TD>
								</TR>
							</TBODY>
						</TABLE>

						<TABLE width="900px" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD>

										<h:dataTable
											rowClasses="#{pc_Xrm01402.propPayList.rowClasses}" cellpadding="0"
											cellspacing="0" headerClass="headerClass" footerClass="footerClass"
											styleClass="meisai_scroll" id="htmlPayList"
											value="#{pc_Xrm01402.propPayList.list}" var="varlist" width="900">
							
											<h:column id="column1">
												<!-- 内訳科目 -->
												<f:facet name="header">
													<hx:jspPanel>
														<center><h:outputText styleClass="outputText"
															id="lblListUtiwakekamoku" value="内訳科目">
														</h:outputText>
														</center>
													</hx:jspPanel>
												</f:facet>
												<f:attribute value="180" name="width" />
												<h:outputText styleClass="outputText" id="htmlListUtiwakekamoku"
													value="#{varlist.utiwakekamokuOut.displayValue}"
													title="#{varlist.utiwakekamoku}">
												</h:outputText>
											</h:column>
											<h:column id="column2">
												<!-- (当該)勘定科目コード -->
												<f:facet name="header">
													<hx:jspPanel>
														<center><h:outputText styleClass="outputText"
															id="lblListTouKanjyoKamokuCd" value="勘定科目コード">
														</h:outputText>
														</center>
													</hx:jspPanel>
												</f:facet>
												<f:attribute value="75" name="width" />
												<f:attribute value="text-align: left" name="style" />
												<h:outputText styleClass="outputText" id="htmlListTouKanjyoKamokuCd"
													value="#{varlist.togaiKanjyoKamokuCd}">
												</h:outputText>
											</h:column>
							
											<!-- 当該(入金(金額、日付)・返金(金額、日付)) -->
											<!-- 前受(勘定科目コード)                     -->
											<!-- 前受(入金(金額、日付)・返金(金額、日付)) -->
											<h:column id="column3">
												<f:facet name="header">
													<hx:jspPanel id="jspPanel1">
														<TABLE  cellpadding="0" cellspacing="0" width="0"
															height="100%">
															<TBODY>
																<TR>
																	<TH 
																		width="300" colspan="4"><h:outputText styleClass="outputText"
																		id="lblListTogai" value="当該"></h:outputText></TH>
																	<TH 
																		width="75" rowspan="3"><h:outputText styleClass="outputText"
																		id="lblListMeukeKanjyoKamokuCd" value="勘定科目コード"></h:outputText></TH>
																	<TH 
																		width="300" colspan="4"><h:outputText styleClass="outputText"
																		id="lblListMeuke" value="前受"></h:outputText></TH>
																</TR>
																<TR>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblTogainyukin" value="入金"></h:outputText></TH>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblTogaihenkin" value="返金"></h:outputText></TH>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblMeukenyukin" value="入金"></h:outputText></TH>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblMeukehenkin" value="返金"></h:outputText></TH>	
																</TR>
																<TR>
																	<TH 
																		width="75"><h:outputText styleClass="outputText"
																		id="lblTogaiNyukinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblTogaiNyukinDate" value="日付"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblTogaiHenkinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblTogaiHenkinDate" value="日付"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblMeukeNyukinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblMeukeNyukinDate" value="日付"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblMeukeHenkinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblMeukeHenkinDate" value="日付"></h:outputText></TH>	
																</TR>
															</TBODY>
														</TABLE>
													</hx:jspPanel>
												</f:facet>
												
												
												<hx:jspPanel id="jspPanel2">
													<TABLE style="table-layout: fixed;" cellpadding="0" cellspacing="0"  height="100%"
															style="border-bottom-style:none;border-top-style:none;">
														<TBODY>
															<TR>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblTogaiNyukinMoney_list" 
																	value="#{varlist.togaiNyukinMoney}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblTogaiNyukinDate_list"
																	value="#{varlist.togaiNyukinDate}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblTogaiHenkinMoney_list"
																	value="#{varlist.togaiHenkinMoney}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblTogaiHenkinDate_list"
																	value="#{varlist.togaiHenkinDate}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: left" width="70"><h:outputText styleClass="outputText"
																	id="lblMeukeKanjyoKamokuCd_list"
																	value="#{varlist.meukeKanjyoKamokuCd}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblMeukeNyukinMoney_list"
																	value="#{varlist.meukeNyukinMoney}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblMeukeNyukinDate_list"
																	value="#{varlist.meukeNyukinDate}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblMeukeHenkinMoney_list"
																	value="#{varlist.meukeHenkinMoney}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblMeukeHenkinDate_list"
																	value="#{varlist.meukeHenkinDate}"></h:outputText></TD>
															</TR>
														</TBODY>
													</TABLE>
												</hx:jspPanel>
											</h:column>
							
										</h:dataTable>

									</TD>
								</TR>

								<TR>
									<TD height="10px">
									</TD>
								</TR>

							</TBODY>
						</TABLE>

						<TABLE width="900px" border="0" cellpadding="0" cellspacing="0">
							<TBODY>

								<%-- 振替状況一覧(下段)--%>
								<TR>
									<TD>

										<TABLE border="0" width="100%" cellpadding="0" cellspacing="0" class="table2">
											<TBODY>
												<TR>
													<!-- (振替)振替 -->
													<TH align="left">
														<h:outputText styleClass="outputText" id="lblFurikaeDate"
															value="#{pc_Xrm01402.propFurikaeDate.labelName}"
															style="#{pc_Xrm01402.propFurikaeDate.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="150px">
														<h:outputText styleClass="outputText" id="htmlFurikaeDate"
															style="#{pc_Xrm01402.propFurikaeDate.style}"
															value="#{pc_Xrm01402.propFurikaeDate.value}">
														</h:outputText>
													</TD>
													<!-- (振替)納付金パターン名称 -->
													<TH align="left">
														<h:outputText styleClass="outputText" id="lblFurikaeNofuPatternNm"
															value="#{pc_Xrm01402.propFurikaeNofuPatternNm.labelName}"
															style="#{pc_Xrm01402.propFurikaeNofuPatternNm.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="350px">
														<h:outputText styleClass="outputText" id="htmlFurikaeNofuPatternNm"
															style="#{pc_Xrm01402.propFurikaeNofuPatternNm.style}"
															value="#{pc_Xrm01402.propFurikaeNofuPatternNm.value}">
														</h:outputText>
													</TD>
													<!-- (振替)振替依頼人コード -->
													<TH align="left">
														<h:outputText styleClass="outputText" id="lblFurikaeFurikomiIraiCd" 
															value="#{pc_Xrm01402.propFurikaeFurikomiIraiCd.labelName}"
															style="#{pc_Xrm01402.propFurikaeFurikomiIraiCd.labelStyle}">
														</h:outputText>
													</TH>
													<TD align="left" width="100px">
														<h:outputText styleClass="outputText" id="htmlFurikaeFurikomiIraiCd"
															style="#{pc_Xrm01402.propFurikaeFurikomiIraiCd.style}"
															value="#{pc_Xrm01402.propFurikaeFurikomiIraiCd.value}">
														</h:outputText>
													</TD>

												</TR>
											</TBODY>
										</TABLE>

									</TD>
								</TR>
							</TBODY>
						</TABLE>

						<TABLE width="900px" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD>

										<h:dataTable
											rowClasses="#{pc_Xrm01402.propPayFurikaeList.rowClasses}" cellpadding="0"
											cellspacing="0" headerClass="headerClass" footerClass="footerClass"
											styleClass="meisai_scroll" id="htmlPayFurikaeList"
											value="#{pc_Xrm01402.propPayFurikaeList.list}" var="varfurikaelist" width="900">
							
											<h:column id="column21">
												<!-- 内訳科目 -->
												<f:facet name="header">
													<hx:jspPanel>
														<center><h:outputText styleClass="outputText"
															id="lblListFurikaeUtiwakekamoku" value="内訳科目">
														</h:outputText>
														</center>
													</hx:jspPanel>
												</f:facet>
												<f:attribute value="180" name="width" />
												<h:outputText styleClass="outputText" id="htmlListFurikaeUtiwakekamoku"
													value="#{varfurikaelist.furikaeUtiwakekamokuOut.displayValue}"
													title="#{varfurikaelist.furikaeUtiwakekamoku}">
													
												</h:outputText>
											</h:column>
											<h:column id="column22">
												<!-- (当該)勘定科目コード -->
												<f:facet name="header">
													<hx:jspPanel>
														<center><h:outputText styleClass="outputText"
															id="lblListFurikaeTouKanjyoKamokuCd" value="勘定科目コード">
														</h:outputText>
														</center>
													</hx:jspPanel>
												</f:facet>
												<f:attribute value="75" name="width" />
												<f:attribute value="text-align: left" name="style" />
												<h:outputText styleClass="outputText" id="htmlListFurikaeTouKanjyoKamokuCd"
													value="#{varfurikaelist.furikaeTogaiKanjyoKamokuCd}">
												</h:outputText>
											</h:column>
							
											<!-- 当該(入金(金額、日付)・返金(金額、日付)) -->
											<!-- 前受(勘定科目コード)                     -->
											<!-- 前受(入金(金額、日付)・返金(金額、日付)) -->
											<h:column id="column23">
												<f:facet name="header">
													<hx:jspPanel id="jspPanel3">
														<TABLE  cellpadding="0" cellspacing="0" width="0"
															height="100%">
															<TBODY>
																<TR>
																	<TH 
																		width="300" colspan="4"><h:outputText styleClass="outputText"
																		id="lblListFurikaeTogai" value="当該"></h:outputText></TH>
																	<TH 
																		width="75" rowspan="3"><h:outputText styleClass="outputText"
																		id="lblListFurikaeMeukeKanjyoKamokuCd" value="勘定科目コード"></h:outputText></TH>
																	<TH 
																		width="300" colspan="4"><h:outputText styleClass="outputText"
																		id="lblListFurikaeMeuke" value="前受"></h:outputText></TH>
																</TR>
																<TR>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblFurikaeTogainyukin" value="入金"></h:outputText></TH>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblFurikaeTogaihenkin" value="返金"></h:outputText></TH>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblFurikaeMeukenyukin" value="入金"></h:outputText></TH>
																	<TH
																		width="150" colspan="2"><h:outputText styleClass="outputText"
																		id="lblFurikaeMeukehenkin" value="返金"></h:outputText></TH>	
																</TR>
																<TR>
																	<TH 
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeTogaiNyukinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeTogaiNyukinDate" value="日付"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeTogaiHenkinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeTogaiHenkinDate" value="日付"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeMeukeNyukinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeMeukeNyukinDate" value="日付"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeMeukeHenkinMoney" value="金額"></h:outputText></TH>
																	<TH
																		width="75"><h:outputText styleClass="outputText"
																		id="lblFurikaeMeukeHenkinDate" value="日付"></h:outputText></TH>	
																</TR>
															</TBODY>
														</TABLE>
													</hx:jspPanel>
												</f:facet>

												<hx:jspPanel id="jspPanel4">
													<TABLE style="table-layout: fixed;" cellpadding="0" cellspacing="0"  height="100%"
															style="border-bottom-style:none;border-top-style:none;">
														<TBODY>
															<TR>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeTogaiNyukinMoney_list"
																	value="#{varfurikaelist.furikaeTogaiNyukinMoney}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeTogaiNyukinDate_list"
																	value="#{varfurikaelist.furikaeTogaiNyukinDate}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeTogaiHenkinMoney_list"
			
																	value="#{varfurikaelist.furikaeTogaiHenkinMoney}"></h:outputText></TD>
		
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeTogaiHenkinDate_list"
																	value="#{varfurikaelist.furikaeTogaiHenkinDate}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: left" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeMeukeKanjyoKamokuCd_list"
																	value="#{varfurikaelist.furikaeMeukeKanjyoKamokuCd}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeMeukeNyukinMoney_list"
																	value="#{varfurikaelist.furikaeMeukeNyukinMoney}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeMeukeNyukinDate_list"
																	value="#{varfurikaelist.furikaeMeukeNyukinDate}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: right" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeMeukeHenkinMoney_list"
																	value="#{varfurikaelist.furikaeMeukeHenkinMoney}"></h:outputText></TD>
																<TD style="padding: 2px; border-top-style:none; border-left-style: none; border-bottom-style:none;
				 													text-align: center" width="70"><h:outputText styleClass="outputText"
																	id="lblFurikaeMeukeHenkinDate_list"
																	value="#{varfurikaelist.furikaeMeukeHenkinDate}"></h:outputText></TD>
															</TR>
														</TBODY>
													</TABLE>
												</hx:jspPanel>
											</h:column>
							
										</h:dataTable>

									</TD>
								</TR>

							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			</DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
