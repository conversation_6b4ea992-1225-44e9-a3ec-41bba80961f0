<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc01101T01.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>xrc00101T01.jsp</TITLE>

<SCRIPT type="text/javascript">

	function openBuppinSubWindow(field1) {
	// 物品検索画面
	    var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0101.jsp"
			+ "?retFieldName=" + field1;
		openModalWindow(url, "pXrc0101", "<%=com.jast.gakuen.rev.xrc.PXrc0101.getWindowOpenOption() %>");
		return false;
	}
	function doBuppinAjax(thisObj, thisEvent, targetLabel) {
	// 物品名称を取得する
		var servlet = "rev/xrc/XrcBpnAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

	function confirmOk() {
		document.getElementById('form1:propExecutableSearch').value = "1";
		indirectClick('search');
	}
	
	function confirmCancel() {
		document.getElementById('form1:propExecutableSearch').value = "0";
	}
	
	function fncButtonActive(){
		var codeRegSearch = null;
		//選択ボタン
		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch').value;
		if(codeRegSearch == 1){
			document.getElementById('form1:search').disabled = true;
		}
		//スクロールバーの位置を保持
		window.attachEvent('onload', endload);	
	}
	function endload() {
		//スクロールバーの位置を保持
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
	function openKamokuSubWindow() {
		// 科目検索画面（引数：なし）
		var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlEditKmkCd";
		openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
		return true;
	}
	function doKamokuInfoAjax(thisObj, thisEvent, targetLabel) {
		 // 科目名称を取得する
		 var servlet = "rev/xrc/XrcKmbKmkHaiNoHaiAJAX";
		 var args = new Array();
		 args['gakusekiCd'] = document.getElementById("form1:htmlHiddenEditGaksekiCd").value;
		 args['kamokuCd'] = thisObj.value;

		 var ajaxUtil = new AjaxUtil();
		 ajaxUtil.getCodeName(servlet, targetLabel, args);
	 }
	//-------------------------------------------
	// 学生氏名取得 (AJAX)
	//-------------------------------------------
	function getGakuseiName(thisObj, thisEvent) {
		//学生名称の取得
		var servlet = "rev/co/CobGakseiAJAX";
		var args = new Array();
		args['code1'] = thisObj.value;
		var target = "form1:lblPreGakseiNm";
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
	
	//-------------------------------------------
	// 学生検索子画面オープン
	//-------------------------------------------
	function openPCob0101(targetId) {
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=" + targetId;
		openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
		return false;
	}
	
	//-------------------------------------------
	// 画面ロード時の学生名称再取得
	//-------------------------------------------
	 function loadAction(event){
		getGakuseiName(document.getElementById('form1:htmlGakusekiCd'), event);
		// 画面ロード時の物品コード、物品名称の再取得
		doBuppinAjax(document.getElementById('form1:htmlEditBpnCode'), event, 'form1:htmlEditBpnName');
		doKamokuInfoAjax(document.getElementById('form1:htmlEditKmkCd'), event, 'form1:htmlEditKmkNm');
		document.getElementById("listScroll").scrollTop = document.getElementById('form1:htmlPropTable').value;
	}
	<%-- 検索結果内のスクロールポジションを保持 --%>
	function saveScrollPosition() {
		document.getElementById('form1:htmlPropTable').value = document.getElementById("listScroll").scrollTop;
	}

</SCRIPT></HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="fncButtonActive();loadAction(event);">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrc01101T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrc01101T01.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrc01101T01.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrc01101T01.screenName}"></h:outputText>
			</div>

			<!--↓outer↓-->
	
			<DIV class="outer">

				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>

				<DIV id="content">
					<DIV class="column" align="left">

						<TABLE
							width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE
											border="0"
											cellpadding="0"
											cellspacing="0"
											width="100%">
											<TBODY>
												<TR align="left">
													<TD>
														<hx:commandExButton
															type="submit"
															value="学生指定"
															styleClass="tab_head_on"
															id="htmlGakTab"></hx:commandExButton><hx:commandExButton
															type="submit"
															value="科目指定"
															styleClass="tab_head_off"
															id="htmlKmkTab"
															action="#{pc_Xrc01101T01.doLinkHifKmkTabAction}"></hx:commandExButton><hx:commandExButton
															type="submit"
															value="物品指定"
															styleClass="tab_head_off"
															id="htmlBpnTab"
															action="#{pc_Xrc01101T01.doLinkHifBpnTabAction}">
														</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TD>

														<TABLE
															border="0"
															cellpadding="0"
															cellspacing="0"
															width="100%"
															class="tab_body">
															<TBODY>
																<TR align="center">
																	<TD height="20px" >

													<TABLE width="850px">
														<TR>
															<TD height="5px"></TD>
														</TR>
														<TR>
															<TD>

																<TABLE width="100%" border="0" class="table" style="table-layout: fixed;">
																	<TBODY>
																		<TR>
																			<!-- 学籍番号 -->
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					id="lblGakusekiCd"
																					value="#{pc_Xrc01101T01.propGakusekiCd.labelName}"
																					style="#{pc_Xrc01101T01.propGakusekiCd.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText
																					styleClass="inputText"
																					id="htmlGakusekiCd"
																					value="#{pc_Xrc01101T01.propGakusekiCd.stringValue}"
																					maxlength="#{pc_Xrc01101T01.propGakusekiCd.maxLength}"
																					style="#{pc_Xrc01101T01.propGakusekiCd.style}"
																					disabled="#{pc_Xrc01101T01.propGakusekiCd.disabled}"
																					size="10"
																					onblur="return getGakuseiName(this, event);">
																				</h:inputText>
																		
																				
																				<hx:commandExButton type="button" value="検"
																						styleClass="commandExButton_search"
																						onclick="return openPCob0101('form1:htmlGakusekiCd');">
																				</hx:commandExButton>
																				<h:outputText styleClass="outputText" id="lblPreGakseiNm"/>
																				</TD>
																				<TD align="right" 
																					style="background-color: rgb(232,232,232);
																					border-top-style:none; 
																					border-right-style:none; 
																					border-bottom-style:none;">
																					&nbsp;&nbsp;&nbsp;
																				<hx:commandExButton type="submit"
																					value="検索" styleClass="commandExButton_dat" id="search"
																					action="#{pc_Xrc01101T01.doSearchAction}"  >
																				</hx:commandExButton>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>

																<TABLE 
																	border="0"
																	cellpadding="0"
																	cellspacing="0"
																	width="100%">
																	<TBODY>
																		<TR>
																			<TD align="right">
																				<h:outputText
																					styleClass="outputText"
																					id="htmlCount"
																					value="#{pc_Xrc01101T01.propTable.listCount}">
																				</h:outputText>
																				<h:outputText
																					styleClass="outputText"
																					id="lblCount" value="件">
																				</h:outputText>
																			</TD>
																		</TR>
																		<TR>
																			<TD>
																				<DIV style="height: 254px; width=100%;" id="listScroll" onscroll="setScrollPosition('htmlHidScroll',this);" class="listScroll">
																					<h:dataTable
																						footerClass="footerClass"
																						rowClasses="#{pc_Xrc01101T01.propTable.rowClasses}"
																						headerClass="headerClass"
																						styleClass="meisai_scroll"
																						id="htmlTable"
																						value="#{pc_Xrc01101T01.propTable.list}"
																						var="varlist"
																						width="850px">
																						
																						<h:column id="column1">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propHaihonDate.labelName}"
																									id="lblHaihonDate">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlHaihonDate"
																								value="#{varlist.haihonDate}"
																								styleClass="outputText">
																							</h:outputText>
																							<f:attribute value="70px" name="width" />
																						</h:column>
																						
																						<h:column id="column2">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propBpnCode.labelName}"
																									id="lblBpnCode">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlBpnCode"
																								value="#{varlist.bpnCode}"
																								styleClass="outputText">
																							</h:outputText>
																							<f:attribute value="70px" name="width" />
																						</h:column>
																						<h:column id="column3">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propBpnName.labelName}"
																									id="lblBpnName">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlBpnName"
																								value="#{varlist.bpnNameOut.displayValue}"
																								title="#{varlist.bpnNameOut.value}"
																								styleClass="outputText">
																							</h:outputText>
																								<f:attribute value="180px" name="width" />
																								<f:attribute value="text-align: left" name="style" />
																						</h:column>
																						<h:column id="column4">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propEdaban.labelName}"
																									id="lblEdaban">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlEdaban"
																								value="#{varlist.edaban}"
																								styleClass="outputText">
																							</h:outputText>
																							<f:attribute value="30px" name="width" />
																						</h:column>
																						<h:column id="column5">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propEdition.labelName}"
																									id="lblEdition">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlEdition"
																								value="#{varlist.edition}"
																								styleClass="outputText">
																							</h:outputText>
																							<f:attribute value="30px" name="width" />
																						</h:column>
																						<h:column id="column6">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propKmkCd.labelName}"
																									id="lblKamokCd">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlKamokCd"
																								value="#{varlist.kamokCd}"
																								styleClass="outputText">
																							</h:outputText>
																							<f:attribute value="70px" name="width" />
																						</h:column>
																						<h:column id="column7">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propKmkNm.labelName}"
																									id="lblKamokName">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlKamokName"
																								value="#{varlist.kamokNameOut.displayValue}"
																								title="#{varlist.kamokNameOut.value}"
																								styleClass="outputText">
																							</h:outputText>
																							<f:attribute value="180px" name="width" />
																							<f:attribute value="text-align: left" name="style" />
																						</h:column>
																						<h:column id="column8">
																							<f:facet name="header">
																								<h:outputText
																									styleClass="outputText"
																									value="#{pc_Xrc01101T01.propHaifuKbn.labelName}"
																									id="lblHaifuKbn">
																								</h:outputText>
																							</f:facet>
																							<h:outputText
																								id="htmlHaifuKbn"
																								value="#{varlist.haifuKbn}"
																								styleClass="outputText">
																							</h:outputText>
																							<f:attribute value="70px" name="width" />
																						</h:column>
																						<h:column id="column9">
																							<f:facet name="header">
																								<h:outputText styleClass="outputText"
																									id="lblButton_select_head">
																								</h:outputText>
																							</f:facet>
																							<f:attribute value="40px" name="width" />
																							<hx:commandExButton type="submit" value="編集"
																								styleClass="commandExButton" id="button_select"
																								action="#{pc_Xrc01101T01.doListEditAction}"
																								onclick="saveScrollPosition();">
																							</hx:commandExButton>
																						</h:column>
																					</h:dataTable>
																				</DIV>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
																<!-- 編集項目 -->
																<BR>
																<TABLE width="100%" height="100px" border="0" class="table" style="table-layout: fixed;">
																	<TBODY>
																		<TR>
																			<!-- 学籍番号 -->
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditGakusekiCd.labelName}"
																					style="#{pc_Xrc01101T01.propEditGakusekiCd.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD colspan="1">
																				<h:outputText
																					styleClass="outputText"
																					id="htmlEditGakusekiCd"
																					value="#{pc_Xrc01101T01.propEditGakusekiCd.stringValue}"/>
																			</TD>
																			<!-- 学生氏名 -->
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditGakuseiName.labelName}"
																					style="#{pc_Xrc01101T01.propEditGakuseiName.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD colspan="1">
																				<h:outputText
																					styleClass="outputText"
																					id="htmlEditGakseiName"
																					value="#{pc_Xrc01101T01.propEditGakuseiName.stringValue}"/>
																			</TD>
																		</TR>
																		<!-- 配本日 -->
																		<TR>
																			<TH width="150" class="v_d">
																				<h:outputText styleClass="outputText"
																					style="#{pc_Xrc01101T01.propEditHaihonDate.labelStyle}"
																					value="#{pc_Xrc01101T01.propEditHaihonDate.labelName}">
																				</h:outputText>
																			</TH>
																			<TD colspan="3">
																				<h:inputText styleClass="inputText"
																					id="htmlEditHaihonDate" size="10"
																					disabled="#{pc_Xrc01101T01.propEditHaihonDate.disabled}"
																					value="#{pc_Xrc01101T01.propEditHaihonDate.dateValue}">
																					<hx:inputHelperAssist
																						errorClass="inputText_Error" promptCharacter="_" />
																					<f:convertDateTime />
																				<hx:inputHelperDatePicker />
																			</h:inputText>
																			</TD>
																		</TR>
																		<!-- 物品コード -->
																		<TR>
																			<TH class="v_b" width="150">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditBpnCode.labelName}"
																					style="#{pc_Xrc01101T01.propEditBpnCode.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD colspan="3">
																				<h:inputText styleClass="inputText"
																					id="htmlEditBpnCode" size="5"
																					onblur="return doBuppinAjax(this, event, 'form1:htmlEditBpnName');"
																					maxlength="#{pc_Xrc01101T01.propEditBpnCode.max}"
																					style="#{pc_Xrc01101T01.propEditBpnCode.style}"
																					disabled="#{pc_Xrc01101T01.propEditBpnCode.disabled}"
																					value="#{pc_Xrc01101T01.propEditBpnCode.stringValue}">
																				</h:inputText>
																				<hx:commandExButton type="submit" 
																					styleClass="commandExButton_search" id="searchBuppin"
																					onclick="return openBuppinSubWindow('form1:htmlEditBpnCode');" 
																					disabled="#{pc_Xrc01101T01.propEditBpnCode.disabled}">
																				</hx:commandExButton>
																				&nbsp;
																				<h:outputText
																					styleClass="outputText"
																					id="htmlEditBpnName"
																					value="#{pc_Xrc01101T01.propEditBpnName.stringValue}"/>
																			</TD>
																		</TR>
																		<!-- 枝番・版 -->
																		<TR>
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditEdaban.labelName}"
																					style="#{pc_Xrc01101T01.propEditEdaban.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText
																					styleClass="inputText"
																					id="htmlEditEdaban" size="2"
																					value="#{pc_Xrc01101T01.propEditEdaban.integerValue}"
																					maxlength="#{pc_Xrc01101T01.propEditEdaban.maxLength}"
																					style="#{pc_Xrc01101T01.propEditEdaban.style}"
																					disabled="#{pc_Xrc01101T01.propEditEdaban.disabled}">
																				<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter=""/>
																				<f:convertNumber pattern="#0;#0"/>
																				</h:inputText>
																			</TD>
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditEdition.labelName}"
																					style="#{pc_Xrc01101T01.propEditEdition.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText
																					styleClass="inputText"
																					id="htmlEditEdition" size="3"
																					value="#{pc_Xrc01101T01.propEditEdition.integerValue}"
																					maxlength="#{pc_Xrc01101T01.propEditEdition.maxLength}"
																					style="#{pc_Xrc01101T01.propEditEdition.style}"
																					disabled="#{pc_Xrc01101T01.propEditEdition.disabled}">
																				<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter=""/>
																				<f:convertNumber pattern="##0;##0"/>
																				</h:inputText>
																			</TD>
																		</TR>
																		<!-- 科目コード -->
																		<TR>
																			<TH nowrap class="v_a" width="150">
																					<h:outputText
																						styleClass="outputText"
																						value="#{pc_Xrc01101T01.propEditKmkCd.labelName}"
																						style="#{pc_Xrc01101T01.propEditKmkCd.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD colspan="3">
																				<h:inputText
																					styleClass="inputText"
																					id="htmlEditKmkCd" size="10"
																					maxlength="#{pc_Xrc01101T01.propEditKmkCd.maxLength}"
																					disabled="#{pc_Xrc01101T01.propEditKmkCd.disabled}"
																					value="#{pc_Xrc01101T01.propEditKmkCd.stringValue}"
																					style="#{pc_Xrc01101T01.propEditKmkCd.style}"
																					onblur="return doKamokuInfoAjax(this, event, 'form1:htmlEditKmkNm');">
																				</h:inputText>
																				<hx:commandExButton
																					type="button"
																					value="検"
																					styleClass="commandExButton_search"
																					id="htmlKamokuCodeSearchButton"
																					disabled="#{pc_Xrc01101T01.propEditKmkCd.disabled}"
																					onclick="return openKamokuSubWindow('form1:htmlEditKmkCd');">
																				</hx:commandExButton>
																				<h:outputText
																					styleClass="outputText"
																					id="htmlEditKmkNm"
																					value="#{pc_Xrc01101T01.propEditKmkNm.stringValue}"/>
																			</TD>
																		</TR>
																		<!-- 配付区分・数量 -->
																		<TR>
																			<TH width="150" class="v_d">
																				<h:outputText
																					styleClass="outputText" id="lblEditHaifuKbn"
																					value="#{pc_Xrc01101T01.propEditHaifuKbn.labelName}"
																					style="#{pc_Xrc01101T01.propEditHaifuKbn.labelStyle}">
																				</h:outputText>
																			</TH>
																			<TD width="200" colspan="1">
																				<h:selectOneMenu styleClass="selectOneMenu"
																					id="htmlEditHaifuKbn"
																					value="#{pc_Xrc01101T01.propEditHaifuKbn.value}"
																					style="#{pc_Xrc01101T01.propEditHaifuKbn.style};width:200px"
																					disabled="#{pc_Xrc01101T01.propEditHaifuKbn.disabled}">
																					<f:selectItems value="#{pc_Xrc01101T01.propEditHaifuKbn.list}" />
																				</h:selectOneMenu>
																			</TD>
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditSuryo.labelName}"
																					style="#{pc_Xrc01101T01.propEditSuryo.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText
																					styleClass="inputText"
																					id="htmlEditSuryo" size="7"
																					value="#{pc_Xrc01101T01.propEditSuryo.integerValue}"
																					maxlength="#{pc_Xrc01101T01.propEditSuryo.maxLength}"
																					style="#{pc_Xrc01101T01.propEditSuryo.style}"
																					disabled="#{pc_Xrc01101T01.propEditSuryo.disabled}">
																					<f:convertNumber pattern="######0"/>
																					<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																				</h:inputText>
																			</TD>
																		</TR>
																		<!-- 単価・販売価格 -->
																		<TR>
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditTanka.labelName}"
																					style="#{pc_Xrc01101T01.propEditTanka.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText
																					styleClass="inputText"
																					id="htmlEditTanka" size="9"
																					value="#{pc_Xrc01101T01.propEditTanka.integerValue}"
																					maxlength="#{pc_Xrc01101T01.propEditTanka.maxLength}"
																					style="#{pc_Xrc01101T01.propEditTanka.style}"
																					disabled="#{pc_Xrc01101T01.propEditTanka.disabled}">
																					<f:convertNumber pattern="###,###,##0;###,###,##0"/>
																					<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																				</h:inputText>
																			</TD>
																			<TH nowrap class="v_a" width="150px">
																				<h:outputText
																					styleClass="outputText"
																					value="#{pc_Xrc01101T01.propEditHanbaiKakak.labelName}"
																					style="#{pc_Xrc01101T01.propEditHanbaiKakak.labelStyle}" >
																				</h:outputText>
																			</TH>
																			<TD>
																				<h:inputText
																					styleClass="inputText"
																					id="htmlEditHanbaiKakak" size="9"
																					value="#{pc_Xrc01101T01.propEditHanbaiKakak.integerValue}"
																					maxlength="#{pc_Xrc01101T01.propEditHanbaiKakak.maxLength}"
																					style="#{pc_Xrc01101T01.propEditHanbaiKakak.style}"
																					disabled="#{pc_Xrc01101T01.propEditHanbaiKakak.disabled}">
																					<f:convertNumber pattern="###,###,##0;###,###,##0"/>
																					<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																				</h:inputText>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
																<TABLE width="100%">
																	<TBODY>
																		<TR>
																			<TD height="1px"></TD>
																		</TR>
																	</TBODY>
																</TABLE>
																<!-- ボタン -->
																<TABLE width="100%" height="30px">
																	<TBODY>
																		<TR>
																			<TD>
																				<hx:commandExButton
																					type="submit"
																					value="確定"
																					styleClass="commandExButton_dat"
																					id="htmlKakuteiButton"
																					disabled="#{pc_Xrc01101T01.propKakuteiButton.disabled}"
																					confirm="#{msg.SY_MSG_0001W}"
																					action="#{pc_Xrc01101T01.doKakuteiAction}"
																					onclick="saveScrollPosition();">
																				</hx:commandExButton>
																				<hx:commandExButton
																					type="submit"
																					value="削除"
																					styleClass="commandExButton_dat"
																					id="htmlDeleteButton"
																					disabled="#{pc_Xrc01101T01.propDeleteButton.disabled}"
																					confirm="#{msg.SY_MSG_0004W}"
																					action="#{pc_Xrc01101T01.doDeleteAction}"
																					onclick="saveScrollPosition();">
																				</hx:commandExButton>
																				<hx:commandExButton
																					type="submit"
																					value="クリア"
																					styleClass="commandExButton_dat"
																					id="htmlClearButton"
																					disabled="#{pc_Xrc01101T01.propClearButton.disabled}"
																					action="#{pc_Xrc01101T01.doClearAction}"
																					onclick="saveScrollPosition();">
																				</hx:commandExButton>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</TD>
														</TR>
														<TR>
															<TD height="1px"></TD>
														</TR>
													</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			</DIV> 	
			<!--↑outer↑-->

			<h:inputHidden
				value="#{pc_Xrc01101T01.propTable.scrollPosition}"
				id="htmlPropTable">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrc01101T01.propActiveControlSearch.value}"
				id="htmlActiveControlSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrc01101T01.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
			<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrc01101T01.propHiddenKanriNo.longValue}"
				id="htmlHiddenKanriNo">
			<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrc01101T01.propHiddenEditGaksekiCd.value}"
				id="htmlHiddenEditGaksekiCd">
			</h:inputHidden>
			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
