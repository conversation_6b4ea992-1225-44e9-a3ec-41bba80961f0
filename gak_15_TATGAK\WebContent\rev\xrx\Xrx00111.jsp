<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00111.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<f:subview id="Xrx00111">
  <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00111.onPageLoadBegin}">
    <%-- ↓ コンテンツ部 ↓ --%>
    <hx:jspPanel>
      <DIV class="column" align="center">
			<TABLE border="0" width="950">
				<TBODY>
					<TR>
						<TD>
						<div class="listScroll" style="height:254px;" id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrx00111.propXrkSyomHakRrk.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xrx00111.propXrkSyomHakRrk.list}" var="varlist">
							
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblTitle_head" styleClass="outputText"
										value="証明書名称">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblTitle"
									title="#{varlist.title.value}"
									value="#{varlist.title.displayValue}"></h:outputText>
								<f:attribute value="340" name="width" />
								<f:attribute value="left" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblHakkoNendo_head" styleClass="outputText"
										value="発行年度">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblHakkoNendo"
									value="#{varlist.hakkoNendo}"></h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblHakkoNo_head" styleClass="outputText"
										value="発行NO">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblHakkoNo"
									value="#{varlist.hakkoNo}"></h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblHakkoDate_head" styleClass="outputText"
										value="発行日付">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblHakkoDate"
									value="#{varlist.hakkoDate}">
									<f:convertDateTime />
								</h:outputText>
								<f:attribute value="90" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblOutputDate_head" styleClass="outputText"
										value="出力日付">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblOutputDate"
									value="#{varlist.outputDate}">
									<f:convertDateTime />
								</h:outputText>
								<f:attribute value="90" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column6">
								<f:facet name="header">
									<h:outputText id="lblSakuseiFlg_head" styleClass="outputText"
										value="発行一覧作成済フラグ">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblSakuseiFlg"
									value="#{varlist.sakuseiFlg}"></h:outputText>
								<f:attribute value="180" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column7">
								<f:facet name="header">
									<h:outputText id="lblMokuteki_head" styleClass="outputText"
										value="証明書使用目的コード">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblMokuteki"
									title="#{varlist.mokuteki.value}"
									value="#{varlist.mokuteki.displayValue}"></h:outputText>
								<f:attribute value="210" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>      </DIV>
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
  </hx:scriptCollector>
</f:subview>