<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

	// バーコード入力：KeyDown処理
	function fncKeyEvt() {
		if (event.keyCode == 13) {
			var btnObj = document.getElementById('form1:readBarcode');
			//btnObj.click();
			indirectClick('readBarcode');
		}
	}	

	function func_1(thisObj, thisEvent, fieldName) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=" + fieldName;
		 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	}
	
		function func_2(thisObj, thisEvent, fieldName) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=" + fieldName + "&kyoShokuin=2";
		 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	}
	
	
	// 教員名称を取得する
	function getJinjiName(thisObj, thisEvent, target ) {
  		var servlet = "rev/co/CobJinjAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;

	    var ajaxUtil = new AjaxUtil();
  		ajaxUtil.getCodeName(servlet, target, args);
 	}
 
	 function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}

	// 科目名称を取得する
	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
		var servlet = "rev/km/KmzKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}

	// 学生氏名を取得する
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

	// 検索ボタンクリック：OK選択時の処理
	function confirmOk() {
		var phase;
		phase = parseInt(document.getElementById('form1:htmlBackPhase').value);
		if (phase == 0) {
			document.getElementById('form1:htmlBackPhase').value = 1;
			indirectClick('search');
		}
	}
	// 検索ボタンクリック：キャンセル選択時の処理
	function confirmCancel() {
		document.getElementById('form1:htmlBackPhase').value = 9;
		indirectClick('search');
	}
	
	function loadAction(event){
	 doKamokuAjax(document.getElementById('form1:htmlKamokuCd'), event, 'form1:lblKamokuName');
	 doGakuseiAjax(document.getElementById('form1:htmlGakusekiNo'), event, 'form1:lblName');
	 getJinjiName(document.getElementById('form1:htmlTensakuKyouinCd'), event,'form1:lblTensakuKyouinName');
	 getJinjiName(document.getElementById('form1:htmlKousinKyouinCd'), event,'form1:lblKousinKyouinName');
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

	<BODY OnLoad="loadAction(event)">

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00501.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<hx:commandExButton
					type="submit" value="評価登録" tabindex="4"
					styleClass="commandExButton" id="entry"
					action="#{pc_Xrf00501.doEntryAction}" 
					disabled="#{pc_Xrf00501.propEntry.disabled}"
					style="#{pc_Xrf00501.propEntry.style}">
				</hx:commandExButton>
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
						<TBODY>
				    		<TR>
				        		<TH class="v_a" width="115">
					            	<!--バーコード -->
				                	<h:outputText styleClass="outputText" id="lblBarcode"
				                		value="#{pc_Xrf00501.propBarcode.labelName}"
				                		style="#{pc_Xrf00501.propBarcode.labelStyle}">
				                	</h:outputText>
				            	</TH>
								<TD>
									<h:inputText styleClass="inputText" id="htmlBarcode" size="25"
										onkeydown="fncKeyEvt();" style="#{pc_Xrf00501.propBarcode.style}"
										tabindex="5"
										maxlength="#{pc_Xrf00501.propBarcode.maxLength}"
										value="#{pc_Xrf00501.propBarcode.stringValue}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" imeMode="disabled"/>
									</h:inputText>
									<hx:commandExButton type="submit" value="読取"
										tabindex="6"
										styleClass="cmdBtn_dat_s" id="readBarcode"
										action="#{pc_Xrf00501.doReadBarcodeAction}">
									</hx:commandExButton>
								</TD>
              				</TR>
						</TBODY>
				</TABLE>
				
				<BR>
				
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
					<TBODY>
			           <TR>
			              <TH nowrap class="v_a" width="110">
			              		<!--年度 -->
			                	<h:outputText styleClass="outputText" id="lblNendo"
			                		value="#{pc_Xrf00501.propNendo.labelName}"
			                		style="#{pc_Xrf00501.propNendo.labelStyle}">
			                	</h:outputText>
			              </TH>
			              <TD width="120">
			              		<h:inputText styleClass="inputText"
			                		id="htmlNendo" size="5"
			                		value="#{pc_Xrf00501.propNendo.dateValue}"
			                		readonly="#{pc_Xrf00501.propNendo.readonly}"
									tabindex="7"
			                		style="#{pc_Xrf00501.propNendo.style}">
									<f:convertDateTime pattern="yyyy" />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
			                	</h:inputText>
			              </TD>
						  <TH class="v_a" width="110">
								<!--科目コード -->
								<h:outputText styleClass="outputText"
									id="lblKamokuCd"
									value="#{pc_Xrf00501.propKamokuCd.labelName}" 
									style="#{pc_Xrf00501.propKamokuCd.labelStyle}">
								</h:outputText>
						  </TH>
						  <TD width="330" nowrap>
								<h:inputText id="htmlKamokuCd" styleClass="inputText" 
                                    size="10"
                                    onblur="return doKamokuAjax(this, event, 'form1:lblKamokuName');"
									value="#{pc_Xrf00501.propKamokuCd.stringValue}"
									readonly="#{pc_Xrf00501.propKamokuCd.readonly}"
									maxlength="#{pc_Xrf00501.propKamokuCd.maxLength}"
									tabindex="8"
									style="#{pc_Xrf00501.propKamokuCd.style}">
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" imeMode="disabled" />
								</h:inputText>
								<hx:commandExButton type="button"
									styleClass="commandExButton_search" id="searchKamoku"
									tabindex="9"
									onclick="return openKamokuSearchWindow(this, event);">
								</hx:commandExButton>									
          						<h:outputText
          							styleClass="outputText" id="lblKamokuName"
          							value="#{pc_Xrf00501.propKamokuName.stringValue}">
          						</h:outputText>				
      					  </TD>
          				</TR>				
			
		           		<TR>
			              <TH nowrap class="v_a" width="100">
			              		<!--分冊 -->
			                	<h:outputText styleClass="outputText" id="lblBunsatu"
			                		value="#{pc_Xrf00501.propBunsatu.labelName}"
			                		style="#{pc_Xrf00501.propBunsatu.labelStyle}">
			                	</h:outputText>
			              </TH>
			              <TD width="120">
			              		<h:inputText styleClass="inputText"
			                		id="htmlBunsatu" size="2"
									readonly="#{pc_Xrf00501.propBunsatu.readonly}"
									maxlength="#{pc_Xrf00501.propBunsatu.maxLength}"
									style="#{pc_Xrf00501.propBunsatu.style}"
			                		value="#{pc_Xrf00501.propBunsatu.stringValue}"
									tabindex="10">
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="disabled" promptCharacter="_" />
			                	</h:inputText>
			              </TD>
			              
			              <TH nowrap class="v_a" width="110">
			              		<!--学籍番号 -->
			                	<h:outputText styleClass="outputText" id="lblGaksekiNo"
			                		value="#{pc_Xrf00501.propGakusekiNo.labelName}"
			                		style="#{pc_Xrf00501.propGakusekiNo.labelStyle}">
			                	</h:outputText>
			              </TH>
			              <TD width="320">
			              		<h:inputText styleClass="inputText"
			                		id="htmlGakusekiNo" size="10"
			                		value="#{pc_Xrf00501.propGakusekiNo.stringValue}"
			                		readonly="#{pc_Xrf00501.propGakusekiNo.readonly}"
			                		style="#{pc_Xrf00501.propGakusekiNo.style}"
			                		maxlength="#{pc_Xrf00501.propGakusekiNo.maxLength}"
			                		onblur="return doGakuseiAjax(this, event, 'form1:lblName');"
									tabindex="11"
			                		>
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="disabled" />
			                	</h:inputText>
			                	<hx:commandExButton type="button"
			                		styleClass="commandExButton_search" id="searchGakuseiNo"
			                		disabled="#{pc_Xrf00501.propSearchGakusekiNo.disabled}"
									tabindex="12"
			                		onclick="openSubWindow('form1:htmlGakusekiNo');"
			                		>
			                	</hx:commandExButton>
								<h:outputText styleClass="outputText"
									id="lblName"
									value="#{pc_Xrf00501.propName.stringValue}">
								</h:outputText>
			              </TD>
			            </TR>
			            			              
         			   <TR>
			              <TH nowrap class="v_a" width="100">
								<!--添削教員コード -->
			                	<h:outputText styleClass="outputText" id="lblTensakuKyouinCd"
			                		value="#{pc_Xrf00501.propTensakuKyouinCd.labelName}"
			                		style="#{pc_Xrf00501.propTensakuKyouinCd.labelStyle}">
			                	</h:outputText>
			              </TH>
			              <TD colspan="3">
								<h:inputText id="htmlTensakuKyouinCd" styleClass="inputText" 
									readonly="#{pc_Xrf00501.propTensakuKyouinCd.readonly}" 
									disabled="#{pc_Xrf00501.propTensakuKyouinCd.disabled}"
									maxlength="#{pc_Xrf00501.propTensakuKyouinCd.maxLength}"
									style="#{pc_Xrf00501.propTensakuKyouinCd.style}"
									value="#{pc_Xrf00501.propTensakuKyouinCd.stringValue}"
									tabindex="13"
									size="10"
									onblur="return getJinjiName(this, event,'form1:lblTensakuKyouinName');">
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="disabled" />
								</h:inputText>
								<hx:commandExButton type="button"
									styleClass="commandExButton_search" id="searchTensakuKyouin"
									disabled="#{pc_Xrf00501.propSearchTensakuKyouin.disabled}"
									rendered="#{pc_Xrf00501.propSearchTensakuKyouin.rendered}"
									style="#{pc_Xrf00501.propSearchTensakuKyouin.style}"
									tabindex="14"
									onclick="return func_1(this, event, 'form1:htmlTensakuKyouinCd');">
								</hx:commandExButton>
								<h:outputText
          							styleClass="outputText"
          							id="lblTensakuKyouinName"
          							value="#{pc_Xrf00501.propTensakuKyouinName.stringValue}">
          						</h:outputText>
          					</TD>
          				</TR>				              
			              
         			   <TR>
			              <TH nowrap class="v_a" width="100">
								<!--更新職員コード -->
			                	<h:outputText styleClass="outputText" id="lblKousinKyouinCd"
			                		value="#{pc_Xrf00501.propKousinKyouinCd.labelName}"
			                		style="#{pc_Xrf00501.propKousinKyouinCd.labelStyle}">
			                	</h:outputText>
			              </TH>
			              <TD colspan="3">
								<h:inputText id="htmlKousinKyouinCd" styleClass="inputText" 
									readonly="#{pc_Xrf00501.propKousinKyouinCd.readonly}" 
									disabled="#{pc_Xrf00501.propKousinKyouinCd.disabled}"
									maxlength="#{pc_Xrf00501.propKousinKyouinCd.maxLength}"
									style="#{pc_Xrf00501.propKousinKyouinCd.style}"
									value="#{pc_Xrf00501.propKousinKyouinCd.stringValue}"
									tabindex="15"
									size="10"
									onblur="return getJinjiName(this, event,'form1:lblKousinKyouinName');">
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="disabled" />
								</h:inputText>
								<hx:commandExButton type="button"
									styleClass="commandExButton_search" id="searchKousinKyouin"
									disabled="#{pc_Xrf00501.propSearchKousinKyouin.disabled}"
									rendered="#{pc_Xrf00501.propSearchKousinKyouin.rendered}"
									style="#{pc_Xrf00501.propSearchKousinKyouin.style}"
									tabindex="16"
									onclick="return func_2(this, event, 'form1:htmlKousinKyouinCd');">
								</hx:commandExButton>
								<h:outputText
          							styleClass="outputText"
          							id="lblKousinKyouinName"
          							value="#{pc_Xrf00501.propKousinKyouinName.stringValue}">
          						</h:outputText>
          					</TD>
          				</TR>				              				              
			              
						<TR>
							<TH class="v_a" width="100">
								<h:outputText styleClass="outputText"
									id="lblUketukebi"
									value="レポート受付日">
								</h:outputText>
							</TH>
							<TD colspan="3">
								<h:inputText id="htmlUketukebiFrom"
									styleClass="inputText" size="12"
									style="#{pc_Xrf00501.propUketukebiFrom.style}"
									tabindex="17"
									value="#{pc_Xrf00501.propUketukebiFrom.dateValue}">
									<f:convertDateTime />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_"  imeMode="disabled" />
							<hx:inputHelperDatePicker />
						</h:inputText>
								
								<h:outputText styleClass="outputText" id="text1" value="～">
								</h:outputText>
								
								<h:inputText id="htmlUketukebiTo"
									styleClass="inputText" size="12"
									style="#{pc_Xrf00501.propUketukebiTo.style}"
									tabindex="18"
									value="#{pc_Xrf00501.propUketukebiTo.dateValue}">
									<f:convertDateTime />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_"  imeMode="disabled" />
							<hx:inputHelperDatePicker />
						</h:inputText>
							</TD>
						</TR>
										              
						<TR>
							<TH class="v_a" width="100">
								<h:outputText styleClass="outputText"
									id="lblNyuryokubi" value="評価入力日"
									style="#{pc_Xrf00501.propNyuryokubiFrom.labelStyle}">
								</h:outputText>
							</TH>
							<TD colspan="3">
								<h:inputText id="htmlNyuryokubiFrom"
									styleClass="inputText" style="#{pc_Xrf00501.propNyuryokubiFrom.style}"
									tabindex="19"
									value="#{pc_Xrf00501.propNyuryokubiFrom.dateValue}" size="12">
									<f:convertDateTime />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText>
								
								<h:outputText styleClass="outputText" id="text2" value="～">
								</h:outputText>
								
								<h:inputText id="htmlNyuryokubiTo"
									styleClass="inputText" style="#{pc_Xrf00501.propNyuryokubiTo.style}"
									tabindex="20"
									value="#{pc_Xrf00501.propNyuryokubiTo.dateValue}" size="12">
									<f:convertDateTime />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
							<hx:inputHelperDatePicker />
						</h:inputText>
							</TD>
						</TR>				              

						<TR>
							<TH class="v_a">
								<h:outputText styleClass="outputText"
									id="lblHyoukaJyoutai"
									value="#{pc_Xrf00501.propHyoukaJyoutai.name}"
									style="#{pc_Xrf00501.propHyoukaJyoutai.style}">
								</h:outputText>
							</TH>
							<TD colspan="3">
								<h:selectManyCheckbox
									disabledClass="selectManyCheckbox_Disabled"
									styleClass="selectManyCheckbox" 
									id="htmlHyoukaJyoutai"
									tabindex="21"
									value="#{pc_Xrf00501.propHyoukaJyoutai.value}">
									<f:selectItem itemLabel="未評価" itemValue="0" />
									<f:selectItem itemLabel="評価1回" itemValue="1" />
									<f:selectItem itemLabel="評価2回" itemValue="2" />
							</h:selectManyCheckbox></TD>
						</TR>				              
			       </TBODY>
				</TABLE>
				
				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
						<TBODY>
							<TR>
						
								<TD>
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="search"
										action="#{pc_Xrf00501.doSearchAction}"
										tabindex="22"
										value="検索">
									</hx:commandExButton>
							
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="clear"
										action="#{pc_Xrf00501.doClearAction}"
										tabindex="23"
										value="クリア">
									</hx:commandExButton>
								</TD>	
								
							</TR>
						</TBODY>
				</TABLE>					       
				       
				<TABLE border="0">
					<TBODY>
						<TR>
							<TD width="940" align="right">
								<h:outputText styleClass="outputText"
									id="lblKensakuListCount"
									value="#{pc_Xrf00501.propKensakuListCount.stringValue}"
									style="#{pc_Xrf00501.propKensakuListCount.style}">
								</h:outputText>
							</TD>
						</TR>
						
						<TR>
						<!-- ↓データテーブル部↓ -->
						<TD>
							<DIV id="listScroll" class="listScroll" style="height: 256px;">
								<h:dataTable border="0" cellpadding="0" cellspacing="0"
									columnClasses="columnClass" headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Xrf00501.propKensakuList.rowClasses}"
									styleClass="meisai_scroll" id="htmlKensakuList" width="940"
									value="#{pc_Xrf00501.propKensakuList.list}" var="varlist">

									<h:column id="column1">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="科目コード"
												id="lblListKamokCdColumn">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKamokCd"
												value="#{varlist.kamokCd.stringValue}">
										</h:outputText>
										<f:attribute value="100" name="width" value="text-align: center" name="style" />
									</h:column>
									
									<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="科目名"
													id="lblListKamokNameColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListKamokName"
												value="#{varlist.kamokName.displayValue}"
												title="#{varlist.kamokName.stringValue}"
											>
											</h:outputText>
											<f:attribute value="180" name="width" />
									</h:column>

									<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="分冊"
													id="lblListBunsatuColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListBunsatu"
												value="#{varlist.bunsatu.stringValue}">
											</h:outputText>
											<f:attribute value="35" name="width" value="text-align: center" name="style" />
									</h:column>

									<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="枝番"
													id="lblListEdabanColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListEdaban"
												value="#{varlist.edaban}">
											</h:outputText>
											<f:attribute value="35" name="width" value="text-align: center" name="style" />
									</h:column>
									
									<h:column id="column5">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="教員コード"
												id="tensakuKyoinCd">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText"
											id="htmlListKyouinCd" 
											value="#{varlist.tensakuKyoinCd.stringValue}">
										</h:outputText>
										<f:attribute value="80" name="width" />
									</h:column>
										
									<h:column id="column6">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="教員氏名"
												id="tensakuKyoinSimei">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText"
											id="htmlListKyouinSimei" 
											value="#{varlist.tensakuKyoinSimei.displayValue}"
											title="#{varlist.tensakuKyoinSimei.stringValue}"
											>
										</h:outputText>
										<f:attribute value="130" name="width" />
									</h:column>
	
									<h:column id="column7">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="学籍番号"
												id="gakusekiNo">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText"
											id="htmlListGakusekiNo" 
											value="#{varlist.gakusekiNo.stringValue}">
										</h:outputText>
										<f:attribute value="90" name="width" />
									</h:column>

									<h:column id="column8">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="氏名"
												id="gakuseiShimei">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText"
											id="htmlListSimei" 
											value="#{varlist.gakuseiShimei.displayValue}"
											title="#{varlist.gakuseiShimei.stringValue}"
											>
										</h:outputText>
										<f:attribute value="130" name="width" />
									</h:column>
										
									<h:column id="column9">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="評価"
													id="hyouka">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
											id="htmlListHyouka" 
											value="#{varlist.hyouka.stringValue}" 
											>
											</h:outputText>
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="70" name="width" />
									</h:column>
												
									<h:column id="column10">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="評価状態"
													id="hyoukaJyoutai">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
											id="htmlListHyoukaJyoutai" 
											value="#{varlist.hyoukaJyoutai.stringValue}" 
											style="text-align: center">
											</h:outputText>
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="80" name="width" />
									</h:column>

									<h:column id="column11">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit"
											value="編集"
											styleClass="commandExButton" tabindex="24"
											id="edit"
											action="#{pc_Xrf00501.doEditAction}"
											disabled="#{varlist.propListEdit.disabled}"
											style="#{varlist.propListEdit.style}">
										</hx:commandExButton>
										<f:attribute value="true" name="nowrap" />
										<f:attribute value="30" name="width" />
									</h:column>							
								</h:dataTable>
								
							</DIV>
						</TD>
						</TR>
					</TBODY>
				</TABLE>	
									
				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
					
							<TD>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="csv"
									action="#{pc_Xrf00501.doCsvAction}"
									tabindex="25"
									value="CSV作成"
									>
								</hx:commandExButton>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="sitei"
									tabindex="26"
									value="出力項目指定"
									disabled="#{pc_Xrf00501.propSitei.disabled}"
									action="#{pc_Xrf00501.doSetoutputAction}">
								</hx:commandExButton>
							</TD>	
						</TR>
					</TBODY>
				</TABLE>

				</DIV>
			</DIV>
			
			<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrf00501.propBackPhase.integerValue}">
			</h:inputHidden>
		</DIV>
		<h:inputHidden
			value="#{pc_Xrf00501.propKensakuList.scrollPosition}"
			id="scroll">
		</h:inputHidden>
		</h:form>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		<h:inputHidden value="#{pc_Xrf00501.propActiveControl.integerValue}"
			id="htmlActiveControl">
			<f:convertNumber />
		</h:inputHidden>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
