<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00101.java" --%><%-- /jsf:pagecode --%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@ taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK href="../../theme/stylesheet.css" rel="stylesheet" type="text/css">
<TITLE>Xrl00101.jsp</TITLE>
<script language="JavaScript">
    
    //ボタンの活性化制御
    function fncButtonActive(){		
    	//hdnConfirmFlag値が復元
    	document.getElementById('form1:hdnConfirmFlag').value = '0';
    	
		//フォーカスの設定
		setFocus();

		//スクロール位置保持
		changeScrollPosition('scroll', 'listScroll');
	}
	
	//フォーカスの設定
	function setFocus(){
		
		var id = null;

		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;

		//選択ボタン押下時
		if (document.getElementById('form1:htmlMenjList:0:select') != null) {
			if ((id != null) && (id != "")) {
				document.getElementById(id).focus();
			} else {
				document.getElementById("form1:htmlMenjList:0:select").focus();
			}
		}
	}

	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		document.getElementById('form1:hdnConfirmFlag').value = '1';
		indirectClick('delete');
	}

	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		return;
	}

	</script>


</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncButtonActive();">

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00101.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrl00101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00101.screenName}"></h:outputText></div>


			<!--↓OUTER↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　</DIV>

			<!--↓CONTENT↓-->
			<DIV id="content">
			<DIV class="column" align="center">

			<TABLE border="0" cellpadding="0" cellspacing="0" width="750px">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText styleClass="outputText"
										id="text5" value="#{pc_Xrl00101.propTeateGyomuList.listCount}">
									</h:outputText> <h:outputText styleClass="outputText"
										id="text6" value="件">
									</h:outputText></TD>
								</TR>
								<TR>
									<TD>
									<DIV style="height: 338px; width=100%;" id="listScroll"
										onscroll="setScrollPosition('scroll',this);"
										class="listScroll"><h:dataTable styleClass="meisai_scroll"
										id="htmlTeateGyomuList"
										value="#{pc_Xrl00101.propTeateGyomuList.list}" var="varlist"
										footerClass="footerClass"
										rows="#{pc_Xrl00101.propTeateGyomuList.rows}" width="750px"
										rowClasses="#{pc_Xrl00101.propTeateGyomuList.rowClasses}"
										headerClass="headerClass">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText value="#{pc_Xrl00101.propTantoGyomu.name}"
													styleClass="outputText" id="lblTantoGyomu"></h:outputText>
											</f:facet>
											<h:outputText id="lblTantoGyomuValue"
												value='#{varlist.teateTantoGyomuNm}' styleClass="outputText"></h:outputText>
											<f:attribute value="80px" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText id="lblTeateGyomuCd"
													value="#{pc_Xrl00101.propTeateGyomuCd.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<h:outputText id="lblTeateGyomuCdValue"
												value="#{varlist.teateGyomuCd}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="100px" name="width" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText id="lblTeateGyomuNm"
													value="#{pc_Xrl00101.propTeateGyomuNm.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<h:outputText id="lblTeateGyomuNmValue"
												value="#{varlist.teateGyomuNm}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="*" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText id="lblTeateCd"
													value="#{pc_Xrl00101.propTeate.name}"
													styleClass="outputText"></h:outputText>
											</f:facet>
											<h:outputText id="lblTeateCdValue" value="#{varlist.teateCd}"
												styleClass="outputText"></h:outputText>
											<f:attribute value="100px" name="width" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText id="lblTeateNm"
													value="#{pc_Xrl00101.propTeateNm.name}"
													styleClass="outputText"></h:outputText>
											</f:facet>
											<h:outputText id="lblTeateNmValue" value="#{varlist.teateNm}"
												styleClass="outputText"></h:outputText>
											<f:attribute value="220px" name="width" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="40px" name="width" />
											<hx:commandExButton type="submit" value="選択"
												styleClass="cmdBtn_dat_s" id="select"
												action="#{pc_Xrl00101.doSelectAction}">
											</hx:commandExButton>
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="20"></TD>
					</TR>
					<TR>
						<TD width="100%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="100%">
							<TBODY>
								<TR>
									<TH width="150px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTantoGyomuMeisai"
										value="#{pc_Xrl00101.propTantoGyomu.name}"
										style="#{pc_Xrl00101.propTantoGyomu.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:selectOneMenu
										styleClass="selectOneMenu" id="cmbTantoGyomu"
										style="width:330px"
										value="#{pc_Xrl00101.propTantoGyomu.stringValue}">
										<f:selectItems value="#{pc_Xrl00101.propTantoGyomu.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblTeateGyomuCdMeisai"
										value="#{pc_Xrl00101.propTeateGyomuCd.labelName}"
										style="#{pc_Xrl00101.propTeateGyomuCd.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="*"><h:inputText styleClass="inputText"
										id="txtTeateGyomuCd"
										value="#{pc_Xrl00101.propTeateGyomuCd.stringValue}"
										style="#{pc_Xrl00101.propTeateGyomuCd.style}" size="6"
										maxlength="#{pc_Xrl00101.propTeateGyomuCd.maxLength}"
										disabled="#{pc_Xrl00101.propTeateGyomuCd.disabled}">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblTeateGyomuNmMeisai"
										value="#{pc_Xrl00101.propTeateGyomuNm.labelName}"
										style="#{pc_Xrl00101.propTeateGyomuNm.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="*"><h:inputText styleClass="inputText"
										id="txtTeateGyomuNm"
										value="#{pc_Xrl00101.propTeateGyomuNm.stringValue}"
										style="#{pc_Xrl00101.propTeateGyomuNm.style}" size="75"
										maxlength="#{pc_Xrl00101.propTeateGyomuNm.maxLength}">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblTeateMeisai"
										value="#{pc_Xrl00101.propTeateNm.name}"
										style="#{pc_Xrl00101.propTeateNm.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:selectOneMenu
										styleClass="selectOneMenu" id="cmbTeate"
										style="width:330px"
										value="#{pc_Xrl00101.propTeateNm.stringValue}">
										<f:selectItems value="#{pc_Xrl00101.propTeateNm.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD></TD>
					</TR>
					<TR>
						<TD width="100%">
						<TABLE width="100%" border="0" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="regist"
										confirm="#{msg.SY_MSG_0001W}"
										action="#{pc_Xrl00101.doRegistAction}">
									</hx:commandExButton>&nbsp; <hx:commandExButton type="submit"
										value="削除" styleClass="commandExButton_dat" id="delete"
										confirm="#{msg.SY_MSG_0004W}"
										action="#{pc_Xrl00101.doDeleteAction}">
									</hx:commandExButton>&nbsp; <hx:commandExButton type="submit"
										value="クリア" styleClass="commandExButton_etc" id="clear"
										action="#{pc_Xrl00101.doClearAction}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrl00101.propTeateGyomuList.scrollPosition}" id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrl00101.propScrollPos.stringValue}"
				id="htmlScrollPos"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrl00101.propConfirmFlag.integerValue}"
				id="hdnConfirmFlag">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>

	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
