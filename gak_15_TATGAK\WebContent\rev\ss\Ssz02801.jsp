<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz02801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz02801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz02801.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz02801.doCloseDispAction}"
></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz02801.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz02801.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD width="550" align="right"><h:outputText
							styleClass="outputText" id="htmlCount" value="#{pc_Ssz02801.propCount.stringValue}" style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="htmlCountlbl" value="件" style="font-size: 8pt"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD width="551">
						<div id="listScroll" class="listScroll" style="height:296px;" onscroll="setScrollPosition('scroll',this);">
						<h:dataTable border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz02801.propKabJojo.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz02801.propKabJojo.list}" var="varlist"
							width="532">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblCd" styleClass="outputText" value="区分"
										style="clear: static; text-align: center; vertical-align: middle"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblTabCd"
									value="#{varlist.kabuJoujouKbn}"></h:outputText>
								<f:attribute value="50" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblName" value="名称"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblTabName"
									value="#{varlist.kabuJoujouKbnName}"></h:outputText>
								<f:attribute value="445" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz02801.doSelectAction}"></hx:commandExButton>
								<f:attribute value="30" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable></TD>
						<TD width="363"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD width="550">
						<TABLE border="0" class="table" width="550" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TH class="v_a" align="left" width="169"><h:outputText
										styleClass="outputText" id="lblKabuJoujouKbn"
										value="#{pc_Ssz02801.propKabuJoujouKbn.labelName}"
										style="#{pc_Ssz02801.propKabuJoujouKbn.labelStyle}"></h:outputText></TH>
									<TD width="373"><h:inputText value="#{pc_Ssz02801.propKabuJoujouKbn.stringValue}"
										maxlength="#{pc_Ssz02801.propKabuJoujouKbn.maxLength}"
										style="#{pc_Ssz02801.propKabuJoujouKbn.style}" size="2" id="htmlKabuJoujouKbn">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" align="left" width="169"><h:outputText
										styleClass="outputText" id="lblKabuJoujouKbnName"
										value="#{pc_Ssz02801.propKabuJoujouKbnName.labelName}"
										style="#{pc_Ssz02801.propKabuJoujouKbnName.labelStyle}"></h:outputText></TH>
									<TD width="373"><h:inputText
										value="#{pc_Ssz02801.propKabuJoujouKbnName.stringValue}"
										styleClass="inputText"
										style="#{pc_Ssz02801.propKabuJoujouKbnName.style}"
										maxlength="#{pc_Ssz02801.propKabuJoujouKbnName.maxLength}"
										size="50" id="htmlKabuJoujouKbnName">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="確 定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz02801.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削 除" styleClass="commandExButton_dat"
							confirm="#{msg.SY_MSG_0004W}" id="delete"
							action="#{pc_Ssz02801.doDeleteAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz02801.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz02801.propKabJojo.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE = "JavaScript">
changeScrollPosition("scroll","listScroll")
</SCRIPT>
</HTML>

