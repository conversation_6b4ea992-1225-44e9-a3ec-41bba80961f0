<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00401.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00401.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 --> <hx:commandExButton
				type="submit" value="科目情報登録" styleClass="commandExButton"
				tabindex="6" id="kamokuTouroku"
				disabled="#{pc_Xrh00401.propKamokuTouroku.disabled}"
				action="Xrh00403">
			</hx:commandExButton> <hx:commandExButton type="submit" value="新規登録"
				styleClass="commandExButton" id="sinkiTouroku" tabindex="7"
				disabled="#{pc_Xrh00401.propSinkiTouroku.disabled}"
				action="Xrh00402T01">
			</hx:commandExButton> <!-- ↑ここにボタンを配置 --></DIV>

			<DIV id="content">
			<DIV class="column" align="center">

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="700">
				<TBODY>
					<TR>
						<TH class="v_a" width="190"><!--年度 --> <h:outputText
							styleClass="outputText" id="lblNendo"
							value="#{pc_Xrh00401.propNendo.labelName}"
							style="#{pc_Xrh00401.propNendo.labelStyle}">
						</h:outputText></TH>
						<TD><h:inputText styleClass="inputText" id="htmlNendo" size="4"
							value="#{pc_Xrh00401.propNendo.dateValue}"
							maxlength="#{pc_Xrh00401.propNendo.maxLength}" tabindex="1"
							disabled="#{pc_Xrh00401.propNendo.disabled}"
							style="#{pc_Xrh00401.propNendo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="disabled" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="700">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="190"><!--試験回数 --> <h:outputText
							styleClass="outputText" id="lblKaisu"
							value="#{pc_Xrh00401.propKaisu.labelName}"
							style="#{pc_Xrh00401.propKaisu.labelStyle}">
						</h:outputText></TH>
						<TD><h:inputText styleClass="inputText" id="htmlKaisu" size="2"
							value="#{pc_Xrh00401.propKaisu.integerValue}"
							disabled="#{pc_Xrh00401.propKaisu.disabled}"
							maxlength="#{pc_Xrh00401.propKaisu.maxLength}" tabindex="2"
							style="#{pc_Xrh00401.propKaisu.style}">
							<f:convertNumber type="number" pattern="#0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" imeMode="disabled" />
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE width="700" border="0" cellpadding="0" cellspacing="0"
				class="button_bar">
				<TBODY>
					<TR>

						<TD><hx:commandExButton type="submit"
							action="#{pc_Xrh00401.doSearchAction}" tabindex="3"
							styleClass="commandExButton_dat" id="search" value="検索">
						</hx:commandExButton></TD>

					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="700" align="right"><h:outputText
							styleClass="outputText" id="lblKensakuListCount"
							value="#{pc_Xrh00401.propKensakuListCount.stringValue}">
						</h:outputText></TD>
					</TR>

					<TR>
						<!-- ↓データテーブル部↓ -->
						<TD>
						<DIV id="listScroll" class="listScroll" style="height: 400px;"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrh00401.propKensakuList.rowClasses}"
							styleClass="meisai_scroll" id="htmlKensakuList" width="700"
							value="#{pc_Xrh00401.propKensakuList.list}" var="varlist">

							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="回数"
										id="lblListKaisuColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									value="#{varlist.kaisu.stringValue}" id="htmlListKaisu">
								</h:outputText>
								<f:attribute value="30" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="試験名称"
										id="lblListSikenNameColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									title="#{varlist.sikenName.stringValue}"
									value="#{varlist.sikenName.displayValue}"
									id="htmlListSikenName">
								</h:outputText>
								<f:attribute value="290" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>

							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="試験日"
										id="lblListSikenbiColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									value="#{varlist.sikenbi.dateValue}" id="htmlListSikenbi">
									<f:convertDateTime pattern="yyyy/MM/dd" />
								</h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="試験日曜日"
										id="lblListSikenbiYoubiColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListSikenbiYoubi"
									value="#{varlist.sikenbiYoubi.stringValue}">
								</h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="試験区分"
										id="lblListSikenKubunColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListSikenKubun"
									value="#{varlist.sikenKubun.displayValue}"
									title="#{varlist.sikenKubun.stringValue}">
								</h:outputText>
								<f:attribute value="150" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>


							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" tabindex="4"
									value="#{varlist.btnEdit.caption}"
									action="#{pc_Xrh00401.doEditAction}"
									styleClass="commandExButton" id="edit">
								</hx:commandExButton>
								<hx:commandExButton type="submit"
									value="#{varlist.btnDelete.caption}" tabindex="5"
									onclick="return confirm('#{msg.SY_MSG_0004W}');"
									action="#{pc_Xrh00401.doDeleteAction}"
									styleClass="commandExButton" id="delete">
								</hx:commandExButton>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="70" name="width" />
								<f:attribute value="center" name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
			<h:inputHidden value="#{pc_Xrh00401.propKensakuList.scrollPosition}"
				id="scroll"></h:inputHidden></DIV>

			</DIV>
		</h:form>

		<!-- フッターインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
	changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
