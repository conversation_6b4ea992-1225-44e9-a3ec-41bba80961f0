<%-- 
	証明書出力
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrk/Xrk00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrk00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

	// 学生検索画面
	function openSubWindow() {
	  var url="${pageContext.request.contextPath}/faces/rev/co/";
	  var outputTarget = document.getElementsByName('form1:htmlOutputTarget');
	
	  if (outputTarget[1].checked) {
	    url = url + "pCob0101.jsp"
	              + "?retFieldName=form1:htmlGakuSekiCd"
	              + "&gakuseiSearchKbn=0";
	    openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  } else if (outputTarget[2].checked) {
	    url = url + "pCob0201.jsp"
	              + "?retFieldName=form1:htmlGakuSekiCd"
	              + "&retFieldName2=form1:htmlHidSotNendo"
	              + "&retFieldName3=form1:htmlHidSotGakki"
	              + "&gakuseiSearchKbn=0";  
	    openModalWindow(url, "PCob0201", "<%=com.jast.gakuen.rev.co.PCob0201.getWindowOpenOption() %>");
	  } else {
	    // 何もしない
	  }
	
	  return false;
	}

	// 学生氏名を取得する
	function getGakuseiNmOnAjax(thisObj, thisEven, targetLabel) {
	  var outputTarget = document.getElementsByName('form1:htmlOutputTarget');
	  if (outputTarget[1].checked) {
	    var servlet = "rev/xrk/XrkCobGakusekiAJAX";
	    var args = new Array();
	    args['code1'] = thisObj.value;
	
	    var ajaxUtil = new AjaxUtil();
	    ajaxUtil.getPluralValueSetMethod(servlet, targetLabel, args, "setZaiSotinfo");
	  }else if (outputTarget[2].checked) {
	  	var servlet = "rev/xrk/XrkCobGakusekiAJAX";
	    var args = new Array();
	    args['code1'] = thisObj.value;
	
	    var ajaxUtil = new AjaxUtil();
	    ajaxUtil.getPluralValueSetMethod(servlet, targetLabel, args, "setZaiSotinfo");
		}
	}
	// 在学生・卒業生情報を取得する（CallBack関数）
	function setZaiSotinfo(value){
	
		var gakuseiName = value['gakuseiName'];

		if(gakuseiName != ""){
			document.getElementById('form1:htmlGakusekiName').innerHTML = value['gakuseiName'];
			
			var gakuseiKbn = value['syuturyokuKbu'];
			
				if(gakuseiKbn == 0){
					document.getElementsByName('form1:htmlOutputTarget')[1].checked = true;
				}else if(gakuseiKbn == 1){
					document.getElementsByName('form1:htmlOutputTarget')[2].checked = true;
				}
		}else {
		    document.getElementById('form1:htmlGakusekiName').innerHTML = "";
		}
	  }

	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfilm").value = "0";
		} catch (e) {
		}
	}
	
	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfilm").value = "1";
			indirectClick(document.getElementById("form1:htmlAction").value);
		} catch (e) {
		}
	}
	
	function radioChange() {
	
		//Radio.発行するを選択した場合に発行番号テキストエリアを活性化
		 var selObjs = document.getElementsByName("form1:htmlHakkoNoKbn");
		 var selValue = "";
		 
		 for(i=1; i<selObjs.length; i++)  {
		   if(selObjs[i].checked) {
		   	selValue = selObjs[i].value;
		   }
		 }
		 var flag = selValue == 2;
		 if(flag){
		 	document.getElementById('form1:htmlHakkoNo').disabled = false;
		 	
		 } else {
		 	document.getElementById('form1:htmlHakkoNo').disabled = true;
		 }
		 
		 var thisObj = document.getElementById('form1:htmlGakusekiCd');
		 //getGakuseiNmOnAjax(this, event, 'form1:htmlGakusekiName');
		 getGakuseiNmOnAjax(thisObj, this.event, 'form1:htmlGakusekiName');
	}
	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="radioChange();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrk00301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrk00301.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrk00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrk00301.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<!-- ↑ここにコンポーネントを配置 -->
			<BR>
			
			<TABLE border="0" class="table" width="600" cellpadding="0"
				cellspacing="0">
				<TBODY>
					<TR>
						<!-- 発行年度 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropHakkoNendo" 
							value="#{pc_Xrk00301.propHakkoNendo.labelName}"
							style="#{pc_Xrk00301.propHakkoNendo.labelStyle}"></h:outputText></TH>
						<TD colspan="5"><h:inputText styleClass="inputText"
							id="htmlHakkoNendo"
							value="#{pc_Xrk00301.propHakkoNendo.value}"
							maxlength="#{pc_Xrk00301.propHakkoNendo.maxLength}" size="4"
                        	disabled="#{pc_Xrk00301.propHakkoNendo.disabled}"
                        	readonly="#{pc_Xrk00301.propHakkoNendo.readonly}"
							style="#{pc_Xrk00301.propHakkoNendo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"/>
						</h:inputText></TD>
					</TR>
					
                    <TR align="center" valign="middle">
                      <TH nowrap class="v_d" width="200">
                      <!--出力対象 -->
                        <h:outputText 
                          styleClass="outputText"
                          id="lblOutputTarget"
                          value="出力対象"
                          style="#{pc_Xrx00100.propOutputTarget.labelStyle}">
                        </h:outputText>
                      </TH>
                      <TD colspan="5">
                        <h:selectOneRadio
                          disabledClass="selectOneRadio_Disabled"
                          styleClass="selectOneRadio"
                          id="htmlOutputTarget"
                  		  disabled="#{pc_Xrk00301.propOutputTarget.disabled}"
                          value="#{pc_Xrk00301.propOutputTarget.value}">
                          <f:selectItem itemValue="0" itemLabel="在学生" />
                          <f:selectItem itemValue="1" itemLabel="卒業生" />
                        </h:selectOneRadio>
                      </TD>
                    </TR>
					
					<TR>
						<!-- 学籍番号 -->
						<TH  class="v_d" width="200"><h:outputText styleClass="outputText"
							id="lblPropGakusekiCd" value="#{pc_Xrk00301.propGakusekiCd.labelName}"
							style="#{pc_Xrk00301.propGakusekiCd.labelStyle}">
							</h:outputText></TH>
					    <TD colspan="5"><h:inputText
		                    styleClass="inputText" id="htmlGakusekiCd"
		                    value="#{pc_Xrk00301.propGakusekiCd.value}"
		                   	style="ime-mode:disabled"
                        	disabled="#{pc_Xrk00301.propGakusekiCd.disabled}"
                        	readonly="#{pc_Xrk00301.propGakusekiCd.readonly}"
		                    maxlength="#{pc_Xrk00301.propGakusekiCd.maxLength}"
		                    onblur="return getGakuseiNmOnAjax(this, event, 'form1:htmlGakusekiName');" size="10"></h:inputText>
		                    <hx:commandExButton
		                    type="button" styleClass="commandExButton_search" id="search"
		                    disabled="#{pc_Xrk00301.propSelect.disabled}"
		                    onclick="openSubWindow();"></hx:commandExButton><h:outputText
							styleClass="outputText" id="lblGakusekiNameBlank" value="　"></h:outputText>
					        <h:outputText styleClass="outputText"
							id="htmlGakusekiName" value=" "></h:outputText></TD>
					</TR>

					<TR>
						<!-- 証明書種類 -->
						<TH nowrap class="v_d" width="200">
							<h:outputText styleClass="outputText" id="lblPropShomShurui"
							value="#{pc_Xrk00301.propShomShurui.labelName}"
							style="#{pc_Xrk00301.propShomShurui.labelStyle}"></h:outputText></TH>
                        <TD colspan="5">
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlShomShurui"
                            	value="#{pc_Xrk00301.propShomShurui.value}"
                            	disabled="#{pc_Xrk00301.propShomShurui.disabled}"
                            	style="width:350px">
                            	<f:selectItems value="#{pc_Xrk00301.propShomShurui.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>
					
					<TR>
						<!-- 使用目的 -->
						<TH nowrap class="v_d" width="200">
							<h:outputText styleClass="outputText" id="lblPropMokuteki"
							value="#{pc_Xrk00301.propMokuteki.labelName}"
							style="#{pc_Xrk00301.propMokuteki.labelStyle}"></h:outputText></TH>
                        <TD colspan="5">
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlMokuteki"
                            	value="#{pc_Xrk00301.propMokuteki.value}"
                            	disabled="#{pc_Xrk00301.propMokuteki.disabled}"
                            	style="width:350px">
                            	<f:selectItems value="#{pc_Xrk00301.propMokuteki.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>
					
					<TR>
						<!-- 発行通数 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropHakkoTusu" 
							value="#{pc_Xrk00301.propHakkoTusu.labelName}"
							style="#{pc_Xrk00301.propHakkoTusu.labelStyle}"></h:outputText></TH>
						<TD colspan="5"><h:inputText styleClass="inputText"
							id="htmlHakkoTusu"
							value="#{pc_Xrk00301.propHakkoTusu.integerValue}"
							maxlength="#{pc_Xrk00301.propHakkoTusu.maxLength}" size="2"
                    		disabled="#{pc_Xrk00301.propHakkoTusu.disabled}"
                        	readonly="#{pc_Xrk00301.propHakkoTusu.readonly}"
							style="#{pc_Xrk00301.propHakkoTusu.style}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					
					<TR>
						<!-- 発行番号 -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblHakkoNoKbn" 
							value="#{pc_Xrk00301.propHakkoNoKbn.labelName}"
							style="#{pc_Xrk00301.propHakkoNoKbn.labelStyle}"></h:outputText></TH>
						<TD>
							<h:selectOneRadio
								styleClass="selectOneRadio" id="htmlHakkoNoKbn"
                        		readonly="#{pc_Xrk00301.propHakkoNoKbn.readonly}"
                        		disabled="#{pc_Xrk00301.propHakkoNoKbn.disabled}"
								value="#{pc_Xrk00301.propHakkoNoKbn.value}"
								onclick="radioChange();">
								<f:selectItem itemValue="0" itemLabel="発行する" />
								<f:selectItem itemValue="1" itemLabel="発行しない" />
								<f:selectItem itemValue="2" itemLabel="指定する" />
							</h:selectOneRadio></TD>
						<TD colspan="1">
							<h:inputText styleClass="inputText"
								id="htmlHakkoNo"
								value="#{pc_Xrk00301.propHakkoNo.integerValue}"
								style="#{pc_Xrk00301.propHakkoNo.style}"
								maxlength="#{pc_Xrk00301.propHakkoNo.maxLength}" size="7"
                        		disabled="#{pc_Xrk00301.propHakkoNo.disabled}"
                        		readonly="#{pc_Xrk00301.propHakkoNo.readonly}">
							<f:convertNumber type="number" pattern="######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="選択" styleClass="commandExButton"
							id="select"
							action="#{pc_Xrk00301.doSelectAction}"
							disabled="#{pc_Xrk00301.propSelect.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="解除" styleClass="commandExButton"
							id="unselect"
							action="#{pc_Xrk00301.doUnselectAction}"
							disabled="#{pc_Xrk00301.propUnSelect.disabled}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="table" width="600" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR style="${pc_Xrk00301.dispSiteiYmd}">
						<!-- 指定年月日 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropSiteiYmd" 
							value="#{pc_Xrk00301.propSiteiYmd.labelName}"
							style="#{pc_Xrk00301.propSiteiYmd.labelStyle}"></h:outputText></TH>
							
						<TD><h:inputText styleClass="inputText"
                    		id="htmlSiteiYmd"
                    		value="#{pc_Xrk00301.propSiteiYmd.dateValue}">
                    		<f:convertDateTime />
                    		<hx:inputHelperDatePicker />
                    		<hx:inputHelperAssist errorClass="inputText_Error"
                      		promptCharacter="_"></hx:inputHelperAssist>
                  			</h:inputText></TD>
					</TR>
					
					<TR style="${pc_Xrk00301.dispSiteiYm}">
						<!-- 指定年月 -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblPropSiteiYear" 
							value="#{pc_Xrk00301.propSiteiYear.labelName}"
							style="#{pc_Xrk00301.propSiteiYear.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlSiteiYear"
							value="#{pc_Xrk00301.propSiteiYear.integerValue}"
							maxlength="#{pc_Xrk00301.propSiteiYear.maxLength}" size="4"
							style="#{pc_Xrk00301.propSiteiYear.style}">
							<f:convertNumber type="number" pattern="###0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText>
							&nbsp;年&nbsp;
							<h:inputText styleClass="inputText"
							id="htmlSiteiMonth"
							value="#{pc_Xrk00301.propSiteiMonth.integerValue}"
							maxlength="#{pc_Xrk00301.propSiteiMonth.maxLength}" size="2"
							style="#{pc_Xrk00301.propSiteiMonth.style}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							</h:inputText>
							&nbsp;月&nbsp;</TD>
					</TR>
					
					<TR style="${pc_Xrk00301.dispSiteiKikan1}">
						<!-- 指定期間1 -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblPropSiteiKikan1From" 
							value="#{pc_Xrk00301.propSiteiKikan1From.labelName}"
							style="#{pc_Xrk00301.propSiteiKikan1From.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
                    		id="htmlSiteiKikan1From"
                    		value="#{pc_Xrk00301.propSiteiKikan1From.dateValue}">
                    		<f:convertDateTime />
                    		<hx:inputHelperDatePicker />
                    		<hx:inputHelperAssist errorClass="inputText_Error"
                      		promptCharacter="_"></hx:inputHelperAssist>
	              			</h:inputText>
							&nbsp;～&nbsp;
						    <h:inputText styleClass="inputText"
                    		id="htmlSiteiKikan1To"
                    		value="#{pc_Xrk00301.propSiteiKikan1To.dateValue}">
                    		<f:convertDateTime />
                    		<hx:inputHelperDatePicker />
                    		<hx:inputHelperAssist errorClass="inputText_Error"
                      		promptCharacter="_"></hx:inputHelperAssist>
	              			</h:inputText></TD>
					</TR>

					<TR style="${pc_Xrk00301.dispSiteiKikan2}">
						<!-- 指定期間2 -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblPropSiteiKikan2From" 
							value="#{pc_Xrk00301.propSiteiKikan2From.labelName}"
							style="#{pc_Xrk00301.propSiteiKikan2From.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
                    		id="htmlSiteiKikan2From"
                    		value="#{pc_Xrk00301.propSiteiKikan2From.dateValue}">
                    		<f:convertDateTime />
                    		<hx:inputHelperDatePicker />
                    		<hx:inputHelperAssist errorClass="inputText_Error"
                      		promptCharacter="_"></hx:inputHelperAssist>
	              			</h:inputText>
							&nbsp;～&nbsp;
							<h:inputText styleClass="inputText"
                    		id="htmlSiteiKikan2To"
                    		value="#{pc_Xrk00301.propSiteiKikan2To.dateValue}">
                    		<f:convertDateTime />
                    		<hx:inputHelperDatePicker />
                    		<hx:inputHelperAssist errorClass="inputText_Error"
                      		promptCharacter="_"></hx:inputHelperAssist>
	              			</h:inputText></TD>
					</TR>

					<TR style="${pc_Xrk00301.dispMenkyojyoShurui}">
						<!-- 免許状種類 -->
						<TH nowrap class="v_d" width="200">
							<h:outputText styleClass="outputText" id="lblPropMenkyojyoShurui"
							value="#{pc_Xrk00301.propMenkyojyoShurui.labelName}"
							style="#{pc_Xrk00301.propMenkyojyoShurui.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlMenkyojyoShurui"
                            	value="#{pc_Xrk00301.propMenkyojyoShurui.value}"
                            	disabled="#{pc_Xrk00301.propMenkyojyoShurui.disabled}"
                            	style="width:350px">
                            	<f:selectItems value="#{pc_Xrk00301.propMenkyojyoShurui.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>
					
					<TR style="${pc_Xrk00301.dispTitleShurui}">
						<!-- 数学用タイトル種類 -->
						<TH nowrap class="v_d" width="200">
							<h:outputText styleClass="outputText" id="lblPropTitleShurui"
							value="#{pc_Xrk00301.propTitleShurui.labelName}"
							style="#{pc_Xrk00301.propTitleShurui.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlTitleShurui"
                            	value="#{pc_Xrk00301.propTitleShurui.value}"
                            	disabled="#{pc_Xrk00301.propTitleShurui.disabled}"
                            	style="width:350px">
                            	<f:selectItems value="#{pc_Xrk00301.propTitleShurui.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>					

					<TR style="${pc_Xrk00301.dispNinteiTani}">
						<!-- 認定単位 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropNinteiTani" 
							value="#{pc_Xrk00301.propNinteiTani.labelName}"
							style="#{pc_Xrk00301.propNinteiTani.labelStyle}"></h:outputText></TH>
						<TD  colspan="2"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlNinteiTani"
							value="#{pc_Xrk00301.propNinteiTani.value}">
							<f:selectItem itemValue="0" itemLabel="含む" />
							<f:selectItem itemValue="1" itemLabel="含まない" />
						</h:selectOneRadio></TD>
					</TR>					

					<TR style="${pc_Xrk00301.dispBetuhyoShurui}">
						<!-- 学力に関する証明用別表種類 -->
						<TH nowrap class="v_d" width="200">
							<h:outputText styleClass="outputText" id="lblPropBetuhyoShurui"
							value="#{pc_Xrk00301.propBetuhyoShurui.labelName}"
							style="#{pc_Xrk00301.propBetuhyoShurui.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlBetuhyoShurui"
                            	value="#{pc_Xrk00301.propBetuhyoShurui.value}"
                            	disabled="#{pc_Xrk00301.propBetuhyoShurui.disabled}"
                            	style="width:350px">
                            	<f:selectItems value="#{pc_Xrk00301.propBetuhyoShurui.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>					

					<TR style="${pc_Xrk00301.dispFusokuKirikae}">
						<!-- 附則第2条・第3条切替 -->
						<TH nowrap class="v_d" width="200">
							<h:outputText styleClass="outputText" id="lblPropFusokuKirikae"
							value="#{pc_Xrk00301.propFusokuKirikae.labelName}"
							style="#{pc_Xrk00301.propFusokuKirikae.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlFusokuKirikae"
                            	value="#{pc_Xrk00301.propFusokuKirikae.value}"
                            	disabled="#{pc_Xrk00301.propFusokuKirikae.disabled}"
                            	style="width:350px">
                            	<f:selectItems value="#{pc_Xrk00301.propFusokuKirikae.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>					

					<TR style="${pc_Xrk00301.dispKyugakuKirikae}">
						<!-- 休学情報出力有無 -->
						<TH nowrap class="v_d" width="200">
							<h:outputText styleClass="outputText" id="lblPropKyugakuKirikae"
							value="#{pc_Xrk00301.propKyugakuKirikae.labelName}"
							style="#{pc_Xrk00301.propKyugakuKirikae.labelStyle}"></h:outputText></TH>
						<TD  colspan="2"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlKyugakuKirikae"
							value="#{pc_Xrk00301.propKyugakuKirikae.value}">
							<f:selectItem itemValue="1" itemLabel="出力する" />
							<f:selectItem itemValue="0" itemLabel="出力しない" />
						</h:selectOneRadio></TD>
					</TR>					

					<TR style="${pc_Xrk00301.dispSikakuShutokuNendo}">
						<!-- 資格取得年度出力有無 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropSikakuShutokuNendo" 
							value="#{pc_Xrk00301.propSikakuShutokuNendo.labelName}"
							style="#{pc_Xrk00301.propSikakuShutokuNendo.labelStyle}"></h:outputText></TH>
						<TD  colspan="2"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlSikakuShutokuNendo"
							value="#{pc_Xrk00301.propSikakuShutokuNendo.value}">
							<f:selectItem itemValue="1" itemLabel="出力する" />
							<f:selectItem itemValue="0" itemLabel="出力しない" />
						</h:selectOneRadio></TD>
					</TR>					

				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR style="${pc_Xrk00301.dispPdfOut}">
						<TD>
							<hx:commandExButton
								type="submit"
								value="PDF作成"
								styleClass="commandExButton_out"
								id="pdfOut"
								action="#{pc_Xrk00301.doPdfOutAction}"
								confirm="#{msg.SY_MSG_0019W}">
							</hx:commandExButton>

							<hx:commandExButton
								type="submit"
								value="印刷"
								styleClass="commandExButton_out"
								id="print"
								confirm="#{msg.SY_MSG_0022W}"
								action="#{pc_Xrk00301.doPrintAction}">
							</hx:commandExButton>

							<hx:commandExButton
								type="submit"
								value="印刷（控え）"
								styleClass="commandExButton_out"
								id="printCopy"
								confirm="#{msg.SY_MSG_0022W}"
								action="#{pc_Xrk00301.doPrintCopyAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden id="htmlHidSotNendo" value="#{pc_Xrx00100.propHtmlHidSotNendo.value}"></h:inputHidden>
<h:inputHidden id="htmlHidSotGakki" value="#{pc_Xrx00100.propHtmlHidSotGakki.value}"></h:inputHidden>
<h:inputHidden id="htmlHidGakuseiNm" value="#{pc_Xrx00100.propHtmlHidGakuseiNm.value}"></h:inputHidden>
<h:inputHidden id="htmlHidKanriNo" value="#{pc_Xrx00100.propHtmlHidKanriNo.longValue}"></h:inputHidden>
<h:inputHidden id="htmlHidKamokCd" value="#{pc_Xrx00100.propHtmlHidKamokCd.stringValue}"></h:inputHidden>
<h:inputHidden id="htmlConfilm" value="#{pc_Xrk00301.propConfilm.stringValue}"></h:inputHidden>
<h:inputHidden id="htmlAction" value="#{pc_Xrk00301.propAction.stringValue}"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
</HTML>

