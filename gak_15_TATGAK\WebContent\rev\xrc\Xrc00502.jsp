<%-- 
	入出庫登録(在庫管理)
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc00502.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc00502.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc00502.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc00502.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc00502.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer" align="">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Xrc00502.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content" class="outer">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE width="900" class="table">
							<TBODY>
								<TR>
									<TH width="150"><h:outputText 
										styleClass="outputText" id="text1" 
										value="物品コード"
										style="#{pc_Xrc00502.propBuppinCd.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblBuppinCd" 
										value="#{pc_Xrc00502.propBuppinCd.stringValue}"
										style="#{pc_Xrc00502.propBuppinCd.style}"></h:outputText></TD>
									<TH width="130"><h:outputText
										styleClass="outputText" id="text2" 
										value="物品名称"></h:outputText></TH>
									<TD width="470" colspan="3"><h:outputText styleClass="outputText"
										id="lblBuppinName"
										value="#{pc_Xrc00502.propBuppinName.stringValue}"
										style="#{pc_Xrc00502.propBuppinName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_a"><h:outputText 
										styleClass="outputText" id="text3" 
										value="物品区分"
										style="#{pc_Xrc00502.propBuppinKbnName.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblBuppinKbnName" 
										value="#{pc_Xrc00502.propBuppinKbnName.stringValue}"
										style="#{pc_Xrc00502.propBuppinKbnName.style}"></h:outputText></TD>
									<TH width="150"><h:outputText 
										styleClass="outputText" id="text4" 
										value="廃止フラグ"
										style="#{pc_Xrc00502.propHaisiFlgName.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblTaxFlgName" 
										value="#{pc_Xrc00502.propHaisiFlgName.stringValue}"
										style="#{pc_Xrc00502.propHaisiFlgName.style}"></h:outputText></TD>
									<TH width="150" ><h:outputText
										styleClass="outputText" id="text5" 
										value="配本版"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblHaihonEd"
										value="#{pc_Xrc00502.propHaihonEd.stringValue}"
										style="#{pc_Xrc00502.propHaihonEd.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD></TD><TD></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" nowrap align="right"><h:outputText
							styleClass="outputText" id="lblCount" 
							value="#{pc_Xrc00502.propBpnZaiko.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text6" value="件"></h:outputText></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" align="center">
                    	<div class="listScroll" style="height:125px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrc00502.propBpnZaiko.rowClasses}"
							styleClass="meisai_scroll" id="htmlBpnZaiko"
							value="#{pc_Xrc00502.propBpnZaiko.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text7" styleClass="outputText" value="版"></h:outputText>
								</f:facet>
								<f:attribute value="40" name="width" />
								<h:outputText styleClass="outputText" id="text8"
									value="#{varlist.edition}"></h:outputText>
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column2">								
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="書名" id="text9"></h:outputText>
								</f:facet>
								<f:attribute value="700" name="width" />
								<h:outputText styleClass="outputText" id="text10"
									value="#{varlist.syosekiName}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="在庫数" id="text11"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text12" value="#{varlist.zaiko}" >
                               </h:outputText>
								<f:attribute value="90" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="45" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="selectEd"
									action="#{pc_Xrc00502.doSelectEdAction}"></hx:commandExButton>
								<f:attribute value="center" name="align" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
						</h:dataTable>
						<div>
						</TD>
					</TR>
					<TR>
						<TD><BR></TD><TD><BR></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE class="table" width="896">
							<TBODY>
								<TR>
									<TH width="150"><h:outputText
										styleClass="outputText" id="lblEdition"
										style="#{pc_Xrc00502.propEdition.labelStyle}"
										value="#{pc_Xrc00502.propEdition.labelName}"></h:outputText></TH>
                              		<TD width="200"><h:outputText styleClass="outputText"
										id="htmlEdition" 
										value="#{pc_Xrc00502.propEdition.integerValue}"
										style="#{pc_Xrc00502.propEdition.style}"></h:outputText></TD>
									<TH width="100"><h:outputText
										styleClass="outputText" id="lblSyosekiName"
										style="#{pc_Xrc00502.propSyosekiName.labelStyle}"
										value="#{pc_Xrc00502.propSyosekiName.labelName}"></h:outputText></TH>
                              		<TD width="450" colspan="2"><h:outputText styleClass="outputText"
										id="htmlSyosekiName" 
										value="#{pc_Xrc00502.propSyosekiName.stringValue}"
										style="#{pc_Xrc00502.propSyosekiName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblImpression"
										value="#{pc_Xrc00502.propImpression.labelName}"
										style="#{pc_Xrc00502.propImpression.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="4"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlImpression" 
										value="#{pc_Xrc00502.propImpression.stringValue}"
										style="#{pc_Xrc00502.propImpression.style};width:150px"
										disabled="#{pc_Xrc00502.propImpression.disabled}">
										<f:selectItems value="#{pc_Xrc00502.propImpression.list}" />
										 </h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH><h:outputText 
									    styleClass="outputText" id="lblNyukoDate"
										value="#{pc_Xrc00502.propNyukoDate.labelName}"
										style="#{pc_Xrc00502.propNyukoDate.labelStyle}"></h:outputText>
									 </TH>
									<TD  colspan="4"><h:inputText id="htmlNyukoDate"
										styleClass="inputText" size="13"
										value="#{pc_Xrc00502.propNyukoDate.dateValue}"
										disabled="#{pc_Xrc00502.propNyukoDate.disabled}">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" /></h:inputText>
									</TD>
								</TR>								
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblKingak"
										value="#{pc_Xrc00502.propKingak.labelName}"
										style="#{pc_Xrc00502.propKingak.labelStyle}"></h:outputText></TH>
									<TD  colspan="4"><h:inputText styleClass="inputText"
										id="htmlKingak"
										value="#{pc_Xrc00502.propKingak.integerValue}"
										style="#{pc_Xrc00502.propKingak.style}"
										size="6" 
										disabled="#{pc_Xrc00502.propKingak.disabled}">
										<f:convertNumber type="number" pattern="########0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblSuryo"
										value="#{pc_Xrc00502.propSuryo.labelName}"
										style="#{pc_Xrc00502.propSuryo.labelStyle}"></h:outputText></TH>
									<TD  colspan="4"><h:inputText styleClass="inputText"
										id="htmlSuryo"
										value="#{pc_Xrc00502.propSuryo.integerValue}"
										style="#{pc_Xrc00502.propSuryo.style}"
										size="4" 
										disabled="#{pc_Xrc00502.propSuryo.disabled}">
										<f:convertNumber type="number" pattern="######0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblNyusyukkoKbn"
										value="#{pc_Xrc00502.propNyusyukkoKbn.labelName}"
										style="#{pc_Xrc00502.propNyusyukkoKbn.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="4"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNyusyukkoKbn" 
										value="#{pc_Xrc00502.propNyusyukkoKbn.stringValue}"
										style="#{pc_Xrc00502.propNyusyukkoKbn.style};width:150px"
										disabled="#{pc_Xrc00502.propNyusyukkoKbn.disabled}">
										<f:selectItems value="#{pc_Xrc00502.propNyusyukkoKbn.list}" />
										 </h:selectOneMenu><hx:commandExButton type="submit" styleClass="commandExButton"
									     id="selectNskKbn" value="選択" action="#{pc_Xrc00502.doSelectNskKbnAction}"
									     disabled="#{pc_Xrc00502.propSelectNskKbnButton.disabled}"></hx:commandExButton>
								        <f:attribute value="30" name="width" />
								        <f:attribute value="text-align: center; vertical-align: middle"
									    name="style" /><hx:commandExButton type="submit" value="解除"
										styleClass="commandExButton" id="unselect"
										action="#{pc_Xrc00502.doUnSelectAction}"
										disabled="#{pc_Xrc00502.propUnSelectButton.disabled}">
									</hx:commandExButton>
									</TD>
								</TR>								
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblNsksakiSbtMotoCd"
										value="#{pc_Xrc00502.propNsksakiSbtMotoCd.labelName}"
										style="#{pc_Xrc00502.propNsksakiSbtMotoCd.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="4"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNsksakiSbtMotoCd" 
										value="#{pc_Xrc00502.propNsksakiSbtMotoCd.stringValue}"
										style="#{pc_Xrc00502.propNsksakiSbtMotoCd.style};width:400px"
										disabled="#{pc_Xrc00502.propNsksakiSbtMotoCd.disabled}">
										<f:selectItems value="#{pc_Xrc00502.propNsksakiSbtMotoCd.list}" />
										 </h:selectOneMenu>
									</TD>
								</TR>								
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblNsksakiSbtSakiCd"
										value="#{pc_Xrc00502.propNsksakiSbtSakiCd.labelName}"
										style="#{pc_Xrc00502.propNsksakiSbtSakiCd.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="4"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNsksakiSbtSakiCd" 
										value="#{pc_Xrc00502.propNsksakiSbtSakiCd.stringValue}"
										style="#{pc_Xrc00502.propNsksakiSbtSakiCd.style};width:400px"
										disabled="#{pc_Xrc00502.propNsksakiSbtSakiCd.disabled}">
										<f:selectItems value="#{pc_Xrc00502.propNsksakiSbtSakiCd.list}" />
										 </h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblIdoKbn"
										value="#{pc_Xrc00502.propIdoKbn.labelName}"
										style="#{pc_Xrc00502.propIdoKbn.labelStyle}"></h:outputText>
									</TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlIdoKbn" 
										value="#{pc_Xrc00502.propIdoKbn.stringValue}"
										style="#{pc_Xrc00502.propIdoKbn.style};width:180px"
										disabled="#{pc_Xrc00502.propIdoKbn.disabled}">
										<f:selectItems value="#{pc_Xrc00502.propIdoKbn.list}" />
										 </h:selectOneMenu>
									<TH width="100"><h:outputText
										styleClass="outputText" id="lblTaxFlg"
										value="#{pc_Xrc00502.propTaxFlg.labelName}"
										style="#{pc_Xrc00502.propTaxFlg.labelStyle}"></h:outputText></TH>
									<TD width="300"><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="htmlTaxFlg"
										value="#{pc_Xrc00502.propTaxFlg.checked}"
										style="#{pc_Xrc00502.propTaxFlg.style}"
										disabled="#{pc_Xrc00502.propTaxFlg.disabled}"></h:selectBooleanCheckbox>
									</TD>									
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR class="hr" noshade>
			<BR>
			<TABLE width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Xrc00502.doRegisterAction}"
							disabled="#{pc_Xrc00502.propRegisterButton.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrc00502.doClearAction}"
							disabled="#{pc_Xrc00502.propClearButton.disabled}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrc00502.propBpnZaiko.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition("scroll","listScroll")
</SCRIPT>
</HTML>

