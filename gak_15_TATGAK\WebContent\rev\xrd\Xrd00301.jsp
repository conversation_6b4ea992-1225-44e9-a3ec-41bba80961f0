<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrd/Xrd00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrd00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >
<SCRIPT type="text/javascript">
// 確認ダイアログで「ＯＫ」の場合
function confirmOk() {
	addListWarnOK("htmlExecutableBtnAdd", "btnAdd1");
}
// 確認ダイアログで「キャンセル」の場合
function confirmCancel() {	
	addListWarnCancel("htmlExecutableBtnAdd");
}
</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/>

	<BODY>
		<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrd00301.onPageLoadBegin}">
			<h:form styleClass="form" id="form1">

				<!-- ヘッダーインクルード -->
				<jsp:include page ="../inc/header.jsp" />

				<!-- ヘッダーへのデータセット領域 -->
				<div style="display:none;">
					<hx:commandExButton type="submit" value="閉じる"
						styleClass="commandExButton" id="closeDisp"
						action="#{pc_Xrd00301.doCloseDispAction}">
					</hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrd00301.funcId}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrd00301.screenName}"></h:outputText>
				</div>

				<!--↓outer↓-->
				<DIV class="outer">
					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
					</FIELDSET>

					<!--↓content↓-->
					<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
						<!-- ↑ここに戻る／閉じるボタンを配置 -->
						　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
					</DIV>

					<DIV id="content">
						<DIV class="column" align="center">
							<!-- ↓ここにコンポーネントを配置 -->

							<TABLE border="0" cellpadding="0" cellspacing="0">
								<TBODY>
									<TR>
										<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="610">
												<TBODY>
													<TR>
														<TH nowrap class="v_a" width="182">
															<!-- 実習年度 -->
															<h:outputText 
																styleClass="outputText"	id="lblJissyuNendo"
																value="#{pc_Xrd00301.propJissyuNendo.labelName}"
																style="#{pc_Xrd00301.propJissyuNendo.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="422">
															<h:inputText 
																id="htmlJissyuNendo" styleClass="inputText" size="4" 
																value="#{pc_Xrd00301.propJissyuNendo.dateValue}"
																disabled="#{pc_Xrd00301.propJissyuNendo.disabled}"
																style="#{pc_Xrd00301.propJissyuNendo.style}"
																tabindex="1">
																<hx:inputHelperAssist errorClass="inputText_Error"
											    				imeMode="inactive" promptCharacter="_" />
																<f:convertDateTime pattern="yyyy" />
															</h:inputText>
														</TD>
													</TR>
													<TR>
														<TH nowrap class="v_a" width="182">
															<!-- 支払方法 -->
															<h:outputText 
																styleClass="outputText"	id="lblShiharaiMethod" 
																value="#{pc_Xrd00301.propShiharaiMethod.labelName}" 
																style="#{pc_Xrd00301.propShiharaiMethod.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="422">
															<h:selectOneMenu styleClass="selectOneMenu"
															id="htmlShiharaiMethodCombo"
															value="#{pc_Xrd00301.propShiharaiMethod.value}"
															readonly="#{pc_Xrd00301.propShiharaiMethod.readonly}"
															disabled="#{pc_Xrd00301.propShiharaiMethod.disabled}"
															style="#{pc_Xrd00301.propShiharaiMethod.style};width:150px"
															tabindex="2">
																<f:selectItems value="#{pc_Xrd00301.propShiharaiMethod.list}" />
															</h:selectOneMenu>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE width="100%" border="0" class="table" cellpadding="0" cellspacing="0">
												<TBODY>
													<TR>
														<TH nowrap class="v_a" width="182">
															<!-- 支払日 -->
															<h:outputText
															 styleClass="outputText" id="lblShiharaiDay"
															 value="#{pc_Xrd00301.propShiharaiDay.labelName}"
															 style="#{pc_Xrd00301.propShiharaiDay.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="2">
															<h:inputText styleClass="inputText"
															 id="htmlShiharaiDay" size="10"
															 value="#{pc_Xrd00301.propShiharaiDay.dateValue}"
															 disabled="#{pc_Xrd00301.propShiharaiDay.disabled}"
															 style="#{pc_Xrd00301.propShiharaiDay.style}"
															 tabindex="3">
																<f:convertDateTime />
																<hx:inputHelperAssist errorClass="inputText_Error"
																 promptCharacter="_" />
																<hx:inputHelperDatePicker />
															</h:inputText>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
												<TBODY>
													<TR align="right">
														<TD align="center">
															<hx:commandExButton type="submit" value="CSV作成"
															 styleClass="commandExButton_out" id="CsvOut" action="#{pc_Xrd00301.doBtnCsvOutAction}"
															 confirm="#{msg.SY_MSG_0020W}">
															</hx:commandExButton>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>

							<!-- ↑ここにコンポーネントを配置 -->
						</DIV>
					</DIV>
					<!--↑content↑--> 
				</DIV>
				<!--↑outer↑-->

				<!-- フッターインクルード -->
				<jsp:include page ="../inc/footer.jsp" />
			</h:form>
		</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
