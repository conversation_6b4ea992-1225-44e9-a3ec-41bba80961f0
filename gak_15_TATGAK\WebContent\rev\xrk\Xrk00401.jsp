<%-- 
	修正対象選択
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrk/Xrk00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrk00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrk00401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrk00401.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrk00401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrk00401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<!-- ↑ここにコンポーネントを配置 -->
			<BR>
			<TABLE border="0" class="table" width="700" cellpadding="0"
				cellspacing="0">
				<TBODY>
					<TR>
						<!-- パターン -->
						<TH nowrap class="v_c" width="200">
							<h:outputText styleClass="outputText" id="lblPatternName"
							value="#{pc_Xrk00401.propPatternName.labelName}"
							style="#{pc_Xrk00401.propPatternName.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlPatternName"
                            	value="#{pc_Xrk00401.propPatternName.value}"
                            	disabled="#{pc_Xrk00401.propPatternName.disabled}"
                            	style="width:360px">
                            	<f:selectItems value="#{pc_Xrk00401.propPatternName.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>

					<TR>
						<!-- 発行年度 -->
						<TH class="v_a" width="200"><h:outputText
							styleClass="outputText" id="lblHakkoNendo"
							value="#{pc_Xrk00401.propHakkoNendo.labelName}"
							style="#{pc_Xrk00401.propHakkoNendo.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlHakkoNendo"
							value="#{pc_Xrk00401.propHakkoNendo.value}"
							maxlength="#{pc_Xrk00401.propHakkoNendo.maxLength}" size="4"
                        	style="#{pc_Xrk00401.propHakkoNendo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					
					<TR>
						<!-- 発行番号 -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblHakkoNoFrom" 
							value="#{pc_Xrk00401.propHakkoNoFrom.labelName}"
							style="#{pc_Xrk00401.propHakkoNoFrom.labelStyle}"></h:outputText></TH>
						<TD width="200"><h:inputText styleClass="inputText"
							id="htmlHakkoNoFrom"
							value="#{pc_Xrk00401.propHakkoNoFrom.integerValue}"
							maxlength="#{pc_Xrk00401.propHakkoNoFrom.maxLength}" size="7"
							style="#{pc_Xrk00401.propHakkoNoFrom.style}">
							<f:convertNumber type="number" pattern="######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText>
						&nbsp;～&nbsp;
						<h:inputText styleClass="inputText"
							id="htmlHakkoNoTo"
							value="#{pc_Xrk00401.propHakkoNoTo.integerValue}"
							maxlength="#{pc_Xrk00401.propHakkoNoTo.maxLength}" size="7"
							style="#{pc_Xrk00401.propHakkoNoTo.style}">
							<f:convertNumber type="number" pattern="######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>

				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
						    <hx:commandExButton
								type="submit"
								value="検索"
								styleClass="commandExButton_dat"
								id="search"
								action="#{pc_Xrk00401.doSearchAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<hx:inputHelperSetFocus target="htmlPatternName"></hx:inputHelperSetFocus>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

