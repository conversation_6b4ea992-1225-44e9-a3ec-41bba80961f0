<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00601.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<TITLE>Xrc00601.jsp</TITLE>
<SCRIPT type="text/javascript"></SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY>
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrc00601.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp"
                    action="#{pc_Xrc00601.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrc00601.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrc00601.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" 
                    	value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
											<TBODY>
												<TR>
													<TH width="150px" nowrap class="v_a">		
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
															<TBODY>
																<TR>
																	<TH class="clear_border">
																		<h:outputText styleClass="outputText" 
																			id="lblInputFile"
																			value="#{pc_Xrc00601.propInputFile.name}"
																			style="#{pc_Xrc00601.propInputFile.labelStyle}">
																		</h:outputText>
																	</TH>
																</TR>
																<TR>
																	<TH class="clear_border">
																		<h:outputText styleClass="outputText"
																			id="lblInputFileOldLabel"
																			value="#{pc_Xrc00601.propInputFileOld.name}"
																			style="#{pc_Xrc00601.propInputFileOld.labelStyle}">
																		</h:outputText>
																	</TH>
																</TR>
															</TBODY>
														</TABLE>
													</TH>
													<TD width="*" nowrap>
														<TABLE border="0" cellpadding="0" cellspacing="0" width="98%">
															<TBODY>
																<TR>
																	<TD class="clear_border">
																		<hx:fileupload styleClass="fileupload"
																			id="htmlInputFile"
																			value="#{pc_Xrc00601.propInputFile.value}" 
																			disabled=""
																			readonly="" 
																			style="#{pc_Xrc00601.propInputFile.style} width:650px">
																		<hx:fileProp name="fileName"
																			value="#{pc_Xrc00601.propInputFile.fileName}" />
																		<hx:fileProp name="contentType"
																			value="#{pc_Xrc00601.propInputFile.contentType}" />
																		</hx:fileupload>
																	</TD>
																</TR>
																<TR>
																	<TD class="clear_border">
																		<h:outputText styleClass="outputText"
																			id="lblInputFileOld"
																			value="#{pc_Xrc00601.propInputFileOld.value}"
																			style="#{pc_Xrc00601.propInputFileOld.style}">
																		</h:outputText></TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TH width="150px" nowrap class="v_c">
														<h:outputText styleClass="outputText"
															id="lblSyoriKbnLabel"
															style="#{pc_Xrc00601.propSyoriKbn.style}"
															value="#{pc_Xrc00601.propSyoriKbnLabel.name}">
														</h:outputText>
													</TH>
													<TD width="*" nowrap>
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
															id="htmlSyoriKbn"
															value="#{pc_Xrc00601.propSyoriKbn.checked}">
														</h:selectBooleanCheckbox>
														<h:outputText styleClass="outputText"
															id="lblSyoriKbn" 
															value="#{pc_Xrc00601.propSyoriKbn.name}"
															style="#{pc_Xrc00601.propSyoriKbn.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH width="150px" nowrap class="v_d">
														<h:outputText styleClass="outputText" 
															id="lblChkList"
															value="#{pc_Xrc00601.propChkListLabel.name}">
														</h:outputText>
													</TH>
													<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
															<TBODY>
																<TR>
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListNormal"
																			value="#{pc_Xrc00601.propChkListNormal.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText" 
																			id="lblChkListNormal"
																			value="#{pc_Xrc00601.propChkListNormal.name}"
																			style="#{pc_Xrc00601.propChkListNormal.style}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListError"
																			value="#{pc_Xrc00601.propChkListError.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText"
																			id="lblChkListError"
																			value="#{pc_Xrc00601.propChkListError.name}"
																			style="#{pc_Xrc00601.propChkListError.style}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>													
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListWarning"
																			value="#{pc_Xrc00601.propChkListWarning.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText" 
																			id="lblChkListWarning"
																			value="#{pc_Xrc00601.propChkListWarning.name}"
																			style="#{pc_Xrc00601.propChkListWarning.style}">
																		</h:outputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								<TR>
									<TD>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton type="submit" value="入力項目指定"
															styleClass="commandExButton_etc" id="setoutput"
															action="#{pc_Xrc00601.doSetoutpuAction}">
														</hx:commandExButton>&nbsp;
														<hx:commandExButton type="submit" 
															value="実行"
															styleClass="commandExButton_dat" id="exec"
															confirm="#{msg.SY_MSG_0001W}" 
															action="#{pc_Xrc00601.doExecAction}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
            	<!--↑CONTENT↑-->
            
			</DIV>
	        <!--↑outer↑-->

	        <!-- フッダーインクルード -->
	        <jsp:include page ="../inc/footer.jsp" />

		</h:form>
    </hx:scriptCollector>
    </BODY>
    
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
