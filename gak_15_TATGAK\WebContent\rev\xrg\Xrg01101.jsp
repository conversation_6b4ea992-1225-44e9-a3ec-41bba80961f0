<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg01101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg01101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<SCRIPT type="text/javascript">
function confirmOk() {
	//許可日消去確認フェーズ
	var chkKbn = document.getElementById('form1:htmlExecutableChkKbn').value;
	
	if(chkKbn == "1"){

		var ChkKbn1 = document.getElementById('form1:htmlExecutableChkKbn1').value;
		var ChkKbn2 = document.getElementById('form1:htmlExecutableChkKbn2').value;
		
		if(ChkKbn1 == "0"){
			indirectClick('register');
		} else if (ChkKbn2 == "0") {
			indirectClick('register');
		}else{
			document.getElementById('form1:htmlExecutableEntryFlg').value = "1";
			document.getElementById('form1:htmlExecutableChkKbn').value = "2";
			indirectClick('register');
		}

	} else {
		//無効理由確認フェーズ
		var kyoseCchk1 = document.getElementById('form1:htmlKyoseiKyokaKbn1').value;
		var kyoseCchk2 = document.getElementById('form1:htmlKyoseiKyokaKbn2').value;
		var kyoseCchk3 = document.getElementById('form1:htmlKyoseiKyokaKbn3').value;
		var kyoseCchk4 = document.getElementById('form1:htmlKyoseiKyokaKbn4').value;
		var kyoseCchk5 = document.getElementById('form1:htmlKyoseiKyokaKbn5').value;
		var kyoseCchk6 = document.getElementById('form1:htmlKyoseiKyokaKbn6').value;
		var kyoseCchk7 = document.getElementById('form1:htmlKyoseiKyokaKbn7').value;
		var kyoseCchk8 = document.getElementById('form1:htmlKyoseiKyokaKbn8').value;
		var kyoseCchk9 = document.getElementById('form1:htmlKyoseiKyokaKbn9').value;
		var kyoseCchk10 = document.getElementById('form1:htmlKyoseiKyokaKbn10').value;
		var kyoseCchk11 = document.getElementById('form1:htmlKyoseiKyokaKbn11').value;
		var kyoseCchk12 = document.getElementById('form1:htmlKyoseiKyokaKbn12').value;
		var kyoseCchk13 = document.getElementById('form1:htmlKyoseiKyokaKbn13').value;
		var kyoseCchk14 = document.getElementById('form1:htmlKyoseiKyokaKbn14').value;
		var kyoseCchk15 = document.getElementById('form1:htmlKyoseiKyokaKbn15').value;
		var kyoseCchk16 = document.getElementById('form1:htmlKyoseiKyokaKbn16').value;
		var kyoseCchk17 = document.getElementById('form1:htmlKyoseiKyokaKbn17').value;
		var kyoseCchk18 = document.getElementById('form1:htmlKyoseiKyokaKbn18').value;
		
		if(kyoseCchk1 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn1').value = "1";
			indirectClick('register');
		} else if(kyoseCchk2 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn2').value = "1";
			indirectClick('register');
		} else if(kyoseCchk3 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn3').value = "1";
			indirectClick('register');
		} else if(kyoseCchk4 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn4').value = "1";
			indirectClick('register');
		} else if(kyoseCchk5 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn5').value = "1";
			indirectClick('register');
		} else if(kyoseCchk6 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn6').value = "1";
			indirectClick('register');
		} else if(kyoseCchk7 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn7').value = "1";
			indirectClick('register');
		} else if(kyoseCchk8 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn8').value = "1";
			indirectClick('register');
		} else if(kyoseCchk9 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn9').value = "1";
			indirectClick('register');
		} else if(kyoseCchk10 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn10').value = "1";
			indirectClick('register');
		} else if(kyoseCchk11 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn11').value = "1";
			indirectClick('register');
		} else if(kyoseCchk12 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn12').value = "1";
			indirectClick('register');
		} else if(kyoseCchk13 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn13').value = "1";
			indirectClick('register');
		} else if(kyoseCchk14 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn14').value = "1";
			indirectClick('register');
		} else if(kyoseCchk15 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn15').value = "1";
			indirectClick('register');
		} else if(kyoseCchk16 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn16').value = "1";
			indirectClick('register');
		} else if(kyoseCchk17 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn17').value = "1";
			indirectClick('register');
		} else if(kyoseCchk18 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn18').value = "1";
			indirectClick('register');
		} 
	}
}

function confirmCancel() {
	//許可日消去確認フェーズ
	var chkKbn = document.getElementById('form1:htmlExecutableChkKbn').value;
	
	if(chkKbn == "1"){
		document.getElementById('form1:htmlExecutableEntryFlg').value = "0";
		document.getElementById('form1:htmlExecutableChkKbn').value = "1";
		document.getElementById('form1:htmlExecutableChkKbn1').value = "0";
		document.getElementById('form1:htmlExecutableChkKbn2').value = "0";

	} else {
		//無効理由確認フェーズ
		var kyoseCchk1 = document.getElementById('form1:htmlKyoseiKyokaKbn1').value;
		var kyoseCchk2 = document.getElementById('form1:htmlKyoseiKyokaKbn2').value;
		var kyoseCchk3 = document.getElementById('form1:htmlKyoseiKyokaKbn3').value;
		var kyoseCchk4 = document.getElementById('form1:htmlKyoseiKyokaKbn4').value;
		var kyoseCchk5 = document.getElementById('form1:htmlKyoseiKyokaKbn5').value;
		var kyoseCchk6 = document.getElementById('form1:htmlKyoseiKyokaKbn6').value;
		var kyoseCchk7 = document.getElementById('form1:htmlKyoseiKyokaKbn7').value;
		var kyoseCchk8 = document.getElementById('form1:htmlKyoseiKyokaKbn8').value;
		var kyoseCchk9 = document.getElementById('form1:htmlKyoseiKyokaKbn9').value;
		var kyoseCchk10 = document.getElementById('form1:htmlKyoseiKyokaKbn10').value;
		var kyoseCchk11 = document.getElementById('form1:htmlKyoseiKyokaKbn11').value;
		var kyoseCchk12 = document.getElementById('form1:htmlKyoseiKyokaKbn12').value;
		var kyoseCchk13 = document.getElementById('form1:htmlKyoseiKyokaKbn13').value;
		var kyoseCchk14 = document.getElementById('form1:htmlKyoseiKyokaKbn14').value;
		var kyoseCchk15 = document.getElementById('form1:htmlKyoseiKyokaKbn15').value;
		var kyoseCchk16 = document.getElementById('form1:htmlKyoseiKyokaKbn16').value;
		var kyoseCchk17 = document.getElementById('form1:htmlKyoseiKyokaKbn17').value;
		var kyoseCchk18 = document.getElementById('form1:htmlKyoseiKyokaKbn18').value;
		
		if(kyoseCchk1 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn1').value = "2";
			indirectClick('register');
		} else if(kyoseCchk2 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn2').value = "2";
			indirectClick('register');
		} else if(kyoseCchk3 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn3').value = "2";
			indirectClick('register');
		} else if(kyoseCchk4 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn4').value = "2";
			indirectClick('register');
		} else if(kyoseCchk5 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn5').value = "2";
			indirectClick('register');
		} else if(kyoseCchk6 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn6').value = "2";
			indirectClick('register');
		} else if(kyoseCchk7 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn7').value = "2";
			indirectClick('register');
		} else if(kyoseCchk8 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn8').value = "2";
			indirectClick('register');
		} else if(kyoseCchk9 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn9').value = "2";
			indirectClick('register');
		} else if(kyoseCchk10 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn10').value = "2";
			indirectClick('register');
		} else if(kyoseCchk11 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn11').value = "2";
			indirectClick('register');
		} else if(kyoseCchk12 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn12').value = "2";
			indirectClick('register');
		} else if(kyoseCchk13 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn13').value = "2";
			indirectClick('register');
		} else if(kyoseCchk14 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn14').value = "2";
			indirectClick('register');
		} else if(kyoseCchk15 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn15').value = "2";
			indirectClick('register');
		} else if(kyoseCchk16 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn16').value = "2";
			indirectClick('register');
		} else if(kyoseCchk17 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn17').value = "2";
			indirectClick('register');
		} else if(kyoseCchk18 == "0"){
			document.getElementById('form1:htmlKyoseiKyokaKbn18').value = "2";
			indirectClick('register');
		}
	}
}

// 学生検索画面
function openGakuseiWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
		+ "?retFieldName=" + field1;
		
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return false;
}

// 学生氏名を取得する
function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// 決定授業コード（検索用）検索アイコン押下時処理
function openSerchKetteiJugyoCdSearchWindow(thisObj, thisEvent) {
	
	var nendo = document.getElementById('form1:htmlNendo').value;
	var schSbtCd = document.getElementById('form1:htmlSchooling').value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlJugyoCd&nendo="+nendo+"&schSbtCd="+schSbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return false;
}

// 決定授業コード（登録更新用）検索アイコン押下時処理
function openEntryKetteiJugyoCdSearchWindow(thisObj, thisEvent) {
	
	var nendo = document.getElementById('form1:htmlNendo').value;
	var schSbtCd = document.getElementById('form1:htmlSchooling').value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlKetteiJugyoCd&nendo="+nendo+"&schSbtCd="+schSbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return false;
}

// 申込授業コード１（登録更新用）検索アイコン押下時処理
function openEntry1JugyoCdSearchWindow(thisObj, thisEvent) {
	
	var nendo = document.getElementById('form1:htmlNendo').value;
	var schSbtCd = document.getElementById('form1:htmlSchooling').value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlEntry1JugyoCd&nendo="+nendo+"&schSbtCd="+schSbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return false;
}

// 申込授業コード２（登録更新用）検索アイコン押下時処理
function openEntry2JugyoCdSearchWindow(thisObj, thisEvent) {
	
	var nendo = document.getElementById('form1:htmlNendo').value;
	var schSbtCd = document.getElementById('form1:htmlSchooling').value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlEntry2JugyoCd&nendo="+nendo+"&schSbtCd="+schSbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return false;
}

// 申込授業コード３（登録更新用）検索アイコン押下時処理
function openEntry3JugyoCdSearchWindow(thisObj, thisEvent) {
	
	var nendo = document.getElementById('form1:htmlNendo').value;
	var schSbtCd = document.getElementById('form1:htmlSchooling').value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlEntry3JugyoCd&nendo="+nendo+"&schSbtCd="+schSbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return false;
}

// 申込授業コード４（登録更新用）検索アイコン押下時処理
function openEntry4JugyoCdSearchWindow(thisObj, thisEvent) {
	
	var nendo = document.getElementById('form1:htmlNendo').value;
	var schSbtCd = document.getElementById('form1:htmlSchooling').value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlEntry4JugyoCd&nendo="+nendo+"&schSbtCd="+schSbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return false;
}

//決定授業コード（検索用） 授業名称取得
function doSerchKetteiJugyoAjax(thisObj, thisEvent) {
	var servlet = "rev/xrg/XrgJugyAJAX";
	var target = "form1:htmlJugyoName";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//決定授業コード（登録用） 授業名称取得
function doEntryKetteiJugyoNmAjax(thisObj, thisEvent) {
	var servlet = "rev/xrg/XrgJugyAJAX";
	var target = "form1:htmlKetteiJugyoName";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// 決定授業コード（登録用）検索アイコン押下時専用Ajax
function doEntryKetteiJugyoAjax(thisObj, thisEvent) {

	doEntryKetteiJugyoNmAjax(thisObj, thisEvent);

	//科目コード・科目名称を取得する。
	servlet = "rev/xrg/XrgJugyKamokAJAX";
	target = "form1:htmlKetteiKamokuCd";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;
	args['gakusekiCd'] = document.getElementById("form1:htmlGakuseki").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード１（登録用） 授業名称取得
function doEntry1JugyoNmAjax(thisObj, thisEvent) {
	var servlet = "rev/xrg/XrgJugyAJAX";
	var target = "form1:htmlEntry1JugyoName";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード１（登録用） 検索アイコン押下時専用Ajax
function doEntry1JugyoAjax(thisObj, thisEvent) {

	doEntry1JugyoNmAjax(thisObj, thisEvent);

	//科目コード・科目名称を取得する。
	servlet = "rev/xrg/XrgJugyKamokAJAX";
	target = "form1:htmlEntry1KamokuCd";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;
	args['gakusekiCd'] = document.getElementById("form1:htmlGakuseki").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード２（登録用） 授業名称取得
function doEntry2JugyoNmAjax(thisObj, thisEvent) {
	var servlet = "rev/xrg/XrgJugyAJAX";
	var target = "form1:htmlEntry2JugyoName";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード２（登録用）検索アイコン押下時専用Ajax
function doEntry2JugyoAjax(thisObj, thisEvent) {
	doEntry2JugyoNmAjax(thisObj, thisEvent);

	//科目コード・科目名称を取得する。
	servlet = "rev/xrg/XrgJugyKamokAJAX";
	target = "form1:htmlEntry2KamokuCd";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;
	args['gakusekiCd'] = document.getElementById("form1:htmlGakuseki").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード３（登録用） 授業名称取得
function doEntry3JugyoNmAjax(thisObj, thisEvent) {
	var servlet = "rev/xrg/XrgJugyAJAX";
	var target = "form1:htmlEntry3JugyoName";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード３（登録用） 検索アイコン押下時専用Ajax
function doEntry3JugyoAjax(thisObj, thisEvent) {
	doEntry3JugyoNmAjax(thisObj, thisEvent);

	//科目コード・科目名称を取得する。
	servlet = "rev/xrg/XrgJugyKamokAJAX";
	target = "form1:htmlEntry3KamokuCd";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;
	args['gakusekiCd'] = document.getElementById("form1:htmlGakuseki").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード４（登録用） 授業名称取得
function doEntry4JugyoNmAjax(thisObj, thisEvent) {
	var servlet = "rev/xrg/XrgJugyAJAX";
	var target = "form1:htmlEntry4JugyoName";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

//申込授業コード４（登録用） 検索アイコン押下時専用Ajax
function doEntry4JugyoAjax(thisObj, thisEvent) {
	doEntry4JugyoNmAjax(thisObj, thisEvent);

	//科目コード・科目名称を取得する。
	servlet = "rev/xrg/XrgJugyKamokAJAX";
	target = "form1:htmlEntry4KamokuCd";
	var args = new Array();
	args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchooling").value;
	args['jugyoCd'] = thisObj.value;
	args['gakusekiCd'] = document.getElementById("form1:htmlGakuseki").value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

var schSbtCd = "";
function getSchoolingSbtCb() {
	// スクーリング種別コンボボックス取得AJAX
	var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
	var args = new Array();
	args['nendo'] = document.getElementById('form1:htmlNendo').value;
	args['textdelFlg'] = "true";
	var target = "";
	
	comb = document.getElementById('form1:htmlSchooling');
	schSbtCd = comb.options[comb.selectedIndex].value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
}

function callBackMethod(value){
	var comb = document.getElementById('form1:htmlSchooling');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg01101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg01101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg01101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg01101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="800">
				<TBODY>
					<TR>
						<TD>
							<TABLE class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="130">
											<h:outputText styleClass="outputText" id="lblNendo"
												value="#{pc_Xrg01101.propNendo.labelName}"
												style="#{pc_Xrg01101.propNendo.labelStyle}">
											</h:outputText>
	
										</TH>
										<TD width="550">
											<h:inputText id="htmlNendo" styleClass="inputText"
												value="#{pc_Xrg01101.propNendo.dateValue}"  tabindex="1"
												disabled="#{pc_Xrg01101.propNendo.disabled}"
												onblur="getSchoolingSbtCb();"
												size="5">
												<hx:inputHelperAssist errorClass="inputText_Error"
						    						imeMode="inactive" promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
										<TD rowspan="3" width="130" style="background-color: transparent;border-bottom-style: none; border-top-style: none; border-right-style: none; text-align: right">
											<hx:commandExButton type="submit" value="選択"
												styleClass="commandExButton" id="select"  tabindex="7"
												action="#{pc_Xrg01101.doSelectAction}"
												disabled="#{pc_Xrg01101.propSelect.disabled}"
												style="#{pc_Xrg01101.propSelect.style}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="解除" tabindex="8"
												styleClass="commandExButton" id="release" 
												action="#{pc_Xrg01101.doReleaseAction}"
												disabled="#{pc_Xrg01101.propRelease.disabled}"
												style="#{pc_Xrg01101.propRelease.style}">
											</hx:commandExButton>
										</TD>
									</TR>
									<TR>
										<TH class="v_b" width="130">
											<h:outputText styleClass="outputText" id="lblSchooling"
												value="#{pc_Xrg01101.propSchooling.labelName}"
												style="#{pc_Xrg01101.propSchooling.labelStyle}">
											</h:outputText>
										</TH>
										<TD nowrap width="550">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlSchooling" value="#{pc_Xrg01101.propSchooling.value}"
												readonly="#{pc_Xrg01101.propSchooling.readonly}"  tabindex="2"
												disabled="#{pc_Xrg01101.propSchooling.disabled}"
												style="#{pc_Xrg01101.propSchooling.style}">
												<f:selectItems value="#{pc_Xrg01101.propSchooling.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="130">
											<h:outputText
												styleClass="outputText" id="lblGakuseki"
												value="#{pc_Xrg01101.propGakuseki.labelName}"
												style="#{pc_Xrg01101.propGakuseki.labelStyle}">
											</h:outputText>
										</TH>
										<TD nowrap width="550">
											<h:inputText id="htmlGakuseki"
												styleClass="inputText"
												value="#{pc_Xrg01101.propGakuseki.stringValue}"
												maxlength="#{pc_Xrg01101.propGakuseki.max}"  tabindex="3"
												onblur="return doGakuseiAjax(this, event, 'form1:htmlGakuseiName');"
												disabled="#{pc_Xrg01101.propGakuseki.disabled}"
												size="13">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="htmlSearchGakuseki" 
												onclick="openGakuseiWindow('form1:htmlGakuseki');"  tabindex="4"
												disabled="#{pc_Xrg01101.propSearchGakuseki.disabled}">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlGakuseiName"
												style="#{pc_Xrg01101.propGakuseiName.style}"
												value="#{pc_Xrg01101.propGakuseiName.stringValue}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_d" width="130">
											<h:outputText
												styleClass="outputText" id="lblJugyoCd" value="決定授業コード">
											</h:outputText>
										</TH>
										<TD nowrap width="550">
											<h:inputText id="htmlJugyoCd"
												styleClass="inputText"
												value="#{pc_Xrg01101.propJugyoCd.stringValue}"
												onblur="return doSerchKetteiJugyoAjax(this, event);"
												disabled="#{pc_Xrg01101.propJugyoCd.disabled}"
												maxlength="#{pc_Xrg01101.propJugyoCd.max}"  tabindex="5"
												size="5">
											</h:inputText>
											<hx:commandExButton type="button"  tabindex="6"
												styleClass="commandExButton_search" id="htmlSearchJugyoCd"
												onclick="return openSerchKetteiJugyoCdSearchWindow(this, event);"
												disabled="#{pc_Xrg01101.propSearchJugyoCd.disabled}">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlJugyoName"
												value="#{pc_Xrg01101.strJugyoName}">
											</h:outputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><BR/></TD>
					</TR>
					<TR>
						<TD>
						<div class="listScroll" style="height:100px;" id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><B>
							<h:dataTable
							border="0" cellpadding="0" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrg01101.propSchoolingEntryList.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xrg01101.propSchoolingEntryList.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="申込日時"
										id="lblMoshikomiDate">
									</h:outputText>
								</f:facet>
								<h:outputText
									styleClass="outputText" id="uketsukeNum_d"
									value="#{varlist.moshikomiDate}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
								<f:attribute value="110" name="width" />
							</h:column>							
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="開催期"
										id="lblKaisaiki_d">
									</h:outputText>
								</f:facet>
								<h:outputText
									styleClass="outputText" id="kaisaiki_d"
									value="#{varlist.kiNm}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
								<f:attribute value="50" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="時限"
										id="lblJigen_d">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="jigen_d" value="#{varlist.jigenNm}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
								<f:attribute value="45" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="決定授業"
										id="lblJugyo_d">
									</h:outputText>
								</f:facet>
								<h:outputText
									styleClass="outputText" id="jugyo_d"
									value="#{varlist.propKetteiJugyoNm.displayValue}"
									title="#{varlist.propKetteiJugyoNm.stringValue}">
								</h:outputText>
								<f:attribute value="122" name="width" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="申込授業1"
										id="lblEntry1_d">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="entry1_d" value="#{varlist.propMskJugyoNm1.displayValue}"
									title="#{varlist.propMskJugyoNm1.stringValue}">
								</h:outputText>
								<f:attribute value="122" name="width" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="申込授業2"
										id="lblEntry2_d">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="entry2_d" value="#{varlist.propMskJugyoNm2.displayValue}"
									title="#{varlist.propMskJugyoNm2.stringValue}">
								</h:outputText>
								<f:attribute value="122" name="width" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="申込授業3"
										id="lblEntry3_d">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="entry3_d" value="#{varlist.propMskJugyoNm3.displayValue}"
									title="#{varlist.propMskJugyoNm3.stringValue}">
								</h:outputText>
								<f:attribute value="122" name="width" />
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="申込授業4"
										id="lblEntry4_d">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="entry4_d" value="#{varlist.propMskJugyoNm4.displayValue}"
									title="#{varlist.propMskJugyoNm4.stringValue}">
								</h:outputText>
								<f:attribute value="122" name="width" />
							</h:column>
							<h:column id="column9">
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select_d"
									action="#{pc_Xrg01101.doEditAction}"></hx:commandExButton>
								<f:facet name="header">
								</f:facet>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="35" name="width" />
							</h:column>
						</h:dataTable>
						</TD>
					</TR>
					<TR><TD></TD></TR>
					<TR>
						<TD>
							<TABLE class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="140">
											<h:outputText styleClass="outputText" id="lblEntryKbn"
												value="#{pc_Xrg01101.propEntryKbn.labelName}"
												style="#{pc_Xrg01101.propEntryKbn.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlEntryKbn" value="#{pc_Xrg01101.propEntryKbn.value}"
												readonly="#{pc_Xrg01101.propEntryKbn.readonly}"
												disabled="#{pc_Xrg01101.propEntryKbn.disabled}"
												style="#{pc_Xrg01101.propEntryKbn.style}">
												<f:selectItems value="#{pc_Xrg01101.propEntryKbn.list}" />
											</h:selectOneMenu>
										</TD >
										<TH class="v_a" width="90">
											<h:outputText styleClass="outputText" id="lblJitai"
												value="#{pc_Xrg01101.propJitai.labelName}"
												style="#{pc_Xrg01101.propJitai.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlJitai" value="#{pc_Xrg01101.propJitai.value}"
												disabled="#{pc_Xrg01101.propJitai.disabled}"
												readonly="#{pc_Xrg01101.propJitai.readonly}"
												style="#{pc_Xrg01101.propJitai.style}">
												<f:selectItems value="#{pc_Xrg01101.propJitai.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH class="v_a" width="140">
											<h:outputText styleClass="outputText" id="lblMskmDate"
												value="#{pc_Xrg01101.propMskmDate.labelName}"
												style="#{pc_Xrg01101.propMskmDate.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:inputText id="htmlMskmDate"
												styleClass="inputText"
												value="#{pc_Xrg01101.propMskmDate.dateValue}"
												disabled="#{pc_Xrg01101.propMskmDate.disabled}"
												size="12">
												<f:convertDateTime />
												<hx:inputHelperDatePicker />
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText> 
											<h:inputText id="htmlMskmTime"
												styleClass="inputText"
												style="#{pc_Xrg01101.propMskmTime.style}"
												value="#{pc_Xrg01101.propMskmTime.dateValue}"
												disabled="#{pc_Xrg01101.propMskmTime.disabled}" size="5">
												<f:convertDateTime type="time" timeStyle="short" />
												<hx:inputHelperDatePicker delta="60" />
												<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
											</h:inputText>
										</TD>
										<TH class="v_a" width="90">
											<h:outputText styleClass="outputText" id="lblJitaibi"
												value="#{pc_Xrg01101.propJitaibi.labelName}"
												style="#{pc_Xrg01101.propJitaibi.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:inputText id="htmlJitaibi"
												styleClass="inputText"
												value="#{pc_Xrg01101.propJitaibi.dateValue}"
												disabled="#{pc_Xrg01101.propJitaibi.disabled}"
												size="12">
												<f:convertDateTime />
												<hx:inputHelperDatePicker />
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText> 
										</TD>
									</TR>
									<TR>
										<TH class="v_a" width="140">
											<h:outputText styleClass="outputText" id="lblKyokabi"
												value="#{pc_Xrg01101.propKyokabi.labelName}"
												style="#{pc_Xrg01101.propKyokabi.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3" >
											<h:inputText id="htmlKyokabi"
												styleClass="inputText"
												value="#{pc_Xrg01101.propKyokabi.dateValue}"
												disabled="#{pc_Xrg01101.propKyokabi.disabled}"
												size="12">
												<f:convertDateTime />
												<hx:inputHelperDatePicker />
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText> 
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
							<TABLE class="table">
								<TBODY>
									<TR>
										<TH rowspan="2" width="55">
											<h:outputText styleClass="outputText" value="決定"
												id="lblKettei">
											</h:outputText>
										</TH>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblKetteiJugyoCd"
												value="#{pc_Xrg01101.propKetteiJugyoCd.labelName}"
												style="#{pc_Xrg01101.propKetteiJugyoCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:inputText id="htmlKetteiJugyoCd"
												styleClass="inputText"
												value="#{pc_Xrg01101.propKetteiJugyoCd.stringValue}"
												onblur="return doEntryKetteiJugyoAjax(this, event);"
												disabled="#{pc_Xrg01101.propKetteiJugyoCd.disabled}"
												maxlength="#{pc_Xrg01101.propKetteiJugyoCd.max}"
												style="width: 40px"
												size="5">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="htmlSearchKetteiJugyoCd"
												onclick="return openEntryKetteiJugyoCdSearchWindow(this, event);"
												disabled="#{pc_Xrg01101.propSearchKetteiJugyoCd.disabled}">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlKetteiJugyoName"
												value="#{pc_Xrg01101.strKetteiJugyoName}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblKetteiClassNum"
												value="#{pc_Xrg01101.propKetteiClassNum.labelName}"
												style="#{pc_Xrg01101.propKetteiClassNum.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:inputText id="htmlKetteiClassNum"
												styleClass="inputText"
												value="#{pc_Xrg01101.propKetteiClassNum.stringValue}"
												maxlength="#{pc_Xrg01101.propKetteiClassNum.max}"
												disabled="#{pc_Xrg01101.propKetteiClassNum.disabled}"
												style="width: 25px"
												size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblKetteiKamokuCd"
												value="#{pc_Xrg01101.propKetteiKamokuCd.labelName}"
												style="#{pc_Xrg01101.propKetteiKamokuCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:outputText styleClass="outputText" id="htmlKetteiKamokuCd"
												value="#{pc_Xrg01101.strKetteiKamokuCd}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblKetteiMukoRiyu"
												value="#{pc_Xrg01101.propKetteiMukoRiyu.labelName}"
												style="#{pc_Xrg01101.propKetteiMukoRiyu.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlKetteiMukoRiyu" value="#{pc_Xrg01101.propKetteiMukoRiyu.value}"
												readonly="#{pc_Xrg01101.propKetteiMukoRiyu.readonly}"
												disabled="#{pc_Xrg01101.propKetteiMukoRiyu.disabled}"
												style="width: 260px;#{pc_Xrg01101.propKetteiMukoRiyu.style}">
												<f:selectItems value="#{pc_Xrg01101.propKetteiMukoRiyu.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH rowspan="2" width="55">
											<h:outputText styleClass="outputText" value="申込1"
												id="lblEntry1">
											</h:outputText>
										</TH>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry1JugyoCd"
												value="#{pc_Xrg01101.propEntry1JugyoCd.labelName}"
												style="#{pc_Xrg01101.propEntry1JugyoCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:inputText id="htmlEntry1JugyoCd"
												styleClass="inputText"
												value="#{pc_Xrg01101.propEntry1JugyoCd.stringValue}"
												onblur="return doEntry1JugyoAjax(this, event);"
												disabled="#{pc_Xrg01101.propEntry1JugyoCd.disabled}"
												maxlength="#{pc_Xrg01101.propEntry1JugyoCd.max}"
												style="width: 40px"
												size="5">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="htmlSearchEntry1JugyoCd"
												onclick="return openEntry1JugyoCdSearchWindow(this, event);"
												disabled="#{pc_Xrg01101.propSearchEntry1JugyoCd.disabled}">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlEntry1JugyoName"
												value="#{pc_Xrg01101.strEntry1JugyoName}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry1ClassNum"
												value="#{pc_Xrg01101.propEntry1ClassNum.labelName}"
												style="#{pc_Xrg01101.propEntry1ClassNum.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:inputText id="htmlEntry1ClassNum"
												styleClass="inputText"
												maxlength="#{pc_Xrg01101.propEntry1ClassNum.max}"
												disabled="#{pc_Xrg01101.propEntry1ClassNum.disabled}"
												value="#{pc_Xrg01101.propEntry1ClassNum.stringValue}"
												style="width: 25px"
												size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry1KamokuCd"
												value="#{pc_Xrg01101.propEntry1KamokuCd.labelName}"
												style="#{pc_Xrg01101.propEntry1KamokuCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:outputText styleClass="outputText" id="htmlEntry1KamokuCd"
												value="#{pc_Xrg01101.strEntry1KamokuCd}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry1MukoRiyu"
												value="#{pc_Xrg01101.propEntry1MukoRiyu.labelName}"
												style="#{pc_Xrg01101.propEntry1MukoRiyu.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlEntry1MukoRiyu" value="#{pc_Xrg01101.propEntry1MukoRiyu.value}"
												readonly="#{pc_Xrg01101.propEntry1MukoRiyu.readonly}"
												disabled="#{pc_Xrg01101.propEntry1MukoRiyu.disabled}"
												style="width: 260px;#{pc_Xrg01101.propEntry1MukoRiyu.style}">
												<f:selectItems value="#{pc_Xrg01101.propEntry1MukoRiyu.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH rowspan="2" width="55">
											<h:outputText styleClass="outputText" value="申込2"
												id="lblEntry2">
											</h:outputText>
										</TH>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry2JugyoCd"
												value="#{pc_Xrg01101.propEntry2JugyoCd.labelName}"
												style="#{pc_Xrg01101.propEntry2JugyoCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:inputText id="htmlEntry2JugyoCd"
												styleClass="inputText"
												maxlength="#{pc_Xrg01101.propEntry2JugyoCd.max}"
												onblur="return doEntry2JugyoAjax(this, event);"
												disabled="#{pc_Xrg01101.propEntry2JugyoCd.disabled}"
												value="#{pc_Xrg01101.propEntry2JugyoCd.stringValue}"
												style="width: 40px"
												size="5">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="htmlSearchEntry2JugyoCd"
												onclick="return openEntry2JugyoCdSearchWindow(this, event);"
												disabled="#{pc_Xrg01101.propSearchEntry2JugyoCd.disabled}">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlEntry2JugyoName"
												value="#{pc_Xrg01101.strEntry2JugyoName}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry2ClassNum"
												value="#{pc_Xrg01101.propEntry2ClassNum.labelName}"
												style="#{pc_Xrg01101.propEntry2ClassNum.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:inputText id="htmlEntry2ClassNum"
												styleClass="inputText"
												maxlength="#{pc_Xrg01101.propEntry2ClassNum.max}"
												disabled="#{pc_Xrg01101.propEntry2ClassNum.disabled}"
												value="#{pc_Xrg01101.propEntry2ClassNum.stringValue}"
												style="width: 25px"
												size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry2KamokuCd"
												value="#{pc_Xrg01101.propEntry2KamokuCd.labelName}"
												style="#{pc_Xrg01101.propEntry2KamokuCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:outputText styleClass="outputText" id="htmlEntry2KamokuCd"
												value="#{pc_Xrg01101.strEntry2KamokuCd}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry2MukoRiyu"
												value="#{pc_Xrg01101.propEntry2MukoRiyu.labelName}"
												style="#{pc_Xrg01101.propEntry2MukoRiyu.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlEntry2MukoRiyu" value="#{pc_Xrg01101.propEntry2MukoRiyu.value}"
												readonly="#{pc_Xrg01101.propEntry2MukoRiyu.readonly}"
												disabled="#{pc_Xrg01101.propEntry2MukoRiyu.disabled}"
												style="width: 260px;#{pc_Xrg01101.propEntry2MukoRiyu.style}">
												<f:selectItems value="#{pc_Xrg01101.propEntry2MukoRiyu.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH rowspan="2" width="55">
											<h:outputText styleClass="outputText" value="申込3"
												id="lblEntry3">
											</h:outputText>
										</TH>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry3JugyoCd"
												value="#{pc_Xrg01101.propEntry3JugyoCd.labelName}"
												style="#{pc_Xrg01101.propEntry3JugyoCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:inputText id="htmlEntry3JugyoCd"
												styleClass="inputText"
												maxlength="#{pc_Xrg01101.propEntry3JugyoCd.max}"
												onblur="return doEntry3JugyoAjax(this, event);"
												disabled="#{pc_Xrg01101.propEntry3JugyoCd.disabled}"
												value="#{pc_Xrg01101.propEntry3JugyoCd.stringValue}"
												style="width: 40px"
												size="5">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="htmlSearchEntry3JugyoCd"
												onclick="return openEntry3JugyoCdSearchWindow(this, event);"
												disabled="#{pc_Xrg01101.propSearchEntry3JugyoCd.disabled}">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlEntry3JugyoName"
												value="#{pc_Xrg01101.strEntry3JugyoName}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry3ClassNum"
												value="#{pc_Xrg01101.propEntry3ClassNum.labelName}"
												style="#{pc_Xrg01101.propEntry3ClassNum.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:inputText id="htmlEntry3ClassNum"
												styleClass="inputText"
												maxlength="#{pc_Xrg01101.propEntry3ClassNum.max}"
												disabled="#{pc_Xrg01101.propEntry3ClassNum.disabled}"
												value="#{pc_Xrg01101.propEntry3ClassNum.stringValue}"
												style="width: 25px"
												size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry3KamokuCd"
												value="#{pc_Xrg01101.propEntry3KamokuCd.labelName}"
												style="#{pc_Xrg01101.propEntry3KamokuCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:outputText styleClass="outputText" id="htmlEntry3KamokuCd"
												value="#{pc_Xrg01101.strEntry3KamokuCd}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry3MukoRiyu"
												value="#{pc_Xrg01101.propEntry3MukoRiyu.labelName}"
												style="#{pc_Xrg01101.propEntry3MukoRiyu.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlEntry3MukoRiyu" value="#{pc_Xrg01101.propEntry3MukoRiyu.value}"
												readonly="#{pc_Xrg01101.propEntry3MukoRiyu.readonly}"
												disabled="#{pc_Xrg01101.propEntry3MukoRiyu.disabled}"
												style="width: 260px; #{pc_Xrg01101.propEntry3MukoRiyu.style}">
												<f:selectItems value="#{pc_Xrg01101.propEntry3MukoRiyu.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									<TR>
										<TH rowspan="2" width="55">
											<h:outputText styleClass="outputText" value="申込4"
												id="lblEntry4">
											</h:outputText>
										</TH>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry4JugyoCd"
												value="#{pc_Xrg01101.propEntry4JugyoCd.labelName}"
												style="#{pc_Xrg01101.propEntry4JugyoCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:inputText id="htmlEntry4JugyoCd"
												styleClass="inputText"
												maxlength="#{pc_Xrg01101.propEntry4JugyoCd.max}"
												onblur="return doEntry4JugyoAjax(this, event);"
												disabled="#{pc_Xrg01101.propEntry4JugyoCd.disabled}"
												value="#{pc_Xrg01101.propEntry4JugyoCd.stringValue}"
												style="width: 40px"
												size="5">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="htmlSearchEntry4JugyoCd"
												onclick="return openEntry4JugyoCdSearchWindow(this, event);"
												disabled="#{pc_Xrg01101.propSearchEntry4JugyoCd.disabled}">
											</hx:commandExButton>
											<h:outputText styleClass="outputText" id="htmlEntry4JugyoName"
												value="#{pc_Xrg01101.strEntry4JugyoName}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry4ClassNum"
												value="#{pc_Xrg01101.propEntry4ClassNum.labelName}"
												style="#{pc_Xrg01101.propEntry4ClassNum.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:inputText id="htmlEntry4ClassNum"
												styleClass="inputText"
												maxlength="#{pc_Xrg01101.propEntry4ClassNum.max}"
												disabled="#{pc_Xrg01101.propEntry4ClassNum.disabled}"
												value="#{pc_Xrg01101.propEntry4ClassNum.stringValue}"
												style="width: 25px"
												size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH width="85">
											<h:outputText styleClass="outputText" id="lblEntry4KamokuCd"
												value="#{pc_Xrg01101.propEntry4KamokuCd.labelName}"
												style="#{pc_Xrg01101.propEntry4KamokuCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="280">
											<h:outputText styleClass="outputText" id="htmlEntry4KamokuCd"
												value="#{pc_Xrg01101.strEntry4KamokuCd}">
											</h:outputText>
										</TD>
										<TH width="90">
											<h:outputText styleClass="outputText" id="lblEntry4MukoRiyu"
												value="#{pc_Xrg01101.propEntry4MukoRiyu.labelName}"
												style="#{pc_Xrg01101.propEntry4MukoRiyu.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="260">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlEntry4MukoRiyu" value="#{pc_Xrg01101.propEntry4MukoRiyu.value}"
												readonly="#{pc_Xrg01101.propEntry4MukoRiyu.readonly}"
												disabled="#{pc_Xrg01101.propEntry4MukoRiyu.disabled}"
												style="width: 260px; #{pc_Xrg01101.propEntry4MukoRiyu.style}">
												<f:selectItems value="#{pc_Xrg01101.propEntry4MukoRiyu.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR/>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
				width="100%">
				<TBODY>
					<TR>
						<TD align="center" width="100%"><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							disabled="#{pc_Xrg01101.propDeterminate.disabled}"
							action="#{pc_Xrg01101.doRegisterAction}"
							confirm="#{msg.SY_MSG_0002W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Xrg01101.doDeleteAction}"
							disabled="#{pc_Xrg01101.propDelete.disabled}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 -->

			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			
			<h:inputHidden value="#{pc_Xrg01101.propSchoolingEntryList.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propExecutableEntryFlg.stringValue}" id="htmlExecutableEntryFlg"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propExecutableChkKbn.stringValue}" id="htmlExecutableChkKbn"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propExecutableChkKbn1.stringValue}" id="htmlExecutableChkKbn1"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propExecutableChkKbn2.stringValue}" id="htmlExecutableChkKbn2"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn1.stringValue}" id="htmlKyoseiKyokaKbn1"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn2.stringValue}" id="htmlKyoseiKyokaKbn2"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn3.stringValue}" id="htmlKyoseiKyokaKbn3"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn4.stringValue}" id="htmlKyoseiKyokaKbn4"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn5.stringValue}" id="htmlKyoseiKyokaKbn5"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn6.stringValue}" id="htmlKyoseiKyokaKbn6"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn7.stringValue}" id="htmlKyoseiKyokaKbn7"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn8.stringValue}" id="htmlKyoseiKyokaKbn8"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn9.stringValue}" id="htmlKyoseiKyokaKbn9"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn10.stringValue}" id="htmlKyoseiKyokaKbn10"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn11.stringValue}" id="htmlKyoseiKyokaKbn11"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn12.stringValue}" id="htmlKyoseiKyokaKbn12"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn13.stringValue}" id="htmlKyoseiKyokaKbn13"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn14.stringValue}" id="htmlKyoseiKyokaKbn14"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn15.stringValue}" id="htmlKyoseiKyokaKbn15"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn16.stringValue}" id="htmlKyoseiKyokaKbn16"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn17.stringValue}" id="htmlKyoseiKyokaKbn17"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyoseiKyokaKbn18.stringValue}" id="htmlKyoseiKyokaKbn18"></h:inputHidden>
			<!-- 以下の項目は一覧選択時に取得する情報を保持 -->
			<h:inputHidden value="#{pc_Xrg01101.propOldKetteiJugyoCd.stringValue}" id="htmlOldKetteiJugyoCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldKetteiClassNum.stringValue}" id="htmlOldKetteiClassNum"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry1JugyoCd.stringValue}" id="htmlOldEntry1JugyoCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry1ClassNum.stringValue}" id="htmlOldEntry1ClassNum"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry2JugyoCd.stringValue}" id="htmlOldEntry2JugyoCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry2ClassNum.stringValue}" id="htmlOldEntry2ClassNum"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry3JugyoCd.stringValue}" id="htmlOldEntry3JugyoCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry3ClassNum.stringValue}" id="htmlOldEntry3ClassNum"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry4JugyoCd.stringValue}" id="htmlOldEntry4JugyoCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldEntry4ClassNum.stringValue}" id="htmlOldEntry4ClassNum"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldMskmDate.dateValue}" id="htmlOldMskmDate"><f:convertDateTime /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propOldMskmTime.dateValue}" id="htmlOldMskmTime"><f:convertDateTime type="time" timeStyle="short" /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propBunkatsuNo.integerValue}" id="htmlBunkatsuNo"><f:convertNumber /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propBunnoKbnCd.integerValue}" id="htmlBunnoKbnCd"><f:convertNumber /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propClassFuriwakFlg.stringValue}" id="htmlClassFuriwakFlg"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propHyokatutiHakkoDate.dateValue}" id="htmlHyokatutiHakkoDate"><f:convertDateTime /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propJigen.integerValue}" id="htmlJigen"><f:convertNumber /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKi.stringValue}" id="htmlKi"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propKyokasyoHakkoDate.dateValue}" id="htmlKyokasyoHakkoDate"><f:convertDateTime /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propNyukinzumiFlg.stringValue}" id="htmlNyukinzumiFlg"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propPatternCd.stringValue}" id="htmlPatternCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propPayCd.stringValue}" id="htmlPayCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propReportTeisyutuDate.dateValue}" id="htmlReportTeisyutuDate"><f:convertDateTime /></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propSikenHyokaCd.stringValue}" id="htmlSikenHyokaCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg01101.propSyusekiKaisu.integerValue}" id="htmlSyusekiKaisu"><f:convertNumber /></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>

