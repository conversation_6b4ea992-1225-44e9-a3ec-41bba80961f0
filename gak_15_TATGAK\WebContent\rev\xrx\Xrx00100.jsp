	<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00100.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrx00100.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >

<SCRIPT type="text/javascript"><!--
// 学生検索画面
function openSubWindow() {
  var url="${pageContext.request.contextPath}/faces/rev/co/";
  var outputTarget = document.getElementsByName('form1:htmlOutputTarget');

  if (outputTarget[1].checked) {
    url = url + "pCob0101.jsp"
              + "?retFieldName=form1:htmlGaksekiNo"
              + "&gakuseiSearchKbn=1";  
    openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
  } else if (outputTarget[2].checked) {
    url = url + "pCob0201.jsp"
              + "?retFieldName=form1:htmlGaksekiNo"
              + "&retFieldName2=form1:htmlHidSotNendo"
              + "&retFieldName3=form1:htmlHidSotGakki"
              + "&gakuseiSearchKbn=1";  
    openModalWindow(url, "PCob0201", "<%=com.jast.gakuen.rev.co.PCob0201.getWindowOpenOption() %>");
  } else {
    // 何もしない
    return false;
  }
}

// 学生氏名を取得する
function getGakuseiNmOnAjax(thisObj, thisEven, targetId) {
  var gaksekiCd = thisObj.value;
  if ("" == gaksekiCd) {
    document.getElementById(targetId).value = "";
    return false;
  }
  
  var outputTarget = document.getElementsByName('form1:htmloutputTarget');
  var outputTarget = document.getElementsByName('form1:htmlOutputTarget');
  var ajaxServlet;
  var args;

 if (outputTarget[1].checked) {
    ajaxServlet = "rev/xrx/XrxCobGakseiAJAX";
    args = {'code1' : gaksekiCd};
  } else if (outputTarget[2].checked) {
    ajaxServlet = "rev/co/CobSyutgakshNmAJAX";
    args = {'code1' : gaksekiCd, 'code2' : document.getElementById("form1:htmlHidSotNendo").value, 'code3' : document.getElementById("form1:htmlHidSotGakki").value};
  } else {
    // 何もしない
    return false;
  }
  new AjaxUtil().getCodeName(ajaxServlet, targetId, args);
}

// 出力対象切り替え時処理.
var beforeSelectOutputTarget;
function outputTargetEvent(selectedValue) {
  if (beforeSelectOutputTarget == selectedValue) {
    return false;
  }
  /** ラジオボタンを切り替えても「学籍番号」はクリアしない様に対応 START DEL **/
  // document.getElementById('form1:htmlGaksekiNo').value = '';
  /** ラジオボタンを切り替えても「学籍番号」はクリアしない様に対応 END   DEL **/
  document.getElementById('form1:htmlGakuseiNm').value = '';
  beforeSelectOutputTarget = selectedValue;
}

// 各種ボタン押下時のイベント
function showInfoWindow(thisObj) {
  // ボタンIDをHiddenに設定
  document.getElementById("form1:htmlHidButtonId").value = thisObj.id;
  // ボタン名称をHiddenに設定
  document.getElementById("form1:htmlHidButtonNm").value = thisObj.value;  
  // 学生氏名をHiddenに設定
  document.getElementById("form1:htmlHidGakuseiNm").value = document.getElementById("form1:htmlGakuseiNm").value;
}
-->
</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>

  <BODY>
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00100.onPageLoadBegin}">
      <gakuen:itemStateCtrl managedbean="pc_Xrx00100">
        <h:form styleClass="form" id="form1">

          <!--↓outer start↓-->
          <DIV class="outer">
            
            <!--↓head start↓-->
            <jsp:include page="../../rev/inc/header.jsp">
              <hx:panelBox styleClass="panelBox" id="boxHeader"></hx:panelBox>
            </jsp:include>

            <DIV style="display:none;">
              <hx:commandExButton
                type="submit"
                value="閉じる" 
                styleClass="commandExButton" 
                id="closeDisp"
                action="#{pc_Xrx00100.doCloseDispAction}"></hx:commandExButton>
              <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrx00100.funcId}"></h:outputText>
              <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
              <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrx00100.screenName}"></h:outputText>
            </DIV>

            <TABLE border="0">
              <TBODY>
                <TR>
                  <TD align="left">
                    <FIELDSET class="fieldset_err">
                      <LEGEND>エラーメッセージ</LEGEND>
                      <h:outputText 
                        id="message" 
                        value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText"
                        escape="false"
                        style="color: red; font-size: 8pt; font-weight: bold"></h:outputText>
                    </FIELDSET>
                  </TD>
                </TR>
              </TBODY>
            </TABLE>
            <!--↑head end↑-->

            <!--↓content start↓-->
            <DIV id="content">
              <DIV class="column" align="center">
                <TABLE border="0" cellpadding="5" width="870">
                  <TBODY>
                    <!-- ボタン一段目 -->
                    <TR>
                      <!-- 学籍基本情報ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton 
                          type="submit"
                          value="学籍基本情報"
                          styleClass="commandExButton"
                          id="xrx00101"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 身分異動情報ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="身分異動情報"
                          styleClass="commandExButton"
                          id="xrx00102"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 資格情報ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="資格情報"
                          styleClass="commandExButton"
                          id="xrx00103"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 履修登録ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="履修登録"
                          styleClass="commandExButton"
                          id="xrx00104"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 科目試験ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="科目試験"
                          styleClass="commandExButton"
                          id="xrx00105"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                    </TR>
                    <!-- ボタン二段目 -->
                    <TR>
                      <!-- スクーリングボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="スクーリング"
                          styleClass="commandExButton"
                          id="xrx00106"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- スクーリング単位認定ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="スクーリング単位認定"
                          styleClass="commandExButton"
                          id="xrx00107"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 教育実習ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="教育実習情報"
                          styleClass="commandExButton"
                          id="xrx00108"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 学費納入ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="学費納入"
                          styleClass="commandExButton"
                          id="xrx00109"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 納入記録ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="納入記録"
                          styleClass="commandExButton"
                          id="xrx00110"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 1px;">
                        </hx:commandExButton>
                      </TD>
                    </TR>
                    <!-- ボタン三段目 -->
                    <TR>
                      <!-- 証明書発行ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="証明書発行"
                          styleClass="commandExButton"
                          id="xrx00111"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 10px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 介護等体験情報ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="介護等体験情報"
                          styleClass="commandExButton"
                          id="xrx00112"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 10px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- レポート・試験情報ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="レポート・試験情報"
                          styleClass="commandExButton"
                          id="xrx00113"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 10px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 自由設定ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="自由設定"
                          styleClass="commandExButton"
                          id="xrx00114"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 10px;">
                        </hx:commandExButton>
                      </TD>
                      <TD width="2.5%"></TD>
                      <!-- 卒業関連ボタン -->
                      <TD width="18%" valign="middle" nowrap>
                        <hx:commandExButton
                          type="submit"
                          value="卒業関連"
                          styleClass="commandExButton"
                          id="xrx00115"
                          disabled="false"
                          action="#{pc_Xrx00100.doExecAction}"
                          onclick="showInfoWindow(this)"
                          style="width:100%; margin-bottom: 10px;">
                        </hx:commandExButton>
                      </TD>
                    </TR>
                  </TBODY>
                </TABLE>

                <TABLE class="table" border="0" cellpadding="5" width="870">
                  <TBODY>
                    <TR align="center" valign="middle">
                      <TH nowrap class="v_a" width="20%">
                      <!--出力対象 -->
                        <h:outputText 
                          styleClass="outputText"
                          id="lblOutputTarget"
                          value="出力対象"
                          style="#{pc_Xrx00100.propOutputTarget.labelStyle}">
                        </h:outputText>
                      </TH>
                      <TD width="*">
                        <h:selectOneRadio
                          disabledClass="selectOneRadio_Disabled"
                          styleClass="selectOneRadio"
                          id="htmlOutputTarget"
                          onclick="outputTargetEvent(this.value);"
                          value="#{pc_Xrx00100.propOutputTarget.value}">
                          <f:selectItem itemValue="0" itemLabel="在学生" />
                          <f:selectItem itemValue="1" itemLabel="出学者" />
                        </h:selectOneRadio>
                      </TD>
                    </TR>
                    <TR align="center" valign="middle">
                      <!--学籍番号(ラベル) -->
                      <TH nowrap class="v_a">
                        <h:outputText
                          styleClass="outputText"
                          id="lblGaksekiNo"
                          value="#{pc_Xrx00100.propGakusekiNo.labelName}"
                          style="#{pc_Xrx00100.propGakusekiNo.labelStyle}">
                        </h:outputText>
                      </TH>
                      <TD>
                        <h:inputText
                          styleClass="inputText"
                          id="htmlGaksekiNo" size="18"
                          maxlength="#{pc_Xrx00100.propGakusekiNo.maxLength}"
                          disabled="#{pc_Xrx00100.propGakusekiNo.disabled}"
                          value="#{pc_Xrx00100.propGakusekiNo.stringValue}"
                          readonly="#{pc_Xrx00100.propGakusekiNo.readonly}"
                          style="#{pc_Xrx00100.propGakusekiNo.style}"
                          onblur="return getGakuseiNmOnAjax(this, event, 'form1:htmlGakuseiNm');">
                        </h:inputText>
                        <hx:commandExButton
                          type="button"
                          value="検" 
                          styleClass="commandExButton_search" 
                          id="btnGakusekiF" 
                          disabled="#{pc_Xrx00100.propGakusekiNo.disabled}" 
                          onclick="openSubWindow();">
                        </hx:commandExButton>
                        <h:inputText
                          styleClass="likeOutput"
                          id="htmlGakuseiNm"
                          size="40"
                          tabindex="-1"
                          readonly="true"
                          value="#{pc_Xrx00100.propGakuseiNm.stringValue}"/>
                      </TD>
                    </TR>
                  </TBODY>
                </TABLE>

                <HR style="hr" noshade>

                <hx:jspPanel rendered="#{pc_Xrx00100.showDisp}">
                  <TABLE border="0" width="870" cellpadding="0" cellspacing="0" style="text-align:left;">
                    <TBODY>
                      <TR>
                        <TD width="150px">
                          <h:outputText styleClass="outputText" id="htmlInfoTitle" value="#{pc_Xrx00100.propInfoTitle.stringValue}"/>
                        </TD>
                        <TD>
                          <h:outputText styleClass="outputText" id="gakuseiInfo" value="#{pc_Xrx00100.propGakuseiInfo.stringValue}"/>
                        </TD>
                      </TR>
                      <TR>
                        <TD>
                          <h:outputText styleClass="outputText" id="lblGakusekiInfoMemo" value="学籍基本情報<メモ>："/>
                        </TD>
                        <TD>
                          <h:outputText styleClass="outputText" id="htmlInfoMemo" value="#{pc_Xrx00100.propInfoMemo.stringValue}"/>
                        </TD>
                      </TR>
                    </TBODY>
                  </TABLE>
                  <HR style="hr" noshade>
                </hx:jspPanel>

                <!-- 押下されたボタンに応じたJSPをインクルード -->
                <c:set var="includeJspPath" value="${pc_Xrx00100.includeJspPath}"/>
                <c:if test="${includeJspPath != ''}">
                   <div id="includeJspArea">
                     <jsp:include page="${includeJspPath}" flush="true"/>
                   </div>
                </c:if>

              </DIV>
            </DIV>

            <!-- Hidden項目群 -->
            <h:inputHidden id="htmlHidSotNendo" value="#{pc_Xrx00100.propHtmlHidSotNendo.value}"/>
            <h:inputHidden id="htmlHidSotGakki" value="#{pc_Xrx00100.propHtmlHidSotGakki.value}"/>
            <h:inputHidden id="htmlHidButtonId" value="#{pc_Xrx00100.propHtmlHidButtonId.value}"/>
            <h:inputHidden id="htmlHidButtonNm" value="#{pc_Xrx00100.propHtmlHidButtonNm.value}"/>
            <h:inputHidden id="htmlHidGakuseiNm" value="#{pc_Xrx00100.propHtmlHidGakuseiNm.value}"/>
            <h:inputHidden id="htmlHidKanriNo" value="#{pc_Xrx00100.propHtmlHidKanriNo.longValue}"/>
            <h:inputHidden id="htmlHidKamokCd" value="#{pc_Xrx00100.propHtmlHidKamokCd.stringValue}"/>
            <!--↑content end↑-->

          </DIV>
          <!--↑outer end↑-->

          <!--↓footer↓-->
          <jsp:include page ="../inc/footer.jsp" />
        </h:form>
      </gakuen:itemStateCtrl>
    </hx:scriptCollector>
  </BODY>
  <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
