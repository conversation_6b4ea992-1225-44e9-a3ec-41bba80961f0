<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00201.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>Xrm00201.jsp</TITLE>
<SCRIPT type="text/javascript"></SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY>
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrm00201.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp"
                    action="#{pc_Xrm00201.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrm00201.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrm00201.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" 
                    	value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
											<TBODY>
												<TR>
													<!-- 学費年度 -->
													<TH nowrap class="v_a" width="180">
														<h:outputText styleClass="outputText" id="lblGhYear"
															value="#{pc_Xrm00201.propGhYear.labelName}"
															style="#{pc_Xrm00201.propGhYear.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText styleClass="inputText" id="htmlGhYear"
															size="4" value="#{pc_Xrm00201.propGhYear.dateValue}"
															style="#{pc_Xrm00201.propGhYear.style}"
															disabled="#{pc_Xrm00201.propGhYear.disabled}"
															tabindex="1">
															<hx:inputHelperAssist imeMode="inactive"
																errorClass="inputText_Error" promptCharacter="_" />
															<f:convertDateTime pattern="yyyy" />
														</h:inputText>
													</TD>
												</TR>
 
												<TR>
													<TH nowrap class="v_a" width="180">
													    <!-- 入学学期 -->
														<h:outputText styleClass="outputText" id="lblGakkiNo" 
														    value="#{pc_Xrm00201.propGakkiNo.name}"
														    style="#{pc_Xrm00201.propGakkiNo.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="*">
														<h:inputText styleClass="inputText"
															id="htmlGakkiNo" size="2"
															maxlength="#{pc_Xrm00201.propGakkiNo.maxLength}"
															disabled="#{pc_Xrm00201.propGakkiNo.disabled}"
															value="#{pc_Xrm00201.propGakkiNo.value}"
															readonly="#{pc_Xrm00201.propGakkiNo.readonly}"
															style="#{pc_Xrm00201.propGakkiNo.style}">
															<f:convertNumber type="number" pattern="#0"/>
															<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
														</h:inputText>
													</TD>
												</TR>

												<TR>
													<TH width="150px" nowrap class="v_c">
														<h:outputText styleClass="outputText"
															id="lblSyoriKbnLabel"
															style="#{pc_Xrm00201.propSyoriKbn.style}"
															value="#{pc_Xrm00201.propSyoriKbnLabel.name}">
														</h:outputText>
													</TH>
													<TD width="*" nowrap>
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
															id="htmlSyoriKbn"
															value="#{pc_Xrm00201.propSyoriKbn.checked}">
														</h:selectBooleanCheckbox>
														<h:outputText styleClass="outputText"
															id="lblSyoriKbn" 
															value="#{pc_Xrm00201.propSyoriKbn.name}"
															style="#{pc_Xrm00201.propSyoriKbn.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH width="150px" nowrap class="v_d">
														<h:outputText styleClass="outputText" 
															id="lblChkList"
															value="#{pc_Xrm00201.propChkListLabel.name}">
														</h:outputText>
													</TH>
													<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
															<TBODY>
																<TR>
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListNormal"
																			value="#{pc_Xrm00201.propChkListNormal.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText" 
																			id="lblChkListNormal"
																			value="#{pc_Xrm00201.propChkListNormal.name}"
																			style="#{pc_Xrm00201.propChkListNormal.style}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListError"
																			value="#{pc_Xrm00201.propChkListError.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText"
																			id="lblChkListError"
																			value="#{pc_Xrm00201.propChkListError.name}"
																			style="#{pc_Xrm00201.propChkListError.style}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>													
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListWarning"
																			value="#{pc_Xrm00201.propChkListWarning.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText" 
																			id="lblChkListWarning"
																			value="#{pc_Xrm00201.propChkListWarning.name}"
																			style="#{pc_Xrm00201.propChkListWarning.style}">
																		</h:outputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								<TR>
									<TD>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton type="submit" 
															value="実行"
															styleClass="commandExButton_dat" id="exec"
															confirm="#{msg.SY_MSG_0001W}" 
															action="#{pc_Xrm00201.doExecAction}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
            	<!--↑CONTENT↑-->
            
			</DIV>
	        <!--↑outer↑-->

	        <!-- フッダーインクルード -->
	        <jsp:include page ="../inc/footer.jsp" />

		</h:form>
    </hx:scriptCollector>
    </BODY>
    
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
