<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg01601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Xrg01601.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--	title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<SCRIPT type="text/javascript">
// 入力項目指定画面を開く
function openPCos0401() {
	setTarget("xxxx");
	openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
	return true;
}

var schSbtCd = "";
function getSchoolingSbtCb() {
	// スクーリング種別コンボボックス取得AJAX
	var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
	var args = new Array();
	args['nendo'] = document.getElementById('form1:htmlNendo').value;
	args['bunrui'] = "1";
	args['upperKakuteiKbn'] = "30";
	var target = "";
	
	comb = document.getElementById('form1:htmlSchooling');
	schSbtCd = comb.options[comb.selectedIndex].value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
}

function callBackMethod(value){
	var comb = document.getElementById('form1:htmlSchooling');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
}

function confirmOk(){
	try{
		document.getElementById("form1:htmlConfilm").value = "1";
		var action = document.getElementById("form1:htmlAction").value;
		indirectClick(action);
	} catch (e) {
	}
}

function confirmCancel(){
	try{
		document.getElementById("form1:htmlConfilm").value = "0";
	} catch (e) {
	}
}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrg01601.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg01601.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg01601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg01601.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">
	
	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
	<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText>
	</FIELDSET>

	<DIV class="head_button_area" >　
	<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	
	<!--↓content↓-->
	<DIV id="content">
		<DIV class="column" align="center">
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD nowrap width="80%">
									<CENTER>
									<TABLE class="table" width="650" border="0" cellpadding="0"
										cellspacing="0" style="">
										<TBODY>
											<TR>
												<TH class="v_a" width="150">
													<h:outputText styleClass="outputText" id="lblNendo"
														value="#{pc_Xrg01601.propNendo.labelName}"
														style="#{pc_Xrg01601.propNendo.labelStyle}">
													</h:outputText>

												</TH>
												<TD nowrap width="500">
													<h:inputText id="htmlNendo" styleClass="inputText"
														readonly="#{pc_Xrg01601.propNendo.readonly}"
														style="#{pc_Xrg01601.propNendo.style}"
														value="#{pc_Xrg01601.propNendo.dateValue}"
														disabled="#{pc_Xrg01601.propNendo.disabled}" size="4"
														onblur="getSchoolingSbtCb();"
														tabindex="1">
														<hx:inputHelperAssist errorClass="inputText_Error"
								    						imeMode="inactive" promptCharacter="_" />
														<f:convertDateTime pattern="yyyy" />
													</h:inputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_a" width="150">
													<h:outputText styleClass="outputText" id="lblSchooling"
														value="#{pc_Xrg01601.propSchooling.labelName}"
														style="#{pc_Xrg01601.propSchooling.labelStyle}">
													</h:outputText>
												</TH>
												<TD nowrap width="500">
													<h:selectOneMenu styleClass="selectOneMenu"
														id="htmlSchooling" value="#{pc_Xrg01601.propSchooling.value}"
														readonly="#{pc_Xrg01601.propSchooling.readonly}"
														style="#{pc_Xrg01601.propSchooling.style}">
														<f:selectItems value="#{pc_Xrg01601.propSchooling.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="150">
													<h:outputText
														styleClass="outputText" id="lblSyoriKbn" value="処理区分指定">
													</h:outputText>
												</TH>
												<TD nowrap width="500">
													<h:selectBooleanCheckbox
														styleClass="selectBooleanCheckbox" id="htmlSyoriKbn" tabindex="3" 
														value="#{pc_Xrg01601.propSyoriKbn.checked}"
														readonly="#{pc_Xrg01601.propSyoriKbn.readonly}"
														disabled="#{pc_Xrg01601.propSyoriKbn.disabled}"
														style="#{pc_Xrg01601.propSyoriKbn.style}">
													</h:selectBooleanCheckbox>
													<h:outputText styleClass="outputText"
														id="lblChkOnly" value="チェックのみ（データの登録/更新は行いません）">
													</h:outputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="150">
													<h:outputText
														styleClass="outputText" id="lblCheckList" value="チェックリスト出力指定">
													</h:outputText>
												</TH>
												<TD nowrap width="500">
													<TABLE border="0" cellpadding="0" cellspacing="0">
														<TR>
															<TD class="clear_border">
																<h:selectBooleanCheckbox
																	styleClass="selectBooleanCheckbox" 
																	id="htmlChkListNormal" tabindex="4" 
																	value="#{pc_Xrg01601.propChkListNormal.checked}"
																	readonly="#{pc_Xrg01601.propChkListNormal.readonly}"
																	disabled="#{pc_Xrg01601.propChkListNormal.disabled}"
																	style="#{pc_Xrg01601.propChkListNormal.style}">
																</h:selectBooleanCheckbox>
																<h:outputText styleClass="outputText"
																	id="lblChkListNormal" value="正常データ">
																</h:outputText>
															</TD>
														</TR>
														<TR>
															<TD class="clear_border">
																<h:selectBooleanCheckbox
																	styleClass="selectBooleanCheckbox" 
																	id="htmlChkListError" tabindex="5"
																	value="#{pc_Xrg01601.propChkListError.checked}"
																	readonly="#{pc_Xrg01601.propChkListError.readonly}"
																	disabled="#{pc_Xrg01601.propChkListError.disabled}"
																	style="#{pc_Xrg01601.propChkListError.style}">
																</h:selectBooleanCheckbox>
																<h:outputText styleClass="outputText"
																	id="lblChkListError" value="エラーデータ">
																</h:outputText>
															</TD>
														</TR>
														<TR>
															<TD class="clear_border">
																<h:selectBooleanCheckbox
																	styleClass="selectBooleanCheckbox"
																	id="htmlChkListWarning" tabindex="6"
																	value="#{pc_Xrg01601.propChkListWarning.checked}"
																	readonly="#{pc_Xrg01601.propChkListWarning.readonly}"
																	disabled="#{pc_Xrg01601.propChkListWarning.disabled}"
																	style="#{pc_Xrg01601.propChkListWarning.style}">
																</h:selectBooleanCheckbox>
																<h:outputText styleClass="outputText"
																	id="lblChkListWarning" value="ワーニングデータ">
																</h:outputText>
															</TD>
														</TR>
													</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</CENTER>
									</TD>
								</TR>
								<TR>
									<TD nowrap width="80%"></TD>
								</TR>
							</TBODY>
						</TABLE><CENTER>
						<TABLE class="button_bar" width="650">
							<TBODY>
								<TR>
									<TD width="650">
										<hx:commandExButton type="submit"
											value="実　行" styleClass="commandExButton_dat" id="exec"
											action="#{pc_Xrg01601.doExecAction}"
											confirm="#{msg.SY_MSG_0001W}" tabindex="8"
											disabled="#{pc_Xrg01601.propExec.disabled}"
											style="#{pc_Xrg01601.propExec.style}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</CENTER></TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<h:inputHidden
				value="#{pc_Xrg01601.propConfilm.stringValue}"
				id="htmlConfilm">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrg01601.propAction.stringValue}"
				id="htmlAction">
			</h:inputHidden>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page ="../inc/footer.jsp" />
	</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
