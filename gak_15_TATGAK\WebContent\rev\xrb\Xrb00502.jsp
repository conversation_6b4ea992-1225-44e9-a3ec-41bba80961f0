<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00502.jsp</TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	

<style type="text/css">
<!--

.tableStyle {
    border-collapse: collapse !important;
    border:0px !important;
}

.beforeHeaderStyle {
    border-top-style:none !important;
    border-left-style:none !important;
}

.afterHeaderStyle {
    border-top-style:none !important;
}

.beforeKamokuCodeHeaderStyle {
    border-left-style:none !important;
    border-bottom-style:none !important;
}

.headerStyle {
    border-bottom-style:none !important;
}

.leftItemStyle {
    border-top-style:none !important;
    border-left-style:none !important;
    border-bottom-style:none !important;
}

.centerItemStyle {
    border-top-style:none !important;
    border-bottom-style:none !important;
}

.rightItemStyle {
    border-top-style:none !important;
    border-right-style:none !important;
    border-bottom-style:none !important;
}


-->
</style>

<SCRIPT type="text/javascript">
var beforeSelectKamokChangeMode;
function openKamokuSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;

	var reOpenSubWinFlg = "0";
	var ajaxServlet = "rev/xrx/XrxRemoveFromSessionAJAX";
	var args = new Array();
		args['pcClass'] = 'com.jast.gakuen.rev.km.PKmz0101';
		args['motoFuncId'] = '';
	var engine      = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) {
			var windowPointer = openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
			if(reOpenSubWinFlg=="0"){
				focus();
			}
		}
	);
	engine.send(ajaxServlet,null,args);
	return false;
}
function doGakuseiAjax(thisObj, thisEvent) {
	 // 学生名称を取得する

	 var servlet = "rev/co/CobGakseiAJAX";
	 var args = new Array();
	 args['code1'] = thisObj.value;
	 var target = "form1:htmlSearchName";
	 var ajaxUtil = new AjaxUtil();
	 ajaxUtil.getCodeName(servlet, target, args);
}
function doBeforeRishuInfoAjax(thisObj, thisEvent) {
	var servlet = "rev/xrb/XrbRishuInfoAJAX";
    var args = new Array();
    args['kanriNo'] = document.getElementById("form1:htmlKanriNoHidden").value;
    args['gakusekiCode'] = document.getElementById("form1:htmlGakusekiCode").value;
    args['kamokuCode'] = thisObj.value;
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, "", args, "beforeRishuInfoAjaxCallBack");
}

function beforeRishuInfoAjaxCallBack(value) {
	if (value['kamokuName'] != "null" && value['kamokuName']) {
		document.getElementById("form1:htmlBeforeKamokuName").value = value['kamokuName'];
		document.getElementById("form1:htmlBeforeKamokuNameHidden").value = value['kamokuName'];
	} else {
		document.getElementById("form1:htmlBeforeKamokuName").value = "";
		document.getElementById("form1:htmlBeforeKamokuNameHidden").value = "";
	}
	if (value['rishuHoho'] != "null" && value['rishuHoho']) {
		document.getElementById("form1:htmlBeforeRishuHohoHidden").value = value['rishuHoho'];
	} else {
		document.getElementById("form1:htmlBeforeRishuHohoHidden").value = "";
	}
	if (value['rishuHohoDisp'] != "null" && value['rishuHohoDisp']) {
		document.getElementById("form1:htmlBeforeRishuHoho").value = value['rishuHohoDisp'];
		document.getElementById("form1:htmlBeforeRishuHohoDispHidden").value = value['rishuHohoDisp'];
	} else {
		document.getElementById("form1:htmlBeforeRishuHoho").value = "";
		document.getElementById("form1:htmlBeforeRishuHohoDispHidden").value =  "";
	}
	if (value['rishuNenji'] != "null" && value['rishuNenji']) {
		document.getElementById("form1:htmlBeforeRishuNenjiHidden").value = value['rishuNenji'];
	} else {
		document.getElementById("form1:htmlBeforeRishuNenjiHidden").value = "";
	}
	if (value['rishuNenjiDisp'] != "null" && value['rishuNenjiDisp']) {
		document.getElementById("form1:htmlBeforeRishuNenji").value = value['rishuNenjiDisp'];
		document.getElementById("form1:htmlBeforeRishuNenjiDispHidden").value = value['rishuNenjiDisp'];
	} else {
		document.getElementById("form1:htmlBeforeRishuNenji").value = "";
		document.getElementById("form1:htmlBeforeRishuNenjiDispHidden").value = "";
	}
	if (value['taniSu'] != "null" && value['taniSu']) {
		document.getElementById("form1:htmlBeforeTaniSu").value = value['taniSu'];
		document.getElementById("form1:htmlBeforeTaniSuHidden").value = value['taniSu'];
	} else {
		document.getElementById("form1:htmlBeforeTaniSu").value = "";
		document.getElementById("form1:htmlBeforeTaniSuHidden").value = "";
	}
	if (value['textTaniSu'] != "null" && value['textTaniSu']) {
		document.getElementById("form1:htmlBeforeTextTaniSuHidden").value = value['textTaniSu'];
	} else {
		document.getElementById("form1:htmlBeforeTextTaniSuHidden").value = "";
	}
	if (value['schoolingTaniSu'] != "null" && value['schoolingTaniSu']) {
		document.getElementById("form1:htmlBeforeSchoolingTaniSuHidden").value = value['schoolingTaniSu'];
	} else {
		document.getElementById("form1:htmlBeforeSchoolingTaniSuHidden").value = "";
	}
}

function doAfterRishuInfoAjax(thisObj, thisEvent) {
	var servlet = "rev/xrb/XrbRishuInfoAJAX";
    var args = new Array();
    args['kanriNo'] = document.getElementById("form1:htmlKanriNoHidden").value;
    args['gakusekiCode'] = document.getElementById("form1:htmlGakusekiCode").value;
    args['kamokuCode'] = thisObj.value;
    args['henkoKbn'] = document.getElementById("form1:htmlHenkoKbn").value;
    args['rishuHoho'] = document.getElementById("form1:htmlAfterRishuHoho").value;
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, "", args, "afterRishuInfoAjaxCallBack");
}

function afterRishuInfoAjaxCallBack(value) {
	if (value['kamokuName'] != "null" && value['kamokuName']) {
		document.getElementById("form1:htmlAfterKamokuName").value = value['kamokuName'];
		document.getElementById("form1:htmlAfterKamokuNameHidden").value = value['kamokuName'];
	} else {
		document.getElementById("form1:htmlAfterKamokuName").value = "";
		document.getElementById("form1:htmlAfterKamokuNameHidden").value = "";
	}
	if (value['taniSuDisp'] != "null" && value['taniSuDisp']) {
		document.getElementById("form1:htmlAfterTaniSu").value = value['taniSuDisp'];
	} else {
		document.getElementById("form1:htmlAfterTaniSu").value = "";
	}
	if (value['textTaniSu'] != "null" && value['textTaniSu']) {
		document.getElementById("form1:htmlAfterTextTaniSuHidden").value = value['textTaniSu'];
	} else {
		document.getElementById("form1:htmlAfterTextTaniSuHidden").value = "";
	}
	if (value['schoolingTaniSu'] != "null" && value['schoolingTaniSu']) {
		document.getElementById("form1:htmlAfterSchoolingTaniSuHidden").value = value['schoolingTaniSu'];
	} else {
		document.getElementById("form1:htmlAfterSchoolingTaniSuHidden").value = "";
	}
	if (value['utiwakeKingaku'] != "null" && value['utiwakeKingaku']) {
		document.getElementById("form1:htmlUtiwakeKingaku").value = value['utiwakeKingaku'];
	} else {
		document.getElementById("form1:htmlUtiwakeKingaku").value = "";
	}
}

function confirmOk() {
	document.getElementById('form1:htmlWarningDialogFlag').value = 1;
	if (document.getElementById('form1:htmlWarningDialogType').value == 0) {
		indirectClick('htmlRegisterButton');
	} else if(document.getElementById('form1:htmlWarningDialogType').value == 1) {
		indirectClick('htmlDeleteButton');
	} else {
		indirectClick('htmlHenkoKbnSelectButton');
	}
}

function confirmCancel() {
	document.getElementById('form1:htmlWarningDialogFlag').value = 0;
}
//-------------------------------------------
// 画面ロード時処理.
//-------------------------------------------
function loadAction(event){
	if (document.getElementsByName('form1:htmlKamokChangeMode')[1].checked) {
	    beforeSelectKamokChangeMode = '1';
	} else {
	    beforeSelectKamokChangeMode = '2';
        document.getElementById('form1:htmlUtiwakeKingaku').value = "0";
        document.getElementById('form1:htmlUtiwakeKingaku').stringValue = "0";
	}
    if(document.getElementById('form1:htmlBeforeKamokuName').value == ""
    && document.getElementById('form1:htmlBeforeKamokuNameHidden').value != "") {
    	document.getElementById('form1:htmlBeforeKamokuName').value=document.getElementById('form1:htmlBeforeKamokuNameHidden').value;
	}
	if(document.getElementById('form1:htmlBeforeRishuNenji').value == ""
    && document.getElementById('form1:htmlBeforeRishuNenjiDispHidden').value != "") {
		document.getElementById('form1:htmlBeforeRishuNenji').value=document.getElementById('form1:htmlBeforeRishuNenjiDispHidden').value;
	}
	if(document.getElementById('form1:htmlBeforeRishuHoho').value == ""
    && document.getElementById('form1:htmlBeforeRishuHohoDispHidden').value != "") {
  		document.getElementById('form1:htmlBeforeRishuHoho').value=document.getElementById('form1:htmlBeforeRishuHohoDispHidden').value;
	}
	if(document.getElementById('form1:htmlBeforeTaniSu').value == ""
    && document.getElementById('form1:htmlBeforeTaniSuHidden').value != "") {
  		document.getElementById('form1:htmlBeforeTaniSu').value=document.getElementById('form1:htmlBeforeTaniSuHidden').value;
	}
	if (document.getElementsByName('form1:htmlKamokChangeMode')[2].checked) {
  	}
	var focusId = document.getElementById('form1:htmlHiddenTargetFocusId').value;
	if ('' != focusId) {
	  if ('1' == beforeSelectKamokChangeMode) {
 		document.getElementsByName('form1:htmlKamokChangeMode')[1].focus();
	  } else {
	  	document.getElementsByName('form1:htmlKamokChangeMode')[2].focus();
	  }
	}
}
//-------------------------------------------
// 科目変更モード切り替え.
//-------------------------------------------
    <%-- 科目変更モード切り替え --%>
	function switching(raidio) {
	  var selectedValue = raidio.value;
	  document.getElementById('form1:htmlHiddenTargetFocusId').value = raidio.name;
	  if(raidio.value == "2"){
	  	document.getElementById('form1:htmlAfterKamokuCode').value="";
  		document.getElementById('form1:htmlAfterKamokuName').value="";
  		document.getElementById('form1:htmlAfterKamokuNameHidden').value="";
  		document.getElementById('form1:htmlAfterRishuNenji').value="|no select|";
 	 	document.getElementById('form1:htmlAfterRishuHoho').value="|no select|";
  		document.getElementById('form1:htmlAfterTaniSu').value="";
  		document.getElementById('form1:htmlAfterTextTaniSuHidden').value="";
  		document.getElementById('form1:htmlAfterSchoolingTaniSuHidden').value="";
	  }
	  if (beforeSelectKamokChangeMode == selectedValue) {
	    return false;
	  }
	  beforeSelectKamokChangeMode = selectedValue;
	  return true;
	}
	<%-- フォーカスＩＤをクリア --%>
	function clearHiddenFocusId() {
		document.getElementById('form1:htmlHiddenTargetFocusId').value = '';
	}
	
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00502.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
styleClass="commandExButton" id="closeDisp"
action="#{pc_Xrb00502.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00502.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00502.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
 <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
  <TBODY>
   <TR>
    <TD>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD align="right">
         <hx:commandExButton
          type="submit"
          value="戻る"
          styleClass="commandExButton"
          style="width: 80px"
          action="#{pc_Xrb00502.doReturnAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 学籍番号 -->
         <h:outputText
          styleClass="outputText"
          id="lblGakusekiCode"
          value="#{pc_Xrb00502.propGakusekiCode.labelName}"
          style="#{pc_Xrb00502.propGakusekiCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="1" style="border-right:none;">
         <h:inputText
          styleClass="inputText"
          id="htmlGakusekiCode" size="10"
          maxlength="#{pc_Xrb00502.propGakusekiCode.maxLength}"
          disabled="#{pc_Xrb00502.propGakusekiCode.disabled}"
          value="#{pc_Xrb00502.propGakusekiCode.stringValue}"
          style="#{pc_Xrb00502.propGakusekiCode.style}"
          readonly="#{pc_Xrb00502.propGakusekiCode.readonly}"
          onblur="return doGakuseiAjax(this, event)">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlGakusekiCdSearchButton"
          disabled="#{pc_Xrb00502.propGakusekiCode.disabled}"
          onclick="openSubWindow('form1:htmlGakusekiCode');">
         </hx:commandExButton>
        <h:inputText
          styleClass="likeOutput"
          id="htmlSearchName"
          size="40"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00502.propSearchName.stringValue}"/>
        </TD>
        <TD width="90" style="text-align: right;padding-right: 3px;border-left:none;">
         <hx:commandExButton
          type="submit"
          value="選択"
          styleClass="commandExButton"
          id="htmlGakuseiSelectButton"
          style="width: 40px"
          action="#{pc_Xrb00502.doGakuseiSelectAction}"
          disabled="#{pc_Xrb00502.propGakuseiSelectButton.disabled}"
          onclick="showInfoName(this)">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="解除"
          styleClass="commandExButton"
          id="htmlGakuseiCancelButton"
          style="width: 40px"
          action="#{pc_Xrb00502.doGakuseiCancelAction}"
          disabled="#{pc_Xrb00502.propGakuseiCancelButton.disabled}">
         </hx:commandExButton>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 申請NO -->
         <h:outputText styleClass="outputText" id="lblSinseiNoTitle"
          value="#{pc_Xrb00502.propSinseiNo.labelName}"
          style="#{pc_Xrb00502.propSinseiNo.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:outputText styleClass="outputText" id="lblSinseiNo"
          style="#{pc_Xrb00502.propSinseiNo.labelStyle}"
          value="#{pc_Xrb00502.propSinseiNo.stringValue}">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 変更区分 -->
         <h:outputText styleClass="outputText" id="lblHenkoKbn"
          value="#{pc_Xrb00502.propHenkoKbn.labelName}"
          style="#{pc_Xrb00502.propHenkoKbn.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:selectOneMenu
          id="htmlHenkoKbn"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00502.propHenkoKbn.value}"
          disabled="#{pc_Xrb00502.propHenkoKbn.disabled}"
          style="#{pc_Xrb00502.propHenkoKbn.style}"
          readonly="#{pc_Xrb00502.propHenkoKbn.readonly}">
          <f:selectItems value="#{pc_Xrb00502.propHenkoKbn.list}" />
         </h:selectOneMenu>
         <hx:commandExButton
          type="submit"
          value="選択"
          styleClass="commandExButton"
          id="htmlHenkoKbnSelectButton"
          style="width: 40px"
          disabled="#{pc_Xrb00502.propHenkoKbnSelectButton.disabled}"
          onclick="clearHiddenFocusId();">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="解除"
          styleClass="commandExButton"
          id="htmlHenkoKbnCancelButton"
          style="width: 40px"
          action="#{pc_Xrb00502.doHenkoKbnCancelAction}"
          disabled="#{pc_Xrb00502.propHenkoKbnCancelButton.disabled}"
          onclick="clearHiddenFocusId();">
         </hx:commandExButton>
        </TD>
        <TH nowrap class="v_a" width="160">
         <!-- 申請状態 -->
         <h:outputText styleClass="outputText" id="lblSinseiJotai"
          value="#{pc_Xrb00502.propSinseiJotai.labelName}"
          style="#{pc_Xrb00502.propSinseiJotai.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:selectOneMenu
          id="htmlSinseiJotai"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00502.propSinseiJotai.value}"
          disabled="#{pc_Xrb00502.propSinseiJotai.disabled}"
          style="#{pc_Xrb00502.propSinseiJotai.style}"
          readonly="#{pc_Xrb00502.propSinseiJotai.readonly}">
          <f:selectItems value="#{pc_Xrb00502.propSinseiJotai.list}" />
         </h:selectOneMenu>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 申請日 -->
         <h:outputText styleClass="outputText" id="lblSinseiDate" 
          value="#{pc_Xrb00502.propSinseiDate.labelName}"
          style="#{pc_Xrb00502.propSinseiDate.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="inputText"
          id="htmlSinseiDate"
          size="10"
          disabled="#{pc_Xrb00502.propSinseiDate.disabled}"
          value="#{pc_Xrb00502.propSinseiDate.dateValue}"
          style="#{pc_Xrb00502.propSinseiDate.style}"
          readonly="#{pc_Xrb00502.propSinseiDate.readonly}">
          <f:convertDateTime />
          <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
          <hx:inputHelperDatePicker />
         </h:inputText>
        </TD>
        <TH nowrap class="v_a">
         <!-- 振込依頼人コード -->
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCodeTitle" 
          value="#{pc_Xrb00502.propHurikomiIraininCode.labelName}"
          style="#{pc_Xrb00502.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCode" 
          value="#{pc_Xrb00502.propHurikomiIraininCode.stringValue}"
          style="#{pc_Xrb00502.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 申請理由 -->
         <h:outputText styleClass="outputText" id="lblSinseiRiyu" 
          value="#{pc_Xrb00502.propSinseiRiyu.labelName}"
          style="#{pc_Xrb00502.propSinseiRiyu.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSinseiRiyu"
          value="#{pc_Xrb00502.propSinseiRiyu.stringValue}"
          style="#{pc_Xrb00502.propSinseiRiyu.style}"
          disabled="#{pc_Xrb00502.propSinseiRiyu.disabled}"
          cols="85"
          rows="6">
         </h:inputTextarea>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 職員コメント -->
         <h:outputText styleClass="outputText" id="lblSyokuinComment" 
          value="#{pc_Xrb00502.propSyokuinComment.labelName}"
          style="#{pc_Xrb00502.propSyokuinComment.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSyokuinComment"
          value="#{pc_Xrb00502.propSyokuinComment.stringValue}"
          style="#{pc_Xrb00502.propSyokuinComment.style}"
          disabled="#{pc_Xrb00502.propSyokuinComment.disabled}"
          cols="85"
          rows="6">
         </h:inputTextarea>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 合計金額 -->
         <h:outputText styleClass="outputText" id="lblGokeiKingakuTitle" 
          value="#{pc_Xrb00502.propGokeiKingaku.labelName}"
          style="#{pc_Xrb00502.propGokeiKingaku.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="710">
         <h:outputText styleClass="outputText" id="lblGokeiKingaku" 
          value="#{pc_Xrb00502.propGokeiKingaku.integerValue}"
          style="#{pc_Xrb00502.propGokeiKingaku.labelStyle}">
          <f:convertNumber pattern="#,###,###,###"/>
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD align="right" nowrap class="outputText" width="100%">
         <h:outputText
          styleClass="outputText"
          id="lblSinseiCount"
          value="#{pc_Xrb00502.propSinseiList.listCount}">
         </h:outputText>件
        </TD>
       </TR>
       <TR>
        <TD>
         <div class="listScroll" style="height: 300px">
          <h:dataTable
           border="0"
           cellpadding="0"
           cellspacing="0"
           headerClass="headerClass"
           footerClass="footerClass"
           columnClasses="columnClass1"
           rowClasses="#{pc_Xrb00502.propSinseiList.rowClasses}"
           styleClass="meisai_scroll"
           id="htmlSisneiList"
           var="varlist"
           value="#{pc_Xrb00502.propSinseiList.list}">
           <h:column id="column1">
            <f:facet name="header">
             <hx:jspPanel id="jspPanel1">
              <TABLE  cellpadding="0" cellspacing="0" width="0" height="100%" class="tableStyle">
               <TBODY>
                <TR>
                 <TH width="360" colspan="5" class="beforeHeaderStyle">
                  <h:outputText styleClass="outputText"
                   id="lblListTogai" value="変更前">
                  </h:outputText>
                 </TH>
                 <TH width="360" colspan="5" class="afterHeaderStyle">
                  <h:outputText styleClass="outputText"
                   id="lblListMeuke" value="変更後">
                  </h:outputText>
                 </TH>
                </TR>
                <TR>
                 <TH width="72" class="beforeKamokuCodeHeaderStyle">
                  <h:outputText id="lblBeforeKamokuCode_head"
                   styleClass="outputText" value="科目コード">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblBeforeKamokuName_head"
                   styleClass="outputText" value="科目名">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblBeforeRishuNenji_head"
                   styleClass="outputText" value="履修年次">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblBeforeRishuHoho_head"
                   styleClass="outputText" value="履修方法">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblBeforeTaniSu_head"
                   styleClass="outputText" value="単位数">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblAfterKamokuCode_head"
                   styleClass="outputText" value="科目コード">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblAfterKamokuName_head"
                   styleClass="outputText" value="科目名">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblAfterRishuNenji_head"
                   styleClass="outputText" value="履修年次">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblAfterRishuHoho_head"
                   styleClass="outputText" value="履修方法">
                  </h:outputText>
                 </TH>
                 <TH width="72" class="headerStyle">
                  <h:outputText id="lblAfterTaniSu_head"
                   styleClass="outputText" value="単位数">
                  </h:outputText>
                 </TH>
                </TR>
               </TBODY>
              </TABLE>
             </hx:jspPanel>
            </f:facet>
            <hx:jspPanel id="jspPanel2">
             <TABLE  cellpadding="0" cellspacing="0" width="0" height="100%" class="tableStyle">
              <TBODY>
               <TR>
                <TD width="72" class="leftItemStyle">
                 <h:outputText styleClass="outputText"
                  id="lblBeforeKamokuCode_list" value="#{varlist.propBeforeKamokuCode.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle">
                 <h:outputText styleClass="outputText"
                  id="lblBeforeKamokuName_list" value="#{varlist.propBeforeKamokuName.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle" style="text-align: center !important">
                 <h:outputText styleClass="outputText"
                  id="lblBeforeRishuNenji_list" value="#{varlist.propBeforeRishuNenji.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle" style="text-align: center !important">
                 <h:outputText styleClass="outputText"
                  id="lblBeforeRishuHoho_list" value="#{varlist.propBeforeRishuHoho.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle" style="text-align: center !important">
                 <h:outputText styleClass="outputText"
                  id="lblBeforeTaniSu_list" value="#{varlist.propBeforeTaniSu.doubleValue}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle">
                 <h:outputText styleClass="outputText"
                  id="lblAfterKamokuCode_list" value="#{varlist.propAfterKamokuCode.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle">
                 <h:outputText styleClass="outputText"
                  id="lblAfterKamokuName_list" value="#{varlist.propAfterKamokuName.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle" style="text-align: center !important">
                 <h:outputText styleClass="outputText"
                  id="lblAfterRishuNenji_list" value="#{varlist.propAfterRishuNenji.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="centerItemStyle" style="text-align: center !important">
                 <h:outputText styleClass="outputText"
                  id="lblAfterRishuHoho_list" value="#{varlist.propAfterRishuHoho.value}">
                 </h:outputText>
                </TD>
                <TD width="72" class="rightItemStyle" style="text-align: center !important">
                 <h:outputText styleClass="outputText"
                  id="lblAfterTaniSu_list" value="#{varlist.propAfterTaniSu.doubleValue}">
                 </h:outputText>
                </TD>
               </TR>
              </TBODY>
             </TABLE>
            </hx:jspPanel>
           </h:column>
           <h:column id="column2">
            <f:facet name="header">
             <h:outputText id="lblUtiwakeKingaku_head"
              styleClass="outputText" value="内訳金額">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblUtiwakeKingaku_list" value="#{varlist.propUtiwakeKingaku.integerValue}">
              <f:convertNumber pattern="###,###,###"/>
            </h:outputText>
            <f:attribute value="70" name="width" />
           </h:column>
           <h:column id="column3">
            <hx:commandExButton type="submit" value="選択"
             styleClass="commandExButton" id="htmlListSelectButton"
             rendered="#{varlist.rendered}"
             disabled="#{varlist.propListSelectButton.disabled}"
             style="width: 40px"
             action="#{pc_Xrb00502.doListSelectAction}"
             onclick="clearHiddenFocusId();">
            </hx:commandExButton>
            <hx:commandExButton type="submit" value="削除"
             styleClass="commandExButton" id="htmlListDeleteButton"
             rendered="#{varlist.rendered}"
             disabled="#{varlist.propListDeleteButton.disabled}"
             style="width: 40px"
             action="#{pc_Xrb00502.doListDeletetAction}"
             onclick="clearHiddenFocusId();">
            </hx:commandExButton>
            <f:attribute value="80" name="width" />
           </h:column>
          </h:dataTable>
         </div>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD align="left">
         <h:outputText
          styleClass="outputText"
          value="変更前科目">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 変更前科目コード -->
         <h:outputText styleClass="outputText" id="lblBeforeKamokuCode"
          value="#{pc_Xrb00502.propBeforeKamokuCode.labelName}"
          style="#{pc_Xrb00502.propBeforeKamokuCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:inputText
          styleClass="inputText"
          id="htmlBeforeKamokuCode" size="10"
          maxlength="#{pc_Xrb00502.propBeforeKamokuCode.maxLength}"
          disabled="#{pc_Xrb00502.propBeforeKamokuCode.disabled}"
          value="#{pc_Xrb00502.propBeforeKamokuCode.stringValue}"
          style="#{pc_Xrb00502.propBeforeKamokuCode.style}"
          readonly="#{pc_Xrb00502.propBeforeKamokuCode.readonly}"
          onblur="return doBeforeRishuInfoAjax(this, event);">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlBeforeKamokuCodeSearchButton"
          disabled="#{pc_Xrb00502.propBeforeKamokuCode.disabled}"
          onclick="return openKamokuSubWindow('form1:htmlBeforeKamokuCode');">
         </hx:commandExButton>
         <h:inputText
          styleClass="likeOutput"
          id="htmlBeforeKamokuName"
          size="20"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00502.propBeforeKamokuName.stringValue}">
         </h:inputText>
        </TD>
        <TH nowrap class="v_a" width="160">
         <!-- 変更前履修年次 -->
         <h:outputText styleClass="outputText" id="lblBeforeRishuNenji"
          value="#{pc_Xrb00502.propBeforeRishuNenji.labelName}"
          style="#{pc_Xrb00502.propBeforeRishuNenji.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:inputText
          styleClass="likeOutput"
          id="htmlBeforeRishuNenji"
          size="20"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00502.propBeforeRishuNenji.stringValue}">
         </h:inputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 変更前履修方法 -->
         <h:outputText styleClass="outputText" id="lblBeforeRishuHoho"
          value="#{pc_Xrb00502.propBeforeRishuHoho.labelName}"
          style="#{pc_Xrb00502.propBeforeRishuHoho.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="likeOutput"
          id="htmlBeforeRishuHoho"
          size="20"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00502.propBeforeRishuHoho.stringValue}">
         </h:inputText>
        </TD>
        <TH nowrap class="v_a">
         <!-- 変更前単位数 -->
         <h:outputText styleClass="outputText" id="lblBeforeTaniSu"
          value="#{pc_Xrb00502.propBeforeTaniSu.labelName}"
          style="#{pc_Xrb00502.propBeforeTaniSu.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="likeOutput"
          id="htmlBeforeTaniSu"
          size="20"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00502.propBeforeTaniSu.stringValue}">
         </h:inputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TH align="left" width="70">
         <h:outputText
          styleClass="outputText"
          value="変更後科目">
         </h:outputText>
        </TH>
        <TD align="left">
         <!-- 科目変更モード -->
         <h:selectOneRadio
           disabledClass="selectOneRadio_Disabled"
           id="htmlKamokChangeMode"
           style="font-size: 9pt"
           value="#{pc_Xrb00502.propKamokChangeMode.stringValue}"
           rendered="#{pc_Xrb00502.propKamokChangeMode.rendered}"
           disabled="#{pc_Xrb00502.propKamokChangeMode.disabled}"
           onclick="if(switching(this)) submit();">
           <f:selectItem itemValue="1" itemLabel="変更"/>
           <f:selectItem itemValue="2" itemLabel="削除"/>
         </h:selectOneRadio>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <hx:jspPanel rendered="#{pc_Xrb00502.propKamokChangeMode.stringValue == '1'}">
	     <TABLE class="table" width="870">
	      <TBODY>
	       <TR>
	        <TH nowrap class="v_a" width="160">
	         <!-- 変更後科目コード -->
	         <h:outputText styleClass="outputText" id="lblAfterKamokuCode"
	          value="#{pc_Xrb00502.propAfterKamokuCode.labelName}"
	          style="#{pc_Xrb00502.propAfterKamokuCode.labelStyle}">
	         </h:outputText>
	        </TH>
	        <TD width="275">
	         <h:inputText
	          styleClass="inputText"
	          id="htmlAfterKamokuCode" size="10"
	          maxlength="#{pc_Xrb00502.propAfterKamokuCode.maxLength}"
	          disabled="#{pc_Xrb00502.propAfterKamokuCode.disabled}"
	          value="#{pc_Xrb00502.propAfterKamokuCode.stringValue}"
	          style="#{pc_Xrb00502.propAfterKamokuCode.style}"
	          readonly="#{pc_Xrb00502.propAfterKamokuCode.readonly}"
	          onblur="return doAfterRishuInfoAjax(this, event);">
	         </h:inputText>
	         <hx:commandExButton
	          type="button"
	          value="検"
	          styleClass="commandExButton_search"
	          id="htmlAfterKamokuCodeSearchButton"
	          disabled="#{pc_Xrb00502.propAfterKamokuCode.disabled}"
	          onclick="return openKamokuSubWindow('form1:htmlAfterKamokuCode');">
	         </hx:commandExButton>
	         <h:inputText
	          styleClass="likeOutput"
	          id="htmlAfterKamokuName"
	          size="20"
	          tabindex="-1"
	          readonly="true"
	          value="#{pc_Xrb00502.propAfterKamokuName.stringValue}">
	         </h:inputText>
	        </TD>
	        <TH nowrap class="v_a" width="160">
	         <!-- 変更後履修年次 -->
	         <h:outputText styleClass="outputText" id="lblAfterRishuNenji"
	          value="#{pc_Xrb00502.propAfterRishuNenji.labelName}"
	          style="#{pc_Xrb00502.propAfterRishuNenji.labelStyle}">
	         </h:outputText>
	        </TH>
	        <TD width="275">
	         <h:selectOneMenu
	          id="htmlAfterRishuNenji"
	          styleClass="selectOneMenu"
	          value="#{pc_Xrb00502.propAfterRishuNenji.value}"
	          disabled="#{pc_Xrb00502.propAfterRishuNenji.disabled}"
	          style="#{pc_Xrb00502.propAfterRishuNenji.style}"
	          readonly="#{pc_Xrb00502.propAfterRishuNenji.readonly}">
	          <f:selectItems value="#{pc_Xrb00502.propAfterRishuNenji.list}" />
	         </h:selectOneMenu>
	        </TD>
	       </TR>
	       <TR>
	        <TH nowrap class="v_a">
	         <!-- 変更後履修方法 -->
	         <h:outputText styleClass="outputText" id="lblAfterRishuHoho"
	          value="#{pc_Xrb00502.propAfterRishuHoho.labelName}"
	          style="#{pc_Xrb00502.propAfterRishuHoho.labelStyle}">
	         </h:outputText>
	        </TH>
	        <TD>
	         <h:selectOneMenu
	          id="htmlAfterRishuHoho"
	          styleClass="selectOneMenu"
	          value="#{pc_Xrb00502.propAfterRishuHoho.value}"
	          disabled="#{pc_Xrb00502.propAfterRishuHoho.disabled}"
	          style="#{pc_Xrb00502.propAfterRishuHoho.style}"
	          readonly="#{pc_Xrb00502.propAfterRishuHoho.readonly}"
	          onchange="document.getElementById('form1:htmlAfterKamokuCode').onblur()">
	          <f:selectItems value="#{pc_Xrb00502.propAfterRishuHoho.list}" />
	         </h:selectOneMenu>
	        </TD>
	        <TH nowrap class="v_a">
	         <!-- 変更後単位数 -->
	         <h:outputText styleClass="outputText" id="lblAfterTaniSu"
	          value="#{pc_Xrb00502.propAfterTaniSu.labelName}"
	          style="#{pc_Xrb00502.propAfterTaniSu.labelStyle}">
	         </h:outputText>
	        </TH>
	        <TD>
	         <h:inputText
	          styleClass="likeOutput"
	          id="htmlAfterTaniSu"
	          size="20"
	          tabindex="-1"
	          readonly="true"
	          value="#{pc_Xrb00502.propAfterTaniSu.stringValue}">
	         </h:inputText>
	        </TD>
	       </TR>
        </TBODY>
       </TABLE>
       <BR>
     </hx:jspPanel>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 内訳金額 -->
         <h:outputText styleClass="outputText" id="lblUtiwakeKingaku" 
          value="#{pc_Xrb00502.propUtiwakeKingaku.labelName}"
          style="#{pc_Xrb00502.propUtiwakeKingaku.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="710">
         <h:inputText
          styleClass="inputText"
          id="htmlUtiwakeKingaku" size="10"
          maxlength="#{pc_Xrb00502.propUtiwakeKingaku.maxLength}"
          disabled="#{pc_Xrb00502.propUtiwakeKingaku.disabled}"
          value="#{pc_Xrb00502.propUtiwakeKingaku.integerValue}"
          style="#{pc_Xrb00502.propUtiwakeKingaku.style}"
          readonly="#{pc_Xrb00502.propUtiwakeKingaku.readonly}">
          <f:convertNumber pattern="###,###,###"/>
         </h:inputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD colspan="2" align="right">
         <hx:commandExButton
          type="submit"
          value="登録"
          styleClass="commandExButton"
          id="htmlRegisterButton"
          style="width: 40px"
          action="#{pc_Xrb00502.doRegisterAction}"
          disabled="#{pc_Xrb00502.propRegisterButton.disabled}"
          onclick="clearHiddenFocusId();">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD>
         <hx:commandExButton
          type="submit"
          value="確定"
          styleClass="commandExButton_dat"
          id="htmlKakuteiButton"
          disabled="#{pc_Xrb00502.propKakuteiButton.disabled}"
          confirm="#{msg.SY_MSG_0001W}"
          action="#{pc_Xrb00502.doKakuteiAction}"
          onclick="clearHiddenFocusId();">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="削除"
          styleClass="commandExButton_dat"
          id="htmlDeleteButton"
          disabled="#{pc_Xrb00502.propDeleteButton.disabled}"
          confirm="#{msg.SY_MSG_0004W}"
          action="#{pc_Xrb00502.doDeleteAction}"
          onclick="clearHiddenFocusId();">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="クリア"
          styleClass="commandExButton_dat"
          id="htmlClearButton"
          disabled="#{pc_Xrb00502.propClearButton.disabled}"
          action="#{pc_Xrb00502.doClearAction}"
          onclick="clearHiddenFocusId();">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="CSV作成"
          styleClass="commandExButton_dat"
          id="htmlCsvButton"
          disabled="#{pc_Xrb00502.propCsvButton.disabled}"
          confirm="#{msg.SY_MSG_0020W}"
          action="#{pc_Xrb00502.doCsvAction}"
          onclick="clearHiddenFocusId();">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
    </TD>
   </TR>
  </TBODY>
 </TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden
 id="htmlKanriNoHidden"
 value="#{pc_Xrb00502.propKanriNoHidden.longValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeKamokuNameHidden"
 value="#{pc_Xrb00502.propBeforeKamokuNameHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeRishuNenjiHidden"
 value="#{pc_Xrb00502.propBeforeRishuNenjiHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeRishuNenjiDispHidden"
 value="#{pc_Xrb00502.propBeforeRishuNenjiDispHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeRishuHohoHidden"
 value="#{pc_Xrb00502.propBeforeRishuHohoHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeRishuHohoDispHidden"
 value="#{pc_Xrb00502.propBeforeRishuHohoDispHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeTextTaniSuHidden"
 value="#{pc_Xrb00502.propBeforeTextTaniSuHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeSchoolingTaniSuHidden"
 value="#{pc_Xrb00502.propBeforeSchoolingTaniSuHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlBeforeTaniSuHidden"
 value="#{pc_Xrb00502.propBeforeTaniSuHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlAfterKamokuNameHidden"
 value="#{pc_Xrb00502.propAfterKamokuNameHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlAfterTextTaniSuHidden"
 value="#{pc_Xrb00502.propAfterTextTaniSuHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlAfterSchoolingTaniSuHidden"
 value="#{pc_Xrb00502.propAfterSchoolingTaniSuHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlWarningDialogFlag"
 value="#{pc_Xrb00502.propWarningDialogFlag.integerValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlWarningDialogType"
 value="#{pc_Xrb00502.propWarningDialogType.integerValue}">
</h:inputHidden>
<h:inputHidden 
 id="htmlHiddenTargetFocusId"
 value="#{pc_Xrb00502.propHiddenTargetFocusId.stringValue}">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

