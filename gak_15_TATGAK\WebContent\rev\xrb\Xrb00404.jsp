<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00404.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00404.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }



function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}
function doAllSelect(thisObj, thisEvent) {
// チェックボックス一括チェック
	check('htmlKamokList','htmlListCheckBox');
}

function doAllUnSelect(thisObj, thisEvent) {
// チェックボックス一括解除
	uncheck('htmlKamokList','htmlListCheckBox');
}

function openKamokuSubWindow(field1) {
// 科目検索画面
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;
	openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	return false;
}

function doKamokuAjax(thisObj, thisEvent, targetLabel) {
// 科目名称を取得する
	var servlet = "rev/km/KmzKmkAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xrb00404.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xrb00404.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xrb00404.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xrb00404.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
				<hx:commandExButton type="submit" value="戻る"
					styleClass="commandExButton" id="returnDisp"
					action="#{pc_Xrb00404.doReturnDispAction}">
				</hx:commandExButton>
            <!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
            <TABLE width="850">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblKamokuFurikaeCd"
	                                	value="科目振替コード">
	                                </h:outputText>
	                            </TH>
	                            <TD width="150">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlKamokuFurikaeCd"
	                                	value="#{pc_Xrb00404.propKamokuFurikaeCd.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                           	<TH class="v_b" width="140">
	                           		<h:outputText styleClass="outputText"
	                                	id="lblTitle"
	                                	value="タイトル">
	                                </h:outputText>
	                            </TH>
	                            <TD width="500">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlTitle"
	                                	value="#{pc_Xrb00404.propTitle.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                        </TR>
	                    </TABLE>
                    </TD>
                </TR>
            </TABLE>
            <BR>
			<TABLE width="850">
				<TR>
					<TD width="810" align="left">
						<TABLE width="810" border="0" cellpadding="0">
							<TR>
								<TD align="right" nowrap class="outputText">
									<h:outputText styleClass="outputText"
										id="htmlKamokListCount"
										value="#{pc_Xrb00404.propKamokListCount.value}">
									</h:outputText>
								</TD>
							</TR>
							<tr>
								<td>
									<DIV style="height:180px" class="listScroll"
										onscroll="setScrollPosition('scroll', this);">
										<h:dataTable border="0" cellpadding="0" cellspacing="0"
											columnClasses="columnClass1" headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Xrb00404.propKamokList.rowClasses}"
											styleClass="meisai_scroll" id="htmlKamokList"
											value="#{pc_Xrb00404.propKamokList.list}" var="varlist">
											<h:column id="column1">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value=""
														id="lblListTaisyoChk">
													</h:outputText>
												</f:facet>
												<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlListCheckBox" value="#{varlist.taisyoChk}">
												</h:selectBooleanCheckbox>
												<f:attribute value="text-align:center" name="style" />
												<f:attribute value="60" name="width" />
											</h:column>
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="科目コード"
														id="lblListKamokCd">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokCd"
													value="#{varlist.kamokCd}">
												</h:outputText>
												<f:attribute value="true" name="nowrap" />
												<f:attribute value="140" name="width" />
											</h:column>
											<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="振替先科目名称"
														id="lblListKamokName">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokName"
													value="#{varlist.kamokName}">
												</h:outputText>
												<f:attribute value="true" name="nowrap" />
												<f:attribute value="500" name="width" />
											</h:column>
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="群認定"
														id="lblListGunnintei">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListGunnintei"
													value="#{varlist.gunnintei}">
												</h:outputText>
												<f:attribute value="true" name="nowrap" />
												<f:attribute value="80" name="width" />
											</h:column>
										</h:dataTable>
										<BR>
									</DIV>
								</td>
							</tr>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
			<TABLE width="850" align="center">
				<TR>
					<TD align="left">
						<hx:commandExButton type="submit" value="一括チェック"
							styleClass="check" id="btnAllSelect"  onclick="return doAllSelect(this, event);">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="一括解除"
							styleClass="uncheck" id="btnAllUnSelect" onclick="return doAllUnSelect(this, event);">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="削除"
							styleClass="commandExButton" id="delete"
							action="#{pc_Xrb00404.doDeleteAction}" tabindex="1">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
            <TABLE width="850">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblKamokCd"
										style="#{pc_Xrb00404.propKamokCd.labelStyle}"
	                                	value="#{pc_Xrb00404.propKamokCd.labelName}">
	                                </h:outputText>
	                            </TH>
	                            <TD width="500">
									<h:inputText styleClass="inputText"
										id="htmlKamokCd"
										value="#{pc_Xrb00404.propKamokCd.stringValue}"
										style="#{pc_Xrb00404.propKamokCd.style}"
	                                	maxlength="#{pc_Xrb00404.propKamokCd.maxLength}"
										onblur="return doKamokuAjax(this, event, 'form1:htmlKamokName');"
										size="10" tabindex="2">
									</h:inputText>
									<hx:commandExButton
										type="button" value="" styleClass="commandExButton_search"
										id="search"
										onclick="return openKamokuSubWindow('form1:htmlKamokCd');">
									</hx:commandExButton>
									<h:outputText styleClass="outputText"
										id="htmlKamokName"
										value="#{pc_Xrb00404.propKamokName.stringValue}">
									</h:outputText>
	                            </TD>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblGunnintei"
	                                	value="群認定">
	                                </h:outputText>
	                            </TH>
	                            <TD width="140">
									<h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox"
										id="htmlGunnintei"
										value="#{pc_Xrb00404.propGunnintei.checked}"
										disabled="#{pc_Xrb00404.propGunnintei.disabled}">
									</h:selectBooleanCheckbox>
	                            </TD>
	                        </TR>
	                    </TABLE>
					</TD>
                </TR>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE width="850" class="button_bar" border="0" cellpadding="0"
                cellspacing="0">
                <TR>
                    <TD >
                    	<hx:commandExButton type="submit" value="追加"
							styleClass="commandExButton_dat" id="addNew"
							style="#{pc_Xrb00404.propAddNew.style}"
							action="#{pc_Xrb00404.doAddNewAction}" tabindex="3">
						</hx:commandExButton>
                    	<hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_dat" id="clear"
							style="#{pc_Xrb00404.propClear.style}"
							action="#{pc_Xrb00404.doClearAction}" tabindex="4">
						</hx:commandExButton>
                    </TD>
                </TR>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE width="850" class="button_bar" border="0" cellpadding="0"
                cellspacing="0">
                <TR>
                    <TD >
                    	<hx:commandExButton type="submit" value="登録"
							styleClass="commandExButton_dat" id="register"
							style="#{pc_Xrb00404.propRegister.style}"
							action="#{pc_Xrb00404.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}" tabindex="5">
						</hx:commandExButton>
                    </TD>
                </TR>
            </TABLE>
			<h:inputHidden value="#{pc_Xrb00404.propHidNyugakNendo.stringValue}" id="htmlHidNyugakNendo"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00404.propHidNyugakGakkiNo.stringValue}" id="htmlHidNyugakGakkiNo"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00404.propHidNyugakNenji.stringValue}" id="htmlHidNyugakNenji"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00404.propHidNyugakSbt.stringValue}" id="htmlHidNyugakSbt"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00404.propHidCurGakkaCd.stringValue}" id="htmlHidCurGakkaCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00404.propHidKamokuFurikaeKbn.stringValue}" id="htmlHidKamokuFurikaeKbn"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00404.propHidKisyutokFlg.stringValue}" id="htmlHidKisyutokFlg"></h:inputHidden>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

