<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm01301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrm01301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript"
	src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/xrm/inc/gakuenXRM.js"></SCRIPT>
<SCRIPT type="text/javascript">

	window.attachEvent("onload", attachFormatNumber);
	
	function fncInit() {

		//ボタンの活性化、非活性化の制御
		fncButtonActive();
	
	}
	

//フォーカスの設定
function setFocus(){
		
	var id = null;

	//フォーカス設定ボタン取得
	id = document.getElementById('form1:htmlFocusId').value;

	//選択ボタン押下時
	if ((id != null) && (id != "")) {
		if (document.getElementById('form1:htmlPaywList:0:paywListEdit') != null) {
			document.getElementById('form1:htmlPaywList:'+(id-1)+':paywListEdit').focus();
		}
	}
}


//ボタンの活性化制御
function fncButtonActive() {

	var propKbn = null;

	activeKbn = document.getElementById('form1:htmlActiveControl').value;
	if (activeKbn == "1"){
		document.getElementById('form1:register').disabled = true;	//確定
		document.getElementById('form1:delete').disabled = true;	//削除
		document.getElementById('form1:unselect').disabled = true;	//解除	
		//document.getElementById('form1:table1:lblerrDate_head').disabled = true;	//勘定日		
	} else if (activeKbn == "2") {
		document.getElementById('form1:register').disabled = false;	//確定
		document.getElementById('form1:delete').disabled = false;	//削除
		document.getElementById('form1:unselect').disabled = false;	//解除	
		//document.getElementById('form1:table1:lblerrDate_head').disabled = false;	//勘定日
	} else if (activeKbn == "3") {
		document.getElementById('form1:register').disabled = true;	//確定
		document.getElementById('form1:delete').disabled = false;	//削除
		document.getElementById('form1:unselect').disabled = false;	//解除
		//document.getElementById('form1:table1:lblerrDate_head').disabled = false;	//勘定日
	}
	
	//Ajaxにて取得したの値を保持するためにjavascriptでreadonlyを設定する
	//document.getElementById('form1:htmlName').readOnly = true;
	document.getElementById('form1:htmlBankName').readOnly = true;
	document.getElementById('form1:htmlSitenName').readOnly = true;
	document.getElementById('form1:htmlNewFurikomiIraiCd').readOnly = true;
	
	
	//取込ボタンの制御 htmlHidErrorKbn
	if(document.getElementById('form1:htmlHidErrorKbn').value == '1'){
		document.getElementById('form1:register').disabled = true;	//確定
		document.getElementById('form1:delete').disabled = true;	//削除
		document.getElementById('form1:unselect').disabled = true;	//解除	
		document.getElementById('form1:pdfout').disabled = true;	//ｐｄｆ出力	
		document.getElementById('form1:popBankSearch').disabled = true;	//取引銀行一覧ボタン
	}
    
	
}


//出力項目指定画面へ遷移
function openPCos0401Window() {
	openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
	return true;
}

//金融機関取引口座検索画面へ遷移
function openPCog1101Window() {
	openPCog1101("<%=com.jast.gakuen.rev.co.PCog1101.getWindowOpenOption() %>");
	
	return true;
}


//銀行名称取得・支店名称取得Ajax呼び出し
function ajaxBankSitenCd() {
	//銀行コード
	var bankCd = document.getElementById('form1:htmlBankCd').value;
	//支店コード
	var sitenCd = document.getElementById('form1:htmlSitenCd').value;
	//銀行名称項目id
	var bankNameId = "form1:htmlBankName";
	//支店名称項目id
	var sitenNameId = "form1:htmlSitenName";
	
	//銀行名称取得Ajax呼び出し
	funcAjaxSetBankCd(bankCd, bankNameId)
	//支店名称取得Ajax呼び出し
	funcAjaxSetSitenCd(bankCd, sitenCd, sitenNameId)
	return true;
}

//新規登録可能な振込依頼人コード取得Ajax呼び出し
function ajaxNewFurikomiIraiCd() {

	var nyugakuShohiFlg = document.getElementById('form1:htmlNyugakuShohiFlg').value;
	//入学検定日のみajax連携を行う。
	if(nyugakuShohiFlg == "1" || nyugakuShohiFlg == "2"){
		//振込依頼人コード
		var furikomiIraiCd = document.getElementById('form1:htmlFurikomiIraiCd').value;
		//新規登録可能な振込依頼人コード項目id
		var newFurikomiIraiCdId = "form1:htmlNewFurikomiIraiCd";
		
		//新規登録可能な振込依頼人コード取得Ajax呼び出し
		funcAjaxSetNewFurikomiIraiCd(furikomiIraiCd, newFurikomiIraiCdId)
	}
		
	return true;
}




</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncInit();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm01301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm01301.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrm01301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm01301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="700">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText styleClass="outputText"
							value="#{pc_Xrm01301.propXrkSyomBmn.listCount == null ? 0 : pc_Xrm01301.propXrkSyomBmn.listCount}"
							style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="htmlCountlbl" value="件"
							style="font-size: 8pt"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="800">
				<TBODY>
					<TR>
						<TD>

						<div class="listScroll" style="height:245px;" id="listScroll"
							><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrm01301.propXrkSyomBmn.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xrm01301.propXrkSyomBmn.list}" var="varlist">

							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblerrDate_head" styleClass="outputText"
										value="エラー勘定日">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="errDate"
									value="#{varlist.errDate}"></h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: middle" name="style" />
							</h:column>

							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblpayGaku_head" styleClass="outputText"
										value="入金額">
									</h:outputText>
									
								</f:facet>
								<h:outputText styleClass="outputText" id="payGaku"
									value="#{varlist.payGakuComma}"></h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>


							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblfurikomiIraiCd_head"
										styleClass="outputText" value="振込依頼人コード">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="furikomiIraiCd"
									value="#{varlist.furikomiIraiCd}"></h:outputText>
								<f:attribute value="90" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>

							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblfurikomiName_head" styleClass="outputText"
										value="振込依頼人氏名">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="furikomiName"
									value="#{varlist.furikomiName}"></h:outputText>
								<f:attribute value="400" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblgyomuKbn_head" styleClass="outputText"
										value="業務区分">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="gyomuKbn"
									value="#{varlist.gyomuKbn}"></h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="50" name="width">
								</f:attribute>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style">
								</f:attribute>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Xrm01301.doSelectAction}">
								</hx:commandExButton>
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>


			<TABLE width="800px" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="left">
							<!-- ※入学諸費用のコメント -->
							<h:outputText styleClass="outputText"
								value="#{pc_Xrm01301.propComment.value}"
								style="font-size: 8pt"  escape="false">
							</h:outputText>
						</TD>
						<TD align="right">
							<!-- 解除ボタン -->
							<hx:commandExButton type="submit" value="解除"
								styleClass="cmdBtn_etc_s" 
								id="unselect" 
								action="#{pc_Xrm01301.doClearAction}"
								disabled="#{!pc_Xrm01301.propKaijyo.disabled}" tabindex="3">
							</hx:commandExButton>
						</TD>						
					</TR>
				</TBODY>
			</TABLE>

			<TABLE width="800px" border="0" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
				
					<TR>
						<TH align="left" nowrap class="v_c"><!-- 勘定日 --><h:outputText
							styleClass="outputText" id="lblNyukinDate"
							value="#{pc_Xrm01301.propNyukinDate.labelName}"
							style="#{pc_Xrm01301.propNyukinDate.labelStyle}">
						</h:outputText></TH>
						<TD colspan="2" width="100px" align="left"><h:inputText
							styleClass="inputText" id="htmlNyukinDate"
							value="#{pc_Xrm01301.propNyukinDate.dateValue}"
							style="#{pc_Xrm01301.propNyukinDate.style}"
							disabled="#{pc_Xrm01301.propNyukinDate.disabled}" size="10"
							tabindex="10">
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH align="left" nowrap class="v_d"><!-- 入金額 --><h:outputText
							styleClass="outputText" id="lblNyukinGaku"
							value="#{pc_Xrm01301.propNyukinGaku.labelName}"
							style="#{pc_Xrm01301.propNyukinGaku.labelStyle}">
						</h:outputText></TH>
						<TD colspan="2" align="left"><h:inputText styleClass="inputText"
							id="htmlNyukinGaku"
							value="#{pc_Xrm01301.propNyukinGaku.stringValue}"
							style="#{pc_Xrm01301.propNyukinGaku.style}"
							disabled="#{pc_Xrm01301.propNyukinGaku.disabled}" 
                            size="9" 
							style="padding-right: 3px; text-align: right" tabindex="11">
						</h:inputText></TD>
					</TR>
					
					
					<TR><!-- 振込依頼人コード -->
 						<TH align="left" nowrap class="v_d"><h:outputText
							styleClass="outputText" id="lblFurikomiIraiCd"
							value="#{pc_Xrm01301.propFurikomiIraiCd.labelName}"
							style="#{pc_Xrm01301.propFurikomiIraiCd.labelStyle}">
						</h:outputText></TH>
						<TD colspan="2" align="left">
							<h:inputText styleClass="inputText"
								id="htmlFurikomiIraiCd"
								value="#{pc_Xrm01301.propFurikomiIraiCd.stringValue}"
								style="#{pc_Xrm01301.propFurikomiIraiCd.style}"
								disabled="#{pc_Xrm01301.propFurikomiIraiCd.disabled}" 
								onblur="return ajaxNewFurikomiIraiCd();"
								maxlength="10"
								style="padding-right: 3px; " size="10" tabindex="12">
							</h:inputText>
							<!-- 新規登録可能な振込依頼人コード -->
							<h:inputText styleClass="likeOutput"
								id="htmlNewFurikomiIraiCd" tabindex="-1"
								value="#{pc_Xrm01301.propNewFurikomiIraiCd.stringValue}"
								style="#{pc_Xrm01301.propNewFurikomiIraiCd.style}"  size="10">
							</h:inputText>
						</TD>
					</TR>
					
					<TR><!-- 振込依頼人氏名 -->
 						<TH align="left" nowrap class="v_d"><h:outputText
							styleClass="outputText" id="lblFurikomiIraiName"
							value="#{pc_Xrm01301.propFurikomiIraiName.labelName}"
							style="#{pc_Xrm01301.propFurikomiIraiName.labelStyle}">
						</h:outputText></TH>
						<TD colspan="2" align="left"><h:inputText styleClass="inputText"
							id="htmlFurikomiIraiName"
							value="#{pc_Xrm01301.propFurikomiIraiName.stringValue}"
							style="#{pc_Xrm01301.propFurikomiIraiName.style}"
							disabled="#{pc_Xrm01301.propFurikomiIraiName.disabled}" 
							maxlength="#{pc_Xrm01301.propFurikomiIraiName.max}"
							style="padding-right: 3px; " tabindex="13" size="50">
						</h:inputText></TD>
					</TR>

					
					<TR><!-- 取引銀行コード -->
						<TH width="160px" align="left" nowrap class="v_f"><h:outputText
							styleClass="outputText" id="lblBankCd"
							value="#{pc_Xrm01301.propBankCd.labelName}"
							style="#{pc_Xrm01301.propBankCd.labelStyle}">
						</h:outputText></TH>
						<TD align="left" colspan="1">
							<h:inputText
								styleClass="inputText" id="htmlBankCd"
								value="#{pc_Xrm01301.propBankCd.stringValue}"
								style="#{pc_Xrm01301.propBankCd.style}"
								maxlength="#{pc_Xrm01301.propBankCd.maxLength}"
								disabled="#{pc_Xrm01301.propBankCd.disabled}"
								onblur="return ajaxBankSitenCd();" size="4" tabindex="14">
							</h:inputText>
							<h:inputText styleClass="likeOutput" size="40"
								id="htmlBankName" tabindex="-1"
								value="#{pc_Xrm01301.propBnkName.stringValue}"
								style="#{pc_Xrm01301.propBnkName.style}">
							</h:inputText>
						</TD>
						<TD rowspan="4" colspan="1" width="280px" align="center">
							<hx:commandExButton
								type="submit" styleClass="commandExButton_search"
								id="popBankSearch" 
								onclick="return openPCog1101Window();"
								action="#{pc_Xrm01301.doPopBankSearchAction}" tabindex="6">
							</hx:commandExButton>
						</TD>
					</TR>
					<TR><!-- 取引銀行支店コード -->
						<TH align="left" nowrap class="v_g"><h:outputText
							styleClass="outputText" id="lblSitenCd"
							value="#{pc_Xrm01301.propSitenCd.labelName}"
							style="#{pc_Xrm01301.propSitenCd.labelStyle}">
						</h:outputText></TH>
						<TD align="left" colspan="1">
							<h:inputText styleClass="inputText"
								id="htmlSitenCd" value="#{pc_Xrm01301.propSitenCd.stringValue}"
								style="#{pc_Xrm01301.propSitenCd.style}"
								maxlength="#{pc_Xrm01301.propSitenCd.maxLength}"
								disabled="#{pc_Xrm01301.propSitenCd.disabled}"
								onblur="return ajaxBankSitenCd();" size="3" tabindex="14">
							</h:inputText>
							<h:outputText value=" ">
							</h:outputText>
							<h:inputText styleClass="likeOutput" size="40"
								id="htmlSitenName" tabindex="-1"
								value="#{pc_Xrm01301.propStnName.stringValue}"
								style="#{pc_Xrm01301.propStnName.style}">
							</h:inputText>
						</TD>
					</TR>
 					<TR><!-- 預金種目 -->
 						<TH align="left" nowrap class="v_c"> 
 							<h:outputText
								id="lblYokinItemCd" 
								style="#{pc_Xrm01301.propYokinItemCd.labelStyle}"
								value="#{pc_Xrm01301.propYokinItemCd.labelName}"
								styleClass="outputText">
							</h:outputText>
						</TH>
						<TD colspan="1" align="left">
							<h:selectOneMenu
								styleClass="selectOneMenu" id="htmlYokinItem"
								readonly="#{pc_Xrm01301.propYokinItemCd.readonly}"
								rendered="#{pc_Xrm01301.propYokinItemCd.rendered}"
								style="#{pc_Xrm01301.propYokinItemCd.style}"
								value="#{pc_Xrm01301.propYokinItemCd.stringValue}" tabindex="7">
								<f:selectItems value="#{pc_Xrm01301.propYokinItemCd.list}" />
							</h:selectOneMenu>
						</TD> 

					<TR><!-- 口座番号 -->
 						<TH align="left" nowrap class="v_d"><h:outputText
							styleClass="outputText" id="lblKouzaNo"
							value="#{pc_Xrm01301.propKouzaNo.labelName}"
							style="#{pc_Xrm01301.propKouzaNo.labelStyle}">
						</h:outputText></TH>
						<TD colspan="1" align="left"><h:inputText styleClass="inputText"
							id="htmlKouzaNo" value="#{pc_Xrm01301.propKouzaNo.stringValue}"
							style="#{pc_Xrm01301.propKouzaNo.style}"
							maxlength="#{pc_Xrm01301.propKouzaNo.maxLength}"
							disabled="#{pc_Xrm01301.propKouzaNo.disabled}" size="10"
							tabindex="16">
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Xrm01301.doRegisterAction}">
							</hx:commandExButton>
							<hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Xrm01301.doDeleteAction}">
							</hx:commandExButton>
							<hx:commandExButton
							type="submit" value="PDF作成" styleClass="commandExButton_out"
							id="pdfout" confirm="#{msg.SY_MSG_0019W}"
							action="#{pc_Xrm01301.doPdfoutAction}" tabindex="12">
							</hx:commandExButton>&nbsp;
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="htmlNyukinGaku=###,###,###;"
				id="htmlFormatNumberOption"></h:inputHidden>
				
			<h:inputHidden value="#{pc_Xrm01301.propActiveControl.integerValue}" 
				id="htmlActiveControl">
				<f:convertNumber />
			</h:inputHidden>
			
			<h:inputHidden value="#{pc_Xrm01301.nyugakuShohiFlg}"
				id="htmlNyugakuShohiFlg">
			</h:inputHidden>

			<h:inputHidden value="#{pc_Xrm01301.propErrorKbn.stringValue}"
				id="htmlHidErrorKbn"></h:inputHidden>
			
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>




