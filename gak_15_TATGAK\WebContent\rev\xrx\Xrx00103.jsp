<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00103.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>


<f:subview id="Xrx00103">
  <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00103.onPageLoadBegin}">
    <%-- ↓ コンテンツ部 ↓ --%>
    <hx:jspPanel>
	<DIV style="width:870px">
    
    <%-- 所属学科組織 --%> 
 	  <DIV align="right" style="width:100%;">
		  	<h:outputText  styleClass="outputText" id="htmlSzkGakkaSosik" value="#{pc_Xrx00103.propSzkGakkaSosik.stringValue}"/>
 	  </DIV>

 	  <DIV align="right" style="width:100%; margin-top:5px;">
		  <h:outputText  styleClass="outputText" id="sikakListCount" value="#{pc_Xrx00103.propSikakListCount.stringValue}"/>
 	  </DIV>

	<%-- 資格情報 --%>     
	<TABLE width="100%" cellspacing="0" cellpadding="0" border="0">
	<TR><TD>
		<DIV 
			onscroll="setScrollPosition('scroll', this)"
			style="height:auto;" 
			id="listScroll" 
			class="list">
					
				<h:dataTable 
						border="0" 
						cellpadding="2"
						cellspacing="0" 
						columnClasses="columnClass1"
						headerClass="headerClass" 
						footerClass="footerClass"
						rowClasses="#{pc_Xrx00103.propSikakList.rowClasses}"
						styleClass="meisai_scroll" 
						id="sikakList"
						value="#{pc_Xrx00103.propSikakList.list}" 
						var="varlist">

				<%-- 資格 --%>				
				<h:column id="colSikak">
					<f:facet name="header">
						<h:outputText 
							id="headSikak" 
							styleClass="outputText"
							value="資格">
						</h:outputText>
					</f:facet>
					<h:outputText 
						styleClass="outputText" 
						id="bodySikak"
						value="#{varlist.sikakName}">
					</h:outputText>
					<f:attribute name="width" value="50%"  />
					<f:attribute name="style" value="text-align: left"  />
				</h:column>
	
				<%-- 主副区分 --%>						
				<h:column id="colSyuFukKbn">
					<f:facet name="header">
							<h:outputText 
								styleClass="outputText" 
								value="主副区分"
								id="headSyuFukKbn">
							</h:outputText>
					</f:facet>
					<h:outputText 
							styleClass="outputText" 
							id="bodySyuFukKbn"
							value="#{varlist.syuFukName}">
					</h:outputText>
					<f:attribute name="width" value="50%"  />
					<f:attribute name="style" value="text-align: left"  />
				</h:column>
				
                <h:column id="colSelectSyuFukKbn">
					<hx:commandExButton 
					    type="submit" 
						styleClass="commandExButton"
						id="selectScore" 
						value="選択"
						action="#{pc_Xrx00103.doSelectSkk}">
                    </hx:commandExButton>	
					<f:facet name="header"></f:facet>
					<f:attribute name="width" value="10%"  />
					<f:attribute name="style" value="text-align: center"  />	
				</h:column>

				</h:dataTable>

			</DIV>
		</TD></TR>
	</TABLE>

	<BR>

    <%-- 資格詳細情報 --%>
<c:if test="${pc_Xrx00103.detailDisp}">
    <TABLE border="0" class="table" width="100%" cellspacing="0" cellpadding="0">
		<TBODY>

				<TR>
					 <%-- 資格希望年度 --%>
					<TH class="v_a" width="20%">
						<h:outputText 
							styleClass="outputText" 
							id="lblSikakKiboNendo"
							value="#{pc_Xrx00103.propSikakKiboNendo.labelName}"
							style="#{pc_Xrx00103.propSikakKiboNendo.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="25%">
						<h:outputText 
							styleClass="outputText"
							id="htmlSikakKiboNendo"
							value="#{pc_Xrx00103.propSikakKiboNendo.stringValue}"
							style="#{pc_Xrx00103.propSikakKiboNendo.style}">
						</h:outputText>
					</TD>

					 <%-- 資格コード --%>
					<TH class="v_a" width="20%">
						<h:outputText 
							styleClass="outputText" 
							id="lblSikakCd"
							value="#{pc_Xrx00103.propSikakCd.labelName}"
							style="#{pc_Xrx00103.propSikakCd.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="35%">
						<h:outputText 
							styleClass="outputText"
							id="htmlSikakCd"
							value="#{pc_Xrx00103.propSikakCd.stringValue}"
							style="#{pc_Xrx00103.propSikakCd.style}">
						</h:outputText>
					</TD>
				</TR>

				<TR>
					 <%-- 主副区分 --%>
					<TH class="v_a" width="20%">
						<h:outputText 
							styleClass="outputText" 
							id="lblSyuFukKbn"
							value="#{pc_Xrx00103.propSyuFukKbn.labelName}"
							style="#{pc_Xrx00103.propSyuFukKbn.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="25%">
						<h:outputText 
							styleClass="outputText"
							id="htmlSyuFukKbn"
							value="#{pc_Xrx00103.propSyuFukKbn.stringValue}"
							style="#{pc_Xrx00103.propSyuFukKbn.style}">
						</h:outputText>
					</TD>

					 <%-- 資格希望学期NO --%>
					<TH class="v_a" width="20%">
						<h:outputText 
						    styleClass="outputText" 
							id="lblSikakKiboGakkiNo"
							value="#{pc_Xrx00103.propSikakKiboGakkiNo.labelName}"
							style="#{pc_Xrx00103.propSikakKiboGakkiNo.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="35%">
						<h:outputText 
							styleClass="outputText"
							id="htmlSikakKiboGakkiNo"
							value="#{pc_Xrx00103.propSikakKiboGakkiNo.stringValue}"
							style="#{pc_Xrx00103.propSikakKiboGakkiNo.style}">
						</h:outputText>
					</TD>
				</TR>

				<TR>
					 <%-- 取得見込区分 --%>
					<TH class="v_a" width="20%">
						<h:outputText
							styleClass="outputText" 
							id="lblSyutokMkmKbn"
							value="#{pc_Xrx00103.propSyutokMkmKbn.labelName}"
							style="#{pc_Xrx00103.propSyutokMkmKbn.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="25%">
						<h:outputText styleClass="outputText"
							id="htmlSyutokMkmKbn"
							value="#{pc_Xrx00103.propSyutokMkmKbn.stringValue}"
							style="#{pc_Xrx00103.propSyutokMkmKbn.style}">
						</h:outputText>
					</TD>

					 <%-- 取得見込日 --%>
					<TH class="v_a" width="20%">
						<h:outputText
							styleClass="outputText" 
							id="lblSyutokMkmDate"
							value="#{pc_Xrx00103.propSyutokMkmDate.labelName}"
							style="#{pc_Xrx00103.propSyutokMkmDate.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="35%">
						<h:outputText 
							styleClass="outputText"
							id="htmlSyutokMkmDate"
							value="#{pc_Xrx00103.propSyutokMkmDate.stringValue}"
							style="#{pc_Xrx00103.propSyutokMkmDate.style}">
						</h:outputText>
					</TD>
				</TR>

				<TR>
					 <%-- 取得区分 --%>
					<TH class="v_a" width="20%">
						<h:outputText
							styleClass="outputText" 
							id="lblSyutokKbn"
							value="#{pc_Xrx00103.propSyutokKbn.labelName}"
							style="#{pc_Xrx00103.propSyutokKbn.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="25%">
						<h:outputText 
							styleClass="outputText"
							id="htmlSyutokKbn"
							value="#{pc_Xrx00103.propSyutokKbn.stringValue}"
							style="#{pc_Xrx00103.propSyutokKbn.style}">
						</h:outputText>
					</TD>

					 <%-- 取得日 --%>
					<TH class="v_a" width="20%">
						<h:outputText 
							styleClass="outputText" 
							id="lblSyutokDate"
							value="#{pc_Xrx00103.propSyutokDate.labelName}"
							style="#{pc_Xrx00103.propSyutokDate.labelStyle}">
						</h:outputText>
					</TH>
					<TD width="35%">
						<h:outputText 
							styleClass="outputText"
							id="htmlSyutokDate"
							value="#{pc_Xrx00103.propSyutokDate.stringValue}"
							style="#{pc_Xrx00103.propSyutokDate.style}">
						</h:outputText>
					</TD>
				</TR>					

			</TBODY>
		</TABLE>
</c:if>
        
      </DIV>
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
  </hx:scriptCollector>
</f:subview>