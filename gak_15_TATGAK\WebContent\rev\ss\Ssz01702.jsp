<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01702.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01702.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz01702.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz01702.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz01702.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz01702.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp"
				action="#{pc_Ssz01702.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="22%"></TD>
						<TD align="left"><TABLE border="0" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="text1" value="#{pc_Ssz01702.propFreTsyCd.name}"></h:outputText></TH>
						<TD width="330"><h:outputText styleClass="outputText"
							id="htmlFreTsyCd" value="#{pc_Ssz01702.propFreTsyCd.stringValue}"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="150"><h:outputText styleClass="outputText"
										id="text3" value="#{pc_Ssz01702.propFreTsyName.name}"
										style="#{pc_Ssz01702.propFreTsyName.style}"></h:outputText></TH>
						<TD width="330"><h:outputText styleClass="outputText"
							id="htmlFreTsyName"
							value="#{pc_Ssz01702.propFreTsyName.stringValue}"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			</TD>
						<TD width="18%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="72%"></TD>
						<TD align="left" width="260"><h:outputText styleClass="outputText" id="text4"
							style="font-size: 8pt; text-align: right; vertical-align: middle"
							value="#{pc_Ssz01702.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text2" style="font-size: 8pt; text-align: center; vertical-align: middle" value="件"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="22%"></TD>
						<TD width="480">
			<div class="listScroll" style="height:191px;width: 493px;" id="listScroll"  onscroll="setScrollPosition('scroll',this);">
			<h:dataTable border="0" cellpadding="2" cellspacing="0"
				headerClass="headerClass"
				footerClass="footerClass"
				rowClasses="#{pc_Ssz01702.propSscKifrKmk.rowClasses}"
				styleClass="meisai_scroll" id="table1"
				value="#{pc_Ssz01702.propSscKifrKmk.list}" var="varlist"
				width="476">
				<h:column id="column1">
					<f:facet name="header">
									<h:outputText id="lblListFreKomokNo" styleClass="outputText"
										value="ＮＯ"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListFreKomokNo"
									value="#{varlist.freKomokNo}"></h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: right; vertical-align: middle"
									name="style" />
							</h:column>
				<h:column id="column2">
					<f:facet name="header">
									<h:outputText styleClass="outputText" value="代表"
										id="lblListTitleFlg"></h:outputText>
								</f:facet>
					
					<f:attribute value="text-align: center; vertical-align: middle"
						name="style" >
					</f:attribute>
								<f:attribute value="70" name="width" />
								<h:outputText styleClass="outputText" id="htmlListTitleFlg"
									value="#{varlist.titleFlg}"></h:outputText>
							</h:column>
				<h:column id="column3">
					<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称"
										id="lblListFreKomokName"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" value="#{varlist.freKomokName}" id="htmlListFreKomokName"></h:outputText>
								<f:attribute value="280" name="width" />
							</h:column>
				<h:column id="column4">
					<f:facet name="header">
					</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz01702.doSelectAction}"></hx:commandExButton>
								<f:attribute value="24" name="width" />
								<f:attribute value="right" name="align" />
							</h:column>
			</h:dataTable></div></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE><BR>

						<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="22%"></TD>
						<TD align="left">
			<TABLE border="0" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
										id="lblFreKomokNo"
										value="#{pc_Ssz01702.propFreKomokNo.labelName}"
										style="#{pc_Ssz01702.propFreKomokNo.labelStyle}"></h:outputText></TH>
						<TD width="330"><h:inputText styleClass="inputText"
										id="htmlFreKomokNo"
										value="#{pc_Ssz01702.propFreKomokNo.stringValue}" size="3"
										style="#{pc_Ssz01702.propFreKomokNo.style}"
										maxlength="#{pc_Ssz01702.propFreKomokNo.maxLength}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="150"><h:outputText styleClass="outputText"
										id="lblFreKomokName"
										value="#{pc_Ssz01702.propFreKomokName.labelName}"
										style="#{pc_Ssz01702.propFreKomokName.labelStyle}"></h:outputText></TH>
						<TD width="330"><h:inputText styleClass="inputText"
										id="htmlFreKomokName"
										value="#{pc_Ssz01702.propFreKomokName.stringValue}" size="20"
										style="#{pc_Ssz01702.propFreKomokName.style}"
										maxlength="#{pc_Ssz01702.propFreKomokName.maxLength}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="150"><h:outputText styleClass="outputText" id="text11" style="#{pc_Ssz01702.propTitleFlg.labelStyle}" value="#{pc_Ssz01702.propTitleFlg.labelName}"></h:outputText></TH>
						<TD width="330"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlTitleFlg"
							value="#{pc_Ssz01702.propTitleFlg.checked}"></h:selectBooleanCheckbox></TD>
					</TR>
				</TBODY>
			</TABLE>
						</TD>
						<TD width="18%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz01702.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Ssz01702.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz01702.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
         </DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz01702.propSscKifrKmk.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

