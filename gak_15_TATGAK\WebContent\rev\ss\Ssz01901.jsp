<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz01901.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz01901.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz01901.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz01901.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="560" style="margin-left:-30px">
				<TBODY>
					<TR>
						<TD width="380"></TD>
						<TD width="380" align="right"><h:outputText
							styleClass="outputText" id="htmlCount"
							value="#{pc_Ssz01901.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text3" value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<table width="100%">
				<tr>
					<td width="18%"></td>
					<td width="561" colspan="2">
					<div id="listScroll" class="listScroll"
						onscroll="setScrollPosition('scroll',this);"
						style="height:296px;"><h:dataTable border="0"
						cellpadding="2" cellspacing="0" headerClass="headerClass"
						footerClass="footerClass"
						rowClasses="#{pc_Ssz01901.propFreTsyList.rowClasses}"
						styleClass="meisai_scroll" id="table1" width="542"
						value="#{pc_Ssz01901.propFreTsyList.list}" var="varlist"
						first="#{pc_Ssz01901.propFreTsyList.first}"
						rows="#{pc_Ssz01901.propFreTsyList.rows}">
						<h:column id="column1">
							<f:facet name="header">
								<h:outputText id="text1" styleClass="outputText" value="コード"></h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" id="text4"
								value="#{varlist.freTsyCd}"></h:outputText>
							<f:attribute value="140" name="width" />
						</h:column>
						<h:column id="column2">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="名称" id="text2"></h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" id="text5"
								value="#{varlist.freTsyName}"></h:outputText>
							<f:attribute value="380" name="width" />
						</h:column>
						<h:column id="column3">
							<f:facet name="header">
							</f:facet>
							<hx:commandExButton type="submit" value="選択"
								styleClass="commandExButton" id="select"
								action="#{pc_Ssz01901.doSelectAction}"></hx:commandExButton>
							<f:attribute value="24" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column4">
							<f:facet name="header">
							</f:facet>
							<hx:commandExButton type="submit" value="編集"
								styleClass="commandExButton" id="edit"
								action="#{pc_Ssz01901.doEditAction}"></hx:commandExButton>
							<f:attribute value="24" name="width" />
						</h:column>
					</h:dataTable></div>
					</td></tr>
			</table>
			<br>
			<table width="100%">
				<tr>
					<td width="18%"></td>
					<td width="560">
					<TABLE width="560" border="0" class="table">
						<TBODY>
							<TR>
								<TH class="v_a"><h:outputText styleClass="outputText"
									id="lblFreTsyCd" value="#{pc_Ssz01901.propFreTsyCd.labelName}"
									style="#{pc_Ssz01901.propFreTsyCd.labelStyle}"></h:outputText></TH>
								<TD width="378"><h:inputText styleClass="inputText"
									id="htmlFreTsyCd" style="#{pc_Ssz01901.propFreTsyCd.style}"
									maxlength="#{pc_Ssz01901.propFreTsyCd.maxLength}"
									value="#{pc_Ssz01901.propFreTsyCd.stringValue}" size="6"></h:inputText></TD>
							</TR>
							<TR>
								<TH class="v_b"><h:outputText styleClass="outputText"
									id="lblFreTsyName"
									style="#{pc_Ssz01901.propFreTsyName.labelStyle}"
									value="#{pc_Ssz01901.propFreTsyName.labelName}"></h:outputText></TH>
								<TD width="378"><h:inputText styleClass="inputText"
									id="htmlFreTsyName"
									maxlength="#{pc_Ssz01901.propFreTsyName.maxLength}"
									style="#{pc_Ssz01901.propFreTsyName.style}"
									value="#{pc_Ssz01901.propFreTsyName.stringValue}" size="32"></h:inputText></TD>
							</TR>
						</TBODY>
					</TABLE>
					</td>
					<td width="1"></td>
				</tr>
			</table>

			<DIV align="right"><br>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="0%"></TD>
						<TD align="right" width="100%" colspan="2">
						<TABLE class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD align="center" width="961"><hx:commandExButton type="submit" value="確定" styleClass="commandExButton_dat" id="register" action="#{pc_Ssz01901.doRegisterAction}" confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton type="submit" value="削除" styleClass="commandExButton_dat" id="delete" action="#{pc_Ssz01901.doDeleteAction}" confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton type="submit" value="クリア" styleClass="commandExButton_etc" id="clear" action="#{pc_Ssz01901.doClearAction}"></hx:commandExButton></TD>


								</TR>
							</TBODY>
						</TABLE>
						</TD></TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<h:inputHidden value="#{pc_Ssz01901.propFreTsyList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	<jsp:include page="../inc/footer.jsp" />
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

