<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm01401.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xrm01401.jsp</TITLE>

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<script language="JavaScript">
	function fncButtonActive(){
		var codeRegGakSearch = null;
		var codeRegUnselect = null;
		var codeRegExec = null;

		//選択(学籍検索用)ボタン
		codeRegGakSearch = document.getElementById('form1:htmlActiveControlGakSearch').value;
		if(codeRegGakSearch == 1){
			document.getElementById('form1:gakSearch').disabled = true;
			document.getElementById('form1:popGaksearch').disabled = true;
		}
		//解除ボタン
		codeRegUnselect = document.getElementById('form1:htmlActiveControlUnselect').value;
		if(codeRegUnselect == 1){
			document.getElementById('form1:unselect').disabled = true;
		}
		//確定ボタン
		codeRegExec = document.getElementById('form1:htmlActiveControlExec').value;
		if(codeRegExec == 1){
			document.getElementById('form1:regist').disabled = true;
		}

		//スクロール位置保持
		changeScrollPosition('scroll', 'listScroll');
		
		//Ajaxにて取得したの値を保持するためにjavascriptでreadonlyを設定する
		document.getElementById('form1:htmlName').readOnly = true;
		document.getElementById('form1:htmlGaknen').readOnly = true;
		//document.getElementById('form1:htmlSemester').readOnly = true;
		document.getElementById('form1:htmlSzks').readOnly = true;
		document.getElementById('form1:htmlIdo').readOnly = true;

	}
	//学費学生検索画面へ遷移
	function openPGhz0301Window() {
		openPGhz0301("<%=com.jast.gakuen.rev.gh.PGhz0301.getWindowOpenOption() %>");
		 return true;
	}

	//学籍情報取得Ajax呼び出し
	function ajaxGakusekiCd(thisObj) {
		//学籍コード
		var gakusekiCd = document.getElementById('form1:htmlGakusekiCd');
		//学生氏名項目id
		var nameId = "form1:htmlName";
		//学年項目id
		var gakunenId = "form1:htmlGaknen";
		//所属学科組織項目id
		var szkGakkaId = "form1:htmlSzks";
		//異動情報項目id
		var idoInfoId = "form1:htmlIdo";		

		//学籍情報取得Ajax呼び出し
		funcAjaxGakusekiCd(thisObj, "1", nameId);
		//学年取得Ajax呼び出し
		funcAjaxGakunenAjax(gakusekiCd, "1", gakunenId);
		//所属学科組織名称取得Ajax呼び出し
		funcAjaxSgksName(gakusekiCd, "1", szkGakkaId);
		//異動情報取得Ajax呼び出し
		funcAjaxIdoInfoAjax(gakusekiCd, "1", idoInfoId);
		
		return true;
	}
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('gakSearch');
	}
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}

</script>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="fncButtonActive();">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrm01401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrm01401.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrm01401.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrm01401.screenName}"></h:outputText>
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>

				<!--↓CONTENT↓-->
				<DIV class="head_button_area" >　
				</DIV>
				<DIV id="content">
					<DIV class="column" align="center">
						<TABLE width="900px">
							<TBODY>
								<TR>
									<TD width="100%">
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<TABLE border="0" width="100%" cellpadding="0" cellspacing="0"
															class="table">
															<TBODY>
																<TR>
																	<TH align="center" nowrap class="v_a" width="150px">
																		<h:outputText styleClass="outputText" id="lblGhYear"
																			value="#{pc_Xrm01401.propGhYear.labelName}"
																			style="#{pc_Xrm01401.propGhYear.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD valign="middle" width="145px">
																		<h:inputText styleClass="inputText" id="htmlGhYear"
																			size="4" value="#{pc_Xrm01401.propGhYear.dateValue}"
																			style="#{pc_Xrm01401.propGhYear.style}"
																			maxlength="#{pc_Xrm01401.propGhYear.maxLength}"
																			disabled="#{pc_Xrm01401.propGhYear.disabled}"
																			tabindex="1">
																			<hx:inputHelperAssist imeMode="inactive"
																				errorClass="inputText_Error" promptCharacter="_" />
																			<f:convertDateTime pattern="yyyy" />
																		</h:inputText>
																	</TD>
																	<TD rowspan="3" width="*">
																		<hx:commandExButton type="submit" value="選択"
																			styleClass="cmdBtn_dat_s" id="gakSearch"
																			action="#{pc_Xrm01401.doGakSearchAction}"
																			tabindex="4"
																			disabled="#{pc_Xrm01401.propActiveControlGakSearch.disabled}">
																		</hx:commandExButton>
																		<hx:commandExButton
																			type="submit" value="解除" styleClass="cmdBtn_etc_s"
																			id="unselect" action="#{pc_Xrm01401.doUnselectAction}"
																			disabled="#{pc_Xrm01401.propActiveControlUnselect.disabled}">
																		</hx:commandExButton>
																	</TD>
																</TR>
																<TR>
																	<TH width="150px" nowrap class="v_b">
																		<h:outputText styleClass="outputText"
																			id="lblGakusekiCd"
																			value="#{pc_Xrm01401.propGakusekiCd.labelName}"
																			style="#{pc_Xrm01401.propGakusekiCd.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD valign="middle" width="*">
																		<h:inputText
																			styleClass="inputText" id="htmlGakusekiCd"
																			style="#{pc_Xrm01401.propGakusekiCd.style}" size="10"
																			value="#{pc_Xrm01401.propGakusekiCd.value}"
																			maxlength="#{pc_Xrm01401.propGakusekiCd.maxLength}"
																			disabled="#{pc_Xrm01401.propGakusekiCd.disabled}"
																			tabindex="2"
																			onblur="return ajaxGakusekiCd(this);">
																		</h:inputText>
																		<hx:commandExButton type="submit"
																			styleClass="commandExButton_search" id="popGaksearch"
																			disabled="#{pc_Xrm01401.propActiveControlGakSearch.disabled}"
																			action="#{pc_Xrm01401.doPopGaksearchAction}"
																			tabindex="3"
																			onclick="return openPGhz0301Window();">
																		</hx:commandExButton>
																	</TD>
																</TR>

																<!-- 業務コード -->
																<TR>
																	<TH width="150px" nowrap class="v_b">
																		<h:outputText styleClass="outputText" id="propgyoumCD"
																			value="#{pc_Xrm01401.propgyoumCD.name}"
																			style="#{pc_Xrm01401.propgyoumCD.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD>
																	<h:selectOneMenu styleClass="selectOneMenu" id="propgyoumList"
																		style="width:250px;" value="#{pc_Xrm01401.propgyoumList.value}"
																		disabled="#{pc_Xrm01401.propgyoumList.disabled}"
																		tabindex="7">
																		<f:selectItems value="#{pc_Xrm01401.propgyoumList.list}" />
																	</h:selectOneMenu>
																	</TD>
																</TR>

															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD height="20px">
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE border="0" width="100%" cellpadding="0" cellspacing="0"
															class="table">
															<TBODY>
																<TR>
																	<TH align="center" nowrap class="v_c" width="150px">
																		<h:outputText styleClass="outputText" id="lblName"
																			value="#{pc_Xrm01401.propName.labelName}"
																			style="#{pc_Xrm01401.propName.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD nowrap width="*">
																		<h:inputText styleClass="likeOutput"
																			id="htmlName" style="#{pc_Xrm01401.propName.style}"
																			size="100"
																			value="#{pc_Xrm01401.propName.value}"
																			tabindex="-1">
																		</h:inputText>
																	</TD>
																</TR>
																<TR>
																	<TH align="center" nowrap class="v_d" width="150px">
																		<h:outputText styleClass="outputText" id="lblGaknen"
																			value="#{pc_Xrm01401.propGaknen.labelName}"
																			style="#{pc_Xrm01401.propGaknen.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD nowrap width="*">
																		<h:inputText styleClass="likeOutput" id="htmlGaknen"
																			size="1"
																			style="#{pc_Xrm01401.propGaknen.style}"
																			value="#{pc_Xrm01401.propGaknen.value}"
																			tabindex="-1">
																		</h:inputText>
																	</TD>
																</TR>
																<TR>
																	<TH align="center" nowrap class="v_f" width="150px">
																		<h:outputText styleClass="outputText" id="lblSzks"
																			value="#{pc_Xrm01401.propSzks.labelName}"
																			style="#{pc_Xrm01401.propSzks.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD nowrap width="*">
																		<h:inputText styleClass="likeOutput"
																			id="htmlSzks" style="#{pc_Xrm01401.propSzks.style}"
																			size="120"
																			value="#{pc_Xrm01401.propSzks.value}"
																			tabindex="-1">
																		</h:inputText>
																	</TD>
																</TR>
																<TR>
																	<TH align="center" nowrap class="v_g" width="150px">
																		<h:outputText styleClass="outputText" id="lblIdo"
																			value="#{pc_Xrm01401.propIdo.labelName}"
																			style="#{pc_Xrm01401.propIdo.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD nowrap width="*">
																		<h:inputText styleClass="likeOutput" id="htmlIdo"
																			style="#{pc_Xrm01401.propIdo.style}"
																			value="#{pc_Xrm01401.propIdo.value}"
																			size="100"
																			tabindex="-1">
																		</h:inputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>

												<%-- 振替状況一覧(上段)--%>
												<TR>
													<TD height="5px">
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="layoutTable">
															<TBODY>
																<TR>
																	<TD align="left">
																		<h:outputText styleClass="outputText" id="lblNowDate"
																			id="text3" value="振替状況">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD>
																		<DIV style="height:170px; width=100%;" id="listScroll" onscroll="setScrollPosition('scroll',this);" class="listScroll">
																			<h:dataTable
																				rows="#{pc_Xrm01401.propPayList.rows}"
																				rowClasses="#{pc_Xrm01401.propPayList.rowClasses}"
																				headerClass="headerClass"
																				footerClass="footerClass" styleClass="meisai_scroll" id="table1"
																				value="#{pc_Xrm01401.propPayList.list}" var="varlist" width="885px">
																				<h:column id="column2">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="納付金" id="text90">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text91">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text5" value="#{varlist.payCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="57px" name="width" />
																				</h:column>
																				<h:column id="column3">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="パターン" id="text92">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text93">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text7" value="#{varlist.patternCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="54px" name="width" />
																				</h:column>
																				<h:column id="column4">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="分納区分" id="text94">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text95">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text9" value="#{varlist.bunnoKbnCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="58px" name="width" />
																					<f:attribute value="text-align: center" name="style" />
																				</h:column>
																				<h:column id="column5">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propPayName.labelName}" id="lblPayName">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text11" value="#{varlist.payName}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="*" name="width" />
																				</h:column>
																				<h:column id="column6">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText" value="優先" id="text96">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText" value="順位" id="text97">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text13" value="#{varlist.nyukinPriority}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="46px" name="width" />
																					<f:attribute value="text-align: center" name="style" />
																				</h:column>
																				<h:column id="column7">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText" value="#{pc_Xrm01401.propItemGakuTotal.labelName}"
																								id="lblItemGakuTotal">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text15" value="#{varlist.itemTotal}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column8">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propMenjKingaku.labelName}"
																							id="lblMenjGakuTotal">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text17" value="#{varlist.menjKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column9">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propYoChosyuKingaku.labelName}"
																							id="lblYouCyoshuGaku">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text19" value="#{varlist.cyoushuKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column19">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propNofuZumiGaku.labelName}"
																							id="lblNoufuZumiGaku">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text21" value="#{varlist.nyuukinKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>

																				<h:column id="column20">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="ステータス" id="text22">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text23" value="#{varlist.payStatusKbn}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="70px" name="width" />
																					<f:attribute value="text-align: center" name="style" />
																				</h:column>

																				<h:column id="column1">
																					<f:facet name="header">
																					</f:facet>
																					<hx:commandExButton type="submit" value="選択"
																						styleClass="cmdBtn_dat_s" id="select"
																						rendered="#{varlist.rendered}"
																						action="#{pc_Xrm01401.doSelectAction}">
																					</hx:commandExButton>
																					<f:attribute value="30px" name="width" />
																				</h:column>
																			</h:dataTable>
																		</DIV>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>

												<%-- 現納付状況一覧(下段)--%>
												<TR>
													<TD height="5px">
													</TD>
												</TR>
												<TR>
													<TD>
														<TABLE width="100%" border="0" cellpadding="0" cellspacing="0" class="layoutTable">
															<TBODY>
																<TR>
																	<TD align="left">
																		<h:outputText styleClass="outputText"
																			id="text101" value="現納付状況">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD>
																		<DIV style="height:55px; width=100%;" id="listScroll" onscroll="setScrollPosition('scroll',this);" class="listScroll">
																			<h:dataTable
																				rows="#{pc_Xrm01401.propPayTsujoList.rows}"
																				rowClasses="#{pc_Xrm01401.propPayTsujoList.rowClasses}"
																				headerClass="headerClass"
																				footerClass="footerClass" styleClass="meisai_scroll" id="table2"
																				value="#{pc_Xrm01401.propPayTsujoList.list}" var="varTsujolist" width="885px">
																				<h:column id="column22">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="納付金" id="text190">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text191">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text105" value="#{varTsujolist.payCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="57px" name="width" />
																				</h:column>
																				<h:column id="column23">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="パターン" id="text192">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text193">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text107" value="#{varTsujolist.patternCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="54px" name="width" />
																				</h:column>
																				<h:column id="column24">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText"
																									value="分納区分" id="text194">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText"
																									value="コード" id="text195">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text109" value="#{varTsujolist.bunnoKbnCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="58px" name="width" />
																					<f:attribute value="text-align: center" name="style" />
																				</h:column>
																				<h:column id="column25">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propPayName.labelName}" id="lblTsujoPayName">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text111" value="#{varTsujolist.payName}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="*" name="width" />
																				</h:column>
																				<h:column id="column26">
																					<f:facet name="header">
																						<hx:jspPanel>
																							<center>
																								<h:outputText styleClass="outputText" value="優先" id="text196">
																								</h:outputText><br>
																								<h:outputText styleClass="outputText" value="順位" id="text197">
																								</h:outputText>
																							</center>
																						</hx:jspPanel>
																					</f:facet>
																					<h:outputText id="text113" value="#{varTsujolist.nyukinPriority}"
																						styleClass="outputText">
																					</h:outputText>
																						<f:attribute value="46px" name="width" />
																						<f:attribute value="text-align: center" name="style" />
																				</h:column>
																				<h:column id="column27">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText" value="#{pc_Xrm01401.propItemGakuTotal.labelName}"
																								id="lblTsujoItemGakuTotal">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text115" value="#{varTsujolist.itemTotal}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column28">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propMenjKingaku.labelName}"
																							id="lblTsujoMenjGakuTotal">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text117" value="#{varTsujolist.menjKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column29">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propYoChosyuKingaku.labelName}"
																							id="lblTsujoYouCyoshuGaku">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text119" value="#{varTsujolist.cyoushuKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				<h:column id="column30">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="#{pc_Xrm01401.propNofuZumiGaku.labelName}"
																							id="lblTsujoNoufuZumiGaku">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text121" value="#{varTsujolist.nyuukinKingaku}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="75px" name="width" />
																					<f:attribute value="text-align: right" name="style" />
																				</h:column>
																				
																				<h:column id="column31">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="ステータス" id="text122">
																						</h:outputText>
																					</f:facet>
																					<h:outputText id="text123" value="#{varTsujolist.payStatusKbn}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="70px" name="width" />
																					<f:attribute value="text-align: center" name="style" />
																				</h:column>

																				<%-- ダミー列 --%>
																				<h:column id="column32">
																					<f:facet name="header">
																						<h:outputText styleClass="outputText"
																							value="" id="text124">
																						</h:outputText>
																					</f:facet>
																					<f:attribute value="30px" name="width" />
																				</h:column>
																				
																			</h:dataTable>
																		</DIV>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>

												<TR>
													<TD>
														<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD>
																		<hx:commandExButton type="submit"
																			value="確定" styleClass="commandExButton_dat"
																			id="regist" action="#{pc_Xrm01401.doExecAction}"
																			disabled="#{pc_Xrm01401.propActiveControlExec.disabled}">
																		</hx:commandExButton>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			</DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrm01401.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm01401.propPayList.scrollPosition}" id="scroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm01401.propActiveControlUnselect.value}"
				id="htmlActiveControlUnselect">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm01401.propActiveControlExec.value}"
				id="htmlActiveControlExec">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm01401.propActiveControlGakSearch.value}"
				id="htmlActiveControlGakSearch">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
