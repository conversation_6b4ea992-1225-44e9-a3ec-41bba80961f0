<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	
 	//チェックボックスAll On
	function fncCheckAll(thisObj, thisEvent) {
		check('htmlList','htmlListCheck');
	}

	//チェックボックスAll Off
	function fncCheckNone(thisObj, thisEvent) {
		uncheck('htmlList','htmlListCheck');
	}
 	
 	// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}
	
	// 学生氏名を取得する
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh01101.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01101.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" >
						<TBODY>
							<TR>
							<TD>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblNendo"
												value="#{pc_Xrh01101.propNendo.labelName}" 
												style="#{pc_Xrh01101.propNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="140">
											<h:inputText id="htmlNendo" styleClass="inputText" 
												readonly="#{pc_Xrh01101.propNendo.readonly}" 
												value="#{pc_Xrh01101.propNendo.dateValue}"
												disabled="#{pc_Xrh01101.propNendo.disabled}" 
												size="4" tabindex="1">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblKaisu"
												value="#{pc_Xrh01101.propKamokSikenCnt.labelName}"
												style="#{pc_Xrh01101.propKamokSikenCnt.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="120">
											<h:inputText id="htmlKamokSikenCnt" styleClass="inputText" 
												readonly="#{pc_Xrh01101.propKamokSikenCnt.readonly}" 
												value="#{pc_Xrh01101.propKamokSikenCnt.integerValue}"
												disabled="#{pc_Xrh01101.propKamokSikenCnt.disabled}" 
													size="4" tabindex="2">
												<hx:inputHelperAssist errorClass="inputText_Error"
								    			imeMode="inactive" promptCharacter="_" />
												<f:convertNumber type="number" pattern="#0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
						            	<TH nowrap class="v_a" width="150">
											<!--学籍番号 -->
						                	<h:outputText 
						                		styleClass="outputText" 
						                		id="lblGakusekiCd"
						                		value="#{pc_Xrh01101.propGakusekiCd.labelName}"
						                		style="#{pc_Xrh01101.propGakusekiCd.labelStyle}">
						                	</h:outputText>
						              	</TH>
						              	<TD colspan="3">
											<h:inputText id="htmlGakusekiCd" styleClass="inputText" 
												readonly="#{pc_Xrh01101.propGakusekiCd.readonly}" 
												disabled="#{pc_Xrh01101.propGakusekiCd.disabled}"
												maxlength="#{pc_Xrh01101.propGakusekiCd.maxLength}"
												size="10"
												tabindex="3"
												value="#{pc_Xrh01101.propGakusekiCd.stringValue}"
												onblur="return doGakuseiAjax(this, event, 'form1:lblName');">
												<hx:inputHelperAssist imeMode="disabled"
													errorClass="inputText_Error" />
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="searchGakuseki"
												disabled="#{pc_Xrh01101.propSearchGakuseki.disabled}"
												rendered="#{pc_Xrh01101.propSearchGakuseki.rendered}"
												style="#{pc_Xrh01101.propSearchGakuseki.style}"
												onclick="openSubWindow('form1:htmlGakusekiCd');" tabindex="4">
											</hx:commandExButton>
											<h:outputText
		              							styleClass="outputText"
		              							id="lblName"
		              							value="#{pc_Xrh01101.propName.stringValue}">
		              						</h:outputText>
		              					</TD>
		              				</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblSikenbiYobi"
												value="#{pc_Xrh01101.propSikenbiYobi.labelName}" 
												style="#{pc_Xrh01101.propSikenbiYobi.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:selectOneMenu 
												styleClass="selectOneMenu" 
												id="htmlSikenbiYobi"
												value="#{pc_Xrh01101.propSikenbiYobi.stringValue}"
												style="#{pc_Xrh01101.propSikenbiYobi.style};width:160px"
												disabled="#{pc_Xrh01101.propSikenbiYobi.disabled}" 
												tabindex="5">
												<f:selectItems 
													value="#{pc_Xrh01101.propSikenbiYobi.list}" />
											</h:selectOneMenu>
										</TD>
									 </TR>
					 									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="出力対象">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
												id="htmlMisyuturyok"
												disabled="#{pc_Xrh01101.propMisyuturyok.disabled}"
												style="#{pc_Xrh01101.propMisyuturyok.style}"
												value="#{pc_Xrh01101.propMisyuturyok.checked}" tabindex="7">
											</h:selectBooleanCheckbox>未出力
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
												id="htmlSyuturyokzumi"
												disabled="#{pc_Xrh01101.propSyuturyokzumi.disabled}"
												style="#{pc_Xrh01101.propSyuturyokzumi.style}"
												value="#{pc_Xrh01101.propSyuturyokzumi.checked}" tabindex="8">
											</h:selectBooleanCheckbox>出力済
									    </TD>
									</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="ソート順">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:selectOneRadio
								              	disabledClass="selectOneRadio_Disabled"
								              	styleClass="selectOneRadio" id="htmlSort"
								              	value="#{pc_Xrh01101.propSort.stringValue}"
								              	style="#{pc_Xrh01101.propSort.style}"
								              	disabled="#{pc_Xrh01101.propSort.disabled}" tabindex="9">
								              	<f:selectItem itemValue="0" itemLabel="学籍番号" />
								              	<f:selectItem itemValue="1" itemLabel="学生氏名" />
									        </h:selectOneRadio>
									     </TD>
									</TR>
								</TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE width="700" border="0" cellpadding="0" cellspacing="0" 
									class="button_bar">
									<TBODY>
										<TR>
											<TD>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="select"
													value="検索"
													action="#{pc_Xrh01101.doSearchAction}"
													disabled="#{pc_Xrh01101.propSearch.disabled}"
													rendered="#{pc_Xrh01101.propSearch.rendered}"
													style="#{pc_Xrh01101.propSearch.style}" tabindex="10">
												</hx:commandExButton>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="clear"
													value="クリア"
													action="#{pc_Xrh01101.doClearAction}"
													disabled="#{pc_Xrh01101.propClear.disabled}"
													rendered="#{pc_Xrh01101.propClear.rendered}"
													style="#{pc_Xrh01101.propClear.style}" tabindex="11">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								
								
								<TABLE border="0">
								<TBODY>
									<TR>
										<TD align="right">
											<h:outputText styleClass="outputText"
												id="lblListCnt"
												value="#{pc_Xrh01101.propListCnt.stringValue}"
												style="#{pc_Xrh01101.propListCnt.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
									<TD>
									<DIV id="listScroll" class="listScroll" style="height: 256px;">
										<h:dataTable 
											columnClasses="columnClass" 
											headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Xrh01101.propList.rowClasses}"
											styleClass="meisai_scroll" id="htmlList"
											value="#{pc_Xrh01101.propList.list}" var="varlist">
											
											<h:column id="column1">
												<f:facet name="header">
												</f:facet>
												<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlListCheck"
													disabled="#{pc_Xrh01101.propListCheck.disabled}"
													readonly="#{pc_Xrh01101.propListCheck.readonly}"
													rendered="#{pc_Xrh01101.propListCheck.rendered}"
													style="#{pc_Xrh01101.propListCheck.style}"
													value="#{varlist.checked}" tabindex="12">
												</h:selectBooleanCheckbox>
												<f:attribute value="32" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="学籍番号"
														id="lblListGakusekiCd">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListGakusekiCd"
													style="#{pc_Xrh01101.propListGakusekiCd.style}"
													value="#{varlist.gakusekiCd.stringValue}">
												</h:outputText>
												<f:attribute value="128" name="width" />
												<f:attribute value="text-align: left" name="style" />
											</h:column>
											
											<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="氏名"
														id="lblListName">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListName"
													style="#{pc_Xrh01101.propListName.style}"
													value="#{varlist.name.displayValue}"
													title="#{varlist.name.stringValue}">
												</h:outputText>
												<f:attribute value="160" name="width" />
												<f:attribute value="text-align: left" name="style" />
											</h:column>
											
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="試験日曜日"
														id="lblListSikenbiYobi">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListSikenbiYobi"
													style="#{pc_Xrh01101.propListName.style}"
													value="#{varlist.sikenbiName.stringValue}">
												</h:outputText>
												<f:attribute value="80" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column5">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="許可証発行日"
														id="lblListKyokasyoHakkoDate">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListKyokasyoHakkoDate"
													style="#{pc_Xrh01101.propListName.style}"
													value="#{varlist.kyokasyoHakkoDate.stringValue}">
												</h:outputText>
												<f:attribute value="160" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
										</h:dataTable>
									</DIV>
									</TD>
									</TR>
									<TR>
									<TD align="left">
										<hx:commandExButton type="button"
									 		styleClass="check" id="allCheck"
									 		disabled="#{pc_Xrh01101.propAllCheck.disabled}"
											onclick="return fncCheckAll(this, event);" tabindex="13">
										</hx:commandExButton> 
										<hx:commandExButton
											type="button" styleClass="uncheck" id="allUnCheck"
											disabled="#{pc_Xrh01101.propAllUnCheck.disabled}"
											onclick="return fncCheckNone(this, event);" tabindex="14">
										</hx:commandExButton>
									</TD>
									</TR>
								</TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE  width="581"  cellpadding="0" cellspacing="0" class="table" >
								  <TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="#{pc_Xrh01101.propKyokasyoHakkoDate.labelName}" 
												style="#{pc_Xrh01101.propKyokasyoHakkoDate.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlpropKyokasyoHakkoDate"
												styleClass="inputText" size="12"
												value="#{pc_Xrh01101.propKyokasyoHakkoDate.dateValue}">
												<f:convertDateTime />
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText>（未設定時はシステム日付）
										</TD>
									</TR>
								  </TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE width="700" border="0" cellpadding="0" cellspacing="0" 
									class="button_bar">
									<TBODY>
										<TR>
											<TD>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="pdfOut"
													value="PDF作成"
													action="#{pc_Xrh01101.doPdfOutAction}"
													disabled="#{pc_Xrh01101.propPdfOut.disabled}"
													rendered="#{pc_Xrh01101.propPdfOut.rendered}"
													style="#{pc_Xrh01101.propPdfOut.style}" tabindex="15">
												</hx:commandExButton>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="csvOut"
													value="CSV作成"
													action="#{pc_Xrh01101.doCsvOutAction}"
													disabled="#{pc_Xrh01101.propCsvOut.disabled}"
													rendered="#{pc_Xrh01101.propCsvOut.rendered}"
													style="#{pc_Xrh01101.propCsvOut.style}" tabindex="16">
												</hx:commandExButton>
												<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="setOutput" value="出力項目指定"
										action="#{pc_Xrh01101.doSetOutputAction}"
										disabled="#{pc_Xrh01101.propSetOutput.disabled}"
										rendered="#{pc_Xrh01101.propSetOutput.rendered}"
										style="#{pc_Xrh01101.propSetOutput.style}" tabindex="17">
									</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
							</TR>
						</TBODY>
						</TABLE>
						
				</DIV>
			</DIV>
			
		</DIV>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
				
