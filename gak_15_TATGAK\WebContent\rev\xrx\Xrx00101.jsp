<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00101.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<style type="text/css">
<!--
.tableFontSize th {
  font-size:13px;
}
.tableFontSize td {
  font-size:13px;
}
 -->
</style>
<f:subview id="Xrx00101">
 
 <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00101.onPageLoadBegin}">

<%-- ↓ コンテンツ部 ↓ --%>
<hx:jspPanel>
<DIV style="width:870px">

<%-- 学籍基本情報 --%>
<c:if test="${pc_Xrx00101.bean !=  null}">
	<TABLE border="0" class="table tableFontSize" width="100%" cellspacing="0" cellpadding="0">
		<TBODY>
			<TR>
			    <%-- 学籍番号 --%>
			    <TH class="v_a" width="20%"><span>学籍番号</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gaksekiCd}"/></span></TD>
			    <%-- 受付番号番号 --%>
			    <TH class="v_a" width="20%"><span>受付番号</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.uketukeNo}"/></span></TD>
			</TR>
			<TR>
			    <%-- 旧学籍番号1 --%>
			    <TH class="v_a" width="20%"><span>旧学籍番号1</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakNoOld}"/></span></TD>
			    <%-- 旧学籍番号2 --%>
			    <TH class="v_a" width="20%"><span>旧学籍番号2</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakNoOld2}"/></span></TD>
			</TR>
			<TR>
			    <%-- 旧学籍番号3 --%>
			    <TH class="v_a" width="20%"><span>旧学籍番号3</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakNoOld3}"/></span></TD>
			    <%-- 旧学籍番号4 --%>
			    <TH class="v_a" width="20%"><span>旧学籍番号4 </span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakNoOld4}"/></span></TD>
			</TR>
			<TR>
			    <%-- 学生氏名(漢字) --%>
			    <TH class="v_a" width="20%"><span>学生氏名(漢字) </span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.name}"/></span></TD>
			    <%-- 学生氏名(カナ) --%>
			    <TH class="v_a" width="20%"><span>学生氏名(カナ)</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.nameKana}"/></span></TD>
			</TR>
			<TR>
			    <%-- 学生氏名(英語) --%>
			    <TH class="v_a" width="20%"><span>学生氏名(英語) </span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.nameEng}"/></span></TD>
			</TR>
			<TR>
			    <%-- 就学種別 --%>
			    <TH class="v_a" width="20%"><span>就学種別 </span></TH>
			    <TD width="30%">
					<span>
					    <c:if test="${pc_Xrx00101.bean.syugakSbt != '' && pc_Xrx00101.bean.syugakSbtName != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.syugakSbt}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.syugakSbtName}" />
					        </h:outputFormat>
					    </c:if>
					</span>
				</TD>			    
			    <%-- 入学種別 --%>
			    <TH class="v_a" width="20%"><span>入学種別</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.nyugakSbt != '' && pc_Xrx00101.bean.nyugakSbtName != '' }">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.nyugakSbt}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.nyugakSbtName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
			</TR>
			<TR>
			    <%-- 入学年度 --%>
			    <TH class="v_a" width="20%"><span>入学年度</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.nyugakNendo}"/></span></TD>
			    <%-- 入学学期ＮＯ --%>
			    <TH class="v_a" width="20%"><span>入学学期ＮＯ</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakkiName}"/></span></TD>
			</TR>
			<TR>
			    <%-- 入学年次 --%>
			    <TH class="v_a" width="20%"><span>入学年次</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.nyugakNenjiName}"/></span></TD>
			</TR>
			<TR>
			    <%-- みなし入学年度 --%>
			    <TH class="v_a" width="20%"><span>みなし入学年度</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.nyugakNendoCur}"/></span></TD>
			    <%-- みなし入学学期ＮＯ --%>
			    <TH class="v_a" width="20%"><span>みなし入学学期ＮＯ</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.nyugakGakkiNameCur}"/></span></TD>
			</TR>
			<TR>
			    <%-- 所属学科組織 --%>
			    <TH class="v_a" width="20%"><span>所属学科組織</span></TH>
			    <TD width="30%">
                    <span>
			            <c:if test="${pc_Xrx00101.bean.szkGakka != ''  && pc_Xrx00101.bean.szkGakkaName != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.szkGakka}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.szkGakkaName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
                </TD>
			    <%-- カリキュラム学科組織 --%>
			    <TH class="v_a" width="20%"><span>カリキュラム学科組織</span></TH>
			    <TD width="30%">
                    <span>
			            <c:if test="${pc_Xrx00101.bean.curGakka != ''  && pc_Xrx00101.bean.curGakkaName !=''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.curGakka}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.curGakkaName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
                </TD>
			</TR>
			<TR>
			    <%-- 入学許可 --%>
			    <TH class="v_a" width="20%"><span>入学許可</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.nyugakKyok != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.nyugakKyok}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.nyugakKyokName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
			   </TD>
			    <%-- 学年 --%>
			    <TH class="v_a" width="20%"><span>学年</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakunen}"/></span></TD>
			</TR>
			<TR>
			    <%-- 生年月日 --%>
			    <TH class="v_a" width="20%"><span>生年月日</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.birth}"/></span></TD>
			    <%-- 性別 --%>
			    <TH class="v_a" width="20%"><span>性別</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.seibetuName != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.seibetu}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.seibetuName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
			</TD>
			</TR>
			<TR>
			    <%-- 郵便番号 --%>
			    <TH class="v_a" width="20%"><span>郵便番号</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.addrCd}"/></span></TD>
			    <%-- 都道府県 --%>
			    <TH class="v_a" width="20%"><span>都道府県</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.syussin != '' && pc_Xrx00101.bean.syussinName != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.syussin}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.syussinName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
			</TR>
			<TR>
			    <%-- 住所1 --%>
			    <TH class="v_a" width="20%"><span>住所1</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.addr1}"/></span></TD>
			</TR>
			<TR>
			    <%-- 住所2 --%>
			    <TH class="v_a" width="20%"><span>住所2</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.addr2}"/></span></TD>
			</TR>
			<TR>
			    <%-- 住所3 --%>
			    <TH class="v_a" width="20%"><span>住所3</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.addr3}"/></span></TD>
			</TR>
			<TR>
			    <%-- 電話番号1 --%>
			    <TH class="v_a" width="20%"><span>電話番号1</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.tel1}"/></span></TD>
			    <%-- 電話番号2 --%>
			    <TH class="v_a" width="20%"><span>電話番号2</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.tel2}"/></span></TD>
			</TR>
			<TR>
			    <%-- 携帯電話番号 --%>
			    <TH class="v_a" width="20%"><span>携帯電話番号</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.keitaiTel}"/></span></TD>
			    <%-- FAX番号 --%>
			    <TH class="v_a" width="20%"><span>FAX番号</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.fax}"/></span></TD>
			</TR>
			<TR>
			    <%-- 職業区分 --%>
			    <TH class="v_a" width="20%"><span>職業区分</span></TH>
			    <TD colspan="3">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.syokugyoKbn != '' && pc_Xrx00101.bean.syokugyoKbnName != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.syokugyoKbn}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.syokugyoKbnName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>			    
			</TR>
			<TR>
			    <%-- 勤務先名 --%>
			    <TH class="v_a" width="20%"><span>勤務先名</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.kinmusakiName}"/></span></TD>
			    <%-- 勤務先電話番号 --%>
			    <TH class="v_a" width="20%"><span>勤務先電話番号</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.kinmusakiTel}"/></span></TD>
			</TR>
			<TR>
			    <%-- 本籍地 --%>
			    <TH class="v_a" width="20%"><span>本籍地</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.honsekiName}"/></span></TD>
			</TR>
			<TR>
			    <%-- 出身校入学年月 --%>
			    <TH class="v_a" width="20%"><span>出身校入学年月</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.syussinkoNyugakYm}"/></span></TD>
			    <%-- 出身校卒業年月 --%>
			    <TH class="v_a" width="20%"><span>出身校卒業年月</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.syussinkoSottaiYm}"/></span></TD>
			</TR>
			<TR>
			    <%-- 出身校卒退区分 --%>
			    <TH class="v_a" width="20%"><span>出身校卒退区分</span></TH>
			    <TD colspan="3">
			        <span>
				        <c:if test="${pc_Xrx00101.bean.kykInkaiSyodak != '' && pc_Xrx00101.bean.syussinkoSottaiKbnName != ''}">
						        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
						          <f:param name="code" value="#{pc_Xrx00101.bean.syussinkoSottaiKbn}" />
						          <f:param name="name" value="#{pc_Xrx00101.bean.syussinkoSottaiKbnName}" />
						        </h:outputFormat>
				        </c:if>
			        </span>
			    </TD>
			</TR>
			<TR>
			    <%-- 出身校種別 --%>
			    <TH class="v_a" width="20%"><span> 出身校種別</span></TH>
			    <TD width="30%">
			        <span>
				        <c:if test="${pc_Xrx00101.bean.ssnSbt != '' && pc_Xrx00101.bean.ssnSbtName != ''}">
						        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
						          <f:param name="code" value="#{pc_Xrx00101.bean.ssnSbt}" />
						          <f:param name="name" value="#{pc_Xrx00101.bean.ssnSbtName}" />
						        </h:outputFormat>
				        </c:if>
			        </span>
			    </TD>
			    <%-- 出身校 --%>
			    <TH class="v_a" width="20%"><span>出身校</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.ssnName}"/></span></TD>
			</TR>
			<TR>
				<%-- 2年在学の為の既存学期間 --%>
			    <TH class="v_a" width="20%"><span>2年在学の為の<br> &nbsp; 既存学期間</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.ninenZaigakKikan}"/></span></TD>
			</TR>
			<TR>
				<%-- 2年在学資格の有無 --%>
			    <TH class="v_a" width="20%"><span>2年在学資格の有無</span></TH>
			    <TD width="30%">
			        <span>
		                <c:if test="${pc_Xrx00101.bean.niZaigakSikakUmu != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}">
					            <f:param name="name" value="#{pc_Xrx00101.bean.niZaigakSikakUmuName}" />
					        </h:outputFormat>
			            </c:if>
			        </span>
			    </TD>
			    <%-- 2年以上在学資格取得日 --%>
			    <TH class="v_a" width="20%"><span>2年以上在学資格取得日</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.niZaiSikakStkDate}"/></span></TD>
			</TR>
			<TR>
			    <%-- 合格証発行日 --%>
			    <TH class="v_a" width="20%"><span>合格証発行日</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gsyoHakkoDate}"/></span></TD>
			    <%-- 学生証発行日 --%>
			    <TH class="v_a" width="20%"><span>学生証発行日</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakseiSyoHakkoDate}"/></span></TD>
			</TR>
			<TR>
			    <%-- 学生証シール発行年度 --%>
			    <TH class="v_a" width="20%"><span>学生証シール発行年度</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakseaLHakkoNendo}"/></span></TD>
			    <%-- 学生証シール発行日 --%>
			    <TH class="v_a" width="20%"><span>学生証シール発行日</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakseaLHakkoDate}"/></span></TD>
			</TR>
			<TR>
			    <%-- 身分区分 --%>
			    <TH class="v_a" width="20%"><span>身分区分</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.mibunKbn != '' && pc_Xrx00101.bean.mibunKbnName != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.mibunKbn}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.mibunKbnName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
			    </TD>
			    <%-- 玉通発送年月号 --%>
			    <TH class="v_a" width="20%"><span>玉通発送年月号</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.tamatuNengetu}"/></span></TD>
			</TR>
			<TR>
			    <%-- 振込依頼人コード --%>
			    <TH class="v_a" width="20%"><span>振込依頼人コード</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.furikomiIraiCd}"/></span></TD>
			    <%-- 一括単位認定除外 --%>
			    <TH class="v_a" width="20%"><span>一括単位認定除外</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.ikkatuNinteiGai != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.ikkatuNinteiGai}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.ikkatuNinteiGaiName}" />
					        </h:outputFormat>
			            </c:if>
			       </span>
			    </TD>
			</TR>
			<TR>
			    <%-- 免許取得方法 --%>
			    <TH class="v_a" width="20%"><span>免許取得方法</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.menkyoSyutokHohoName}"/></span>
			</TR>
			<TR>
			    <%-- 学位記番号 --%>
			    <TH class="v_a" width="20%"><span>学位記番号</span></TH>
			    <TD colspan="3"><span><c:out value="${pc_Xrx00101.bean.gakuikiNo}"/></span></TD>
			</TR>
			<TR>
			    <%-- 学費期限年度 --%>
			    <TH class="v_a" width="20%"><span>学費期限年度</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.gakuhiKigenNendo != '0'}">
			                <c:out value="${pc_Xrx00101.bean.gakuhiKigenNendo}"/>
			            </c:if>
			        </span>
			    </TD>
			    <%-- 学費期限学期NO --%>
			    <TH class="v_a" width="20%"><span>学費期限学期NO</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.gakuhiKigenGakkiName}"/></span></TD>
			</TR>
			<TR>
			    <%-- 学費免除 --%>
			    <TH class="v_a" width="20%"><span>学費免除</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.ikkatuNinteiGai != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.gakuhiMenjyo}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.gakuhiMenjyoName}" />
					        </h:outputFormat>
			            </c:if>
			        </span>
			    </TD>
			    <%-- 選考結果日 --%>
			    <TH class="v_a" width="20%"><span>選考結果日</span></TH>
			    <TD width="30%"><span><c:out value="${pc_Xrx00101.bean.senkokekkaDate}"/></span></TD>
			</TR>
			<TR>
			    <%-- 人物問題有 --%>
			    <TH class="v_a" width="20%"><span>人物問題有</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.mondaiAri != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					          <f:param name="code" value="#{pc_Xrx00101.bean.mondaiAri}" />
					          <f:param name="name" value="#{pc_Xrx00101.bean.mondaiAriName}" />
					        </h:outputFormat>
			            </c:if>
			        </span>
			    </TD>
			    <%-- WEB履修希望しない --%>
			    <TH class="v_a" width="20%"><span>WEB履修希望しない</span></TH>
			    <TD width="30%">
			        <span>
		                <c:if test="${pc_Xrx00101.bean.webRsyuKiboShinaiFlg != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					            <f:param name="code" value="#{pc_Xrx00101.bean.webRsyuKiboShinaiFlg}" />
					            <f:param name="name" value="#{pc_Xrx00101.bean.webRsyuKiboShinaiFlgName}" />
					        </h:outputFormat>
			            </c:if>
			        </span>
			    </TD>
			</TR>		
			<TR>
			    <%-- 教育実習辞退 --%>
			    <TH class="v_a" width="20%"><span>教育実習辞退</span></TH>
			    <TD width="30%">
			        <span>
		                <c:if test="${pc_Xrx00101.bean.kyoikuJisshuJitai != ''}">
					        <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
					            <f:param name="code" value="#{pc_Xrx00101.bean.kyoikuJisshuJitai}" />
					            <f:param name="name" value="#{pc_Xrx00101.bean.kyoikuJisshuJitaiName}" />
					        </h:outputFormat>
			            </c:if>
			        </span>
			    </TD>
			    <%-- 教育委員会承諾--%>
			    <TH class="v_a" width="20%"><span>教育委員会承諾</span></TH>
			    <TD width="30%">
			        <span>
			            <c:if test="${pc_Xrx00101.bean.kykInkaiSyodak != ''}">
						    <h:outputFormat styleClass="outputFormat" value="{0}：{1}">
						        <f:param name="code" value="#{pc_Xrx00101.bean.kykInkaiSyodak}" />
						        <f:param name="name" value="#{pc_Xrx00101.bean.kykInkaiSyodakName}" />
						    </h:outputFormat>
				        </c:if>
			        </span>
			    </TD>
			</TR>
			<TR>
			    <%-- メモ--%>
			    <TH class="v_a" width="20%" rowspan="5"><span>メモ</span></TH>
			    <TD colspan="3" style="vertical-align:top; height:auto;">
			        <span><textarea rows="2" cols="83" class="inputTextare" readonly><c:out value="${pc_Xrx00101.bean.memo}"/></textarea></span>
			    </TD>
			</TR>	
		</TBODY>
	</TABLE>
</c:if>
</DIV>
</hx:jspPanel>
<BR>

<%-- ↑ コンテンツ部 ↑ --%>

</hx:scriptCollector>
</f:subview>