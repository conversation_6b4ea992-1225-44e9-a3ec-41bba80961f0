<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx04101.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>

<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xrx04101.jsp</TITLE>

<SCRIPT type="text/javascript">
	function init(){
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:htmlGakuseiNm');
		getSinseiCb();
		changeScrollPosition('scroll','listScroll');
	}
	function doGakuseiAjax(thisObj, thisEven, targetLabel){
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;
	
	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	function getSinseiCb() {
	// 新規登録申請名称コンボボックス取得AJAX
	var servlet = "rev/xrx/XrxSinseiAJAX";
	var args = new Array();
	args['sinseibi'] = document.getElementById('form1:htmlSinseiDate').value;
	var target = "";
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
	}

	function callBackMethod(value){

		var comb = document.getElementById('form1:htmlSsiNameList');
		var length = value['length'];
		comb.length = length;
	
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];

		if(i == 0){
			comb.options[i].selected = true;
			}
		}
	}
	
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;
	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}
	function confirmOk() {

		var val = document.getElementById('form1:htmlExecute').value;
		var target;
		if(val === '2'){
			target = "update";
		} else if(val === '3'){
			target = "update";
		}		
		document.getElementById('form1:htmlExecute').value = "1";
		
		indirectClick(target);
	}
	function confirmCancel(){
		return true;
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="init();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx04101.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Xrx04101">
<h:form styleClass="form" id="form1">
<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />
<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
  value="閉じる" styleClass="commandExButton" id="closeDisp"
  action="#{pc_Xrx04101.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrx04101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrx04101.screenName}"></h:outputText>
</div>
<!--↓outer↓-->
<DIV class="outer">
<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
  id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText></FIELDSET>
<!--↓content↓-->
<DIV class="head_button_area">
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<h:inputHidden
	value="#{pc_Xrx04101.propExecute.integerValue}"
	id="htmlExecute">
	<f:convertNumber/>
</h:inputHidden>
<DIV id="content">
<DIV class="column" align="center">
<TABLE border="0" cellpadding="5">
	<TBODY>
		<TR>
			<TD width="870">
			<TABLE class="table" width="100%">
				<TBODY>
				<TR>
					<TH nowrap class="v_a"  width="150">
						<h:outputText styleClass="outputText" id="lblSinseiDate"
                  			value="#{pc_Xrx04101.propSinseiDate.labelName}"
                  			style="#{pc_Xrx04101.propSinseiDate.labelStyle}"></h:outputText>
					</TH>
					<TD width="285">
						<h:inputText styleClass="inputText"
							id="htmlSinseiDate" size="12"
							onblur="getSinseiCb();"
							value="#{pc_Xrx04101.propSinseiDate.dateValue}">
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
							<hx:inputHelperAssist errorClass="inputText_Error"promptCharacter="_" />
						</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText" id="lblSsiName"
                  				value="#{pc_Xrx04101.propSsiNameList.labelName}"
                  				style="#{pc_Xrx04101.propSsiNameList.labelStyle}"></h:outputText>
						</TH>
						<TD width="720">
							<h:selectOneMenu
                    			styleClass="selectOneMenu" id="htmlSsiNameList"
                    			value="#{pc_Xrx04101.propSsiNameList.value}"
                    			disabled="#{pc_Xrx04101.propSsiNameList.disabled}"
                    			style="#{pc_Xrx04101.propSsiNameList.style} width:400px;">
                    			<f:selectItems value="#{pc_Xrx04101.propSsiNameList.list}" />
                  			</h:selectOneMenu>
                  			<hx:commandExButton type="submit" value=" 新規登録 "
                      			styleClass="commandExButton" id="newCreate"
                      			action="#{pc_Xrx04101.doCreateAction}">
                  			</hx:commandExButton>
						</TD>					
					</TR>
				</TBODY>
			</TABLE>
			<TABLE class="table" width="100%" style="margin-top:7px;">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="150">
							<h:outputText styleClass="outputText" id="lblGaksekiNo"
				                value="#{pc_Xrx04101.propGakusekiCd.labelName}"
				                style="#{pc_Xrx04101.propGakusekiCd.labelStyle}"></h:outputText>
						</TH>
						<TD colspan="3"><h:inputText styleClass="inputText"
		                  id="htmlGakusekiCd" size="18"
		                  maxlength="#{pc_Xrx04101.propGakusekiCd.maxLength}"
		                  value="#{pc_Xrx04101.propGakusekiCd.stringValue}"
		                  style="#{pc_Xrx04101.propGakusekiCd.style}"
		                  readonly="#{pc_Xrx04101.propGakusekiCd.readonly}"
		                  maxlength="#{pc_Xrx04101.propGakusekiCd.maxLength}"
		                  onblur="return doGakuseiAjax(this, event, 'form1:htmlGakuseiNm');"></h:inputText>
		                <hx:commandExButton type="button" value="検"
		                  styleClass="commandExButton_search" id="select"
		                  onclick="openSubWindow('form1:htmlGakusekiCd');"></hx:commandExButton>
		                <h:inputText id="htmlGakuseiNm" tabindex="-1"
		                  styleClass="likeOutput" size="40"
		                  value="#{pc_Xrx04101.propGakuseiNm.stringValue}"
		                  readonly="#{pc_Xrx04101.propGakuseiNm.readonly}"></h:inputText>
		              </TD>
					</TR>
					<TR>
						<TH nowrap class="v_c">
							<h:outputText styleClass="outputText"
                  				id="lblSzks" value="#{pc_Xrx04101.propSzkGakkCd.labelName}"></h:outputText>
						</TH>
						<TD width="285">
							<h:selectOneMenu styleClass="selectOneMenu"
                    			id="htmlSzksList" value="#{pc_Xrx04101.propSzkGakkCd.value}"
                    			style="width:260px;">
                    			<f:selectItems value="#{pc_Xrx04101.propSzkGakkCd.list}" />
                  			</h:selectOneMenu>
						</TD>
						<TH nowrap class="v_b">
							<h:outputText styleClass="outputText"
                  				id="lblGaknen" value="#{pc_Xrx04101.propGaknen.labelName}"></h:outputText>
						</TH>
						<TD width="285">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlGaknen"
                    			value="#{pc_Xrx04101.propGaknen.value}" style="width:260px;">
                    			<f:selectItems value="#{pc_Xrx04101.propGaknen.list}" />
                  			</h:selectOneMenu>
              			</TD>
					</TR>
					<TR>
						<TH nowrap class="v_c">
							<h:outputText styleClass="outputText" id="lblKbn"
                  				value="#{pc_Xrx04101.propKbn.labelName}"></h:outputText>
						</TH>
						<TD width="285">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlKbn"
                    			value="#{pc_Xrx04101.propKbn.value}" style="width:260px;">
                    			<f:selectItems value="#{pc_Xrx04101.propKbn.list}" />
                  				</h:selectOneMenu>
              			</TD>
              			<TH nowrap class="v_b">
              				<h:outputText styleClass="outputText" id="lblSgkSbt"
                  				value="#{pc_Xrx04101.propSgkSbt.labelName}"></h:outputText>
              			</TH>
              			<TD width="285">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlSgkSbt"
                    			value="#{pc_Xrx04101.propSgkSbt.value}" style="width:260px;">
                    			<f:selectItems value="#{pc_Xrx04101.propSgkSbt.list}" />
                  			</h:selectOneMenu>
              			</TD>
					</TR>
					<TR>
						<TH nowrap class="v_a">
							<h:outputText styleClass="outputText" id="lblSearchSsiName"
                  				value="#{pc_Xrx04101.propSearchSsiName.labelName}"
                  				style="#{pc_Xrx04101.propSearchSsiName.labelStyle}"></h:outputText>
						</TH>
						<TD width="285">
							<h:selectOneMenu styleClass="selectOneMenu"
                    			id="htmlSearchSsiName" value="#{pc_Xrx04101.propSearchSsiName.value}"
                    			style="width:260px;">
                    			<f:selectItems value="#{pc_Xrx04101.propSearchSsiName.list}" />
                  			</h:selectOneMenu>
						</TD>
						<TH nowrap class="v_f" rowspan="5">
							<h:outputText styleClass="outputText" id="lblSsiStatus"
                  				value="#{pc_Xrx04101.propSsiStatus.labelName}"></h:outputText>
						</TH>
						<TD rowspan="5" width="285">
							<h:selectManyListbox
                  				styleClass="selectManyListbox" id="htmlSsiStatus"
                  				value="#{pc_Xrx04101.propSsiStatus.value}"
                  				size="5"
                  				style="width:260px; height:100px;">
                 				 <f:selectItems value="#{pc_Xrx04101.propSsiStatus.list}" /></h:selectManyListbox>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_a">
							<h:outputText styleClass="outputText" id="lblSsiDate"
                  				value="#{pc_Xrx04101.propSsiDateFrom.labelName}"></h:outputText>
						</TH>
						<TD width="285">
							<h:inputText styleClass="inputText"
								id="htmlSsiDateFrom" size="12"
								value="#{pc_Xrx04101.propSsiDateFrom.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"promptCharacter="_" />
							</h:inputText>&nbsp;～
							<h:inputText styleClass="inputText"
								id="htmlSsiDateTo" size="12"
								value="#{pc_Xrx04101.propSsiDateTo.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"promptCharacter="_" />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_a">
							<h:outputText styleClass="outputText" id="lblJuriDate"
                  				value="#{pc_Xrx04101.propJuriDateFrom.labelName}"></h:outputText>
						</TH>
						<TD width="285">
							<h:inputText styleClass="inputText"
								id="htmlJuriDateFrom" size="12"
								value="#{pc_Xrx04101.propJuriDateFrom.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>&nbsp;～
							<h:inputText styleClass="inputText"
								id="htmlJuriDateTo" size="12"
								value="#{pc_Xrx04101.propJuriDateTo.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"promptCharacter="_" />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_a">
							<h:outputText styleClass="outputText" id="lblSsiNo"
                  				value="#{pc_Xrx04101.propSsiNoFrom.labelName}"></h:outputText>
						</TH>
						<TD width="285">
							<h:inputText styleClass="inputText"
                  				id="htmlSsiNoFrom" size="12"
                  				value="#{pc_Xrx04101.propSsiNoFrom.longValue}"
                  				style="#{pc_Xrx04101.propSsiNoFrom.style}"
                  				maxlength="#{pc_Xrx04101.propSsiNoFrom.maxLength}">
                  				<hx:inputHelperAssist errorClass="inputText_Error"
                  				imeMode="inactive" promptCharacter="_" />
                  				<f:convertNumber type="number" pattern="###########"/>
                 	 		</h:inputText>&nbsp;～
                 	 		<h:inputText styleClass="inputText"
                  				id="htmlSsiNoTo" size="12"
                  				value="#{pc_Xrx04101.propSsiNoTo.longValue}"
                  				style="#{pc_Xrx04101.propSsiNoTo.style}"
                  				maxlength="#{pc_Xrx04101.propSsiNoTo.maxLength}">
                  				<hx:inputHelperAssist errorClass="inputText_Error"
                  				imeMode="inactive" promptCharacter="_" />
                  				<f:convertNumber type="number" pattern="###########"/>
                  			</h:inputText>
						</TD>
					</TR>
					
					
					
					<TR>
						<TH nowrap class="v_c">
							<h:outputText styleClass="outputText" id="lblCommentUmuKbn"
                  				value="#{pc_Xrx04101.propCommentUmuKbn.labelName}"></h:outputText>
						</TH>
						<TD width="285">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlCommentUmuKbn"
                    			value="#{pc_Xrx04101.propCommentUmuKbn.value}" style="width:260px;">
                    			<f:selectItems value="#{pc_Xrx04101.propCommentUmuKbn.list}" />
                  				</h:selectOneMenu>
              			</TD>
					</TR>
					
					
				</TBODY>
			</TABLE>
			<TABLE cellpadding="1" cellspacing="1" class="button_bar" align="center" width="100%" style="margin-top:7px;">
	          <TR>
	            <TD align="center" valign="middle">
	              <hx:commandExButton type="submit" value="検索"
	                styleClass="commandExButton_dat" id="search"
	                action="#{pc_Xrx04101.doSearchAction}"></hx:commandExButton>
	              <hx:commandExButton type="submit" value="クリア"
	                styleClass="commandExButton_etc" id="clear"
	                action="#{pc_Xrx04101.doClearAction}"></hx:commandExButton>
	            </TD>
	          </TR>
	        </TABLE>
	        <hx:jspPanel rendered="#{pc_Xrx04101.propSsiList.rendered == true}">
	        <TABLE border="0" width="100%" style="margin-top:7px;">
	        	<TR>
	        		<TD width="435" style="text-align:left">
						<h:outputText styleClass="outputText"
							value="#{pc_Xrx04101.propKensuu.value}">
						</h:outputText>
	        		</TD>
	        		<TD>
	        			<TABLE class="table" width="100%">
	        			<TR>
		        			<TH nowrap class="v_a" width="150">
			        			<h:outputText styleClass="outputText" id="lblChangeSsiStatus"
		                  			value="#{pc_Xrx04101.propChangeSsiStatus.labelName}"></h:outputText>
			        		</TH>
			        		<TD width="285">
			        			<h:selectOneMenu styleClass="selectOneMenu"
		                    		id="htmlChangeSsiStatus" value="#{pc_Xrx04101.propChangeSsiStatus.value}"
		                    		disabled="#{pc_Xrx04101.propChangeSsiStatus.disabled}"
		                    		style="width:150px;">
		                    		<f:selectItems value="#{pc_Xrx04101.propChangeSsiStatus.list}" />
		                  		</h:selectOneMenu>
		                  		<hx:commandExButton type="submit" value=" 一括変更 "
		                      		styleClass="commandExButton" id="updateAll"
		                      		action="#{pc_Xrx04101.doUpdateAllAction}">
		                  		</hx:commandExButton>
			        		</TD>
		        		</TR>
	        			</TABLE>
	        		</TD>
	        		
	        	</TR>
	        </TABLE>
	        <TABLE border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-top:1px;">
	        	<TR>
	        		<TD style="border-top-style:none;border-left-style:none;border-bottom-style:none">
	        			<div class="listScroll" id="listScroll" style="height: 235px"
              				onscroll="setScrollPosition('scroll', this);">
              				<h:dataTable
					              border="1" cellpadding="2" cellspacing="0"
					              columnClasses="columnClass1" headerClass="headerClass"
					              footerClass="footerClass"
					              rowClasses="#{pc_Xrx04101.propSsiList.rowClasses}"
					              styleClass="meisai_scroll" id="htmlSsiList"
					              value="#{pc_Xrx04101.propSsiList.list}" var="varlist">
								<h:column id="column1">
									<f:facet name="header">
                  						<h:outputText id="lblKbn_head" styleClass="outputText"
											value="区分"></h:outputText>
									</f:facet>
									<f:attribute value="50" name="width" />
										<h:outputText styleClass="likeOutput" id="lblKbn_list"
											value="#{varlist.kbn}"></h:outputText>
								</h:column>
								<h:column id="column2">
									<f:facet name="header">
                  						<h:outputText id="lblSsiDate_head" styleClass="outputText"
											value="申請日"></h:outputText>
									</f:facet>
									<f:attribute value="80" name="width" />
										<h:outputText styleClass="likeOutput" id="lblSsiDate_list"
											value="#{varlist.ssiDate}"></h:outputText>
								</h:column>
								<h:column id="column3">
									<f:facet name="header">
                  						<h:outputText id="lblJuriDate_head" styleClass="outputText"
											value="受理日"></h:outputText>
									</f:facet>
									<f:attribute value="80" name="width" />
										<h:outputText styleClass="likeOutput" id="lblJuriDate_list"
											value="#{varlist.juriDate}"></h:outputText>
								</h:column>
								<h:column id="column4">
									<f:facet name="header">
                  						<h:outputText id="lblGakusekiNo_head" styleClass="outputText"
											value="学籍番号"></h:outputText>
									</f:facet>
									<f:attribute value="80" name="width" />
										<h:outputText styleClass="likeOutput" id="lblGakusekiNo_list"
											value="#{varlist.gakusekiNo}"></h:outputText>
								</h:column>
								<h:column id="column5">
									<f:facet name="header">
                  						<h:outputText id="lblGakuseiName_head" styleClass="outputText"
											value="学生氏名"></h:outputText>
									</f:facet>
									<f:attribute value="120" name="width" />
										<h:outputText styleClass="likeOutput" id="lblGakuseiName_list"
											value="#{varlist.gakuseiName}"></h:outputText>
								</h:column>
								<h:column id="column6">
									<f:facet name="header">
                  						<h:outputText id="lblSsiNo_head" styleClass="outputText"
											value="申請NO"></h:outputText>
									</f:facet>
									<f:attribute value="110" name="width" />
										<h:outputText styleClass="likeOutput" id="lblSsiNo_list"
											value="#{varlist.ssiNo}"></h:outputText>
								</h:column>
								<h:column id="column7">
									<f:facet name="header">
                  						<h:outputText id="lblSsiName_head" styleClass="outputText"
											value="申請名称"></h:outputText>
									</f:facet>
									<f:attribute value="130" name="width" />
										<h:outputText styleClass="likeOutput" id="lblSsiName_list"
											title="#{varlist.propSsiName.value}"
											value="#{varlist.propSsiName.displayValue}"></h:outputText>
								</h:column>
								<h:column id="column8">
									<f:facet name="header">
                  						<h:outputText id="lblComment_head" styleClass="outputText"
											value="コメント"></h:outputText>
									</f:facet>
									<f:attribute value="50" name="width" />
										<h:outputText styleClass="likeOutput" id="lblComment_list"
											value="#{varlist.comment}"></h:outputText>
								</h:column>
								<h:column id="column9">
									<f:facet name="header">
									</f:facet>
									<f:attribute value="50" name="width" />
									<hx:commandExButton type="submit" value="編集"
                      					styleClass="commandExButton" id="hensyu"
                      					action="#{pc_Xrx04101.doHensyuAction}">
                  					</hx:commandExButton>
								</h:column>
								<h:column id="column10">
									<f:facet name="header">
                  						<h:outputText id="lblSsiStatus_head" styleClass="outputText"
											value="申請状態"></h:outputText>
									</f:facet>
									<f:attribute value="110" name="width" />
									<h:selectOneMenu styleClass="selectOneMenu"
		                    			id="htmlSsiStatus_list" value="#{varlist.ssiStatus.value}"
		                    			disabled="#{varlist.ssiStatus.disabled}"
		                    			style="width:105px;">
		                    			<f:selectItems value="#{varlist.ssiStatus.list}" />
		                  			</h:selectOneMenu>
								</h:column>
					        </h:dataTable>
              			</div>
	        		</TD>
	        	</TR>
	        	<TR>
	        		<TD>
	        			<FONT color="#ff0000">申請状態変更後は確定ボタンを押下して下さい。</FONT>
	        		</TD>
	        	</TR>
	        </TABLE>
	        <TABLE cellpadding="1" cellspacing="1" class="button_bar" align="center" width="100%" style="margin-top:7px;">
	          <TR>
	            <TD align="center" valign="middle">
	              <hx:commandExButton type="submit" value="確定"
	                styleClass="commandExButton_dat" id="update"
	                action="#{pc_Xrx04101.doUpdateAction}"></hx:commandExButton>
	              <hx:commandExButton type="submit" value="CSV作成"
	                styleClass="commandExButton_etc" id="csv"
	                action="#{pc_Xrx04101.doCSVAction}"></hx:commandExButton>
	            </TD>
	          </TR>
	        </TABLE>
	        </hx:jspPanel>
			</TD>
		</TR>
	</TBODY>
</TABLE>
</DIV>
</DIV>
</DIV>
<h:inputHidden value="#{pc_Xrx04101.propSsiList.scrollPosition}" id="scroll"></h:inputHidden>
</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>

<jsp:include page ="../inc/common.jsp" />

</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>
