<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssi00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssi00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
// チェックボックス一括チェック
function doAllSelect(thisObj, thisEvent) {
	check('table1','rowSelect1');
}

// チェックボックス一括解除
function doAllUnSelect(thisObj, thisEvent) {
	uncheck('table1','rowSelect1');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssi00201.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssi00201.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssi00201.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssi00201.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 --><BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%">

						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD width="225"><TABLE border="0" cellpadding="0"
										cellspacing="0" width="100%" class="table">
										<TBODY>
											<TR>
												<TH width="96" class="v_a"><h:outputText
													styleClass="outputText" id="text1" value="現求人年度"></h:outputText></TH>
												<TD width="129"><h:outputText styleClass="outputText"
													id="htmlKjnNendo" value="#{pc_Ssi00201.propKjnNendo.stringValue}"></h:outputText></TD>
											</TR>
											<TR>
												<TH width="96" class="v_b"><h:outputText
													styleClass="outputText" id="text3" value="現求人学期"></h:outputText></TH>
												<TD width="129"><h:outputText styleClass="outputText"
													id="htmlKjnGakkiNo" value="#{pc_Ssi00201.propKjnGakkiNo.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="80"></TD>
									<TD width="259">

									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
										<TBODY>
											<TR>
												<TH width="116" class="v_a"><h:outputText
													styleClass="outputText" id="text5" value="次求人年度"></h:outputText></TH>
												<TD width="141"><h:outputText styleClass="outputText"
													id="htmlNextKjnNendo" value="#{pc_Ssi00201.propNextKjnNendo.stringValue}"></h:outputText></TD>
											</TR>
											<TR>
												<TH width="116" class="v_b"><h:outputText
													styleClass="outputText" id="text7" value="次求人学期"></h:outputText></TH>
												<TD width="141"><h:outputText styleClass="outputText"
													id="htmlNextGakkiNo" value="#{pc_Ssi00201.propNextGakkiNo.stringValue}"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="139"><h:outputText
										styleClass="outputText" id="text9" value="不要データ削除内容"></h:outputText></TH>
									<TD width="423">
										<h:outputText styleClass="outputText" id="delinfo" value="削除対象は下記の通り" />
										<TABLE class="clear_border" border="0" cellpadding="0" cellspacing="0" style="margin-left: 10px;">
											<TBODY>
												<TR>
													<TD style="padding-right:20px;">
														<h:outputText styleClass="outputText" id="delinfo01title"
																value="求人情報">
														</h:outputText>
													</TD>
													<TD style="padding-right:50px;">
														<h:outputFormat	styleClass="outputFormat"
																id="delinfo01format1"
																value="（保存年数　{00}年）">
															<f:param name="delinfo01save" value="#{pc_Ssi00201.propNen.stringValue}"></f:param>
														</h:outputFormat>
													</TD>
													<TD style="padding-right:30px;">
														<h:outputFormat	styleClass="outputFormat"
																id="delinfo01format2"
																value="{0000}年以前のデータ">
															<f:param name="delinfo01year" value="#{pc_Ssi00201.propNendo.stringValue}"></f:param>
														</h:outputFormat>
													</TD>
												</TR>
												<TR>
													<TD colspan="3" style="text-align:center" valign="top">
														<h:outputText styleClass="outputText" id="delinfo02"
																style="vertical-align:top;"
																value="（添付ファイル、および添付情報も併せて削除されます。）">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TD style="padding-right:20px;">
														<h:outputText styleClass="outputText" id="delinfo03title"
																value="就職活動情報">
														</h:outputText>
													</TD>
													<TD style="padding-right:50px;">
														<h:outputFormat	styleClass="outputFormat"
																id="delinfo03format1"
																value="（保存年数　{00}年）">
															<f:param name="delinfo03save" value="#{pc_Ssi00201.propNenSS.stringValue}"></f:param>
														</h:outputFormat>
													</TD>
													<TD style="padding-right:30px;">
														<h:outputFormat	styleClass="outputFormat"
																id="delinfo01format3"
																value="{0000}年以前のデータ">
															<f:param name="delinfo03year" value="#{pc_Ssi00201.propNendoSS.stringValue}"></f:param>
														</h:outputFormat>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="20%"></TD>
					</TR>
					
					<TR>
						<TD width="20%"></TD>
						<TD width="60%" align="left">
						<h:outputText styleClass="note" id="text20"
							value="※本番環境で実行した場合のみ、削除された求人に紐付く添付ファイルは削除されます。"></h:outputText>
						</TD>
						<TD width="20%"></TD>
					</TR>

				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="54"><h:outputText
										styleClass="outputText" id="text11"
										value="#{pc_Ssi00201.propNenKako.name}"></h:outputText></TH>
									<TD width="37"><h:inputText styleClass="inputText"
										id="htmlNenKako" size="2"
										value="#{pc_Ssi00201.propNenKako.value}"
										style="#{pc_Ssi00201.propNenKako.style}"></h:inputText></TD>
									<TD width="470"><h:outputText styleClass="outputText"
										id="text13" value="年間求人のない企業の求人票発行区分を「発行対象外」にします。"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD align="left"><h:outputText styleClass="outputText" id="text14" value="卒業生経歴初期設定データ内容"></h:outputText></TD>
					</TR>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%">

						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="124"><h:outputText
										styleClass="outputText" id="text15"
										value="#{pc_Ssi00201.propDateProsess.labelName}"
										style="#{pc_Ssi00201.propDateProsess.labelStyle}"></h:outputText></TH>
									<TD width="438"><h:inputText styleClass="inputText"
										id="htmlDateProcess" size="12"
										value="#{pc_Ssi00201.propDateProsess.dateValue}"
										style="#{pc_Ssi00201.propDateProsess.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<f:convertDateTime dateStyle="medium"/>
										<hx:inputHelperDatePicker />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%" align="left"><h:outputText styleClass="note" id="text17"
							value="※就職では管理しない学生の就学種別を選択してください。"></h:outputText></TD>
						<TD width="20%"></TD>
					</TR>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%" align="right"><h:outputText styleClass="outputText" id="text18"
							value="#{pc_Ssi00201.propSyugakSbt.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text19" value="件"></h:outputText></TD>
						<TD width="20%"></TD>
					</TR>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%">
						<DIV class="listScroll" style="height:178px;" onscroll="setScrollPosition('scroll',this);"
							id="listScroll">
						<h:dataTable border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="rowClass1" styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssi00201.propSyugakSbt.list}" var="varlist"
							width="550px">
							<h:column id="column1">
							<f:attribute value="20" name="width" />
							<f:attribute value="text-align:right" name="style" />
							<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
									id="rowSelect1" value="#{varlist.selected}"
									rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
								<f:facet name="header"></f:facet>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="コード" id="text4"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text2"
									value="#{varlist.syugakSbtCd}"></h:outputText>
								<f:attribute value="80" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="区分"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text6"
									value="#{varlist.syugakKbn}"></h:outputText>
								<f:attribute value="50" name="width" />
								<f:attribute value="text-align:center" name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text8"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text12"
									value="#{varlist.syugakSbtName}"></h:outputText>
							</h:column>
						</h:dataTable>
						</DIV></TD>
						<TD width="20%"></TD>
					</TR>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%" align="left"><hx:commandExButton type="submit"
							styleClass="check" id="selectAll" onclick="return doAllSelect(this, event);"></hx:commandExButton>
							<hx:commandExButton type="submit" styleClass="uncheck"
							id="selectNone" onclick="return doAllUnSelect(this, event);"></hx:commandExButton></TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar" style="margin-top: 8px;">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="実行"
							styleClass="commandExButton_dat" id="exec"
							confirm="#{msg.SY_MSG_0001W}" action="#{pc_Ssi00201.doExecAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssi00201.propSyugakSbt.scrollPosition}" id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
	<SCRIPT LANGUAGE="JavaScript">
		window.attachEvent('onload', endload);
		function endload() {
			changeScrollPosition('scroll','listScroll');
		}
	</SCRIPT>
</HTML>

