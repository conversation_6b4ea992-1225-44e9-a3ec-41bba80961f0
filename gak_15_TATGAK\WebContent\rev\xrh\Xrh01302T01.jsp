<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01302T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	// バーコード入力：KeyDown処理
	function fncKeyEvt() {
		if (event.keyCode == 13) {
			var btnObj = document.getElementById('form1:htmlSeiriNo');
			indirectClick('add');
		}
	}
	
	//タブ移動時
	function confirmMoveTab(msg){
		var obj = document.getElementById('form1:htmlListSeiriNo');
		if(obj.length != 0){
			return confirm(msg);
		}
		return true;
	}
	
	//ページ初期化
	function reloadPage( thisEvent ){
		
			
		document.getElementById("form1:returnDisp").focus();
	}
	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="reloadPage(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01302T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh01302T01.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01302T01.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01302T01.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right">
						<hx:commandExButton type="submit"
							value="戻　る" styleClass="commandExButton" id="returnDisp"
							tabindex="1"
							action="#{pc_Xrh01302T01.xrh01302.doReturnDispAction}"
							onclick="return confirmMoveTab('#{msg.SY_MSG_0014W}');">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここにボタンを配置 --></DIV>

			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
			<TBODY>
				<TR>
				<TD width="900" valign="top"><!-- ↓タブ間共有テーブル↓ -->
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
					<TBODY>
						<TR>
							<TH class="v_a" width="190">
								<!--年度 --> 
								<h:outputText
									styleClass="outputText" id="lblNendo"
									value="#{pc_Xrh01302T01.xrh01302.propNendo.labelName}"
									style="#{pc_Xrh01302T01.xrh01302.propNendo.labelStyle}">
								</h:outputText>
							</TH>
							<TD width="400">
								<h:inputText styleClass="inputText"
									id="htmlNendo" size="4" tabindex="2"
									value="#{pc_Xrh01302T01.xrh01302.propNendo.dateValue}"
									readonly="#{pc_Xrh01302T01.xrh01302.propNendo.readonly}"
									disabled="#{pc_Xrh01302T01.xrh01302.propNendo.disabled}"
									style="#{pc_Xrh01302T01.xrh01302.propNendo.style}">
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="inactive" promptCharacter="_" />
									<f:convertDateTime pattern="yyyy" />
								</h:inputText>
							</TD>
							<TD width="100" align="right" rowspan="2"
								style="background-color: transparent; text-align: right"
								class="clear_border">
								<hx:commandExButton type="submit"
									value="#{pc_Xrh01302T01.xrh01302.propSelect.caption}"
									styleClass="commandExButton" 
									id="select" tabindex="3"
									disabled="#{pc_Xrh01302T01.xrh01302.propSelect.disabled}"
									rendered="#{pc_Xrh01302T01.xrh01302.propSelect.rendered}"
									style="#{pc_Xrh01302T01.xrh01302.propSelect.style}"
									action="#{pc_Xrh01302T01.xrh01302.doSelectAction}">
								</hx:commandExButton> 
								<hx:commandExButton type="submit"
									value="#{pc_Xrh01302T01.xrh01302.propUnSelect.caption}"
									styleClass="commandExButton" id="unselect" tabindex="4"
									disabled="#{pc_Xrh01302T01.xrh01302.propUnSelect.disabled}"
									rendered="#{pc_Xrh01302T01.xrh01302.propUnSelect.rendered}"
									style="#{pc_Xrh01302T01.xrh01302.propUnSelect.style}"
									action="#{pc_Xrh01302T01.xrh01302.doUnSelectAction}">
								</hx:commandExButton>
							</TD>
						</TR>
						
						<TR>
							<TH class="v_a" width="190">
								<h:outputText
									styleClass="outputText" id="lblKamokSikenCnt"
									style="#{pc_Xrh01302T01.xrh01302.propKamokSikenCnt.labelStyle}"
									value="#{pc_Xrh01302T01.xrh01302.propKamokSikenCnt.labelName}">
								</h:outputText>
							</TH>
							<TD>
								<h:inputText id="htmlSikenKaisu"
									styleClass="inputText" size="2" tabindex="5"
									value="#{pc_Xrh01302T01.xrh01302.propKamokSikenCnt.integerValue}"
									readonly="#{pc_Xrh01302T01.xrh01302.propKamokSikenCnt.readonly}"
									disabled="#{pc_Xrh01302T01.xrh01302.propKamokSikenCnt.disabled}"
									style="#{pc_Xrh01302T01.xrh01302.propKamokSikenCnt.style}">
									<f:convertNumber type="number" pattern="#0" />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
								</h:inputText>
								<h:outputText
									styleClass="outputText" value="回">
								</h:outputText>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
					
					<HR>
					
					<!-- ↑タブ間共有テーブル↑ --> <BR>
					<TABLE border="0" cellpadding="20" cellspacing="0">
					<TBODY>
					<TR>
					<TD width="860" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0">
						<TBODY>
							<TR>
								<TD class="tab_head_on">
									<hx:commandExButton type="button"
										value="出欠状況" 
										id="moveSyukketu" tabindex="6"
										disabled="true"
										styleClass="tab_head_on">
									</hx:commandExButton>
								</TD>
								<TD class="tab_head_off">
									<hx:commandExButton type="submit"
										value="新旧刊区分" 
										styleClass="tab_head_off" 
										id="moveLSinkyuTab" tabindex="7"
										disabled="false"
										onclick="return confirmMoveTab('#{msg.SY_MSG_0014W}');"
										action="#{pc_Xrh01302T01.doMoveSinkyuTabAction}">
									</hx:commandExButton>
								</TD>
							</TR>
						</TBODY>
						</TABLE>
					</TD>
					</TR>
					<TR>
					<TD valign="top" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="tab_body" width="100%">
						<TBODY>
							<TR>
							<TD>
							
							<BR>
								<TABLE  width="600" border="0" cellpadding="0" 
									cellspacing="0" class="table" style="margin-left:50px;">
								<TBODY>
									<TR>
										<TH nowrap class="v_a" width="100">
											<!--出欠状況 --> 
											<h:outputText
												styleClass="outputText" id="lblSyukketu"
												value="#{pc_Xrh01302T01.propSyukketu.labelName}"
												style="#{pc_Xrh01302T01.propSyukketu.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="2">
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlSyukketu" tabindex="8"
												onchange=""
												value="#{pc_Xrh01302T01.propSyukketu.stringValue}"
												style="#{pc_Xrh01302T01.propSyukketu.style};width:90px"
												disabled="#{pc_Xrh01302T01.propSyukketu.disabled}">
												<f:selectItems
													value="#{pc_Xrh01302T01.propSyukketu.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
										
									<TR>
										<TH nowrap class="v_a" width="120">
											<!--許可整理番号 --> 
											<h:outputText
												styleClass="outputText" id="lblSieiNo"
												value="#{pc_Xrh01302T01.propSeiriNo.labelName}"
												style="#{pc_Xrh01302T01.propSeiriNo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="200">
											<h:inputText styleClass="inputText" id="htmlSeiriNo"
												tabindex="9" onchange=""
												value="#{pc_Xrh01302T01.propSeiriNo.stringValue}"
												disabled="#{pc_Xrh01302T01.propSeiriNo.disabled}"
												maxlength="#{pc_Xrh01302T01.propSeiriNo.maxLength}" 
												style="#{pc_Xrh01302T01.propSeiriNo.style}"
												onkeydown="fncKeyEvt();">
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="disabled"/>
											</h:inputText>
											<hx:commandExButton type="submit"
												value="追加"
												styleClass="commandExButton" id="add"
												disabled="#{pc_Xrh01302T01.propAdd.disabled}"
												rendered="#{pc_Xrh01302T01.propAdd.rendered}"
												style="#{pc_Xrh01302T01.propAdd.style}"
												action="#{pc_Xrh01302T01.doAddAction}" tabindex="10">
											</hx:commandExButton>
										</TD>
										<TD width="80">
										</TD>
									</TR>
								</TBODY>
								</TABLE>		
								
								<BR>
								
								<TABLE border="0" cellspacing="0" style="margin-left:10px;" width="800" >
				    			<TBODY>
				                 	<TR>
				                    	<TD align="left" colspan="2">
				                      		<h:outputText styleClass="outputText"
				                  				value="許可整理番号">
				                      		</h:outputText>
				                      		<h:outputText styleClass="outputText" style="margin-left:40px"
				                  				value="科目名称">
				                      		</h:outputText>
				                      		<h:outputText styleClass="outputText" style="margin-left:120px"
				                  				value="学生氏名">
				                      		</h:outputText>
				                      		<h:outputText styleClass="outputText" style="margin-left:80px"
				                  				value="試験地">
				                      		</h:outputText>
				                      		<h:outputText styleClass="outputText" style="margin-left:140px"
				                  				value="時限">
				                      		</h:outputText>
				                      		<h:outputText styleClass="outputText" style="margin-left:20px"
				                  				value="出欠">
				                      		</h:outputText>
				                     	</TD>
						            </TR>
				      				<TR>
				        				<TD width="700">
			        						<h:selectManyListbox styleClass="selectManyListbox"
			        							id="htmlListSeiriNo" size="16" style="width: 100%;"
			        							value="#{pc_Xrh01302T01.propListSeiriNo.value}" tabindex="11">
			        							<f:selectItems value="#{pc_Xrh01302T01.propListSeiriNo.list}"/>
			      							</h:selectManyListbox>
				        				</TD>
				        				<TD valign="top" align="center">
			        						<hx:commandExButton type="submit" value="全て除外"
						                        styleClass="commandExButton" id="removeall"
						                        disabled="#{pc_Xrh01302T01.propRemoveAll.disabled}"
						                        action="#{pc_Xrh01302T01.doAllRemoveAction}" style="width:60px" tabindex="12">
						                     </hx:commandExButton><BR>
							                     
						                     <BR>
						                     <h:outputText styleClass="outputText"
						                        value="（複数選択可）">
						                     </h:outputText><BR>
			          						 
			          						 <BR>
						                     
						                     <hx:commandExButton
						                        type="submit" value="除外" styleClass="commandExButton"
						                        id="remove" action="#{pc_Xrh01302T01.doRemoveAction}"
						                        disabled="#{pc_Xrh01302T01.propRemove.disabled}"
						                        style="width:60px" tabindex="13">
			          						  </hx:commandExButton>
							              </TD>
							         </TR>
							                  
					                 <TR>
				                      	<TD colspan="2">
				                      		<h:outputText styleClass="outputText" 
				                      			style="margin-left:630px;"
				                  				value="件数：">
				                      		</h:outputText>
				                      		<h:outputText styleClass="outputText"
				                  				value="#{pc_Xrh01302T01.propListCnt.stringValue}">
				                      		</h:outputText>
				                      		<h:outputText styleClass="outputText" 
				                      			value="件">
				                      		</h:outputText>
				                     	</TD>
				                     </TR>
				                     
				                     <TR>
				                      	<TD colspan="2" height="36" valign="top" align="center">
				                      		<hx:commandExButton type="submit"
												styleClass="commandExButton_dat" id="exec"
												onclick="return confirm('#{msg.SY_MSG_0002W}');"
												tabindex="14"
												action="#{pc_Xrh01302T01.doExecAction}" value="確定"
												disabled="#{pc_Xrh01302T01.propExec.disabled}">
											</hx:commandExButton>
				                     	</TD>
				                     </TR>
				                     
				      			
				      			</TBODY>
				      			</TABLE>
				      			
				      			
												
						
						</TD>
						</TR>
						</TBODY>
						</TABLE>
					</TD>
					</TR>
					</TBODY>
					</TABLE>
			</TD>
			</TR>
			</TBODY>
			</TABLE>
			</DIV>
			
			</DIV>
			</DIV>
		
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
