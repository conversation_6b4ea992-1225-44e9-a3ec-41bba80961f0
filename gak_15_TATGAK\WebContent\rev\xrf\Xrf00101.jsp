<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrf00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<style type="text/css">
<!--
 .setWidth TD {width: 110px; white-space: nowrap;}
-->
</style>

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrf00101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrf00101.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrf00101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrf00101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
　<!-- ←レイアウトのため全角１文字 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD height="73">
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="800">
								<TR>
									<TD align="left" class="clear_border" colspan="2" style="font-size:10pt;background-color:#f7f7f7">
									コピー元</TD>
									<TD align="left" class="clear_border" colspan="2" style="font-size:10pt;background-color:#f7f7f7">
									コピー先</TD>
								</TR>
							<TBODY>
								<TR>
									<TH class="v_a" colspan="" width="150"><h:outputText
										styleClass="outputText" id="lblNendoMoto"
										value="#{pc_Xrf00101.propNendoMoto.labelName}"
										style="#{pc_Xrf00101.propNendoMoto.labelStyle}"></h:outputText></TH>
									<TD class="" colspan="" width="250"><h:inputText
										styleClass="inputText" id="htmlNendoMoto" size="5"
										tabindex="1" style="#{pc_Xrf00101.propNendoMoto.style}"
										value="#{pc_Xrf00101.propNendoMoto.dateValue}"
										disabled="#{pc_Xrf00101.propNendoMoto.disabled}"
										readonly="#{pc_Xrf00101.propNendoMoto.readonly}">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH class="v_b" colspan="" width="150"><h:outputText
										styleClass="outputText" id="lblNendoSaki"
										value="#{pc_Xrf00101.propNendoSaki.labelName}"
										style="#{pc_Xrf00101.propNendoSaki.labelStyle}"></h:outputText></TH>
									<TD class="" colspan="" width="250"><h:inputText
										styleClass="inputText" id="htmlNendoSaki" size="5"
										tabindex="2" style="#{pc_Xrf00101.propNendoSaki.style}"
										value="#{pc_Xrf00101.propNendoSaki.dateValue}"
										disabled="#{pc_Xrf00101.propNendoSaki.disabled}"
										readonly="#{pc_Xrf00101.propNendoSaki.readonly}">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE></TD>
					</TR>
					<TR>
						<TD height="45">
						<TABLE border="0" cellpadding="0" cellspacing="0" style="" class="table" width="800">
 							<TBODY>
								<TR>
									<TH width="150" class="v_d" height="45"><h:outputText
										styleClass="outputText" value="チェックリスト出力指定"></h:outputText></TH>
									<TD width="650" height="45">
									<TABLE border="0">
										<TBODY>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlNormalDataSitei"
													value="#{pc_Xrf00101.propNormalDataSitei.checked}"
													tabindex="3"></h:selectBooleanCheckbox> <h:outputText
													styleClass="outputText" value="正常データ"></h:outputText></TD>
											</TR>
											<TR>
												<TD class="clear_border"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlErrerDataSitei"
													value="#{pc_Xrf00101.propErrerDataSitei.checked}"
													tabindex="4"></h:selectBooleanCheckbox> <h:outputText
													styleClass="outputText" value="エラーデータ"></h:outputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE></TD>
					</TR>
					<TR>
						<TD height="118">
						<!-- 出力指定 --><TABLE border="0" width="800" class="button_bar" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton
										type="submit" value="実行" styleClass="commandExButton_dat"
										id="exec" action="#{pc_Xrf00101.doExecAction}" tabindex="5"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

