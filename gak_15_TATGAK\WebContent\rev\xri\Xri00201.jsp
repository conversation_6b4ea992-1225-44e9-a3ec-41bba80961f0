<%-- 
	在学満了除籍
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ns/Xri00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xri00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript"></SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/><f:loadBundle basename="properties.messageCO" var="msgCO"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xri00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xri00201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xri00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xri00201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ←レイアウトの問題の為に、全角スペースを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="4">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="700">
							<TBODY>


								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblStgkSbt" value="出学種別"
										style="#{pc_Xri00201.propStgkSbt.labelStyle}"></h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlStgkSbt"
										value="#{pc_Xri00201.propStgkSbt.value}"
										style="#{pc_Xri00201.propStgkSbt.style};width:150px">
										<f:selectItems value="#{pc_Xri00201.propStgkSbt.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="143" class="v_c"><h:outputText
										styleClass="outputText" id="lblJskDate"
										value="#{pc_Xri00201.propJskDate.labelName}"
										style="#{pc_Xri00201.propJskDate.labelStyle}"></h:outputText></TH>
									<TD width=""><h:inputText styleClass="inputText"
										id="htmlTdkDate" value="#{pc_Xri00201.propJskDate.dateValue}"
										size="10">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="700" style="margin-top:10px">
							<TBODY>
								<TR>
									<TH nowrap class="v_c" width="144"><h:outputText
										styleClass="outputText" id="lblSyoryKbnSitei"
										value="#{pc_Xri00201.propSyoriKbnSitei.labelName}"
										style="#{pc_Xri00201.propSyoriKbnSitei.labelStyle}"></h:outputText></TH>
									<TD class="" width="456"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSyoriKbnSitei"
										value="#{pc_Xri00201.propSyoriKbnSitei.checked}"></h:selectBooleanCheckbox>チェックのみ（データの登録/更新は行いません）</TD>
								</TR>
								<TR>
									<TH class="v_d" nowrap width="144"><h:outputText
										styleClass="outputText" id="lblCheckList"
										value="#{pc_Xri00201.propCheckList.labelName}"
										style="#{pc_Xri00201.propCheckList.style}"></h:outputText></TH>
									<TD height="300" class="" width="556"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlCheckList"
										layout="pageDirection"
										value="#{pc_Xri00201.propCheckList.value}">
										<f:selectItem itemValue="0" itemLabel="正常データ" />
										<f:selectItem itemValue="1" itemLabel="エラーデータ" />
										<f:selectItem itemValue="2" itemLabel="ワーニングデータ" />
									</h:selectManyCheckbox></TD>

								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" width="700" class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit"
										value="実行" styleClass="commandExButton_dat" id="exec"
										action="#{pc_Xri00201.doExecAction}"
										confirm="#{msg.SY_MSG_0001W}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

