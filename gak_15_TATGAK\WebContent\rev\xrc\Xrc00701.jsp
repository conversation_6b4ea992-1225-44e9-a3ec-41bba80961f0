<%-- 
	配付データ登録
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc00701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

	function init() {
		// 学生名表示
		doGakuseiAjax(document.getElementById("form1:htmlGakusekiCd"), event, 'form1:htmlGakusekiName');
		var buppinCd = document.getElementById("form1:htmlBuppinCd").value;
		if (buppinCd != null && buppinCd != '') {
			// 物品名称表示
			doBuppinAjax(document.getElementById("form1:htmlBuppinCd"), event, 'form1:htmlBuppinName')
		}
		var kamokuCd = document.getElementById("form1:htmlKamokuCd").value;
		if (kamokuCd != null && kamokuCd != '') {
			// 物品名称表示
			doKamokuAjax(document.getElementById("form1:htmlKamokuCd"), event, 'form1:htmlKamokuName')
		}
	}

	function openGakusekiSubWindow(field1) {
		// 学生検索画面
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
			+ "?retFieldName=" + field1;
		openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
		return false;
	}
	
	function doGakuseiAjax(thisObj, thisEvent, targetLabel) {
		 // 学生名称を取得する
		 var servlet = "rev/co/CobGakseiAJAX";
		 var args = new Array();
		 args['code1'] = thisObj.value;

		 var ajaxUtil = new AjaxUtil();
		 ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

	function openBuppinSubWindow(field1) {
		//物品検索画面
		var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0101.jsp"
			+ "?retFieldName=" + field1;
		openModalWindow(url, "pXrc0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
		return false;
	}

	function doBuppinAjax(thisObj, thisEvent, targetLabel) {
		//物品名称を取得する
		var servlet = "rev/xrc/XrcBpnAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

	function openKamokuSubWindow(field1) {
		//科目検索画面
		var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
			+ "?retFieldName=" + field1;
		openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
		return false;
	}

	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
		//科目名称を取得する
		var servlet = "rev/km/KmzKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="init();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrc00701.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrc00701.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrc00701.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrc00701.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<BR>
			<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="780px">
				<TBODY>
					<TR>
						<TH width="150px" nowrap class="v_a">
							<h:outputText styleClass="outputText"
								style="#{pc_Xrc00701.propGakusekiCd.labelStyle}"
								value="#{pc_Xrc00701.propGakusekiCd.labelName}">
							</h:outputText>
						</TH>
						<TD nowrap width="*" colspan="2">
							<h:inputText styleClass="inputText"
								id="htmlGakusekiCd" size="10"
								onblur="return doGakuseiAjax(this, event, 'form1:htmlGakusekiName');"
								value="#{pc_Xrc00701.propGakusekiCd.stringValue}"
								style="#{pc_Xrc00701.propGakusekiCd.style}"
								maxlength="#{pc_Xrc00701.propGakusekiCd.max}" 
								disabled="#{pc_Xrc00701.propGakusekiCd.disabled}" tabindex="1">
							</h:inputText>
							<hx:commandExButton type="submit" 
								styleClass="commandExButton_search" id="searchGakuseki"
								onclick="return openGakusekiSubWindow('form1:htmlGakusekiCd');" 
								disabled="#{pc_Xrc00701.propSearchGakuseki.disabled}"
								action="#{pc_Ghb00201xxx.doPaySearchAction}" tabindex="2">
							</hx:commandExButton>
							<hx:commandExButton type="submit"
								value="選択" styleClass="cmdBtn_dat_s" id="selectGakuseki"
								disabled="#{pc_Xrc00701.propSelectGakuseki.disabled}"
								action="#{pc_Xrc00701.doSelectGakusekiAction}" tabindex="3">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="解除"
								styleClass="cmdBtn_etc_s" id="unselectGakuseki" 
								disabled="#{pc_Xrc00701.propUnselectGakuseki.disabled}"
								action="#{pc_Xrc00701.doUnselectGakusekiAction}" tabindex="4">
							</hx:commandExButton>
							&nbsp;
							<h:outputText styleClass="outputText" id="htmlGakusekiName" value="#{pc_Xrc00701.propGakusekiName.stringValue}"/>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="780">
				<TR>
					<TD height="10px">
					</TD>
				</TR>
			</TABLE>


			<TABLE border="0" width="780">
				<TBODY>
					<TR>
						<TD  align="right"><h:outputText styleClass="outputText"
							style="font-size: 8pt"
							value="#{pc_Xrc00701.propHifList.listCount == null ? 0 : pc_Xrc00701.propHifList.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text21" style="font-size: 8pt"
							value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="780">
				<TBODY>
					<TR>
						<TD>
						<DIV class="listScroll" style="height:229px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);">
							<h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							styleClass="meisai_scroll" id="htmlHifList"
							value="#{pc_Xrc00701.propHifList.list}" var="varlist"
							rowClasses="#{pc_Xrc00701.propHifList.rowClasses}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="物品コード"></h:outputText>
								</f:facet>
								<f:attribute value="80" name="width" />
								<h:outputText styleClass="outputText" id="text1"
									value="#{varlist.buppinCd}"></h:outputText>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="物品名称"></h:outputText>
								</f:facet>
								<f:attribute value="310" name="width" />
								<h:outputText styleClass="outputText"
									value="#{varlist.buppinName.displayValue}"
									title="#{varlist.buppinName.value}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								  <h:outputText styleClass="outputText" value="枝番"></h:outputText>
								</f:facet>
								<f:attribute value="50" name="width" />
								<h:outputText styleClass="outputText" id="text3"
									value="#{varlist.edaban}"></h:outputText>
								<f:attribute value="text-align: right;padding-right: 10px;" name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								  <h:outputText styleClass="outputText" value="再配本"></h:outputText>
								</f:facet>
								<f:attribute value="70" name="width" />
								<h:outputText styleClass="outputText" id="text4"
									value="#{varlist.saihaihon}"></h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
								  <h:outputText styleClass="outputText" value="出庫倉庫"></h:outputText>
								</f:facet>
								<f:attribute value="240" name="width" />
								<h:outputText styleClass="outputText" id="text5"
									value="#{varlist.skSoko}"></h:outputText>
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="select" value="選択" action="#{pc_Xrc00701.doSelectHifAction}"></hx:commandExButton>
								<f:attribute value="30" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			
			<TABLE border="0" class="table" cellspacing="0" cellpadding="0" width="780px">
				<TBODY>
					<TR>
						<TH class="v_b" width="200"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propBuppinCd.labelName}"
							style="#{pc_Xrc00701.propBuppinCd.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlBuppinCd" size="5"
							onblur="return doBuppinAjax(this, event, 'form1:htmlBuppinName');"
							maxlength="#{pc_Xrc00701.propBuppinCd.max}"
							style="#{pc_Xrc00701.propBuppinCd.style}"
							disabled="#{pc_Xrc00701.propBuppinCd.disabled}"
							tabindex="5"
							value="#{pc_Xrc00701.propBuppinCd.stringValue}">
							</h:inputText>
							<hx:commandExButton type="submit" 
								styleClass="commandExButton_search" id="searchBuppin"
								onclick="return openBuppinSubWindow('form1:htmlBuppinCd');" 
								disabled="#{pc_Xrc00701.propSearchBuppin.disabled}" tabindex="6">
							</hx:commandExButton>
							<hx:commandExButton type="submit"
								value="選択" styleClass="cmdBtn_dat_s" id="selectBuppin"
								disabled="#{pc_Xrc00701.propSelectBuppin.disabled}"
								action="#{pc_Xrc00701.doSelectBuppinAction}" tabindex="7">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="解除"
								styleClass="cmdBtn_etc_s" id="unselectBuppin" 
								disabled="#{pc_Xrc00701.propUnselectBuppin.disabled}"
								action="#{pc_Xrc00701.doUnselectBuppinAction}" tabindex="8">
							</hx:commandExButton>
							&nbsp;
							<h:outputText styleClass="outputText" id="htmlBuppinName" value="#{pc_Xrc00701.propBuppinName.stringValue}"/>
						</TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propEdaban.labelName}"
							style="#{pc_Xrc00701.propEdaban.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlEdaban" size="2" tabindex="9"
							maxlength="#{pc_Xrc00701.propEdaban.max}"
							style="#{pc_Xrc00701.propEdaban.style}"
							disabled="#{pc_Xrc00701.propEdaban.disabled}"
							value="#{pc_Xrc00701.propEdaban.integerValue}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist promptCharacter="_" errorClass="inputText_Error" />
							</h:inputText>
							&nbsp;&nbsp;
							<h:outputText styleClass="outputText"
								value="新規登録時は空欄">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propKamokuCd.labelName}"
							style="#{pc_Xrc00701.propKamokuCd.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlKamokuCd" size="10" tabindex="10"
							onblur="return doKamokuAjax(this, event, 'form1:htmlKamokuName');"
							maxlength="#{pc_Xrc00701.propKamokuCd.max}"
							style="#{pc_Xrc00701.propKamokuCd.style}"
							disabled="#{pc_Xrc00701.propKamokuCd.disabled}"
							value="#{pc_Xrc00701.propKamokuCd.stringValue}">
							<hx:inputHelperAssist imeMode="disabled" errorClass="inputText_Error" />
							</h:inputText>
							<hx:commandExButton type="submit" 
								styleClass="commandExButton_search" id="searchKamoku" 
								onclick="return openKamokuSubWindow('form1:htmlKamokuCd');"
								disabled="#{pc_Xrc00701.propSearchKamoku.disabled}"
								action="#{pc_Ghb00201.doPaySearchAction}" tabindex="11">
							</hx:commandExButton>
							&nbsp;
							<h:outputText styleClass="outputText" id="htmlKamokuName" value="#{pc_Xrc00701.propKamokuName.stringValue}"/>
						</TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propHaifubi.labelName}"
							style="#{pc_Xrc00701.propHaifubi.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlHaifubi" size="10" tabindex="12"
							disabled="#{pc_Xrc00701.propHaifubi.disabled}"
							value="#{pc_Xrc00701.propHaifubi.dateValue}">
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propSaihaihon.labelName}"
							style="#{pc_Xrc00701.propSaihaihon.labelStyle}"></h:outputText></TH>
			            <TD><h:selectOneRadio
				          tabindex="13"
			              disabledClass="selectOneRadio_Disabled"
			              styleClass="selectOneRadio" id="htmlSaihaihon"
			              value="#{pc_Xrc00701.propSaihaihon.stringValue}"
						  disabled="#{pc_Xrc00701.propSaihaihon.disabled}"
			              style="#{pc_Xrc00701.propSaihaihon.style}">
			              <f:selectItem itemValue="0" itemLabel="配本" />
			              <f:selectItem itemValue="1" itemLabel="再配本" />
			            </h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propHaifukano.labelName}"
							style="#{pc_Xrc00701.propHaifukano.labelStyle}"></h:outputText></TH>
			            <TD><h:selectOneRadio
			              tabindex="14"
			              disabledClass="selectOneRadio_Disabled"
			              styleClass="selectOneRadio" id="htmlHaifukano"
			              value="#{pc_Xrc00701.propHaifukano.stringValue}"
						  disabled="#{pc_Xrc00701.propHaifukano.disabled}"
			              style="#{pc_Xrc00701.propHaifukano.style}">
			              <f:selectItem itemValue="1" itemLabel="配付可能" />
			              <f:selectItem itemValue="0" itemLabel="配付不可" />
			            </h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH class="v_a"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propSkSoko.labelName}"
							style="#{pc_Xrc00701.propSkSoko.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSkSoko" tabindex="15"
							value="#{pc_Xrc00701.propSkSoko.stringValue}"
						    disabled="#{pc_Xrc00701.propSkSoko.disabled}"
							style="#{pc_Xrc00701.propSkSoko.style};width:420px">
							<f:selectItems value="#{pc_Xrc00701.propSkSoko.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propFurikomiIraiCd.labelName}"
							style="#{pc_Xrc00701.propFurikomiIraiCd.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlFurikomiIraiCd" size="10" tabindex="16"
							maxlength="#{pc_Xrc00701.propFurikomiIraiCd.max}"
							style="#{pc_Xrc00701.propFurikomiIraiCd.style}"
						    disabled="#{pc_Xrc00701.propFurikomiIraiCd.disabled}"
							value="#{pc_Xrc00701.propFurikomiIraiCd.stringValue}">
							<hx:inputHelperAssist imeMode="disabled" errorClass="inputText_Error" />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b"><h:outputText
							styleClass="outputText"
							value="#{pc_Xrc00701.propSeikyuDate.labelName}"
							style="#{pc_Xrc00701.propSeikyuDate.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlSeikyuDate" size="10" tabindex="17"
						    disabled="#{pc_Xrc00701.propSeikyuDate.disabled}"
							value="#{pc_Xrc00701.propSeikyuDate.dateValue}">
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>
			<HR noshade width="100%" class="hr">
			
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="確定"
								styleClass="commandExButton_dat" id="register" tabindex="18"
								disabled="#{pc_Xrc00701.propRegister.disabled}"
								action="#{pc_Xrc00701.doRegisterAction}" 
								confirm="#{msg.SY_MSG_0001W}">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除"
								styleClass="commandExButton_dat" id="delete" tabindex="19"
								disabled="#{pc_Xrc00701.propDelete.disabled}"
								action="#{pc_Xrc00701.doDeleteAction1}"
								confirm="#{msg.SY_MSG_0004W}">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア"
								styleClass="commandExButton_etc" id="clear" tabindex="20"
								disabled="#{pc_Xrc00701.propClear.disabled}"
								action="#{pc_Xrc00701.doClearAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden value="#{pc_Xrc00701.propHifList.scrollPosition}"
				id="scroll"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

