<%--
 * 在庫一括引落
 * Xrc01001.jsp
 * 作者: k-matsu<PERSON>


 * 作成日: 2014/05/26
 * version 1.0
 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc01001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc01001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc01001.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc01001.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc01001.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc01001.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				styleClass="outputText" id="htmlMessage"
				value="#{requestScope.DISPLAY_INFO.displayMessage}" escape="false"></h:outputText>
			</FIELDSET>
			<DIV class="head_button_area">　<!-- ↓ここに戻る／閉じるボタンを配置 --></DIV>
			<!--↓content↓-->
			<DIV id="content"><!-- ↓ここにコンポーネントを配置 -->
			<DIV class="column">

			<BR>

			<TABLE width="650" border="0" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
						<TH width="180" class="v_d"><h:outputText styleClass="outputText"
							id="text5" style="#{pc_Xrc01001.propHaihonbi.labelStyle}"
							value="#{pc_Xrc01001.propHaihonbi.labelName}"></h:outputText></TH>
						<TD width="469"><h:inputText styleClass="inputText"
							id="htmlHaihonbi"
							value="#{pc_Xrc01001.propHaihonbi.dateValue}">

							<hx:inputHelperAssist
								errorClass="inputText_Error" promptCharacter="_" />
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="180"><h:outputText
							styleClass="outputText" id="lableCheckList"
							value="#{pc_Xrc01001.propCheckList.labelName}"
							style="#{pc_Xrc01001.propCheckList.labelStyle}"></h:outputText>
						</TH>
						<TD width="469">
							<h:selectManyCheckbox
								disabledClass="selectManyCheckbox_Disabled"
								styleClass="selectManyCheckbox" id="htmlCheckList"
								value="#{pc_Xrc01001.propCheckList.value}"
								layout="pageDirection"
								style="#{pc_Xrc01001.propCheckList.style}">
								<f:selectItems value="#{pc_Xrc01001.propCheckList.list}" />
							</h:selectManyCheckbox>
						</TD>
					</TR>


				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="650" border="0" cellpadding="0" cellspacing="0"
				class="button_bar">
				<TBODY>
					<TR>
						<TD align="center" height="8"><hx:commandExButton type="submit"
							value="実行" styleClass="commandExButton_dat" id="exec"
							action="#{pc_Xrc01001.doExecAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>


			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑--></DIV>
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
