<%-- <!DOCTYPE html PUBLIC "-//W3C//Dtd html 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE html PUBLIC "-//W3C//Dtd html 4.01 Transitional//EN" "http://www.w3.org/tr/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00301.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>

<html>
<head>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="GENERATOR" content="IBM Software Development Platform">
<meta http-equiv="Content-Style-Type" content="text/css">

<title>履修状況表</title>

<link rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<script type="text/javascript"><!--

  var beforeSelectOutputDesignation;
  var beforeSelectGakseiSearchTarget;
     
  //-------------------------------------------
  // 画面ロード時処理.
  //-------------------------------------------
  function loadAction(event){
    var gakseiClass = 'tab_head_off';
    var ikkatsuClass = 'tab_head_off';
    if (document.getElementsByName('form1:htmlOutputDesignation')[1].checked) {
      beforeSelectOutputDesignation = '0';
      gakseiClass = 'tab_head_on'
    } else {
      beforeSelectOutputDesignation = '1';
      ikkatsuClass = 'tab_head_on'
    }
    document.getElementById("form1:gakuseiSitei").className = gakseiClass
    document.getElementById("form1:ikkatsuSitei").className = ikkatsuClass

    var gakseiSearchTarget = document.getElementsByName('form1:htmlGakseiSearchTarget')[1];
    if (undefined === gakseiSearchTarget) {
      beforeSelectGakseiSearchTarget = '0';
    } else if (document.getElementsByName('form1:htmlGakseiSearchTarget')[1].checked) {
      beforeSelectGakseiSearchTarget = '0';
    } else {
      beforeSelectGakseiSearchTarget = '1';
    }
  }

  //-------------------------------------------
  // 学生検索画面呼び出し.
  //-------------------------------------------
  function openGakseiSerachWindow() {
    var url="${pageContext.request.contextPath}/faces/rev/co/";
    var gakseiSearchTarget = document.getElementsByName('form1:htmlGakseiSearchTarget');

    if (gakseiSearchTarget[1].checked) {
      url = url + "pCob0101.jsp"
                + "?retFieldName=form1:htmlGakusekiCd"
                + "&gakuseiSearchKbn=1";
      // 学生検索画面を子画面で開く.
      openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
    } else if (gakseiSearchTarget[2].checked) {
      url = url + "pCob0201.jsp"
                + "?retFieldName=form1:htmlGakusekiCd"
                + "&retFieldName2=form1:htmlHidSotNendo"
                + "&retFieldName3=form1:htmlHidSotGakki"
                + "&gakuseiSearchKbn=1";
      // 卒業生検索画面を子画面で開く.
      openModalWindow(url, "PCob0201", "<%=com.jast.gakuen.rev.co.PCob0201.getWindowOpenOption() %>");
    }
    return false;
  }

  //-------------------------------------------
  // 学生氏名取得処理(非同期).
  //-------------------------------------------
  function getGakuseiNmOnAjax(thisObj, thisEven, targetId) {
    var gaksekiCd = thisObj.value;
    if ("" == gaksekiCd) {
      document.getElementById(targetId).value = '';
      return false;
    }
    
    var gakseiSearchTarget = document.getElementsByName('form1:htmlGakseiSearchTarget');
    var ajaxServlet;
    var args;

    if (gakseiSearchTarget[1].checked) {
      ajaxServlet = "rev/co/CobGakseiAJAX";
      args = {'code1' : gaksekiCd};
    } else if (gakseiSearchTarget[2].checked) {
      ajaxServlet = "rev/co/CobSyutgakshNmAJAX";
      args = {'code1' : gaksekiCd, 'code2' : document.getElementById("form1:htmlHidSotNendo").value, 'code3' : document.getElementById("form1:htmlHidSotGakki").value};
    } else {
      // 何もしない
      return false;
    }
    new AjaxUtil().getCodeName(ajaxServlet, targetId, args);
  }

  //-------------------------------------------
  // 出力指定切り替え.
  //-------------------------------------------
  function switching(button) {
    if (beforeSelectOutputDesignation == button.id) {
      return false;
    }
    beforeSelectOutputDesignation = button.id;
    if ('form1:gakuseiSitei' == button.id) {
      document.getElementsByName("form1:htmlOutputDesignation")[1].checked = true;
      document.getElementById("form1:gakuseiSitei").className = 'tab_head_on'
      document.getElementById("form1:ikkatsuSitei").className = 'tab_head_off'
    } else {
      document.getElementsByName("form1:htmlOutputDesignation")[2].checked = true;
      document.getElementById("form1:gakuseiSitei").className = 'tab_head_off'
      document.getElementById("form1:ikkatsuSitei").className = 'tab_head_on'
    }
    return true;
  }

  //-------------------------------------------
  // 学生検索対象切り替え時処理.
  //-------------------------------------------  
  function gakseiSearchTargetEvent(selectedValue) {
    if (beforeSelectGakseiSearchTarget == selectedValue) {
      return false;
    }
    document.getElementById('form1:htmlGakusekiCd').value = '';
    document.getElementById('form1:htmlGakseiName').value = '';
    beforeSelectGakseiSearchTarget = selectedValue;
  }

  //-------------------------------------------
  // 全て除外ボタン押下時処理.
  //-------------------------------------------
  function onRemoveGakseiAll(){
       var length = document.getElementById("form1:htmlGakseiList").length;
       if (length > 0) {
            return confirm("対象学生一覧をクリアします。よろしいですか？");
       }
       return false;
  }

  //-------------------------------------------
  // 除外ボタン押下時処理.
  //-------------------------------------------
  function onRemoveGaksei(){
       var length = document.getElementById("form1:htmlGakseiList").length;
       if (length > 0) {
            return true;
       }
       return false;
  }
--></script>

</head>
<f:view locale=#{SYSTEM_DATA.locale}>
  <f:loadBundle basename="properties.message" var="msg"/>
  <body onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00301.onPageLoadBegin}">
      <h:form styleClass="form" id="form1">

        <!-- ヘッダーインクルード -->
        <jsp:include page="../inc/header.jsp" />

        <!-- ヘッダーへのデータセット領域 -->
        <div style="display:none;">
          <!-- 閉じるボタン -->
          <hx:commandExButton type="submit" value="閉じる" styleClass="commandExButton" id="closeDisp" action="#{pc_Xrb00301.doCloseDispAction}"/>
          <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00301.funcId}"/>
          <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"/>
          <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00301.screenName}"/>
        </div>

        <!--↓outer↓-->
        <div class="outer">

          <fieldset class="fieldset_err">
            <legend>エラーメッセージ</legend>
            <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" styleClass="outputText" escape="false"/>
          </fieldset>

          <!--↓content↓-->
            <div id="content">

              <div class="column" align="center">

                <%-- 出力指定 --%>
                <table border="0" cellpadding="5" width="870">
                  <tbody>
                    <tr>
                      <td align="left">
                      <table border="0" cellpadding="0" cellspacing="0" width="140">
                        <tbody>
                          <tr height="25">
                            <td>
                              <hx:commandExButton type="submit" value="一括指定" styleClass="tab_head_on" id="ikkatsuSitei" onclick="switching(this);"/>
                            </td>
                            <td>
                              <hx:commandExButton type="submit" value="学生指定" styleClass="tab_head_on" id="gakuseiSitei" onclick="switching(this);"/>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      </td>
                    </tr>
                    <%-- 学生指定 --%>
                    <hx:jspPanel rendered="#{pc_Xrb00301.propOutputDesignation.stringValue == '0'}">
                    <tr>
                      <td>
                        <table border="0" cellpadding="0" cellspacing="0" width="870" class="tab_body">
                          <tbody>
                            <tr>
                              <td width="100%">
                                <center>
                                  <table width="700" class="table">
                                    <tbody>
                                      <tr valign="middle">
                                        <th width="20%" class="v_c">
                                          <h:outputText styleClass="outputText" id="lblFileUpLoad" value="#{pc_Xrb00301.propFileUpLoad.labelName}" style="#{pc_Xrb00301.propFileUpLoad.labelStyle}"/>
                                          <br>
                                          <h:outputText styleClass="outputText" id="lblZenkaiFile" value="#{pc_Xrb00301.propZenkaiFile.labelName}"/>
                                        </th>
                                        <td width="80%" colspan="5">
                                          <%-- ファイル指定(fileupload) --%>
                                          <hx:fileupload styleClass="fileupload" value="#{pc_Xrb00301.propFileUpLoad.value}" id="htmlFileUpLoad" style="width: 90%;">
                                            <hx:fileProp name="fileName" value="#{pc_Xrb00301.propFileUpLoad.fileName}" />
                                            <hx:fileProp name="contentType" />
                                          </hx:fileupload>
                                          <%-- 取込(commandExButton) --%>
                                          <hx:commandExButton type="submit" value="取込" styleClass="commandExButton" id="load" action="#{pc_Xrb00301.doLoadAction}"/>
                                          <br>
                                          <%-- 前回ファイル(outputText) --%>
                                          <h:outputText styleClass="outputText" id="htmlZenkaiFile" value="#{pc_Xrb00301.propZenkaiFile.stringValue}"/>
                                        </td>
                                      </tr>
                                      <tr valign="middle">
                                        <th class="v_e">
                                          <h:outputText styleClass="outputText" id="lblGakusekiCd" value="#{pc_Xrb00301.propGakusekiCd.labelName}"/>
                                        </th>
                                        <td style="border-right-style: none; border-left-style: none;">
                                          <%-- 学生検索対象 --%>
                                          <h:selectOneRadio
                                            disabledClass="selectOneRadio_Disabled"
                                            styleClass="selectOneRadio"
                                            id="htmlGakseiSearchTarget"
                                            value="#{pc_Xrb00301.propGakseiSearchTarget.stringValue}"
                                            onclick="gakseiSearchTargetEvent(this.value)">
                                              <f:selectItem itemValue="0" itemLabel="在学生" />
                                              <f:selectItem itemValue="1" itemLabel="出学者" />
                                          </h:selectOneRadio>
                                        </td>
                                        <td style="border-right-style: none; border-left-style: none;">
                                          <%-- 学籍コード(inputText) --%>
                                          <h:inputText 
                                            styleClass="inputText"
                                            id="htmlGakusekiCd"
                                            size="11"
                                            maxlength="10"
                                            value="#{pc_Xrb00301.propGakusekiCd.stringValue}" 
                                            style="margin-left:5px; ime-mode:disabled"
                                            onblur="return getGakuseiNmOnAjax(this, event, 'form1:htmlGakseiName');"/>
                                          <%-- 検索(commandExButton) --%>
                                          <hx:commandExButton 
                                            type="button" 
                                            value="検索" 
                                            styleClass="commandExButton_search"
                                            id="search"
                                            onclick="return openGakseiSerachWindow();"/>
                                        </td>
                                        <td style="border-right-style: none; border-left-style: none;">
                                          <%-- 追加(commandExButton) --%>
                                          <hx:commandExButton
                                            type="submit"
                                            value="追加"
                                            styleClass="commandExButton"
                                            id="add"
                                            action="#{pc_Xrb00301.doAddAction}"/>
                                        </td>
                                        <td style="border-left-style: none;">
                                          <%-- 学生氏名(outputText) --%>
                                          <h:inputText 
                                            styleClass="likeOutput"
                                            style="margin-left:5px"
                                            id="htmlGakseiName"
                                            size="40"
                                            tabindex="-1"
                                            readonly="true"
                                            value="#{pc_Xrb00301.propGakseiName.stringValue}"/>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </center>

                                <br>

                                <center>
                                  <table width="500">
                                    <tbody>
                                      <tr>
                                        <td colspan="2" align="left">
                                          <span style="font-size: 9pt">対象学生一覧</span>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="410">
                                          <%-- 対象学生一覧(selectManyListbox) --%>
                                          <h:selectManyListbox 
                                            styleClass="selectManyListbox"
                                            id="htmlGakseiList"
                                            size="10"
                                            value="#{pc_Xrb00301.propGakseiList.integerValue}"
                                            style="width: 100%">
                                              <f:selectItems value="#{pc_Xrb00301.propGakseiList.list}"/>
                                          </h:selectManyListbox>
                                        </td>
                                        <td valign="top" align="center" width="90">
                                          <%-- 除外(commandExButton) --%>
                                          <hx:commandExButton 
                                            styleClass="commandExButton"
                                            id="remove"
                                            type="submit"
                                            value="除外"
                                            action="#{pc_Xrb00301.doRemoveAction}"
                                            style="width: 60px"
                                            onclick="return onRemoveGaksei();"/>
                                          <br>
                                          <span style="font-size: 9pt">(複数選択可）</span>
                                          <br>
                                          <%-- 全て除外(commandExButton) --%>
                                          <hx:commandExButton
                                            styleClass="commandExButton"
                                            id="removeAll"
                                            type="submit"
                                            value="全て除外"
                                            action="#{pc_Xrb00301.doRemoveAllAction}"
                                            style="width: 60px"
                                            onclick="return onRemoveGakseiAll();"/>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="410" align="right">
                                          <%-- 合計件数(outputFormat) --%>
                                          <h:outputFormat styleClass="outputFormat" value="合計件数：{0}件　正常件数：{1}件　エラー件数：{2}件">
                                            <f:param name="maxCount" value="#{pc_Xrb00301.propGakseiList.listCount}" />
                                            <f:param name="nomalCount" value="#{pc_Xrb00301.propGakseiList.listCount - pc_Xrb00301.propErrorCount.integerValue}" />
                                            <f:param name="errCount" value="#{pc_Xrb00301.propErrorCount.integerValue}" />
                                          </h:outputFormat>
                                        </td>
                                      </tr>
                                      <tr></tr>
                                    </tbody>
                                  </table>
                                </center>
                                <br>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                    </hx:jspPanel>

                    <%-- 一括指定 --%>
                    <hx:jspPanel rendered="#{pc_Xrb00301.propOutputDesignation.stringValue == '1'}">
                    <tr>
                      <td>
                        <table  border="0" cellpadding="0" cellspacing="0" width="870" class="tab_body">
                          <tbody>
                            <tr>
                              <td width="100%" valign="top" height="44">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0"  class="table">
                                  <tbody>
                                    <tr align="center" valign="middle">
                                      <th class="v_a" width="20%">
                                        <%-- 入学年度学期範囲指定(outputText) --%>
                                        <h:outputText 
                                          styleClass="outputText"
                                          id="lblNyugakNendoGakkiRenge"
                                          value="#{pc_Xrb00301.propNyugakNendoGakkiRenge.labelName}"
                                          style="#{pc_Xrb00301.propNyugakNendoGakkiRenge.labelStyle}"
                                          escape="false">
                                        </h:outputText>
                                      </th>
                                      <td width="*" valign="bottom">
                                        <%-- 入学年度FROM(inputText) --%>
                                        <h:inputText
                                          styleClass="inputText"
                                          id="htmlNyugakNendFrom"
                                          value="#{pc_Xrb00301.propNyugakNendFrom.integerValue}"
                                          disabled="#{pc_Xrb00301.propNyugakNendFrom.disabled}"
                                          style="#{pc_Xrb00301.propNyugakNendFrom.style}"
                                          size="10">
                                            <f:convertNumber type="number" pattern="###0"/>
                                            <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_"/>
                                        </h:inputText>
                                        <%-- 入学学期FROM(inputText) --%>
                                        <h:inputText
                                          styleClass="inputText"
                                          id="htmlNyugakGakkiNoFrom"
                                          value="#{pc_Xrb00301.propNyugakGakkiNoFrom.integerValue}"
                                          disabled="#{pc_Xrb00301.propNyugakGakkiNoFrom.disabled}"
                                          style="#{pc_Xrb00301.propNyugakGakkiNoFrom.style}"
                                          size="10">
                                            <f:convertNumber type="number" pattern="#0"/>
                                            <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_"/>
                                        </h:inputText>
                                        <span>～</span>
                                        <%-- 入学年度TO(inputText) --%>
                                        <h:inputText 
                                          styleClass="inputText"
                                          id="htmlNyugakNendTo"
                                          size="10"
                                          disabled="#{pc_Xrb00301.propNyugakNendTo.disabled}"
                                          value="#{pc_Xrb00301.propNyugakNendTo.integerValue}"
                                          style="#{pc_Xrb00301.propNyugakNendTo.style}">
                                            <f:convertNumber type="number" pattern="###0"/>
                                            <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_"/>
                                        </h:inputText>
                                        <%-- 入学学期TO(inputText) --%>
                                        <h:inputText 
                                          styleClass="inputText"
                                          id="htmlNyugakGakkiNoTo"
                                          size="10"
                                          disabled="#{pc_Xrb00301.propNyugakGakkiNoTo.disabled}"
                                          value="#{pc_Xrb00301.propNyugakGakkiNoTo.integerValue}"
                                          style="#{pc_Xrb00301.propNyugakGakkiNoTo.style}">
                                          <f:convertNumber type="number" pattern="#0"/>
                                          <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_"/>
                                        </h:inputText>
                                      </td>
                                    </tr>
                                    <tr align="center" valign="middle">
                                      <th nowrap class="v_a" width="20%">
                                        <%-- 就学種別(outputText) --%>
                                        <h:outputText 
                                          styleClass="outputText"
                                          id="lblSyugakSbt"
                                          value="#{pc_Xrb00301.propSyugakSbt.labelName}"
                                          style="#{pc_Xrb00301.propSyugakSbt.labelStyle}">
                                        </h:outputText>
                                      </th>
                                      <td width="*">
                                        <%-- 就学種別(selectManyCheckbox) --%>
                                        <h:selectManyCheckbox 
                                          styleClass="selectManyCheckbox"
                                          id="htmlSyugakSbt"
                                          value="#{pc_Xrb00301.propSyugakSbt.value}"
                                          disabled="#{pc_Xrb00301.propSyugakSbt.disabled}">
                                          <f:selectItems value="#{pc_Xrb00301.propSyugakSbt.list}"/>
                                        </h:selectManyCheckbox>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                    </hx:jspPanel>
                  </tbody>
                </table>

                <br>

                <%-- 履修状況表エリア --%>
                <table class="table" border="0" cellpadding="5" width="870" style="margin-top: 10px">
                  <tbody>
                    <tr align="center" valign="middle">
                      <th class="v_a" width="20%">
                        <%-- 履修状況表(outputText) --%>
                        <h:outputText 
                          styleClass="outputText"
                          id="lblCourseSttsTable"
                          value="#{pc_Xrb00301.propCourseSttsTable.labelName}"
                          style="#{pc_Xrb00301.propCourseSttsTable.labelStyle}"
                          escape="false">
                        </h:outputText>
                      </th>
                      <td width="*">
                        <%-- 履修状況表(selectBooleanCheckbox) --%>
                        <h:selectBooleanCheckbox
                          styleClass="selectBooleanCheckbox"
                          id="htmlCourseSttsTable"
                          value="#{pc_Xrb00301.propCourseSttsTable.checked}"
                          disabled="#{pc_Xrb00301.propCourseSttsTable.disabled}"
                          style="#{pc_Xrb00301.propCourseSttsTable.style}"/>
                        <span>履修状況表(</span>
                        <%-- 履修登録が完了した学生のみ(selectBooleanCheckbox) --%>
                        <h:selectBooleanCheckbox
                          styleClass="selectBooleanCheckbox"
                          id="htmlCourseComp"
                          value="#{pc_Xrb00301.propCourseComp.checked}"
                          disabled="#{pc_Xrb00301.propCourseComp.disabled}"
                          style="#{pc_Xrb00301.propCourseComp.style}"/>
                        <span>履修登録が完了した学生のみ)</span>
                        <br>
                        <%-- 教育実習辞退者の表示(selectBooleanCheckbox) --%>
                        <h:selectBooleanCheckbox
                          styleClass="selectBooleanCheckbox"
                          id="htmlCchngPrctcRef"
                          value="#{pc_Xrb00301.propTchngPrctcRef.checked}"
                          disabled="#{pc_Xrb00301.propTchngPrctcRef.disabled}"
                          style="#{pc_Xrb00301.propTchngPrctcRef.style}"/>
                        <span>教育実習辞退者の表示</span>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <%-- バーコードシールエリア --%>
                <table class="table" border="0" cellpadding="5" width="870" style="margin-top: 10px">
                  <tbody>
                    <tr align="center" valign="top">
                      <th rowspan="2" class="v_a" width="20%">
                        <%-- バーコードシール(outputText) --%>
                        <h:outputText 
                          styleClass="outputText"
                          id="lblBarCodeSeal"
                          value="#{pc_Xrb00301.propBarCodeSeal.labelName}"
                          style="#{pc_Xrb00301.propBarCodeSeal.labelStyle}"
                          escape="false">
                        </h:outputText>
                      </th>
                      <td width="*">
                        <%-- バーコードシール(selectBooleanCheckbox) --%>
                        <h:selectBooleanCheckbox
                          styleClass="selectBooleanCheckbox"
                          id="htmlBarCodeSeal"
                          value="#{pc_Xrb00301.propBarCodeSeal.checked}"
                          disabled="#{pc_Xrb00301.propBarCodeSeal.disabled}"
                          style="#{pc_Xrb00301.propBarCodeSeal.style}"/>
                        <span>バーコードシール</span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div style="margin-left:15px">
                          <%-- バーコード出力対象(selectOneRadio) --%>
                          <h:selectOneRadio
                            disabledClass="selectOneRadio_Disabled"
                            styleClass="selectOneRadio"
                            id="htmlOutputTargetGaksei"
                            value="#{pc_Xrb00301.propBarcodeOutputTarget.stringValue}">
                            <f:selectItem itemValue="0" itemLabel="新入生"/>
                            <f:selectItem itemValue="1" itemLabel="在校生"/>
                            <f:selectItem itemValue="2" itemLabel="再交付"/>
                          </h:selectOneRadio>
                          <%-- 次年次の出力(selectBooleanCheckbox) --%>
                          <h:selectBooleanCheckbox
                            styleClass="selectBooleanCheckbox"
                            id="htmlNextYearOutput"
                            value="#{pc_Xrb00301.propNextYearOutput.checked}"
                            disabled="#{pc_Xrb00301.propNextYearOutput.disabled}"
                            style="#{pc_Xrb00301.propNextYearOutput.style}"/>
                          <span>次年次の出力</span>
                        </div>
                      </td>
                    </tr>
                    <tr align="center" valign="middle">
                      <th class="v_a" width="20%">
                        <%-- 配本日付範囲指定(outputText) --%>
                        <h:outputText 
                          styleClass="outputText"
                          id="lblHaihonDate"
                          value="#{pc_Xrb00301.propHaihonDate.labelName}"
                          style="#{pc_Xrb00301.propHaihonDate.labelStyle}"
                          escape="false">
                        </h:outputText>
                      </th>
                      <td width="*" valign="bottom">
                        <%-- 配本日付FROM(inputText) --%>
                        <h:inputText
                          styleClass="inputText"
                          id="htmlHaihonDateFrom"
                          value="#{pc_Xrb00301.propHaihonDateFrom.dateValue}"
                          disabled="#{pc_Xrb00301.propHaihonDateFrom.disabled}"
                          style="#{pc_Xrb00301.propHaihonDateFrom.style}"
                          size="10">
						  <f:convertDateTime />
						  <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						  <hx:inputHelperDatePicker />
                        </h:inputText>
                        <span>～</span>
                        <%-- 配本日付TO(inputText) --%>
                        <h:inputText 
                          styleClass="inputText"
                          id="htmlHaihonDateTo"
                          size="10"
                          disabled="#{pc_Xrb00301.propHaihonDateTo.disabled}"
                          value="#{pc_Xrb00301.propHaihonDateTo.dateValue}"
                          style="#{pc_Xrb00301.propHaihonDateTo.style}">
						  <f:convertDateTime />
						  <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						  <hx:inputHelperDatePicker />
                        </h:inputText>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <%-- 履修科目届エリア --%>
                <table class="table" border="0" cellpadding="5" width="870" style="margin-top: 10px">
                  <tbody>
                    <tr align="center" valign="middle">
                      <th class="v_a" width="20%">
                        <%-- 履修科目届(outputText) --%>
                        <h:outputText 
                          styleClass="outputText"
                          id="lblCourseKmk"
                          value="#{pc_Xrb00301.propCourseKmk.labelName}"
                          style="#{pc_Xrb00301.propCourseKmk.labelStyle}"
                          value="履修科目届"
                          escape="false">
                        </h:outputText>
                      </th>
                      <td width="*">
                        <%-- 履修科目届(selectBooleanCheckbox) --%>
                        <h:selectBooleanCheckbox
                          styleClass="selectBooleanCheckbox"
                          id="htmlCourseKmk"
                          value="#{pc_Xrb00301.propCourseKmk.checked}"
                          disabled="#{pc_Xrb00301.propCourseKmk.disabled}"
                          style="#{pc_Xrb00301.propCourseKmk.style}"/>
                        <span>履修科目届(</span>
                        <%-- ＷＥＢ履修を希望しない学生のみ(selectBooleanCheckbox) --%>
                        <h:selectBooleanCheckbox
                          styleClass="selectBooleanCheckbox"
                          id="htmlWebCourseShinai"
                          value="#{pc_Xrb00301.propWebCourseShinai.checked}"
                          disabled="#{pc_Xrb00301.propWebCourseShinai.disabled}"
                          style="#{pc_Xrb00301.propWebCourseShinai.style}"/>
                        <span>ＷＥＢ履修を希望しない学生のみ)</span>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <%-- その他エリア --%>
                <table class="table" border="0" cellpadding="5" width="870" style="margin-top: 10px">
                  <tbody>
                    <tr align="center" valign="middle">
                      <th class="v_a" width="20%">
                        <%-- その他(outputText) --%>
                        <h:outputText 
                          styleClass="outputText"
                          id="lblOther"
                          value="#{pc_Xrb00301.propOther.labelName}"
                          style="#{pc_Xrb00301.propOther.labelStyle}"
                          escape="false">
                        </h:outputText>
                      </th>
                      <td width="*">
                        <%-- 本学への再入学者および他大学(短期大学)退学者(selectBooleanCheckbox) --%>
                        <h:selectBooleanCheckbox
                          styleClass="selectBooleanCheckbox"
                          id="htmlOther"
                          value="#{pc_Xrb00301.propOther.checked}"
                          disabled="#{pc_Xrb00301.propOther.disabled}"
                          style="#{pc_Xrb00301.propOther.style}"/>
                        <span>「本学への再入学者および他大学(短期大学)退学者」</span>
                      </td>
                    </tr>
                  </tbody>
                </table>

                <br>

                <%-- ボタンエリア --%>
                <table border="0" width="100%" class="button_bar">
                  <tbody>
                    <tr>
                      <td>
                        <%-- 印刷(commandExButton) --%>
                        <hx:commandExButton
                          type="submit"
                          value="印刷"
                          styleClass="commandExButton_out"
                          id="print"
                          confirm="#{msg.SY_MSG_0022W}"
                          action="#{pc_Xrb00301.printAction}"/>
                        <%-- PDF作成(commandExButton) --%>
                        <hx:commandExButton
                          type="submit"
                          value="PDF作成"
                          styleClass="commandExButton_out"
                          id="makePfd"
                          action="#{pc_Xrb00301.makePdfAction}"
                          confirm="#{msg.SY_MSG_0019W}"/>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
          </div>
          <!--↑content↑-->

          <!-- Hidden項目群 -->
          <h:inputHidden id="htmlHidSotNendo"/>
          <h:inputHidden id="htmlHidSotGakki"/>
          <h:selectOneRadio
            disabledClass="selectOneRadio_Disabled"
            styleClass="selectOneRadio"
            style="display:none;"
            id="htmlOutputDesignation"
            value="#{pc_Xrb00301.propOutputDesignation.stringValue}">
            <f:selectItem itemValue="0" itemLabel="学生"/>
            <f:selectItem itemValue="1" itemLabel="一括"/>
          </h:selectOneRadio>
        </div>
        <!--↑outer↑-->

        <!-- フッダーインクルード -->
        <jsp:include page ="../inc/footer.jsp"/>
      </h:form>
    </hx:scriptCollector>
  </body>
  <jsp:include page="../inc/common.jsp"/>
</f:view>
</html>