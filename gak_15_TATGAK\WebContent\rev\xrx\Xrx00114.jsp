<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00114.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<style type="text/css">
<!--
.extendCommandExButton {
  height: 17px;
  width: 17px;
  border-left-width: 1px;
  border-right-width: 1px;
  border-bottom-width: 1px;
  border-top-width: 1px;
  cursor:pointer;
  background-color: #DDEAFF;
  color: #000000;
  text-align:center;
  border-bottom-color: #004d99;
  border-top-color: #C0C0FF; 
  border-left-color: #C0C0FF;
  border-right-color: #004d99;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-style: solid;
  border-right-style: solid;
  background-repeat:no-repeat;
  margin:0px 1px 0px 1px;
}
 -->
</style>
<f:subview id="Xrx00114">
  <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00114.onPageLoadBegin}">
    <%-- ↓ コンテンツ部 ↓ --%>
    <hx:jspPanel>
      <DIV style="width:870px">
        <TABLE width="100%" cellspacing="0" cellpadding="0" border="0">
          <TBODY>
            <TR>
              <TD width="574">
                <TABLE class="table" cellspacing="0" cellpadding="0" border="0" width="100%">
                  <TBODY>
                    <TR>
                      <!-- 自由設定対象(Label) -->
                      <TH nowrap class="v_c" width="174">
                        <h:outputText 
                          styleClass="outputText"
                          id="lblFreTsyCd"
                          value="#{pc_Xrx00114.propFreTsyCd.labelName}"
                          style="#{pc_Xrx00114.propFreTsyCd.labelStyle}"/>
                      </TH>
                      <!-- 自由設定対象(Select) -->
                      <TD width="400">
                        <h:selectOneMenu
                          styleClass="selectOneMenu"
                          id="htmlFreTsyCd"
                          disabled="#{pc_Xrx00114.propFreTsyCd.disabled}"
                          value="#{pc_Xrx00114.propFreTsyCd.value}"
                          style="width:400px;">
                          <f:selectItems value="#{pc_Xrx00114.propFreTsyCd.list}" />
                        </h:selectOneMenu>
                      </TD>
                    </TR>
                  </TBODY>
                </TABLE>
              </TD>
              <TD width="*">
                <TABLE width="100%">	
                  <TBODY>
                    <TR>
                      <TD align="left">
                        <SPAN style="margin-left:5px">
                          <!-- 選択ボタン -->
                          <hx:commandExButton
                            type="submit"
                            value="選択"
                            styleClass="commandExButton"
                            id="select"
                            disabled="#{pc_Xrx00114.propSelect.disabled}"
                            action="#{pc_Xrx00114.doSelectAction}">
                          </hx:commandExButton>
                        </SPAN>
                        <SPAN style="margin-left:5px">
                          <!-- 解除ボタン -->
                          <hx:commandExButton
                            type="submit"
                            value="解除"
                            styleClass="commandExButton" id="unselect"
                            disabled="#{pc_Xrx00114.propUnselect.disabled}"
                            action="#{pc_Xrx00114.doUnselectAction}">
                          </hx:commandExButton>
                        </SPAN>
                      </TD>
                    </TR>
                  </TBODY>
                </TABLE>
              </TD>
            </TR>
          </TBODY>
        </TABLE>

        <BR>

        <c:if test="${pc_Xrx00114.propKmiSnfr.listCount != 0}">
        <TABLE width="100%" cellspacing="0" cellpadding="0" border="0">
          <TBODY>
            <TR>
              <TD width="324">
                <TABLE class="table" cellspacing="0" cellpadding="0" border="0" width="100%">
                  <TBODY>
                    <TR>
                      <!-- データNo(Label) -->
                      <TH nowrap class="v_a" width="174">
                        <h:outputText styleClass="outputText" id="lblListNo" value="#{pc_Xrx00114.propListNo.labelName}"/>
                      </TH>
                      <TD width="*">
                        <!-- ｜＜ボタン -->
                        <hx:commandExButton
                          type="submit"
                          styleClass="extendCommandExButton" 
                          id="btnHead" value="#{pc_Xrx00114.propToHead.name}"
                          disabled="#{pc_Xrx00114.propToHead.disabled}"
                          action="#{pc_Xrx00114.doBtnHeadAction}">
                        </hx:commandExButton>
                        <!-- ＜ボタン -->
                        <hx:commandExButton
                          type="submit"
                          styleClass="extendCommandExButton"
                          id="btnBack"
                          value="#{pc_Xrx00114.propBack.name}"
                          disabled="#{pc_Xrx00114.propBack.disabled}"
                          action="#{pc_Xrx00114.doBtnBackAction}">
                        </hx:commandExButton>
                        <!-- データNoカレントページ(Text) -->
                        <h:inputText styleClass="inputText"
                          id="htmlInputNo"
                          value="#{pc_Xrx00114.propListNo.stringValue}"
                          readonly="#{pc_Xrx00114.propListNo.readonly}"
                          maxlength="#{pc_Xrx00114.propListNo.maxLength}" 
                          style="#{pc_Xrx00114.propListNo.style}" size="3">
                        </h:inputText>
                        <!-- ＞ボタン -->
                        <hx:commandExButton
                          type="submit"
                          value="#{pc_Xrx00114.propForword.name}"
                          styleClass="extendCommandExButton"
                          id="btnForword"
                          disabled="#{pc_Xrx00114.propForword.disabled}"
                          action="#{pc_Xrx00114.doBtnForwordAction}">
                        </hx:commandExButton>
                        <!-- ＞｜ボタン -->
                        <hx:commandExButton
                          type="submit"
                          styleClass="extendCommandExButton"
                          id="btnEnd"
                          value="#{pc_Xrx00114.propToEnd.name}"
                          disabled="#{pc_Xrx00114.propToEnd.disabled}"
                          action="#{pc_Xrx00114.doBtnEndAction}">
                        </hx:commandExButton>
                      </TD>
                    </TR>
                  </TBODY>
                </TABLE>
              </TD>
              <TD width="150">
                <!-- データNoジャンプ先(Text) -->
                <h:inputText
                  styleClass="inputText"
                  id="htmlJumpInputNo"
                  value="#{pc_Xrx00114.propJumpListNo.stringValue}"
                  disabled="#{pc_Xrx00114.propJumpListNo.disabled}"
                  maxlength="#{pc_Xrx00114.propJumpListNo.maxLength}"
                  style="#{pc_Xrx00114.propJumpListNo.style}"
                  size="3">
                </h:inputText>
                <hx:commandExButton
                  type="submit"
                  styleClass="commandExButton"
                  id="jump"
                  value="ジャンプ"
                  disabled="#{pc_Xrx00114.propJumpListNo.disabled}"
                  action="#{pc_Xrx00114.doListNoAction}">
                </hx:commandExButton>
              </TD>  
              <TD width="*">
                <DIV align="right">
                  <h:outputText styleClass="outputText" id="lblSearchedNoTitle" style="font-size: 8pt" value="データNo登録件数"/>
                  <h:outputText styleClass="outputText" id="lblSearchedNo" style="font-size: 8pt" value="#{pc_Xrx00114.propSearchedNo.stringValue}"/>
                  <h:outputText styleClass="outputText" id="lblSearchedNoCnt" style="font-size: 8pt" value="件"/>
                </DIV>
              </TD>  
            </TR>
          </TBODY>
        </TABLE>

        <!-- 検索結果一覧 -->
        <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
          <TBODY>
            <TR>
              <TD align="right" nowrap class="outputText" width="100%">
              <h:outputText styleClass="outputText" id="lblListCnt" value="#{pc_Xrx00114.propKmiSnfr.listCount}"></h:outputText>件</TD>
            </TR>
            <TR>
              <TD>
              <DIV class="list">
                <h:dataTable 
                  border="1"
                  cellpadding="2"
                  cellspacing="0"
                  headerClass="headerClass"
                  footerClass="footerClass"
                  columnClasses="columnClass1"
                  rowClasses="#{pc_Xrx00114.propKmiSnfr.rowClasses}"
                  styleClass="meisai_scroll" id="table1"
                  value="#{pc_Xrx00114.propKmiSnfr.list}"
                  var="varlist"
                  style="text-align: center">
                  <!-- 項目ＮＯ -->
                  <h:column id="column1">
                    <f:facet name="header">
                      <h:outputText styleClass="outputText" id="htmlFreKomokNo" value="#{pc_Xrx00114.propFreKomokNo.labelName}"/>
                    </f:facet>
                    <h:outputText styleClass="outputText" id="lblFreKomokNo" value="#{varlist.freKomokNo}"/>
                    <f:attribute value="130" name="width" />
                    <f:attribute value="center" name="align" />
                    <f:attribute value="middle" name="valign" />
                    <f:attribute value="text-align: right; vertical-align: middle" name="style" />
                  </h:column>
                  <!-- 自由設定項目 -->
                  <h:column id="column2">
                    <f:facet name="header">
                      <h:outputText styleClass="outputText" id="htmlFreKomokName" value="#{pc_Xrx00114.propFreKomokName.labelName}"/>
                    </f:facet>
                    <h:outputText styleClass="outputText" id="lblFreKomokName" value="#{varlist.freKomokName}"/>
                    <f:attribute value="230" name="width" />
                    <f:attribute value="text-align: left; vertical-align: middle" name="style" />
                  </h:column>
                  <!-- 内容(全512) -->
                  <h:column id="column3">
                    <f:facet name="header">
                      <h:outputText 
                        styleClass="outputText"
                        id="htmlFreValue"
                        value="#{pc_Xrx00114.propFreValue.labelName}"
                        style="#{pc_Xrx00114.propFreValue.labelStyle}"/>
                    </f:facet>
                    <h:inputTextarea
                      styleClass="inputTextarea"
                      id="htmlFreValueArea"
                      value="#{varlist.freValue}"
                      style="#{pc_Xrx00114.propFreValue.style}; height: 28px; width :100% "
                      readonly="#{pc_Xrx00114.propFreValue.readonly}">
                    </h:inputTextarea>
                    <f:attribute value="500" name="width"/>
                    <f:attribute value="text-align: right" name="style" />
                  </h:column>
                </h:dataTable>
              </div>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        </c:if>
      </DIV>
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
  </hx:scriptCollector>
</f:subview>
