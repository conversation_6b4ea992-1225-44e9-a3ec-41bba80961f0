<%-- 
	教育実習登録（基本）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrd/Xrd00102T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript" src="../../rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT type="text/javascript">


window.attachEvent("onload", attachFormatNumber);

function loadAction(event){
// 画面ロード時の学生名称再取得
  scrollEvent();
  doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:htmlGakuseiName');
}

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlgT01 = document.getElementById("form1:htmlHidChangeDataFlgT01").value;
	if(changeDataFlgT01 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT02 = document.getElementById("form1:htmlHidChangeDataFlgT02").value;
	if(changeDataFlgT02 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT03 = document.getElementById("form1:htmlHidChangeDataFlgT03").value;
	if(changeDataFlgT03 == "1"){
	  return confirm(id);
	}
	windowOnscroll();
	return true;
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlgT01").value = "1";
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  if (confirm(messageCreate(id, args))) {
  	  return true;
  }
  windowOnscroll();
  return false;
}

function doGakuseiAjax(thisObj, thisEvent) {
    // 学生名称を取得する
    var servlet = "rev/co/CobGakseiAJAX";
    var args = new Array();
    args['code1'] = thisObj.value;
    args['code2'] = "";
    args['code3'] = "";
    var target = "form1:htmlGakuseiName";
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, target, args);
}

function openGakusekiCdWindow(thisObj, thisEvent) {
    // 学生検索画面（引数：なし）
    var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakusekiCd";
    openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
    return true;
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
	var action = document.getElementById("form1:htmlHidAction").value;
	indirectClick(action);
	return true;
}

// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById('form1:htmlExecutable1').value = "0";
	document.getElementById('form1:htmlExecutable2').value = "0";
	document.getElementById('form1:htmlExecutable3').value = "0";
	document.getElementById('form1:htmlHidAction').value = "0";
	document.getElementById('form1:htmlGakuhiHidden').value = "0";
	document.getElementById('form1:htmlSotSinseiHidden').value = "0";
	document.getElementById('form1:htmlGakTeishiHidden').value = "0";
	document.getElementById('form1:htmlZaisekiKgnHidden').value = "0";
	document.getElementById('form1:htmlRsyuNenjiHidden').value = "0";
	document.getElementById('form1:htmlJukoCheckHidden').value = "0";
	document.getElementById('form1:htmlTrokKbnCheckHidden').value = "0";
	
	return false;
}

// 項目制御(活性非活性)
function setDesable(thisObj, thisEvent) {
	var value = document.getElementById('form1:htmlPay').value;
	windowOnscroll();
	if( value ){	
		document.getElementById('form1:htmlDesable').click();
	}
}

function windowOnscroll(){
// スクロールポジション取得
    var scrollTop =
        document.documentElement.scrollTop;
        document.getElementById('form1:htmlScrollPosition').value = scrollTop;
}

function scrollEvent(){
// スクロールポジション指定
	var scrollHidden = document.getElementById('form1:htmlScrollPosition').value;
	window.scrollTo(0,scrollHidden);
}	


</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<f:loadBundle basename="properties.messageCO" var="msgCO" />

	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrd00102T01.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Xrd00102T01" property="xrd00102">
	
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrd00102T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrd00102T01.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrd00102T01.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
				<hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" 
				onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');"
				action="#{pc_Xrd00102T01.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="950">
						<!-- 共通  -->
  						<TABLE class="table" border="0" cellpadding="0" cellspacing="0" width="100%" >
							<TBODY>
								<TR>
								<!-- 実習年度 --> 
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblJissyuNendo_head"
										style="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.labelStyle}"
										value="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.labelName}"></h:outputText></TH>
									<TD width="670"><h:inputText styleClass="inputText"
										id="htmlJissyuNendo"
										disabled="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.disabled}"
										value="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.dateValue}"
										size="4">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>
									<TD rowspan="4" style="background-color: transparent; text-align: right; vertical-align: bottom"
										class="clear_border">
										<hx:commandExButton
											type="submit" styleClass="commandExButton" id="select"
											value="選択" disabled="#{pc_Xrd00102T01.xrd00102.propSelect.disabled}"
											action="#{pc_Xrd00102T01.xrd00102.doSelectAction}"
											onclick="return windowOnscroll();"></hx:commandExButton> <hx:commandExButton
											type="submit" value="解除" styleClass="commandExButton"
											id="unselect"
											onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');" 
											action="#{pc_Xrd00102T01.xrd00102.doUnselectAction}"
											disabled="#{pc_Xrd00102T01.xrd00102.propUnSelect.disabled}"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
								<!-- 学籍番号 -->
	                                <TH width="180" class="v_a"><h:outputText
	                                   styleClass="outputText" id="lblGakusekiCd"
	                                   value="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.labelName}"
	                                   style="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.labelStyle}"></h:outputText></TH>
	                                <TD width="670"><h:inputText styleClass="inputText"
	                                    id="htmlGakusekiCd" size="18"
	                                    value="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.stringValue}"
	                                    maxlength="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.maxLength}"
	                                    disabled="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.disabled}"
	                                    style="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.style}"
	                                    onblur="return doGakuseiAjax(this, event);"></h:inputText>
	                                    <hx:commandExButton type="button" styleClass="commandExButton_search"
	                                        id="gakusekiSearch"
	                                        onclick="return openGakusekiCdWindow(this, event);"
	                                        disabled="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.disabled}"></hx:commandExButton>
	                                    <h:outputText styleClass="outputText" id="htmlGakuseiName"></h:outputText>
	                                 </TD>
	                            </TR>
								<TR>
								<!-- 校種区分 --> 
									<TH nowrap class="v_b" width="180"><h:outputText
										styleClass="outputText" id="lblKosyuKbn"
										style="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.labelStyle}"
										value="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.labelName}"></h:outputText></TH>
									<TD width="670"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlKosyuKbnList"
										disabled="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.disabled}"
										value="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.value}">
										<f:selectItems value="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.list}" />
										</h:selectOneMenu></TD>
								</TR>
								<TR>
								<!-- 登録区分 --> 
									<TH nowrap class="v_c" width="180"><h:outputText
										styleClass="outputText" id="lblTorokKbn"
										style="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.labelStyle}"
										value="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.labelName}"></h:outputText></TH>
									<TD nowrap width="670"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlTorokKbnRadio"
										disabled="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.disabled}"
										value="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.value}"
										disabled="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.disabled}">
										<f:selectItems value="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.list}" />
										</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>	
						<!-- タブ -->
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" align="left"
											style="border-bottom-style: none; ">
											<TBODY>
												<TR>
												<TD width="150px" ><hx:commandExButton 
													type="button" styleClass="tab_head_on" id="tabXrd00102T01" style="width:100%"
													value="基本情報" ></hx:commandExButton></TD>
												<TD width="150px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabXrd00102T02" style="width:100%"
													value="事前レポート" 
													action="#{pc_Xrd00102T01.doTabXrd00102T02Action}"
													disabled="#{pc_Xrd00102T01.xrd00102.propRptBtn.disabled}"></hx:commandExButton></TD>
												<TD width="150px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabXrd00102T03" style="width:100%"
													value="日誌" 
													action="#{pc_Xrd00102T01.doTabXrd00102T03Action}"
													disabled="#{pc_Xrd00102T01.xrd00102.propNissiBtn.disabled}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" 
										width="100%" style="border-top-style: none; ">
										<TR>
											<TD align="center" width="100%">
											<div style="height: 690px"><BR>
											<TABLE class="table" width="822">
												<TBODY>
												</TBODY>
											</TABLE>
											<TABLE class="table" width="822">
												<TBODY>
 													<TR>
													<!-- 社会福祉施設体験期間 --> 
														<TH nowrap class="v_e" width="198"><h:outputText
															styleClass="outputText" id="lblSisetuTaikenKikan"
															value="#{pc_Xrd00102T01.propSisetuTaikenKikan.labelName}"></h:outputText></TH>
														<TD><h:outputText
															styleClass="outputText" id="htmlSisetuTaikenKikan"
															value="#{pc_Xrd00102T01.propSisetuTaikenKikan.value}">
														</h:outputText></TD>
													</TR>
 													<TR>
													<!-- 特別支援学校体験期間 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblTokbetuSienTaikenKikan"
															value="#{pc_Xrd00102T01.propTokbetuSienTaikenKikan.labelName}"></h:outputText></TH>
														<TD><h:outputText
															styleClass="outputText" id="htmlTokbetuSienTaikenKikan"
															value="#{pc_Xrd00102T01.propTokbetuSienTaikenKikan.value}"
															style="width:211px;">
														</h:outputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE class="table" width="822">
												<TBODY>
													<TR>
													<!-- 実習期間 --> 
														<TH nowrap class="v_a" width="170"><h:outputText
															styleClass="outputText" id="lblJissyuKikan"
															value="#{pc_Xrd00102T01.proplblJissyuKikan.name}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlJissyuStaDate"
															value="#{pc_Xrd00102T01.propJissyuStaDate.dateValue}"
															style="#{pc_Xrd00102T01.propJissyuStaDate.style}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText> ～ <h:inputText styleClass="inputText"
															id="htmlJissyuEndDate"
															value="#{pc_Xrd00102T01.propJissyuEndDate.dateValue}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText></TD>
													</TR>
													<TR>
													<!-- 実習週間 --> 
														<TH nowrap class="v_b" width="200"><h:outputText
															styleClass="outputText" id="lblJissyuSyukan"
															value="#{pc_Xrd00102T01.propJissyuSyukan.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlJissyuSyukan"
															value="#{pc_Xrd00102T01.propJissyuSyukan.value}"
															onkeydown="onChangeData();"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T01.propJissyuSyukan.list}" />
														</h:selectOneMenu></TD>
													</TR>
													<TR>
													<!-- 単位数 --> 
														<TH nowrap class="v_c" width="200"><h:outputText
															styleClass="outputText" id="lblTaniSu"
															value="#{pc_Xrd00102T01.propTaniSu.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlTaniSu"
															value="#{pc_Xrd00102T01.propTaniSu.value}"
															onkeydown="onChangeData();"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T01.propTaniSu.list}" />
														</h:selectOneMenu></TD>
													</TR>
													<TR>
													<!-- 地域 --> 
														<TH nowrap class="v_d" width="184"><h:outputText
															styleClass="outputText" id="lblTiiki"
															value="#{pc_Xrd00102T01.propTiiki.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlTiiki"
															value="#{pc_Xrd00102T01.propTiiki.value}"
															onkeydown="onChangeData();"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T01.propTiiki.list}" />
														</h:selectOneMenu></TD>
													<!-- 詳細地区 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblDtlChik"
															value="#{pc_Xrd00102T01.propDtlChik.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlDtlChik"
															value="#{pc_Xrd00102T01.propDtlChik.value}"
															onkeydown="onChangeData();"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T01.propDtlChik.list}" />
														</h:selectOneMenu></TD>
													</TR>
 													<TR>
													<!-- 実習先名称 --> 
														<TH nowrap class="v_f" width="184"><h:outputText
															styleClass="outputText" id="lblJissyusakiName"
															value="#{pc_Xrd00102T01.propJissyusakiName.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlJissyusakiName"
															value="#{pc_Xrd00102T01.propJissyusakiName.value}"
															onkeydown="onChangeData();"
															maxlength="#{pc_Xrd00102T01.propJissyusakiName.maxLength}"
															style="width:211px;">
														</h:inputText></TD>
													</TR>
													<TR>
													<!-- 実習先郵便番号 --> 
														<TH nowrap class="v_g" width="184"><h:outputText
															styleClass="outputText" id="lblJissyusakiAddrCd"
															value="#{pc_Xrd00102T01.propJissyusakiAddrCd.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlJissyusakiAddrCd"
															value="#{pc_Xrd00102T01.propJissyusakiAddrCd.value}"
															onkeydown="onChangeData();"
															maxlength="#{pc_Xrd00102T01.propJissyusakiAddrCd.maxLength}"
															style="width:100px;">
														</h:inputText></TD>
													</TR>
													<TR>
													<!-- 実習先都道府県 --> 
														<TH nowrap class="v_h" width="184"><h:outputText
															styleClass="outputText" id="lblJissyusakiKen"
															value="#{pc_Xrd00102T01.propJissyusakiKen.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlJissyusakiKen"
															value="#{pc_Xrd00102T01.propJissyusakiKen.value}"
															onkeydown="onChangeData();"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T01.propJissyusakiKen.list}" />
														</h:selectOneMenu></TD>
													</TR>
													<TR>
													<!-- 実習先住所 --> 
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblJissyusakiAddr"
															value="#{pc_Xrd00102T01.propJissyusakiAddr.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlJissyusakiAddr"
															value="#{pc_Xrd00102T01.propJissyusakiAddr.value}"
															onkeydown="onChangeData();"
															maxlength="#{pc_Xrd00102T01.propJissyusakiAddr.maxLength}"
															style="width:600px;">
														</h:inputText></TD>
													</TR>
													<TR>
													<!-- 実習先電話番号 --> 
														<TH nowrap class="v_j" width="184"><h:outputText
															styleClass="outputText" id="lblJissyusakiTel"
															value="#{pc_Xrd00102T01.propJissyusakiTel.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlJissyusakiTel"
															value="#{pc_Xrd00102T01.propJissyusakiTel.value}"
															onkeydown="onChangeData();"
															maxlength="#{pc_Xrd00102T01.propJissyusakiTel.maxLength}"
															size="50"
															style="width:150px;">
														</h:inputText></TD>
													</TR>
													<TR>
													<!-- 実習先校長・園長名 --> 
														<TH nowrap class="v_k" width="184"><h:outputText
															styleClass="outputText" id="lblJissyusakiKocyoName"
															value="#{pc_Xrd00102T01.propJissyusakiKocyoName.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlJissyusakiKocyoName"
															value="#{pc_Xrd00102T01.propJissyusakiKocyoName.value}"
															onkeydown="onChangeData();"
															maxlength="#{pc_Xrd00102T01.propJissyusakiKocyoName.maxLength}"
															style="width:211px;">
														</h:inputText></TD>
													</TR>
													<TR>
													<!-- 実習先登録日 --> 
														<TH nowrap class="v_l" width="184"><h:outputText
															styleClass="outputText" id="lblJissyuTrkDate"
															value="#{pc_Xrd00102T01.propJissyuTrkDate.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:outputText
															styleClass="outputText" id="htmlJissyuTrkDate"
															value="#{pc_Xrd00102T01.propJissyuTrkDate.value}"
															style="width:211px;">
														</h:outputText></TD>
													</TR>
													<TR>
													<!-- 手続書類発送日 -->
														<TH nowrap class="v_m" width="184"><h:outputText
															styleClass="outputText" id="lblSendDate"
															value="#{pc_Xrd00102T01.propSendDate.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlSendDate"
															value="#{pc_Xrd00102T01.propSendDate.dateValue}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													<!-- 実習振込期限 -->
														<TH nowrap class="v_m" width="184"><h:outputText
															styleClass="outputText" id="lblPayLimit"
															value="#{pc_Xrd00102T01.propPayLimit.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlPayLimit"
															value="#{pc_Xrd00102T01.propPayLimit.dateValue}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 承諾書受付日 -->
														<TH nowrap class="v_o" width="184"><h:outputText
															styleClass="outputText" id="lblAsntDate"
															value="#{pc_Xrd00102T01.propAsntDate.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlAsntDate"
															value="#{pc_Xrd00102T01.propAsntDate.dateValue}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													<!-- 諸費問合せ受付日 -->
														<TH nowrap class="v_p" width="184"><h:outputText
															styleClass="outputText" id="lblSyohiDate"
															value="#{pc_Xrd00102T01.propSyohiDate.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlSyohiDate"
															value="#{pc_Xrd00102T01.propSyohiDate.dateValue}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 支払方法 -->
														<TH nowrap class="v_q" width="184"><h:outputText
															styleClass="outputText" id="lblPay"
															value="#{pc_Xrd00102T01.propPay.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneMenu
															styleClass="selectOneMenu" id="htmlPay"
															value="#{pc_Xrd00102T01.propPay.value}"
															onkeydown="onChangeData();"
															onblur="return setDesable(this, event);"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T01.propPay.list}" />
															
														</h:selectOneMenu></TD>
													<!-- 諸費金額 -->
														<TH nowrap class="v_r" width="184"><h:outputText
															styleClass="outputText" id="lblSyohiGaku"
															value="#{pc_Xrd00102T01.propSyohiGaku.labelName}"
															style="#{pc_Xrd00102T01.propSyohiGaku.labelStyle}"></h:outputText>
														</TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlSyohiGaku"
															value="#{pc_Xrd00102T01.propSyohiGaku.stringValue}"
															style="#{pc_Xrd00102T01.propSyohiGaku.style}"
															disabled="#{pc_Xrd00102T01.propSyohiGaku.disabled}"
															onkeydown="onChangeData();"
															style="padding-right: 3px; text-align: right"
															size="9">
														</h:inputText></TD>
													</TR>
													<TR>
													<!-- 支払日 -->
														<TH nowrap class="v_s" width="184"><h:outputText
															styleClass="outputText" id="lblSyohiPayDate"
															value="#{pc_Xrd00102T01.propSyohiPayDate.name}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlSyohiPayDate"
															value="#{pc_Xrd00102T01.propSyohiPayDate.dateValue}"
															disabled="#{pc_Xrd00102T01.propSyohiPayDate.disabled}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 許可日 -->
														<TH nowrap class="v_t" width="184"><h:outputText
															styleClass="outputText" id="lblKyokaDate"
															value="#{pc_Xrd00102T01.propKyokaDate.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlKyokaDate"
															value="#{pc_Xrd00102T01.propKyokaDate.dateValue}"
															disabled="#{pc_Xrd00102T01.propKyokaDate.disabled}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													<!-- 辞退日 -->
														<TH nowrap class="v_u" width="184"><h:outputText
															styleClass="outputText" id="lblJitaiDate"
															value="#{pc_Xrd00102T01.propJitaiDate.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlJitaiDate"
															value="#{pc_Xrd00102T01.propJitaiDate.dateValue}"
															onkeydown="onChangeData();"
															size="10">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>  
													<TR>
													<!-- 特定対象 --> 
														<TH nowrap class="v_v" width="184"><h:outputText
															styleClass="outputText" id="lblJissyuTokuteiRadio"
															value="#{pc_Xrd00102T01.propJissyuTokuteiRadio.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:selectOneRadio
															disabledClass="selectOneRadio_Disabled"
															styleClass="selectOneRadio" id="htmlJissyuTokuteiRadio"
															value="#{pc_Xrd00102T01.propJissyuTokuteiRadio.value}"
															onkeydown="onChangeData();"
															disabled="#{pc_Xrd00102T01.propJissyuTokuteiRadio.disabled}">
															<f:selectItems value="#{pc_Xrd00102T01.propJissyuTokuteiRadio.list}" />
														</h:selectOneRadio></TD>
													</TR>
													<TR>
														<!-- 備考1 -->
														<TH nowrap class="v_w" width="184">
															<h:outputText styleClass="outputText" id="lblNote1"
															value="#{pc_Xrd00102T01.propNote1.labelName}"
															style="#{pc_Xrd00102T01.propNote1.labelStyle}"></h:outputText><BR>
															<hx:graphicImageEx styleClass="graphicImageEx" id="imageEx1"
															value="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
														<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
															id="htmlNote1" cols="70" rows="3"
															disabled="#{pc_Xrd00102T01.propNote1.disabled}"
															value="#{pc_Xrd00102T01.propNote1.stringValue}"
															readonly="#{pc_Xrd00102T01.propNote1.readonly}"
															onkeydown="onChangeData();"
															style="#{pc_Xrd00102T01.propNote1.style}; height: 56px;"></h:inputTextarea>
														</TD>
													</TR>
													<TR>
														<!-- 備考2 -->
														<TH nowrap class="v_x" width="184">
															<h:outputText styleClass="outputText" id="lblNote2"
															value="#{pc_Xrd00102T01.propNote2.labelName}"
															style="#{pc_Xrd00102T01.propNote2.labelStyle}"></h:outputText><BR>
															<hx:graphicImageEx styleClass="graphicImageEx" id="imageEx2"
															value="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
														<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
															id="htmlNote2" cols="70" rows="3"
															onchange="onCangeData();"
															disabled="#{pc_Xrd00102T01.propNote2.disabled}"
															value="#{pc_Xrd00102T01.propNote2.stringValue}"
															readonly="#{pc_Xrd00102T01.propNote2.readonly}"
															style="#{pc_Xrd00102T01.propNote2.style}; height: 56px;"></h:inputTextarea>
														</TD>
													</TR>
													<TR>
														<!-- 備考3 -->
														<TH nowrap class="v_y" width="184">
															<h:outputText styleClass="outputText" id="lblNote3"
															value="#{pc_Xrd00102T01.propNote3.labelName}"
															style="#{pc_Xrd00102T01.propNote3.labelStyle}"></h:outputText><BR>
															<hx:graphicImageEx styleClass="graphicImageEx" id="imageEx3"
															value="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
														<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
															id="htmlNote3" cols="70" rows="3"
															onchange="onCangeData();"
															disabled="#{pc_Xrd00102T01.propNote3.disabled}"
															value="#{pc_Xrd00102T01.propNote3.stringValue}"
															readonly="#{pc_Xrd00102T01.propNote3.readonly}"
															style="#{pc_Xrd00102T01.propNote3.style}; height: 56px;"></h:inputTextarea>
														</TD>
													</TR>
													<TR>
														<TH width="184" class="v_z">
															<h:outputText styleClass="outputText"
															id="lblSekyuFlg" 
															value="#{pc_Xrd00102T01.propSekyuFlg.labelName}"
															style="#{pc_Xrd00102T01.propSekyuFlg.labelStyle}">
															</h:outputText></TH>
														<TD colspan="3">
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlSekyuFlg"
																value="#{pc_Xrd00102T01.propSekyuFlg.checked}"
																disabled="#{pc_Xrd00102T01.propSekyuFlg.disabled}"
																style="#{pc_Xrd00102T01.propSekyuFlg.style}">
															</h:selectBooleanCheckbox> 作成済み
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											<TABLE width="95%" class="button_bar" cellspacing="1"
												cellpadding="1">
												<TBODY>
													<TR>
														<TD width="100%" align="center">
															<hx:commandExButton
																type="submit" value="確定" styleClass="commandExButton_dat"
																id="kakutei"
																disabled="#{pc_Xrd00102T01.propKakutei.disabled}"
																action="#{pc_Xrd00102T01.doKakuteiAction}"
																confirm="#{msg.SY_MSG_0001W}"
																onclick="return windowOnscroll();">
															</hx:commandExButton>
															<hx:commandExButton
																type="submit" value="クリア"
																styleClass="commandExButton_etc" id="clear"
																onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
																action="#{pc_Xrd00102T01.doClearAction}"
																disabled="#{pc_Xrd00102T01.propClear.disabled}">
															</hx:commandExButton>
															<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
																id="delete"
																onclick="return doPopupMsg('#{msgCO.CO_MSG_0008W}', 'すべてのタブ情報が削除されますが');" 
																disabled="#{pc_Xrd00102T01.propDelete.disabled}"
																action="#{pc_Xrd00102T01.doDeleteAction}">
															</hx:commandExButton>
															<!-- Hidden項目制御(活性・非活性)ボタン -->	
															<hx:commandExButton	
																style="display:none;" id="htmlDesable" 
																action="#{pc_Xrd00102T01.doDesable}"></hx:commandExButton>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											</div>
											</TD>
										</TR>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>	
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<h:inputHidden id="htmlFormatNumberOption" value="htmlSyohiGaku=###,###,###;"></h:inputHidden>
			<h:inputHidden id="htmlExecutable1"
							value="#{pc_Xrd00102T01.propExecutableHidden1.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlExecutable2"
							value="#{pc_Xrd00102T01.propExecutableHidden2.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlExecutable3"
							value="#{pc_Xrd00102T01.propExecutableHidden3.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlHidAction" value="#{pc_Xrd00102T01.xrd00102.propHidAction.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlGakuhiHidden" value="#{pc_Xrd00102T01.xrd00102.propGakuhiHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlSotSinseiHidden" value="#{pc_Xrd00102T01.xrd00102.propSotSinseiHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlGakTeishiHidden" value="#{pc_Xrd00102T01.xrd00102.propGakTeishiHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlZaisekiKgnHidden" value="#{pc_Xrd00102T01.xrd00102.propZaisekiKgnHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlRsyuNenjiHidden" value="#{pc_Xrd00102T01.xrd00102.propRsyuNenjiHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlJukoCheckHidden" value="#{pc_Xrd00102T01.xrd00102.propJukoCheckHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlTrokKbnCheckHidden" value="#{pc_Xrd00102T01.xrd00102.propTrokKbnCheckHidden.integerValue}"></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlgT01" value="#{pc_Xrd00102T01.propHidChangeDataFlgT01.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlgT02" value="#{pc_Xrd00102T02.propHidChangeDataFlgT02.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlgT03" value="#{pc_Xrd00102T03.propHidChangeDataFlgT03.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlScrollPosition" value="#{pc_Xrd00102T01.propScrollPosition.stringValue}"></h:inputHidden>			
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
