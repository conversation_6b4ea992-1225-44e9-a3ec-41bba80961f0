<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00702.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content--Type" content="text/css">
<TITLE>Xrg00702.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード	'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード	'event' の代わりに 'thisEvent' を使用します
var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlJinjiCd&kyoShokuin=3";
 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00702.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00702.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00702.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00702.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
				<TABLE>
					<TR>
						<TD nowrap align="right">
							<hx:commandExButton type="submit"
							value="戻　る" styleClass="commandExButton" id="returnDisp"
							tabindex="2" action="#{pc_Xrg00702.doReturnDispAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TABLE>
			</DIV>
			<!-- ↑ここに戻るボタンを配置 -->


			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="750" class="table" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblNendo"
								value="#{pc_Xrg00702.propNendo.labelName}"
								style="#{pc_Xrg00702.propNendo.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="600">
							<h:outputText styleClass="outputText" id="htmlNendo"
								value="#{pc_Xrg00702.propNendo.stringValue}"
								style="#{pc_Xrg00702.propNendo.style}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblSchoolingSbtCd"
								value="#{pc_Xrg00702.propSchSbtNm.labelName}"
								style="#{pc_Xrg00702.propSchSbtNm.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="600">
							<h:outputText styleClass="outputText" id="htmlSchoolingSbtCd"
								value="#{pc_Xrg00702.propSchSbtNm.displayValue}"
								title="#{pc_Xrg00702.propSchSbtNm.stringValue}"
								style="#{pc_Xrg00702.propSchSbtNm.style}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblChusenKbn"
								value="#{pc_Xrg00702.propTyusenKbn.labelName}"
								style="#{pc_Xrg00702.propTyusenKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="600">
							<h:outputText styleClass="outputText" id="htmlTyusenKbn"
								value="#{pc_Xrg00702.propTyusenKbn.displayValue}"
								title="#{pc_Xrg00702.propTyusenKbn.stringValue}"
								style="#{pc_Xrg00702.propTyusenKbn.style}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="750">
				<TBODY>
					<TR>
						<TD align="right">
							<h:outputText
								styleClass="outputText" id="lblCount"
								value="#{pc_Xrg00702.propCount.value}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>				
			<TABLE border="0" cellpadding="0" cellspacing="0" width="750">
				<TBODY>
					<TR>
						<TD>
							<CENTER>
								<div id="listScroll" class="listScroll"
								onscroll="setScrollPosition('scroll',this);"
								style="height:380px;width:750px;">
									<h:dataTable
										border="0" cellpadding="2" cellspacing="0"
										columnClasses="columnClass1" headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_Xrg00702.propJugyoList.rowClasses}"
										styleClass="meisai_scroll" id="table1"
										value="#{pc_Xrg00702.propJugyoList.list}" var="varlist">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="開催期"
													id="lblKaisaikiColumn"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlKaisaikiColumn"
												value="#{varlist.kaisaiKiNm}"></h:outputText>
											<f:attribute value="60" name="width" />
											<f:attribute value="text-align: center; vertical-align: middle"
												name="style" />
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText id="lblJugyoCdColumn" styleClass="outputText"
													value="授業コード"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlJugyoCdColumn"
												value="#{varlist.schJugyCd}"></h:outputText>
											<f:attribute value="80" name="width" />
											<f:attribute value="text-align: center; vertical-align: middle"
												name="style" />
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="授業名称"
													id="lblJugyoNameColumn"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlJugyoNameColumn"
												value="#{varlist.propSchJugyNm.displayValue}"
												title="#{varlist.propSchJugyNm.stringValue}"></h:outputText>
											<f:attribute value="230" name="width" />
											<f:attribute value="text-align: left; vertical-align: middle"
												name="style" />
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="定員"
													id="lblTeiinColumn"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlTeiinColumn"
												value="#{varlist.teiin}"></h:outputText>
											<f:attribute value="80" name="width" />
											<f:attribute value="text-align: right; vertical-align: middle"
												name="style" />
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="WEB申込"
													id="lblWebMskCntColumn"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlWebMskCntColumn"
												value="#{varlist.webMskCnt}"></h:outputText>
											<f:attribute value="80" name="width" />
											<f:attribute value="text-align: right; vertical-align: middle"
												name="style" />
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="ハガキ申込"
													id="lblhgkMskCntColumn"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlhgkMskCntColumn"
												value="#{varlist.hgkMskCnt}"></h:outputText>
											<f:attribute value="90" name="width" />
											<f:attribute value="text-align: right; vertical-align: middle"
												name="style" />
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column7">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="申込合計人数"
													id="lblTotalMskCnt"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlTotalMskCnt"
												value="#{varlist.propTotalMskCnt.value}"
												style="#{varlist.propTotalMskCnt.style}"></h:outputText>
											<f:attribute value="100" name="width" />
											<f:attribute value="text-align: right; vertical-align: middle"
												name="style" />
											<f:attribute value="left" name="align" />
										</h:column>
										<h:column id="column8">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="当選者確認"
												styleClass="commandExButton" id="detail"
												action="#{pc_Xrg00702.doDetailAction}"
												tabindex="1"
												disabled="#{varlist.propConfirmBtn.disabled}"></hx:commandExButton>
											<f:attribute value="40" name="width" />
											<f:attribute value="text-align: center; vertical-align: middle"
												name="style" />
										</h:column>
									</h:dataTable>
								</div>
							</CENTER>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrg00702.propJugyoList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

