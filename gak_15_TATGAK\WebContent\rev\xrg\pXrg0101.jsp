<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/PXrg0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>PXrg0101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:propExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:propExecutableSearch').value = "0";
}

//親画面に授業コードを返却
function doSelect(thisObj, thisEvent) {

	var buttonid = thisObj.id;
	// 正規表現にて選択行のindex値を取得
	var point_start = buttonid.search(":[0-9]*:");
	var point_end = buttonid.search(":button_select");
	var index = buttonid.substring(point_start+1,point_end);
	var retValue = document.getElementById("form1:htmlJugyoList:"+ index +":lblJugyocd_list").innerHTML;
	var retFieldName = document.getElementById("form1:htmlRetFieldName").value;
	if (window.opener) {
		window.opener.document.getElementById(retFieldName).value = retValue;
		window.opener.document.getElementById(retFieldName).focus();
	}
	return true;
}

var schSbtCd = "";
function getSchoolingSbtCb() {
	// スクーリング種別コンボボックス取得AJAX
	var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
	var args = new Array();
	args['nendo'] = document.getElementById('form1:htmlNendo').value;
	var target = "";
	
	comb = document.getElementById('form1:htmlSchooling');
	schSbtCd = comb.options[comb.selectedIndex].value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
}

function callBackMethod(value){
	var comb = document.getElementById('form1:htmlSchooling');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_PXrg0101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_PXrg0101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_PXrg0101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_PXrg0101.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE class="table" width="688">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="116"><h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_PXrg0101.propNendo.labelName}"
										style="#{pc_PXrg0101.propNendo.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNendo" disabled="#{pc_PXrg0101.propNendo.disabled}"
										style="#{pc_PXrg0101.propNendo.style}"
										value="#{pc_PXrg0101.propNendo.dateValue}"
										onblur="getSchoolingSbtCb();"
										size="5">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH nowrap class="v_b" width="116"><h:outputText
										styleClass="outputText" id="lblSchooling"
										value="#{pc_PXrg0101.propSchooling.labelName}"
										style="#{pc_PXrg0101.propSchooling.labelStyle}"></h:outputText></TH>
									<TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSchooling"
										disabled="#{pc_PXrg0101.propSchooling.disabled}"
										value="#{pc_PXrg0101.propSchooling.value}"
										style="#{pc_PXrg0101.propSchooling.style};width:322px">
										<f:selectItems value="#{pc_PXrg0101.propSchooling.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c" width="116"><h:outputText
										styleClass="outputText" id="lblJugyonm"
										value="#{pc_PXrg0101.propJugyonm.labelName}"></h:outputText></TH>
									<TD width="345" colspan="2"><h:inputText styleClass="inputText"
										id="htmlJugyonm" style="#{pc_PXrg0101.propJugyonm.style}"
										disabled="#{pc_PXrg0101.propJugyonm.disabled}"
										value="#{pc_PXrg0101.propJugyonm.stringValue}"
										maxlength="#{pc_PXrg0101.propJugyonm.maxLength}" size="50"></h:inputText></TD>
									<TD width="227"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlJugyonmFindType"
										value="#{pc_PXrg0101.propJugyonmFindType.value}"
										style="border-bottom-width: 0px; 
											   border-left-width: 0px; 
											   border-right-width: 0px; 
											   border-top-width: 0px; 
											   border-width: 0px; 
											   height: 12px; 
											   margin: 0px; 
											   margin-bottom: 0px; 
											   margin-left: 0px; 
											   margin-right: 0px; 
											   margin-top: 0px; 
											   padding: 0px; 
											   padding-bottom: 0px; 
											   padding-left: 0px; 
											   padding-right: 0px; 
											   padding-top: 0px">
										<f:selectItem itemValue="1" itemLabel="部分一致" />
										<f:selectItem itemValue="0" itemLabel="前方一致" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH nowrap class="v_d" width="116"><h:outputText
										styleClass="outputText" id="lblKi"
										value="#{pc_PXrg0101.propKi.labelName}"></h:outputText></TH>
									<TD width="227"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlKi"
										disabled="#{pc_PXrg0101.propKi.disabled}"
										value="#{pc_PXrg0101.propKi.value}"
										style="#{pc_PXrg0101.propKi.style};width:210px">
										<f:selectItems value="#{pc_PXrg0101.propKi.list}" />
									</h:selectOneMenu></TD>
									<TH nowrap class="v_e" width="116"><h:outputText
										styleClass="outputText" id="lblJigen"
										value="#{pc_PXrg0101.propJigen.labelName}"></h:outputText></TH>
									<TD width="227"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlJigen"
										disabled="#{pc_PXrg0101.propJigen.disabled}"
										value="#{pc_PXrg0101.propJigen.value}"
										style="#{pc_PXrg0101.propJigen.style};width:210px">
										<f:selectItems value="#{pc_PXrg0101.propJigen.list}" />
									</h:selectOneMenu></TD>
								</TR>

							</TBODY>
						</TABLE>
						<TABLE cellspacing="1" cellpadding="1" class="button_bar"
							width="688">
							<TBODY>
								<TR align="right">
									<TD align="center"><hx:commandExButton type="submit"
										value="検　索" styleClass="commandExButton_dat" id="search"
										action="#{pc_PXrg0101.doSearchAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_PXrg0101.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="688">
							<TBODY>
								<TR>
									<TD align="right" nowrap class="outputText" width="688"><h:outputText
										styleClass="outputText" id="lblCount"
										value="#{pc_PXrg0101.propJugyolist.listCount}"></h:outputText>件
									</TD>
								</TR>
								<TR>
									<TD>
									<div class="listScroll" style="height: 310px"><h:dataTable
										border="1" cellpadding="2" cellspacing="0"
										columnClasses="columnClass1" headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_PXrg0101.propJugyolist.rowClasses}"
										styleClass="meisai_scroll" id="htmlJugyoList"
										value="#{pc_PXrg0101.propJugyolist.list}" var="varlist">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="lblSchooling_head" styleClass="outputText"
													value="スクーリング種別"></h:outputText>
											</f:facet>
											<f:attribute value="200" name="width" />
											<h:outputText styleClass="outputText" id="lblSchooling_list"
												value="#{varlist.schooling.displayValue}"
												title="#{varlist.schooling.value}"></h:outputText>
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="開催期"
													id="lblKi_head"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblKi_list"
												value="#{varlist.ki}"></h:outputText>
											<f:attribute value="60" name="width" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="時限"
													id="lblJigen_head"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblJigen_list"
												value="#{varlist.jigen}時限"></h:outputText>
											<f:attribute value="60" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="授業コード"
													id="lblJugyocd_head"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblJugyocd_list"
												value="#{varlist.jugyoCd}"></h:outputText>
											<f:attribute value="80" name="width" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="授業名称"
													id="lblJugyonm_head"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="lblJugyonm_list"
												value="#{varlist.jugyoNm.displayValue}"
												title="#{varlist.jugyoNm.value}"></h:outputText>
											<f:attribute value="250" name="width" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblButton_select_head"></h:outputText>
											</f:facet>
											<f:attribute value="30" name="width" />
											<hx:commandExButton type="submit" value="選択"
												styleClass="commandExButton" id="button_select"
												onclick="return doSelect(this, event);"
												action="#{pc_PXrg0101.doCloseDispAction}"></hx:commandExButton>
										</h:column>
									</h:dataTable> <BR>
									</div>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						</TD>
					</TR>
					<TR>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlRetFieldName"
				value="#{pc_PXrg0101.propRetFieldName.value}"></h:inputHidden> <!-- ↑ここにコンポーネントを配置 -->
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<h:inputHidden
				value="#{pc_PXrg0101.propExecutableSearch.integerValue}"
				id="propExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/childFooter.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
