<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<HTML>
<HEAD>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Xrg00901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<SCRIPT language="javascript">

	function init() {
		var shoriKbn = document.getElementById('form1:htmlHidSyoriKbn').value;
		if (shoriKbn == "1") {
			//一括の場合、学生指定項目を非活性に変える
			document.getElementById('form1:htmlHidSyoriKbn').value = shoriKbn;

			document.getElementById('form1:htmlInputFile').disabled = true;
			document.getElementById('form1:takeIn').disabled = true;
			document.getElementById('form1:htmlGakuseki').disabled = true;
			document.getElementById('form1:htmlSearchGakuseki').disabled = true;
			document.getElementById('form1:addIn').disabled = true;
			document.getElementById('form1:htmlTargetGakseiList').disabled = true;
			document.getElementById('form1:removeAll').disabled = true;
			document.getElementById('form1:remove').disabled = true;

		} else if(shoriKbn == "2") {
			//学生指定の場合、学生指定項目を活性に変える
			document.getElementById('form1:htmlHidSyoriKbn').value = shoriKbn;

			document.getElementById('form1:htmlInputFile').disabled = false;
			document.getElementById('form1:takeIn').disabled = false;
			document.getElementById('form1:htmlGakuseki').disabled = false;
			document.getElementById('form1:htmlSearchGakuseki').disabled = false;
			document.getElementById('form1:addIn').disabled = false;
			document.getElementById('form1:htmlTargetGakseiList').disabled = false;
			document.getElementById('form1:removeAll').disabled = false;
			document.getElementById('form1:remove').disabled = false;
		}
	}

	function openSubWindow(thisObj, thisEvent) {
		// 学生検索画面（引数：なし）
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakuseki";
		openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
		return true;
	}

	function doGakuseiAjax(thisObj, thisEvent) {
		// 学生名称を取得する

		var servlet = "rev/co/CobGakseiAJAX";
		var args = new Array();
		args['code1'] = thisObj.value;
		args['code2'] = "";
		args['code3'] = "";
		var target = "form1:htmlGakuseiName";
		var ajaxUtil = new AjaxUtil();
    	ajaxUtil.getPluralValueSetMethod(servlet, null, args,'callBackMethodKd');
	}

	function callBackMethodKd(value){
	    document.getElementById('form1:htmlGakuseiName').innerText = value['name'];
	    document.getElementById('form1:htmlGakuseiName').title = value['name'];
	}

	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfilm").value = "1";
			var action = document.getElementById("form1:htmlAction").value;
			indirectClick(action);
		} catch (e) {
		}
	}

	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfilm").value = "0";
		} catch (e) {
		}
	}

	function onCangeSyoriTaishoKbn(thisObj, thisEvent) {
		var shoriKbn = thisObj.value;
		if (shoriKbn == "1") {
			//一括の場合、学生指定項目を非活性に変える
						
			document.getElementById('form1:htmlHidSyoriKbn').value = shoriKbn;

			document.getElementById('form1:htmlInputFile').disabled = true;
			document.getElementById('form1:takeIn').disabled = true;
			document.getElementById('form1:htmlGakuseki').disabled = true;
			document.getElementById('form1:htmlSearchGakuseki').disabled = true;
			document.getElementById('form1:addIn').disabled = true;
			document.getElementById('form1:htmlTargetGakseiList').disabled = true;
			indirectClick("removeall");
			document.getElementById('form1:removeAll').disabled = true;
			document.getElementById('form1:remove').disabled = true;

			
			
		} else if(shoriKbn == "2") {
			//学生指定の場合、学生指定項目を活性に変える
			document.getElementById('form1:htmlHidSyoriKbn').value = shoriKbn;

			document.getElementById('form1:htmlInputFile').disabled = false;
			document.getElementById('form1:takeIn').disabled = false;
			document.getElementById('form1:htmlGakuseki').disabled = false;
			document.getElementById('form1:htmlSearchGakuseki').disabled = false;
			document.getElementById('form1:addIn').disabled = false;
			document.getElementById('form1:htmlTargetGakseiList').disabled = false;
			document.getElementById('form1:removeAll').disabled = false;
			document.getElementById('form1:remove').disabled = false;
		}
	}
	
	var schSbtCd = "";
	function getSchoolingSbtCb() {
		// スクーリング種別コンボボックス取得AJAX
		var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
		var args = new Array();
		args['nendo'] = document.getElementById('form1:htmlNendo').value;
		args['bunrui'] = "1";
		args['jukoSekyuFlg'] = "true";
		var target = "";
		
		comb = document.getElementById('form1:htmlSchooling');
		schSbtCd = comb.options[comb.selectedIndex].value;
		
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getPluralValue(servlet, target, args);
	}
	
	function callBackMethod(value){
		var comb = document.getElementById('form1:htmlSchooling');
		var length = value['length'];
		comb.length = length;
		for(i = 0; i < length; i++){
			comb.options[i].value = value['key' + i];
			comb.options[i].text = value['value' + i];
			if(i == 0){
				comb.options[i].selected = true;
			}
			if(schSbtCd == comb.options[i].value){
				comb.options[i].selected = true;
			}
		}
	}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="init()">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrg00901.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00901.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00901.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00901.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">
			
				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
				<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
					styleClass="outputText" escape="false">
				</h:outputText>
				</FIELDSET>

				<DIV class="head_button_area" >　
				<!-- ↓ここに戻る／閉じるボタンを配置 -->
				<!-- ↑ここに戻る／閉じるボタンを配置 -->
				</DIV>
				
				<!--↓content↓-->
				<DIV id="content">
					<DIV class="column" align="center">
						<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
							<TBODY>
								<TR>
									<TD>
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD nowrap width="80%">
														<CENTER>
															<TABLE class="table" width="650" border="0" cellpadding="0"
																cellspacing="0" style="">
																<TBODY>
																	<TR>
																		<TH class="v_a" width="150">
																			<h:outputText styleClass="outputText" id="lblNendo"
																				value="#{pc_Xrg00901.propNendo.labelName}"
																				style="#{pc_Xrg00901.propNendo.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD nowrap width="500" colspan="3">
																			<h:inputText id="htmlNendo" styleClass="inputText"
																				readonly="#{pc_Xrg00901.propNendo.readonly}"
																				style="#{pc_Xrg00901.propNendo.style}"
																				value="#{pc_Xrg00901.propNendo.dateValue}"
																				disabled="#{pc_Xrg00901.propNendo.disabled}" size="4"
																				onblur="getSchoolingSbtCb();"
																				tabindex="1">
																				<hx:inputHelperAssist errorClass="inputText_Error"
														    						imeMode="inactive" promptCharacter="_" />
																				<f:convertDateTime pattern="yyyy" />
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_a" width="150">
																			<h:outputText styleClass="outputText" id="lblSchooling"
																				value="#{pc_Xrg00901.propSchooling.labelName}"
																				style="#{pc_Xrg00901.propSchooling.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD nowrap width="500" colspan="3">
																			<h:selectOneMenu styleClass="selectOneMenu"
																				id="htmlSchooling" value="#{pc_Xrg00901.propSchooling.value}"
																				tabindex="2"
																				style="#{pc_Xrg00901.propSchooling.style}">
																				<f:selectItems value="#{pc_Xrg00901.propSchooling.list}" />
																			</h:selectOneMenu>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_a" width="150">
																			<h:outputText 
																				styleClass="outputText"	id="lblNonyu"
																				value="#{pc_Xrg00901.propNonyu.labelName}"
																				style="#{pc_Xrg00901.propNonyu.labelStyle}">
																			</h:outputText>
																		</TH>
																		 <TD nowrap width="175" >
																			 <h:inputText id="htmlNonyu"
																				styleClass="inputText"
																				readonly="#{pc_Xrg00901.propNonyu.readonly}"
																				style="#{pc_Xrg00901.propNonyu.style}"
																				value="#{pc_Xrg00901.propNonyu.dateValue}"
																				tabindex="3">
																				<f:convertDateTime />
																				<hx:inputHelperDatePicker />
																				<hx:inputHelperAssist errorClass="inputText_Error"
																				promptCharacter="_" />
																			</h:inputText> 
																		</TD>
																		<TH class="v_a" width="150">
																			<h:outputText 
																				styleClass="outputText"	id="lblYuko"
																				value="#{pc_Xrg00901.propYuko.labelName}"
																				style="#{pc_Xrg00901.propYuko.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD nowrap width="175">
																			<h:inputText id="htmlYuko"
																				styleClass="inputText"
																				readonly="#{pc_Xrg00901.propYuko.readonly}"
																				style="#{pc_Xrg00901.propYuko.style}"
																				value="#{pc_Xrg00901.propYuko.dateValue}"
																				tabindex="4">
																				<f:convertDateTime />
																				<hx:inputHelperDatePicker />
																				<hx:inputHelperAssist errorClass="inputText_Error"
																				promptCharacter="_" />
																			</h:inputText> 
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_d" width="150">
																			<h:outputText
																				styleClass="outputText" id="lblSyoritaisyo" value="処理対象">
																			</h:outputText>
																		</TH>
																		<TD nowrap width="500" colspan="3">
																			<h:selectOneRadio
																				disabledClass="selectOneRadio_Disabled"
																				styleClass="selectOneRadio" id="htmlSyoritaisyo"
																				onclick="return onCangeSyoriTaishoKbn(this, event);"
																				tabindex="5" 
																				value="#{pc_Xrg00901.propSyoritaisyo.stringValue}"
																				readonly="#{pc_Xrg00901.propSyoritaisyo.readonly}"
																				disabled="#{pc_Xrg00901.propSyoritaisyo.disabled}"
																				style="#{pc_Xrg00901.propSyoritaisyo.style}">
																				<f:selectItems
																					value="#{pc_Xrg00901.propSyoritaisyo.list}" />
																			</h:selectOneRadio>
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_c" width="150">
																			<h:outputText 
																				styleClass="outputText"	id="lblInputFile" 
																				value="#{pc_Xrg00901.propInputFile.labelName}"
																				style="#{pc_Xrg00901.propInputFile.labelStyle}">
																			</h:outputText>
																			<BR>
																			　(前回ファイル)
																		</TH>
																		<TD nowrap width="500" colspan="3">
																			<hx:fileupload styleClass="fileupload" id="htmlInputFile"
																			tabindex="6" value="#{pc_Xrg00901.propInputFile.value}"
																			style="#{pc_Xrg00901.propInputFile.style};width:430px"
																			size="50">
																			<hx:fileProp name="fileName"
																				value="#{pc_Xrg00901.propInputFile.fileName}" />
																			<hx:fileProp name="contentType"
																				value="#{pc_Xrg00901.propInputFile.contentType}" />
																			</hx:fileupload>
																			<hx:commandExButton type="submit"
						                                                        value="取込" styleClass="commandExButton" id="takeIn"
						                                                        action="#{pc_Xrg00901.doTakeInAction}" tabindex="7">
						                                                    </hx:commandExButton>
						                                                    <BR>
																			<h:outputText 
																				styleClass="outputText"	id="htmlInputFileOld"
																				value="#{pc_Xrg00901.propInputFileOld.stringValue}"
																				style="#{pc_Xrg00901.propInputFileOld.style}">
																			</h:outputText>
																			<BR>
																		</TD>
																	</TR>
																	
																	<TR>
																		<TH class="v_e" width="150">
																			<h:outputText
																				styleClass="outputText" id="lblGakuseki" 
																				value="#{pc_Xrg00901.propGakuseki.labelName}"
																				style="#{pc_Xrg00901.propGakuseki.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD nowrap width="500" colspan="3">
																			<h:inputText id="htmlGakuseki"
																				styleClass="inputText"
																				style="#{pc_Xrg00901.propGakuseki.style}"
																				onblur="return doGakuseiAjax(this, event);"
																				value="#{pc_Xrg00901.propGakuseki.stringValue}"
																				tabindex="8">
																			</h:inputText>
																			<hx:commandExButton type="button"
																				styleClass="commandExButton_search" id="htmlSearchGakuseki"
																				onclick="return openSubWindow(this, event);"
																				tabindex="9">
																			</hx:commandExButton>
																			<hx:commandExButton type="submit"
						                                                        value="追加" styleClass="commandExButton" id="addIn"
						                                                        action="#{pc_Xrg00901.doAddInAction}" tabindex="10">
						                                                    </hx:commandExButton>
						                                                    <h:outputText styleClass="outputText" id="htmlGakuseiName"
																				style="#{pc_Xrg00901.propGakuseiName.style}"
																				value="#{pc_Xrg00901.propGakuseiName.stringValue}">
																			</h:outputText>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
														<CENTER>
															<TABLE width="650" border="0" style="">
																<TBODY>
																	<TR>
																		<TD colspan="2" align="left">
																			<h:outputText
																				styleClass="outputText" id="lblTaisyoGakusei" value="対象学生一覧">
																			</h:outputText>												
																		</TD>
																	</TR>
																	<TR>
																		<TD align="left" width="540">
																			<h:selectManyListbox
																				styleClass="selectManyListbox"
																				id="htmlTargetGakseiList" size="10" tabindex="11"
																				value="#{pc_Xrg00901.propTargetGakseiList.value}"
																				readonly="#{pc_Xrg00901.propTargetGakseiList.readonly}"
																				style="width:100%">
																				<f:selectItems
																				value="#{pc_Xrg00901.propTargetGakseiList.list}" />
																			</h:selectManyListbox>										
																		</TD>
																		<TD align="center" width="110" valign="top">
						                                                    <hx:commandExButton type="submit"
						                                                        value="　除外　" styleClass="commandExButton" id="remove"
						                                                        action="#{pc_Xrg00901.doRemoveAction}" tabindex="12">
						                                                    </hx:commandExButton>
						                                                    <BR/>
						                                                    (複数選択可)
						                                                    <BR/>
																			<hx:commandExButton type="submit"
						                                                        value="全て除外" styleClass="commandExButton" id="removeAll"
						                                                        action="#{pc_Xrg00901.doRemoveAllAction}" tabindex="13">
						                                                    </hx:commandExButton>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
														<CENTER>
															<TABLE class="table" width="650" border="0" cellpadding="0"
																cellspacing="0">
																<TBODY>
																	<TR>
																		<TH class="v_e" width="150">
																			<h:outputText
																				styleClass="outputText" id="lblSyorikbn" value="処理区分指定">
																			</h:outputText>
																		</TH>
																		<TD nowrap width="500">
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox" id="htmlSyoriKbn" tabindex="14" 
																				value="#{pc_Xrg00901.propSyoriKbn.checked}"
																				readonly="#{pc_Xrg00901.propSyoriKbn.readonly}"
																				disabled="#{pc_Xrg00901.propSyoriKbn.disabled}"
																				style="#{pc_Xrg00901.propSyoriKbn.style}">
																			</h:selectBooleanCheckbox>
																			チェックのみ（データの登録/更新は行いません）
																		</TD>
																	</TR>
																	<TR>
																		<TH class="v_e" width="150">
																			<h:outputText
																				styleClass="outputText" id="lblCheckList" value="チェックリスト出力指定">
																			</h:outputText>
																		</TH>
																		<TD nowrap width="500">
																			<TABLE border="0" cellpadding="0" cellspacing="0">
																				<TR>
																					<TD class="clear_border">
																						<h:selectBooleanCheckbox
																							styleClass="selectBooleanCheckbox" 
																							id="htmlChkListNormal" tabindex="15" 
																							value="#{pc_Xrg00901.propChkListNormal.checked}"
																							readonly="#{pc_Xrg00901.propChkListNormal.readonly}"
																							disabled="#{pc_Xrg00901.propChkListNormal.disabled}"
																							style="#{pc_Xrg00901.propChkListNormal.style}">
																						</h:selectBooleanCheckbox>
																						<h:outputText styleClass="outputText"
																							id="lblChkListNormal" value="正常データ">
																						</h:outputText>
																					</TD>
																				</TR>
																				<TR>
																					<TD class="clear_border">
																						<h:selectBooleanCheckbox
																							styleClass="selectBooleanCheckbox" 
																							id="htmlChkListError" tabindex="16"
																							value="#{pc_Xrg00901.propChkListError.checked}"
																							readonly="#{pc_Xrg00901.propChkListError.readonly}"
																							disabled="#{pc_Xrg00901.propChkListError.disabled}"
																							style="#{pc_Xrg00901.propChkListError.style}">
																						</h:selectBooleanCheckbox>
																						<h:outputText styleClass="outputText"
																							id="lblChkListError" value="エラーデータ">
																						</h:outputText>
																					</TD>
																				</TR>
																			</TABLE>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</CENTER>
													</TD>
												</TR>
												<TR>
													<TD nowrap width="80%"></TD>
												</TR>
											</TBODY>
										</TABLE>
										<CENTER>
											<TABLE class="button_bar" width="650">
												<TBODY>
													<TR>
														<TD width="650">
															<hx:commandExButton type="submit"
																value="実　行" styleClass="commandExButton_dat" id="exec"
																action="#{pc_Xrg00901.doExecAction}"
																confirm="#{msg.SY_MSG_0001W}" tabindex="17"
																disabled="#{pc_Xrg00901.propExec.disabled}"
																style="#{pc_Xrg00901.propExec.style}">
															</hx:commandExButton>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</CENTER>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
			</DIV>
			<!-- フッターインクルード -->
			<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden id="htmlHidSyoriKbn" value="#{pc_Xrg00901.hidPropSyoriKbn.stringValue}" ></h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrg00901.propConfilm.stringValue}"
				id="htmlConfilm">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrg00901.propAction.stringValue}"
				id="htmlAction">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
