<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00301T02.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>Xrm00301T02.jsp</TITLE>

<SCRIPT type="text/javascript">

	function motoListChange() {
		objForm = document.getElementById("form1");
		motoPayRadio = objForm.motoSelect;
		len = motoPayRadio.length;
	    for(i=0;i<len;i++) {
	    	if(motoPayRadio[i].checked == true) {
	    		document.getElementById('form1:htmlHidMotoPayListNo').value = i;
	    	}
	    }
	}
	function sakiListChange() {
		objForm = document.getElementById("form1");
		sakiPayRadio = objForm.sakiSelect;
		len = sakiPayRadio.length;
	    for(i=0;i<len;i++) {
	    	if(sakiPayRadio[i].checked == true) {
	    		document.getElementById('form1:htmlHidSakiPayListNo').value = i;
	    	}
	    }
	}
	//ロード時の振替元、先ラジオボタン制御
	function setFurikaeListRadio() {
		objForm = document.getElementById("form1");
		motoRadio = objForm.motoSelect;
		sakiRadio = objForm.sakiSelect;

		no = document.getElementById('form1:htmlHidMotoPayListNo').value;

		if (motoRadio != null && motoRadio[no] != null) {
			motoRadio[no].checked = true;
		} else if (motoRadio != null && motoRadio[0] == null) {
			//１件の場合
			motoRadio.checked = true;
		}
		no = document.getElementById('form1:htmlHidSakiPayListNo').value;

		if (sakiRadio != null && sakiRadio[no] != null) {
			sakiRadio[no].checked = true;
		} else if (sakiRadio != null && sakiRadio[0] == null) {
			//１件の場合
			sakiRadio.checked = true;
		}
		
		//スクロールバーの位置を保持
		window.attachEvent('onload', endload);

		//Ajaxにて取得したの値を保持するためにjavascriptでreadonlyを設定する
		document.getElementById('form1:htmlName').readOnly = true;
		document.getElementById('form1:htmlGakunen').readOnly = true;
		document.getElementById('form1:htmlSzkGakka').readOnly = true;
//document.getElementById('form1:htmlIdoSbt').readOnly = true;
//document.getElementById('form1:htmlSemester').readOnly = true;
	}
	
	//スクロールバーの位置を保持
	function endload() {
		changeScrollPosition('motoScroll', 'motoListScroll');
		changeScrollPosition('sakiScroll', 'sakiListScroll');
	}
	
	//学費学生検索画面へ遷移
	function openPGhz0301Window() {
		openPGhz0301("<%=com.jast.gakuen.rev.gh.PGhz0301.getWindowOpenOption() %>");
		 return true;
	}
	
	//学籍情報取得Ajax呼び出し
	function ajaxGakusekiCd(thisObj) {
		//学籍コード
		var gakusekiCd = document.getElementById('form1:htmlGakusekiCd');
		//学生氏名項目id
		var nameId = "form1:htmlName";
		//学年項目id
		var gakunenId = "form1:htmlGakunen";
		//セメスタ項目id
//		var semesterId = "form1:htmlSemester";
		//所属学科組織項目id
		var szkGakkaId = "form1:htmlSzkGakka";
		//異動情報項目id
//		var idoInfoId = "form1:htmlIdoSbt";		

		//学籍情報取得Ajax呼び出し
		funcAjaxGakusekiCd(thisObj, "1", nameId);
		//学年取得Ajax呼び出し
		funcAjaxGakunenAjax(gakusekiCd, "1", gakunenId);
		//セメスタ取得Ajax呼び出し
//		funcAjaxSemesterAjax(gakusekiCd, "1", semesterId);		
		//所属学科組織名称取得Ajax呼び出し
		funcAjaxSgksName(gakusekiCd, "1", szkGakkaId);
		//異動情報取得Ajax呼び出し
//		funcAjaxIdoInfoAjax(gakusekiCd, "1", idoInfoId);
		
		return true;
	}
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
	
		var procPaywFind = document.getElementById('form1:htmlPaywListFind').value;
		var procPayhFind = document.getElementById('form1:htmlPayhListFind').value;

		//選択処理実行
		if(procPaywFind == "1" || 
			procPayhFind == "1"){
			indirectClick('search');
		}

	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlPaywListFind').value = 0;
		document.getElementById('form1:htmlPayhListFind').value = 0;
	}
	
	// 【振替元納付金の処理日付を使用】をクリック時の処理
	function onClickChkProcessDate(id) {
		indirectClick(id);
	}
	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="setFurikaeListRadio();">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrm00301T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		
			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />
			
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrm00301T02.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrm00301T02.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrm00301T02.screenName}"></h:outputText>
			</div>			

			<!--↓OUTER↓-->
			<DIV class="outer">
				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>
				
                <DIV class="head_button_area" >　</DIV>

                <!--↓CONTENT↓-->
                
                <DIV class="head_button_area" >
				</DIV>
                
                
                <DIV id="content">          
                    <DIV class="column" align="center">





			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="900px" nowrap>

						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR align="left">
									<TD>
										<hx:commandExButton 
										type="submit" value="一括指定" styleClass="tab_head_off"
										id="htmlKihonTab"  action="#{pc_Xrm00301T02.doHtmlKihonTabAction1}" tabindex="2"></hx:commandExButton><hx:commandExButton
										type="submit" value="学生指定" styleClass="tab_head_on"
										id="link02" tabindex="-1"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="20" cellspacing="0" height="405px" width="100%" class="tab_body">
										<TBODY>
											<TR>
												<TD height="20px">
												</TD>
											</TR>
											<TR>
												<TD width="100%">
												<TABLE class="table" width="850px">
													<TBODY>
														<TR>
															<TH nowrap class="v_a" width="150px" colspan= "2">
															<!-- 学費年度 -->
																<h:outputText styleClass="outputText" id="lblGhYear"
																value="#{pc_Xrm00301T02.propGhYear.labelName}"
																style="#{pc_Xrm00301T02.propGhYear.labelStyle}"></h:outputText></TH>
															<TD colspan="3" valign="middle" width="*">
																<h:inputText styleClass="inputText" id="htmlGhYear"
																	size="4" value="#{pc_Xrm00301T02.propGhYear.dateValue}"
																	style="#{pc_Xrm00301T02.propGhYear.style}"
																	disabled="#{pc_Xrm00301T02.propGhYear.disabled}"
																	>
																	<hx:inputHelperAssist imeMode="inactive"
																		errorClass="inputText_Error" promptCharacter="_" />
																	<f:convertDateTime pattern="yyyy" />
																</h:inputText>
															</TD>

															<TD rowspan="4" width="150px">
																<hx:commandExButton type="submit" value="選択"
																	styleClass="cmdBtn_dat_s" id="search"
																	disabled="#{pc_Xrm00301T02.propGhYear.disabled}"
																	action="#{pc_Xrm00301T02.doSearchAction}" tabindex="11"></hx:commandExButton>
																<hx:commandExButton type="submit" value="解除"
																			styleClass="cmdBtn_etc_s" id="unselect"
																			disabled="#{!pc_Xrm00301T02.propGhYear.disabled}"
																			action="#{pc_Xrm00301T02.doUnselectAction}" tabindex="12"></hx:commandExButton>
															</TD>
														</TR>
														<TR>
															<TH nowrap class="v_b" colspan= "2">
															<!-- 学籍番号 -->
																<h:outputText styleClass="outputText" id="lblGakusekiCd"
																value="#{pc_Xrm00301T02.propGakusekiCd.labelName}"
																style="#{pc_Xrm00301T02.propGakusekiCd.labelStyle}"></h:outputText></TH>
															<TD><h:inputText styleClass="inputText"
																id="htmlGakusekiCd" value="#{pc_Xrm00301T02.propGakusekiCd.stringValue}" style="#{pc_Xrm00301T02.propGakusekiCd.style}" size="10"
																disabled="#{pc_Xrm00301T02.propGhYear.disabled}"
																maxlength="#{pc_Xrm00301T02.propGakusekiCd.maxLength}"
																onblur="return ajaxGakusekiCd(this);" tabindex="3"></h:inputText>
																<hx:commandExButton
																	type="submit"
																	styleClass="commandExButton_search"
																	id="select"
																	action="#{pc_Xrm00301T02.doSelectAction}"
																	disabled="#{pc_Xrm00301T02.propGhYear.disabled}"
																	onclick="return openPGhz0301Window();" tabindex="4">
																</hx:commandExButton>
															</TD>
															<!-- 学生氏名 -->
															<TH nowrap class="v_c">
															<h:outputText styleClass="outputText" id="lblName"
																value="#{pc_Xrm00301T02.propName.labelName}"
																style="#{pc_Xrm00301T02.propName.labelStyle}"></h:outputText></TH>
															<TD><h:inputText styleClass="likeOutput" id="htmlName"
																value="#{pc_Xrm00301T02.propName.stringValue}"
																style="#{pc_Xrm00301T02.propName.style}"
																size="40"
																tabindex="-1"></h:inputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_d" colspan= "2">
															<!-- 学年 -->
																<h:outputText styleClass="outputText" id="lblGakunen"
																value="#{pc_Xrm00301T02.propGakunen.labelName}"
																style="#{pc_Xrm00301T02.propGakunen.style}"></h:outputText></TH>
															<TD colspan="3" width="60"><h:inputText styleClass="likeOutput" id="htmlGakunen"
																value="#{pc_Xrm00301T02.propGakunen.stringValue}"
																style="#{pc_Xrm00301T02.propGakunen.style}" size="20"
																tabindex="-1"></h:inputText></TD>
																
														</TR>
														<TR>
															<TH nowrap class="v_f" colspan= "2">
															<!-- 所属学科組織 --><h:outputText styleClass="outputText"
																id="lblSzkGakka"
																value="#{pc_Xrm00301T02.propSzkGakka.labelName}"
																style="#{pc_Xrm00301T02.propSzkGakka.style}"></h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="likeOutput"
																id="htmlSzkGakka" value="#{pc_Xrm00301T02.propSzkGakka.stringValue}" style="#{pc_Xrm00301T02.propSzkGakka.style}"
																tabindex="-1"
																size="82"></h:inputText></TD>
														</TR>

													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TD>
												<TABLE width="850px">
													<TBODY>
														<TR>
															<TD colspan="3">
																<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="layoutTable">
																	<TBODY>
																		<TR>
																			<TD width="48%">
																				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																					<TBODY>
																						<TR align="right">
																							<TD>
																								<h:outputText styleClass="outputText"
																									id="htmlCount" 
																									value="#{pc_Xrm00301T02.propMotoList.listCount}">
																								</h:outputText>
																								<h:outputText styleClass="outputText"
																									id="lblCount" value="件">
																								</h:outputText>
																							</TD>
																						</TR>
																						<TR>
																							<TD>
																								<DIV style="height: 265px; width=100%;" id="motoListScroll" onscroll="setScrollPosition('motoScroll',this);" class="listScroll">
																									<h:dataTable border="0" cellpadding="2"
																										cellspacing="0" headerClass="headerClass"
																										footerClass="footerClass"
																										styleClass="meisai_scroll" id="table1"
																										value="#{pc_Xrm00301T02.propMotoList.list}"
																										rows="#{pc_Xrm00301T02.propMotoList.rows}"
																										var="motolist" width="400px">
																										<h:column id="column1">
																										
																											<hx:jspPanel id="jspPanel3" rendered="#{motolist.rendered}">
																												<TABLE border="0" cellpadding="0" cellspacing="0"
																													width="100%" style="border-bottom-style:none;border-top-style:none;">
																													<TBODY>
																														<TR>
																															<!-- ラジオボタン -->
																															<TD width="31px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<input type="radio" name="motoSelect"
																																	onChange="motoListChange();" tabindex="13"/>
																															</TD>
																															<!-- 納付金コード -->
																															<TD width="50px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text5"
																																	value="#{motolist.payCd}">
																																</h:outputText>
																															</TD>
																															<!-- パターンコード -->
																															<TD width="60px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text2"
																																	value="#{motolist.patternCd}">
																															</h:outputText>
																															</TD>
																															<!-- 分納区分コード -->
																															<TD width="80px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text3"
																																	value="#{motolist.bunnoKbnCd}">
																															</h:outputText>
																															</TD>
																															<!-- 納付金名称 -->
																															<TD width="180px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text4"
																																	value="#{motolist.propPayName.displayValue}"
																																	title="#{motolist.propPayName.value}">
																															</h:outputText>
																															</TD>
																															<!-- 納付金額 -->
																															<TD width="70px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;text-align: right">
																																<h:outputText styleClass="outputText" id="text11"
																																	value="#{motolist.itemKingaku}">
																																</h:outputText>
																															</TD>
																														</TR>
																													</TBODY>
																												</TABLE>
																											</hx:jspPanel>
																
																
																
																											<f:facet name="header">
																												<hx:jspPanel id="jspPanel4">
																													<TABLE	border="0" cellpadding="0" cellspacing="0"
																															width="100%" height="20px">
																														<TBODY>
																															<TR>
																																<TH colspan="5" style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align: center">
																																	<h:outputText styleClass="outputText"
																																		id="lblMotoPay" value="#{pc_Xrm00301T02.propMotoPay.name}"
																																		style="#{pc_Xrm00301T02.propMotoPay.style}">
																																	</h:outputText>
																																</TH>
																															</TR>
																															<TR>
																																<TH width="30px" style="border-bottom-style:none;border-left-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value=" "
																																		id="text93">
																																	</h:outputText>
																																</TH>
																																<TH width="50px" style="border-bottom-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="納付金"
																																		id="text94">
																																	</h:outputText> <br>
																																	<h:outputText styleClass="outputText" value="コード"
																																		id="text95">
																																	</h:outputText>
																																</TH>
																																<TH width="60px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="パターン"
																																		id="text96">
																																	</h:outputText> <br>
																																	<h:outputText styleClass="outputText" value="コード"
																																		id="text97">
																																	</h:outputText>
																																</TH>
																																<TH width="80px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="分納区分"
																																		id="text99">
																																	</h:outputText> <br>
																																	<h:outputText styleClass="outputText" value="コード"
																																		id="text100">
																																	</h:outputText>
																																</TH>
																																<TH width="180px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="納付金名称"
																																		id="text101">
																																	</h:outputText>
																																</TH>
																																<!-- 納付金額 -->
																																<TH width="70px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="納付金額"
																																		id="text110">
																																	</h:outputText>
																																</TH>
																															</TR>
																														</TBODY>
																													</TABLE>
																												</hx:jspPanel>
																											</f:facet>
																										
																										</h:column>
																									</h:dataTable>
																								</DIV>
																							</TD>
																						</TR>
																											
																					</TBODY>
																				</TABLE>
																			</TD>
																			<TD width="2%">
																			</TD>
																			<TD width="48%">
																				<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																					<TBODY>
																						<TR align="right">
																							<TD>
																								<h:outputText styleClass="outputText"
																									id="htmlSakiCount" 
																									value="#{pc_Xrm00301T02.propSakiList.listCount}">
																								</h:outputText>
																								<h:outputText styleClass="outputText"
																									id="lblSakiCount" value="件">
																								</h:outputText>
																							</TD>
																						</TR>
																						<TR>
																							<TD>
																								<DIV style="height: 265px; width=100%;" id="sakiListScroll" onscroll="setScrollPosition('sakiScroll',this);" class="listScroll">
																									<h:dataTable border="0" cellpadding="2" cellspacing="0"
																										headerClass="headerClass" footerClass="footerClass"
																										styleClass="meisai_scroll" id="table2"
																										value="#{pc_Xrm00301T02.propSakiList.list}"
																										rows="#{pc_Xrm00301T02.propSakiList.rows}"
																										var="sakilist" width="400px">
																										<h:column id="column11">
																										
																											<hx:jspPanel id="jspPanel1" rendered="#{sakilist.rendered}">
																												<TABLE border="0" cellpadding="0" cellspacing="0"
																													width="100%" style="border-bottom-style:none;border-top-style:none;">
																													<TBODY>
																														<TR>
																															<!-- ラジオボタン -->
																															<TD width="31px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<input type="radio" name="sakiSelect"
																																	onChange="sakiListChange();" tabindex="14"/>
																															</TD>
																															<!-- 納付金コード -->
																															<TD width="50px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text6"
																																	value="#{sakilist.payCd}">
																																</h:outputText>
																															</TD>
																															<!-- パターンコード -->
																															<TD width="60px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text7"
																																	value="#{sakilist.patternCd}">
																															</h:outputText>
																															</TD>
																															<!-- 分納区分コード -->
																															<TD width="80px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text8"
																																	value="#{sakilist.bunnoKbnCd}">
																															</h:outputText>
																															</TD>
																															<!-- 納付金名称 -->
																															<TD width="180px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;">
																																<h:outputText styleClass="outputText" id="text9"
																																	value="#{sakilist.propPayName.displayValue}"
																																	title="#{sakilist.propPayName.value}">
																															</h:outputText>
																															</TD>
																															<!-- 納付金額 -->
																															<TD width="70px" style="border-top-style:none;border-bottom-style:none;border-left-style:none;text-align: right">
																																<h:outputText styleClass="outputText" id="text10"
																																	value="#{sakilist.itemKingaku}">
																																</h:outputText>
																															</TD>
																														</TR>
																													</TBODY>
																												</TABLE>
																											</hx:jspPanel>

																											<f:facet name="header">
																												<hx:jspPanel id="jspPanel2">
																													<TABLE	border="0" cellpadding="0" cellspacing="0"
																															width="100%" height="20px">
																														<TBODY>
																															<TR>
																																<TH colspan="5" style="border-top-style:none;border-bottom-style:none;border-right-style:none;border-left-style:none;text-align: center">
																																	<h:outputText styleClass="outputText"
																																		id="lblSakiPay" value="#{pc_Xrm00301T02.propSakiPay.name}"
																																		style="#{pc_Xrm00301T02.propSakiPay.style}">
																																	</h:outputText>
																																</TH>
																															</TR>
																															<TR>
																																<TH width="30px" style="border-bottom-style:none;border-left-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value=" "
																																		id="text102">
																																	</h:outputText>
																																</TH>
																																<TH width="50px" style="border-bottom-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="納付金"
																																		id="text103">
																																	</h:outputText> <br>
																																	<h:outputText styleClass="outputText" value="コード"
																																		id="text104">
																																	</h:outputText>
																																</TH>
																																<TH width="60px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="パターン"
																																		id="text105">
																																	</h:outputText> <br>
																																	<h:outputText styleClass="outputText" value="コード"
																																		id="text106">
																																	</h:outputText>
																																</TH>
																																<TH width="80px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="分納区分"
																																		id="text107">
																																	</h:outputText> <br>
																																	<h:outputText styleClass="outputText" value="コード"
																																		id="text108">
																																	</h:outputText>
																																</TH>
																																<TH width="180px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="納付金名称"
																																		id="text109">
																																	</h:outputText>
																																</TH>
																																<!-- 納付金額 -->
																																<TH width="70px" style="border-bottom-style:none;border-right-style:none;text-align: center">
																																	<h:outputText styleClass="outputText" value="納付金額"
																																		id="text111">
																																	</h:outputText>
																																</TH>
																															</TR>
																														</TBODY>
																													</TABLE>
																												</hx:jspPanel>
																											</f:facet>
																										
																										</h:column>
																									</h:dataTable>
																								</DIV>
																							</TD>
																						</TR>
																											
																					</TBODY>
																				</TABLE>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
											<TR>
												<TD height="20px">
												</TD>
											</TR>

										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD height="20px">
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE class="table" width="100%">
							<TBODY>
								<TR align="center">
									<TH nowrap class="v_d" width="150px"><h:outputText styleClass="outputText" id="lblSyoriKbn" value="#{pc_Xrm00301T02.propSyoriKbn.name}" style="#{pc_Xrm00301T02.propSyoriKbn.style}"></h:outputText></TH>
									<TD colspan="2" align="left" width="*"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlCheckOnly" value="#{pc_Xrm00301T02.propCheckOnly.checked}" style="#{pc_Xrm00301T02.propCheckOnly.style}" tabindex="19"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="lblCheckOnly"
										value="#{pc_Xrm00301T02.propCheckOnly.name}"
										style="#{pc_Xrm00301T02.propCheckOnly.style}"></h:outputText></TD>
								</TR>

								<TR>
									<TH width="150px" nowrap class="v_c">
										<h:outputText styleClass="outputText" 
											id="lblChkList"
											value="#{pc_Xrm00301T02.propChkList.name}">
										</h:outputText>
									</TH>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
											<TBODY>
												<TR>
													<TD width="550" nowrap class="clear_border">
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
															id="htmlChkListNormal"
															value="#{pc_Xrm00301T02.propChkListNormal.checked}">
														</h:selectBooleanCheckbox>
														<h:outputText styleClass="outputText" 
															id="lblChkListNormal"
															value="#{pc_Xrm00301T02.propChkListNormal.name}"
															style="#{pc_Xrm00301T02.propChkListNormal.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TD width="550" nowrap class="clear_border">
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
															id="htmlChkListError"
															value="#{pc_Xrm00301T02.propChkListError.checked}">
														</h:selectBooleanCheckbox>
														<h:outputText styleClass="outputText"
															id="lblChkListError"
															value="#{pc_Xrm00301T02.propChkListError.name}"
															style="#{pc_Xrm00301T02.propChkListError.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TD width="550" nowrap class="clear_border">
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
															id="htmlChkListWarning"
															value="#{pc_Xrm00301T02.propChkListWarning.checked}">
														</h:selectBooleanCheckbox>
														<h:outputText styleClass="outputText"
															id="lblChkListWarning"
															value="#{pc_Xrm00301T02.propChkListWarning.name}"
															style="#{pc_Xrm00301T02.propChkListWarning.style}">
														</h:outputText>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>

							</TBODY>
						</TABLE>
						<TABLE cellspacing="0" cellpadding="0" class="button_bar" width="100%">
							<TBODY>
								<TR align="right">
									<TD align="center"><hx:commandExButton type="submit"
										value="実行" styleClass="commandExButton_dat" id="regist"
										confirm="#{msg.SY_MSG_0001W}"
										action="#{pc_Xrm00301T02.doRegistAction}"
										disabled="#{pc_Xrm00301T02.propHidDisabled.disabled}" tabindex="23"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlHidKanriNo" value="#{pc_Xrm00301T02.propHidKanriNo.value}"></h:inputHidden>
			<h:inputHidden id="htmlHidMotoPayListNo" value="#{pc_Xrm00301T02.propHidMotoPayListNo.value}"></h:inputHidden>
			<h:inputHidden id="htmlHidSakiPayListNo" value="#{pc_Xrm00301T02.propHidSakiPayListNo.value}"></h:inputHidden>
			<h:inputHidden id="htmlHidDisabled" value="#{pc_Xrm00301T02.propHidDisabled.value}"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrm00301T02.propSakiList.scrollPosition}" id="sakiScroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrm00301T02.propMotoList.scrollPosition}" id="motoScroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrm00301T02.propPaywListFind.integerValue}" id="htmlPaywListFind"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrm00301T02.propPayhListFind.integerValue}" id="htmlPayhListFind"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrm00301T02.propMenjActive.stringValue}" id="htmlMenjActive"></h:inputHidden>
                    </DIV>
                </DIV>
                <!--↑CONTENT↑-->
            </DIV>
			<!--↑outer↑-->
			
			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
