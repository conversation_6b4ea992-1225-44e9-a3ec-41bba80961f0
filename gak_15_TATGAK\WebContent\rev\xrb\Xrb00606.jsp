<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00606.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00606.jsp</TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00606.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
styleClass="commandExButton" id="closeDisp"
action="#{pc_Xrb00606.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00606.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00606.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
 <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
  <TBODY>
   <TR>
    <TD>
     <TABLE width="800">
      <TBODY>
       <TR>
        <TD align="right">
         <hx:commandExButton
          type="submit"
          value="戻る"
          styleClass="commandExButton"
          style="width: 80px"
          action="#{pc_Xrb00606.doReturnAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="800">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="150">
         <!-- 学籍番号 -->
         <h:outputText
          styleClass="outputText"
          id="lblGakusekiCodeTitle"
          value="#{pc_Xrb00606.propGakusekiCode.labelName}"
          style="#{pc_Xrb00606.propGakusekiCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="250">
         <h:outputText
          styleClass="outputText"
          id="lblGakusekiCode"
          value="#{pc_Xrb00606.propGakusekiCode.stringValue}"
          style="#{pc_Xrb00606.propGakusekiCode.labelStyle}">
         </h:outputText>
        </TD>
        <TH nowrap class="v_a" width="150">
         <!-- 氏名 -->
         <h:outputText
          styleClass="outputText"
          id="lblGakuseiNameTitle"
          value="#{pc_Xrb00606.propGakuseiName.labelName}"
          style="#{pc_Xrb00606.propGakuseiName.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="250">
         <h:outputText
          styleClass="outputText"
          id="lblGakuseiName"
          value="#{pc_Xrb00606.propGakuseiName.stringValue}"
          style="#{pc_Xrb00606.propGakuseiName.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 学科組織 -->
         <h:outputText
          styleClass="outputText"
          id="lblCurGakkaNameTitle"
          value="#{pc_Xrb00606.propCurGakkaName.labelName}"
          style="#{pc_Xrb00606.propCurGakkaName.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:outputText
          styleClass="outputText"
          id="lblCurGakkaName"
          value="#{pc_Xrb00606.propCurGakkaName.stringValue}"
          style="#{pc_Xrb00606.propCurGakkaName.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="800">
      <TBODY>
       <TR>
        <TD align="left">
         <h:outputText
          styleClass="outputText"
          value="履修変更申請">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="800">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="150">
         <!-- 申請NO -->
         <h:outputText styleClass="outputText" id="lblSinseiNoTitle"
          value="#{pc_Xrb00606.propSinseiNo.labelName}"
          style="#{pc_Xrb00606.propSinseiNo.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:outputText styleClass="outputText" id="lblSinseiNo"
          style="#{pc_Xrb00606.propSinseiNo.labelStyle}"
          value="#{pc_Xrb00606.propSinseiNo.stringValue}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a" width="150">
         <!-- 変更区分 -->
         <h:outputText styleClass="outputText" id="lblHenkoKbnTitle"
          value="#{pc_Xrb00606.propHenkoKbn.labelName}"
          style="#{pc_Xrb00606.propHenkoKbn.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="250">
         <h:outputText styleClass="outputText" id="lblHenkoKbn"
          value="#{pc_Xrb00606.propHenkoKbn.stringValue}"
          style="#{pc_Xrb00606.propHenkoKbn.labelStyle}">
         </h:outputText>
        </TD>
        <TH nowrap class="v_a" width="150">
         <!-- 申請日 -->
         <h:outputText styleClass="outputText" id="lblSinseiDateTitle" 
          value="#{pc_Xrb00606.propSinseiDate.labelName}"
          style="#{pc_Xrb00606.propSinseiDate.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="250">
         <h:outputText styleClass="outputText" id="lblSinseiDate" 
          value="#{pc_Xrb00606.propSinseiDate.stringValue}"
          style="#{pc_Xrb00606.propSinseiDate.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 申請理由 -->
         <h:outputText styleClass="outputText" id="lblSinseiRiyuTitle" 
          value="#{pc_Xrb00606.propSinseiRiyu.labelName}"
          style="#{pc_Xrb00606.propSinseiRiyu.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSinseiRiyu" cols="78" rows="3"
          disabled="#{pc_Xrb00606.propSinseiRiyu.disabled}"
          value="#{pc_Xrb00606.propSinseiRiyu.stringValue}"
          readonly="#{pc_Xrb00606.propSinseiRiyu.readonly}"
          style="#{pc_Xrb00606.propSinseiRiyu.style}">
         </h:inputTextarea>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 職員コメント -->
         <h:outputText styleClass="outputText" id="lblSyokuinCommentTitle" 
          value="#{pc_Xrb00606.propSyokuinComment.labelName}"
          style="#{pc_Xrb00606.propSyokuinComment.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSyokuinComment" cols="78" rows="3"
          disabled="#{pc_Xrb00606.propSyokuinComment.disabled}"
          value="#{pc_Xrb00606.propSyokuinComment.stringValue}"
          readonly="#{pc_Xrb00606.propSyokuinComment.readonly}"
          style="#{pc_Xrb00606.propSyokuinComment.style}">
         </h:inputTextarea>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="800">
      <TBODY>
       <TR>
        <TD align="left">
         <h:outputText
          styleClass="outputText"
          value="履修請求情報">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="800">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="150">
         <!-- 年度 -->
         <h:outputText styleClass="outputText" id="lblNendo"
          value="#{pc_Xrb00606.propNendo.labelName}"
          style="#{pc_Xrb00606.propNendo.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="250">
         <h:inputText
          styleClass="inputText"
          id="htmlNendo" size="10"
          maxlength="#{pc_Xrb00606.propNendo.maxLength}"
          disabled="#{pc_Xrb00606.propNendo.disabled}"
          value="#{pc_Xrb00606.propNendo.dateValue}"
          style="#{pc_Xrb00606.propNendo.style}"
          readonly="#{pc_Xrb00606.propNendo.readonly}">
          <f:convertDateTime pattern="yyyy"/>
          <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
         </h:inputText>
        </TD>
        <TH nowrap class="v_a" width="150">
         <!-- 振込依頼人コード -->
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCodeTitle" 
          value="#{pc_Xrb00606.propHurikomiIraininCode.labelName}"
          style="#{pc_Xrb00606.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="250">
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCode" 
          value="#{pc_Xrb00606.propHurikomiIraininCode.stringValue}"
          style="#{pc_Xrb00606.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 納付金名称 -->
         <h:outputText styleClass="outputText" id="lblNohukinNameTitle" 
          value="#{pc_Xrb00606.propNohukinName.labelName}"
          style="#{pc_Xrb00606.propNohukinName.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:outputText styleClass="outputText" id="lblNohukinName" 
          value="#{pc_Xrb00606.propNohukinName.stringValue}"
          style="#{pc_Xrb00606.propNohukinName.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- パターンコード -->
         <h:outputText styleClass="outputText" id="lblPatternCodeTitle"
          value="#{pc_Xrb00606.propPatternCode.labelName}"
          style="#{pc_Xrb00606.propPatternCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblPatternCode"
          value="#{pc_Xrb00606.propPatternCode.stringValue}"
          style="#{pc_Xrb00606.propPatternCode.labelStyle}">
         </h:outputText>
        </TD>
        <TH nowrap class="v_a">
         <!-- 分納区分名称 -->
         <h:outputText styleClass="outputText" id="lblBunoKbnNameTitle" 
          value="#{pc_Xrb00606.propBunoKbnName.labelName}"
          style="#{pc_Xrb00606.propBunoKbnName.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblBunoKbnName" 
          value="#{pc_Xrb00606.propBunoKbnName.stringValue}"
          style="#{pc_Xrb00606.propBunoKbnName.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 分割NO -->
         <h:outputText styleClass="outputText" id="lblBunkatuNoTitle"
          value="#{pc_Xrb00606.propBunkatuNo.labelName}"
          style="#{pc_Xrb00606.propBunkatuNo.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblBunkatuNo"
          value="#{pc_Xrb00606.propBunkatuNo.stringValue}"
          style="#{pc_Xrb00606.propBunkatuNo.labelStyle}">
         </h:outputText>
        </TD>
        <TH nowrap class="v_a">
         <!-- 内訳名称 -->
         <h:outputText styleClass="outputText" id="lblUtiwakeNameTitle" 
          value="#{pc_Xrb00606.propUtiwakeName.labelName}"
          style="#{pc_Xrb00606.propUtiwakeName.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblUtiwakeName" 
          value="#{pc_Xrb00606.propUtiwakeName.stringValue}"
          style="#{pc_Xrb00606.propUtiwakeName.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 請求日 -->
         <h:outputText styleClass="outputText" id="lblSeikyuDate"
          value="#{pc_Xrb00606.propSeikyuDate.labelName}"
          style="#{pc_Xrb00606.propSeikyuDate.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="inputText"
          id="htmlSeikyuDate"
          size="10"
          disabled="#{pc_Xrb00606.propSeikyuDate.disabled}"
          value="#{pc_Xrb00606.propSeikyuDate.dateValue}"
          style="#{pc_Xrb00606.propSeikyuDate.style}"
          readonly="#{pc_Xrb00606.propSeikyuDate.readonly}">
          <f:convertDateTime />
          <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
          <hx:inputHelperDatePicker />
         </h:inputText>
        </TD>
        <TH nowrap class="v_a">
         <!-- 納入期限 -->
         <h:outputText styleClass="outputText" id="lblNonyuKigenTitle" 
          value="#{pc_Xrb00606.propNonyuKigen.labelName}"
          style="#{pc_Xrb00606.propNonyuKigen.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblNonyuKigen" 
          value="#{pc_Xrb00606.propNonyuKigen.stringValue}"
          style="#{pc_Xrb00606.propNonyuKigen.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 請求金額 -->
         <h:outputText styleClass="outputText" id="lblSeikyuKingakuTitle" 
          value="#{pc_Xrb00606.propSeikyuKingaku.labelName}"
          style="#{pc_Xrb00606.propSeikyuKingaku.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:outputText styleClass="outputText" id="lblSeikyuKingaku" 
          value="#{pc_Xrb00606.propSeikyuKingaku.stringValue}"
          style="#{pc_Xrb00606.propSeikyuKingaku.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE width="800">
      <TBODY>
       <TR>
        <TD align="left" nowrap class="outputText" width="100%">
         内訳
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="800">
      <TBODY>
       <TR>
        <TH nowrap class="v_a">
         <!-- 変更後学科組織 -->
         <h:outputText styleClass="outputText" id="lblAfterCurGakkaTitle"
          value="#{pc_Xrb00606.propAfterCurGakka.labelName}"
          style="#{pc_Xrb00606.propAfterCurGakka.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblAfterCurGakka"
          value="#{pc_Xrb00606.propAfterCurGakka.stringValue}"
          style="#{pc_Xrb00606.propAfterCurGakka.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 変更後資格 -->
         <h:outputText styleClass="outputText" id="lblAfterSikak1Title"
          value="#{pc_Xrb00606.propAfterSikak1.labelName}"
          style="#{pc_Xrb00606.propAfterSikak1.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblAfterSikak1"
          value="#{pc_Xrb00606.propAfterSikak1.stringValue}"
          style="#{pc_Xrb00606.propAfterSikak1.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 変更後資格 -->
         <h:outputText styleClass="outputText" id="lblAfterSikak2Title"
          value="#{pc_Xrb00606.propAfterSikak2.labelName}"
          style="#{pc_Xrb00606.propAfterSikak2.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblAfterSikak2"
          value="#{pc_Xrb00606.propAfterSikak2.stringValue}"
          style="#{pc_Xrb00606.propAfterSikak2.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE width="800">
      <TBODY>
       <TR>
        <TD>
         <hx:commandExButton
          type="submit"
          value="確定"
          styleClass="commandExButton_dat"
          id="htmlKakuteiButton"
          disabled="#{pc_Xrb00606.propKakuteiButton.disabled}"
          action="#{pc_Xrb00606.doKakuteiAction}"
          confirm="#{msg.SY_MSG_0001W}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="削除"
          styleClass="commandExButton_dat"
          id="htmlDeleteButton"
          disabled="#{pc_Xrb00606.propDeleteButton.disabled}"
          action="#{pc_Xrb00606.doDeleteAction}"
          confirm="#{msg.SY_MSG_0004W}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
    </TD>
   </TR>
  </TBODY>
 </TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

