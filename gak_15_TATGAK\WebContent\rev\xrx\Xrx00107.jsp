<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00107.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<style type="text/css">
	.left {
		text-align: left;
	}
	.right {
		text-align: right;
	}
</style>
<f:subview id="Xrx00107">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrx00107.onPageLoadBegin}">
		<%-- ↓ コンテンツ部 ↓ --%>
		<hx:jspPanel>
			<DIV style="width:870px">
				<TABLE class="table" border="0" cellpadding="5" width="870">
					<TBODY>
						<TR align="left" valign="middle">
							<TH nowrap class="v_a" width="13%">
								<h:outputText styleClass="outputText" id="lblHitsuyoTanisu"
									value="#{pc_Xrx00107.propHitsuyoTanisu.labelName}">
								</h:outputText>
							</TH>
							<TD width="7%" align="right">
								<h:outputText styleClass="outputText" id="htmlHitsuyoTanisu"
									value="#{pc_Xrx00107.propHitsuyoTanisu.stringValue}">
								</h:outputText>
							</TD>
							<TH nowrap class="v_a" width="13%">
								<h:outputText styleClass="outputText" id="lblMenjomaeDaigaku"
										value="#{pc_Xrx00107.propMenjomaeDaigaku.labelName}">
								</h:outputText>
							</TH>
							<TD width="7%" align="right">
								<h:outputText styleClass="outputText" id="htmlMenjomaeDaigaku"
									value="#{pc_Xrx00107.propMenjomaeDaigaku.stringValue}">
								</h:outputText>
							</TD>
							<TH nowrap class="v_a" width="13%">
								<h:outputText styleClass="outputText" id="lblMenjoKyusekiTani"
									value="#{pc_Xrx00107.propMenjoKyusekiTani.labelName}">
								</h:outputText>
							</TH>
							<TD width="7%" align="right">
								<h:outputText styleClass="outputText" id="htmlMenjoKyusekiTani"
									value="#{pc_Xrx00107.propMenjoKyusekiTani.stringValue}">
								</h:outputText>
							</TD>
							<TH nowrap class="v_a" width="13%">
								<h:outputText styleClass="outputText" id="lblNinteiTotal"
									value="#{pc_Xrx00107.propNinteiTotal.labelName}">
								</h:outputText>
							</TH>
							<TD width="7%" align="right">
								<h:outputText styleClass="outputText" id="htmlNinteiTotal"
									value="#{pc_Xrx00107.propNinteiTotal.stringValue}">
								</h:outputText>
							</TD>
							<TH nowrap class="v_a" width="13%">
								<h:outputText styleClass="outputText" id="lblShutokuTotal"
									value="#{pc_Xrx00107.propShutokuTotal.labelName}">
								</h:outputText>
							</TH>
							<TD width="7%" align="right">
								<h:outputText styleClass="outputText" id="htmlShutokuTotal"
									value="#{pc_Xrx00107.propShutokuTotal.stringValue}">
								</h:outputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<h:dataTable border="0" cellpadding="0" cellspacing="0"
					id="htmlNendoSbtList" value="#{pc_Xrx00107.propNendoSbtList.list}" var="varlist">
					<h:column id="column1">
						<h:panelGrid columns="2" border="0" style="margin-top: 10px;" width="620"
							columnClasses="left,right">
							<h:panelGroup>
								<h:outputText styleClass="outputText" id="htmlNendoColumn"
									escape="false" value="年度：#{varlist.nendo}">
								</h:outputText>
								<h:outputText styleClass="outputText" id="htmlSchSbtColumn" escape="false" 
									value="　スクーリング種別 #{varlist.schoolingSbt.displayValue}"
									title="#{varlist.schoolingSbt.value}">
								</h:outputText>
							</h:panelGroup>
							<h:outputText styleClass="outputText" id="htmlTotalTaniColumn"
								escape="false" value="合計単位：#{varlist.totalTanisu}">
							</h:outputText>
						</h:panelGrid>
						<h:dataTable border="0" cellpadding="0" cellspacing="0"
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass" width="620"
							rowClasses="#{varlist.propJugyoList.rowClasses}"
							styleClass="meisai_scroll" id="htmlJugyoList"
							value="#{varlist.propJugyoList.list}" var="varlistX">
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="labelKamokColumn" styleClass="outputText"
										value="科目">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlKamok"
									value="#{varlistX.propKamok.displayValue}"
									title="#{varlistX.propKamok.value}">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText"
										value="評価"
										id="labelHyokaColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlHyoka"
									value="#{varlistX.hyoka}">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="labelTaniColumn"
										value="単位">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlTani"
									value="#{varlistX.tani}">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
						</h:dataTable>
					</h:column>
				</h:dataTable>
			</DIV>
		</hx:jspPanel>
		<BR>
		<%-- ↑ コンテンツ部 ↑ --%>
	</hx:scriptCollector>
</f:subview>
