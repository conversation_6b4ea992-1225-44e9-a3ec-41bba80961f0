<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00105.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>


<f:subview id="Xrx00105">
  <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00105.onPageLoadBegin}">

    <%-- ↓ コンテンツ部 ↓ --%>
    <hx:jspPanel>
      <DIV style="width:870px" align="center">
        <TABLE class="table" border="0" cellpadding="5" width="870">
          <TBODY>
            <TR align="left" valign="middle">
              <TH class="v_a" width="150">
				<h:outputText styleClass="outputText"
					id="lblNendo"
					value="#{pc_Xrx00105.propNendo.labelName}" 
					style="#{pc_Xrx00105.propNendo.labelStyle}">
				</h:outputText>
			  </TH>
			  <TD width="100">
				<h:inputText 
					id="htmlNendo" 
					styleClass="inputText" 
					readonly="#{pc_Xrx00105.propNendo.readonly}" 
					style="#{pc_Xrx00105.propNendo.style}" 
					value="#{pc_Xrx00105.propNendo.dateValue}"
					disabled="#{pc_Xrx00105.propNendo.disabled}" 
					tabindex="1" size="4">
					<hx:inputHelperAssist errorClass="inputText_Error"
					promptCharacter="_" />
					<f:convertDateTime pattern="yyyy" />
				</h:inputText>
			  </TD>
			  
			  <TH class="v_a" width="150">
				<h:outputText styleClass="outputText"
					id="lblKamokSikenCnt"
					value="#{pc_Xrx00105.propKamokSikenCnt.labelName}" 
					style="#{pc_Xrx00105.propKamokSikenCnt.labelStyle}">
				</h:outputText>
			  </TH>
			  <TD width="100">
				<h:inputText 
					id="htmlKamokSikenCnt" 
					styleClass="inputText" 
					readonly="#{pc_Xrx00105.propKamokSikenCnt.readonly}" 
					style="#{pc_Xrx00105.propKamokSikenCnt.style}" 
					value="#{pc_Xrx00105.propKamokSikenCnt.integerValue}"
					disabled="#{pc_Xrx00105.propKamokSikenCnt.disabled}" 
					tabindex="2" size="4">
					<hx:inputHelperAssist errorClass="inputText_Error"
								    imeMode="inactive" promptCharacter="_" />
					<f:convertNumber type="number" pattern="#0"/>
					<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" />
				</h:inputText>回
			  </TD>
			  
			  <TH class="v_a" width="150">
				<h:outputText styleClass="outputText"
					id="lblKamokCd"
					value="#{pc_Xrx00105.propKamokCd.labelName}" 
					style="#{pc_Xrx00105.propKamokCd.labelStyle}">
				</h:outputText>
			  </TH>
			  <TD width="100">
				<h:inputText 
					id="htmlKamokCd"
					styleClass="inputText" 
					readonly="#{pc_Xrx00105.propKamokCd.readonly}"
					style="#{pc_Xrx00105.propKamokCd.style}" 
					value="#{pc_Xrx00105.propKamokCd.stringValue}"
					disabled="#{pc_Xrx00105.propKamokCd.disabled}"
					maxlength="#{pc_Xrx00105.propKamokCd.maxLength}" 
					tabindex="3" size="8">
				</h:inputText>
			  </TD>
            </TR>
          </TBODY>
        </TABLE>
        
        <BR>
    
        <TABLE width="870" border="0" cellpadding="0" cellspacing="0" 
									class="button_bar">
			<TBODY>
				<TR>
					<TD>
						<hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="search"
							value="検索"
							action="#{pc_Xrx00105.doSearchAction}"
							disabled="#{pc_Xrx00105.propSearch.disabled}"
							rendered="#{pc_Xrx00105.propSearch.rendered}"
							style="#{pc_Xrx00105.propSearch.style}" tabindex="4">
						</hx:commandExButton>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		
		</DIV>
        
        <DIV style="width:920px" align="center">
        				
        <TABLE border="0" style="margin-top:16px">
		<TBODY>
			<TR>
			<TD>
				<DIV id="listScroll" class="listScroll" style="height: 256px;">
					<h:dataTable 
						columnClasses="columnClass" 
						headerClass="headerClass"
						footerClass="footerClass"
						rowClasses="#{pc_Xrx00105.propList.rowClasses}"
						styleClass="meisai_scroll" id="htmlList"
						value="#{pc_Xrx00105.propList.list}" var="varlist">

						<h:column id="column1">
							<f:facet name="header">
								<h:outputText styleClass="outputText"
									value="科目試験名称"
									id="lblListKamokSikenNm">
								</h:outputText>
							</f:facet>
								<h:outputText styleClass="outputText" 
									id="htmlListKamokSikenNm"
									value="#{varlist.kamokSikenNm.displayValue}"
									title="#{varlist.kamokSikenNm.stringValue}">
								</h:outputText>
							<f:attribute value="220" name="width" />
							<f:attribute value="text-align: left" name="style" />
						</h:column>
											
						<h:column id="column2">
							<f:facet name="header">
								<h:outputText styleClass="outputText"
									value="曜日"
									id="lblListYobi">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListYobi"
								style="#{pc_Xrx00105.propListYobi.style}"
								value="#{varlist.yobi}">
							</h:outputText>
							<f:attribute value="60" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
											
						<h:column id="column3">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="試験日"
									id="lblListSikenDate">
								</h:outputText>
							</f:facet>
							<h:outputText 
								styleClass="outputText" 
								id="htmlListSikenDate"
								style="#{pc_Xrx00105.propListSikenDate.style}"
								value="#{varlist.sikenDate}">
								<f:convertDateTime />
							</h:outputText>
							<f:attribute value="130" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
											
						<h:column id="column4">
							<f:facet name="header">
									<h:outputText styleClass="outputText" 
										value="試験地"
										id="lblListSikenti">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" 
									id="htmlListSikenti"
									value="#{varlist.sikenti.displayValue}"
									title="#{varlist.sikenti.stringValue}">
								</h:outputText>
								<f:attribute value="200" name="width" />
								<f:attribute value="text-align: left" name="style" />
						</h:column>
						
						<h:column id="column5">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="科目名称"
									id="lblListKamokNm">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListKamokNm"
								value="#{varlist.kamokNm.displayValue}"
								title="#{varlist.kamokNm.stringValue}">
							</h:outputText>
							<f:attribute value="350" name="width" />
						</h:column>

						<h:column id="column6">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="時限"
									id="lblListJigen">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListJigen"
								value="#{varlist.jigen}">
							</h:outputText>
							<f:attribute value="60" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>

						<h:column id="column7">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="無効理由"
									id="lblListMukoriyu">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListMukoriyu"
								value="#{varlist.mukoriyu.displayValue}"
								title="#{varlist.mukoriyu.stringValue}">
							</h:outputText>
							<f:attribute value="200" name="width" />
							<f:attribute value="text-align: left" name="style" />
						</h:column>
											
						<h:column id="column8">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="整理番号"
									id="lblListSeiriNo">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListSeiriNo"
								style="#{pc_Xrx00105.propListSeiriNo.style}"
								value="#{varlist.seiriNo}">
							</h:outputText>
							<f:attribute value="110" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						
						<h:column id="column9">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="出欠"
									id="lblListSyuketu">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListSyuketu"
								style="#{pc_Xrx00105.propListSyuketu.style}"
								value="#{varlist.syuketu}">
							</h:outputText>
							<f:attribute value="60" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>

						<h:column id="column10">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="評価"
									id="lblListHyoka">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListHyoka"
								style="#{pc_Xrx00105.propListHyoka.style}"
								value="#{varlist.hyoka}">
							</h:outputText>
							<f:attribute value="60" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>

						<h:column id="column11">
							<f:facet name="header">
								<h:outputText styleClass="outputText" 
									value="失効(予定)日"
									id="lblListSikkoYoteiDate">
								</h:outputText>
							</f:facet>
							<h:outputText styleClass="outputText" 
								id="htmlListSikkoYoteiDate"
								style="#{pc_Xrx00105.propListSikkoYoteiDate.style}"
								value="#{varlist.sikkoYoteiDate}">
							</h:outputText>
							<f:attribute value="150" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
					</h:dataTable>
				</DIV>
			</TD>
			</TR>
			
			<TR>
				<TD align="right">
					<h:outputText styleClass="outputText"
						value="※失効予定となった場合、失効（予定）日に*が表示されます。">
					</h:outputText>
				</TD>
			</TR>						
		</TBODY>
		</TABLE>
									
      </DIV>
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
  </hx:scriptCollector>
</f:subview>