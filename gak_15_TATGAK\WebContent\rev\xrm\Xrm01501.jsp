
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm01501.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>Xrm01501.jsp</TITLE>
<SCRIPT type="text/javascript">
	

	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('search');
	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}
	
	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm01501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm01501.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrm01501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm01501.screenName}"></h:outputText></div>

			<!--↓OUTER↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"></DIV>

			<!--↓CONTENT↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="900px">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" width="50%" cellpadding="0" class="table"
							cellspacing="0">
							<TBODY>
								<%-- 基準日 --%>
								<TR>
									<TH nowrap class="v_a" width="150px"><h:outputText
										styleClass="outputText" id="lblKijunDate"
										value="#{pc_Xrm01501.propKijunDate.labelName}"
										style="#{pc_Xrm01501.propKijunDate.labelStyle}">
									</h:outputText></TH>
									<TD nowrap valign="middle" width="*"><h:inputText
										styleClass="inputText" id="htmlKijunDate"
										value="#{pc_Xrm01501.propKijunDate.value}" size="11"
										style="#{pc_Xrm01501.propKijunDate.style}">
										<f:convertDateTime pattern="yyyy/MM/dd" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText></TD>
								</TR>
								<!-- 出力種別 -->
								<TR>
									<TH nowrap class="v_a" width="150px"><h:outputText
										styleClass="outputText" id="lblIktOutTaisyou"
										value="#{pc_Xrm01501.propOutTaisyou.labelName}"
										style="#{pc_Xrm01501.propOutTaisyou.labelStyle}">
									</h:outputText></TH>
									<TD width="*" colspan=3><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlIktOutTaisyou"
										value="#{pc_Xrm01501.propOutTaisyou.value}"
										style="#{pc_Xrm01501.propOutTaisyou.style}" tabindex="25"
										layout="pageDirection">
										
										<f:selectItem itemValue="1" itemLabel="年度別在籍者数" />
										<f:selectItem itemValue="2" itemLabel="年度期別在籍者数" />
										<f:selectItem itemValue="3" itemLabel="年度期年次別在籍者数" />
									</h:selectOneRadio></TD>
								</TR>
								<!-- 就学種別 -->
								<TR>
									<TH width="150px" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSyugakSbtCd"
										value="#{pc_Xrm01501.propSyugakSbt.labelName}"
										style="#{pc_Xrm01501.propSyugakSbt.labelStyle}">
									</h:outputText></TH>

									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSyugakSbtCd"
										disabled="#{pc_Xrm01501.propSyugakSbt.disabled}"
										value="#{pc_Xrm01501.propSyugakSbt.value}"
										
										style="width:210px;">
										<f:selectItems value="#{pc_Xrm01501.propSyugakSbt.list}" />
									</h:selectOneMenu> <h:commandLink styleClass="commandLink"
										id="linkSyugakSbt" style="visibility:hidden"
										action="#{pc_Xrm01501.doLinkSyugakSbtAction}">
										<h:outputText id="htmlLinkSyugakSbt" styleClass="outputText"></h:outputText>
									</h:commandLink></TD>
								</TR>
								<TR>
									<!-- 入学年度 -->
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblNyugakNendo"
										value="#{pc_Xrm01501.propNyugakNendo.labelName}"
										style="#{pc_Xrm01501.propNyugakNendo.labelStyle}"></h:outputText></TH>
									<TD width="225"><h:inputText styleClass="inputText"
										id="htmlNyugakNendo" size="4" 
										disabled="#{pc_Xrm01501.propNyugakNendo.disabled}"
										value="#{pc_Xrm01501.propNyugakNendo.dateValue}"
										readonly="#{pc_Xrm01501.propNyugakNendo.readonly}"
										style="#{pc_Xrm01501.propNyugakNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<%--　入学学期  --%>
								<TR>
									<TH nowrap class="v_a" width="150px"><h:outputText
										styleClass="outputText" id="propgakkiCD"
										value="#{pc_Xrm01501.propgakkiCD.name}"
										style="#{pc_Xrm01501.propgakkiCD.labelStyle}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu" id="propgakki"
										style="width:150px;"
										value="#{pc_Xrm01501.propgakkiList.value}" tabindex="7">
										<f:selectItems value="#{pc_Xrm01501.propgakkiList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<%-- 入学年次 --%>
									<TH width="150px" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="propnenjiCD"
										value="#{pc_Xrm01501.propnenjiCD.name}"
										style="#{pc_Xrm01501.propnenjiCD.labelStyle}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="propnenjiList" style="width:150px;"
										value="#{pc_Xrm01501.propnenjiList.value}" tabindex="7">
										<f:selectItems value="#{pc_Xrm01501.propnenjiList.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>

						<TD height="20px"></TD>

					</TR>

					<TR>
						<TD>
						<TABLE class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD width="100%"><hx:commandExButton type="submit"
										value="CSV作成" styleClass="commandExButton_out" id="csvout"
										confirm="#{msg.SY_MSG_0020W}"
										action="#{pc_Xrm01501.doCsvoutAction}">
									</hx:commandExButton>&nbsp;</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
</SCRIPT>
</HTML>
