<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc01101T03.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<SCRIPT language="text/javascript" ></SCRIPT>

<TITLE>xrc00101T03.jsp</TITLE>

<SCRIPT type="text/javascript">

	function confirmOk() {
		document.getElementById('form1:propExecutableSearch').value = "1";
		indirectClick('search');
	}
	
	function confirmCancel() {
		document.getElementById('form1:propExecutableSearch').value = "0";
	}
	
	function fncButtonActive(){
		var codeRegSearch = null;
		//選択ボタン
		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch').value;
		if(codeRegSearch == 1){
			document.getElementById('form1:search').disabled = true;
		}
		//スクロールバーの位置を保持
		window.attachEvent('onload', endload);	
	}
	function endload() {
		//スクロールバーの位置を保持
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}

	//-------------------------------------------
	// 物品名称取得 
	//-------------------------------------------
	function doBpnAjax(thisObj, thisEvent, targetLabel) {
	// 物品名称を取得する
		var servlet = "rev/xrc/XrcBpnAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	//-------------------------------------------
	// 物品検索子画面オープン
	//-------------------------------------------
	function openPXrc0101(targetId) {
		var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0101.jsp?retFieldName=" + targetId;
		openModalWindow(url, "PXrc0101", "<%=com.jast.gakuen.rev.xrc.PXrc0101.getWindowOpenOption() %>");
		return false;
	}

 	//-------------------------------------------
	// 画面ロード時の物品名称再取得
	//-------------------------------------------
	function loadAction(event){
		doBpnAjax(document.getElementById('form1:htmlInputBuppinCd'), event, 'form1:lblPreBuppinName');
	}

</SCRIPT></HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="fncButtonActive();loadAction(event);">
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrc01101T03.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page ="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrc01101T01.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrc01101T01.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrc01101T01.screenName}"></h:outputText>
			</div>

			<!--↓outer↓-->
	
			<DIV class="outer">

				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>

				<DIV id="content">
					<DIV class="column" align="center">

						<TABLE
							width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE
											border="0"
											cellpadding="0"
											cellspacing="0"
											width="100%">
											<TBODY>
												<TR align="left">
													<TD>
														<hx:commandExButton
															type="submit"
															value="学生指定"
															styleClass="tab_head_off"
															id="htmlGakTab"
															action="#{pc_Xrc01101T03.doLinkHifGakseiTabAction}"></hx:commandExButton><hx:commandExButton
															type="submit"
															value="科目指定"
															styleClass="tab_head_off"
															id="htmlKmkTab"
															action="#{pc_Xrc01101T03.doLinkHifKmkTabAction}"></hx:commandExButton><hx:commandExButton
															type="submit"
															value="物品指定"
															styleClass="tab_head_on"
															id="htmlBpnTab">
														</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TD>

														<TABLE
															border="0"
															cellpadding="0"
															cellspacing="0"
															width="100%"
															class="tab_body">
															<TBODY>
																<TR align="center">
																	<TD height="20px" >

											<TABLE width="850px">
												<TR>
													<TD height="5px"></TD>
												</TR>
												<TR>
													<TD> 

														<TABLE width="100%" border="0" class="table" style="table-layout: fixed;">
															<TBODY>
																<TR>
																	<!-- 物品コード -->
																	<TH nowrap class="v_a" width="150px">
																		<h:outputText
																			styleClass="outputText"
																			value="#{pc_Xrc01101T03.propBpnCode.labelName}"
																			id="lblKmkCd">
																		</h:outputText>
																	</TH>
																	
																	<TD>
																		<h:inputText
																			styleClass="inputText"
																			id="htmlInputBuppinCd"
																			value="#{pc_Xrc01101T03.propXrcBpnCd.stringValue}"
																			maxlength="#{pc_Xrc01101T03.propXrcBpnCd.maxLength}"
																			style="#{pc_Xrc01101T03.propXrcBpnCd.style}"
																			size="10"
																			onblur="return doBpnAjax(this, event, 'form1:lblPreBuppinName');">
																		</h:inputText>
																		<hx:commandExButton type="button" value="検"
																			styleClass="commandExButton_search"
																			onclick="return openPXrc0101('form1:htmlInputBuppinCd');">
																		</hx:commandExButton>
																		<h:outputText styleClass="outputText" id="lblPreBuppinName"/>
																	</TR>
																	<TR>	
																
																	<!-- 配本日 -->
																	<TH nowrap class="v_a" width="150px">
																		<h:outputText
																			styleClass="outputText"
																			id="lblInputHaihonDate"
																			value="#{pc_Xrc01101T03.propHaihonDate.labelName}"
																			style="#{pc_Xrc01101T03.propHaihonDate.labelStyle}">
																		</h:outputText>
																	</TH>
																	<TD>
																		<h:inputText
																			styleClass="inputText"
																			id="htmlBeforeHaihonDate"
																			value="#{pc_Xrc01101T03.propXrcBeforeHaihonDate.dateValue}"
																			readonly="#{pc_Xrc01101T03.propXrcBeforeHaihonDate.readonly}"
																			style="#{pc_Xrc01101T03.propXrcBeforeHaihonDate.style}"
																			disabled="#{pc_Xrc01101T03.propXrcBeforeHaihonDate.disabled}"
																			size="10">
																			<f:convertDateTime />
																				<hx:inputHelperAssist
																					errorClass="inputText_Error"
																					promptCharacter="_" />
																				<hx:inputHelperDatePicker />
																		</h:inputText>
																		&nbsp;&nbsp;&nbsp;～&nbsp;&nbsp;&nbsp;
																		<h:inputText
																			styleClass="inputText"
																			id="htmlAfterHaihonDate"
																			value="#{pc_Xrc01101T03.propXrcAfterHaihonDate.dateValue}"
																			readonly="#{pc_Xrc01101T03.propXrcAfterHaihonDate.readonly}"
																			style="#{pc_Xrc01101T03.propXrcAfterHaihonDate.style}"
																			disabled="#{pc_Xrc01101T03.propXrcAfterHaihonDate.disabled}"
																			size="10">
																			<f:convertDateTime />
																				<hx:inputHelperAssist
																					errorClass="inputText_Error"
																					promptCharacter="_" />
																				<hx:inputHelperDatePicker />	
																		</h:inputText>
																	</TD>
																	<TD style="background-color: rgb(232,232,232);
																		border-top-style:none; 
																		border-right-style:none; 
																		border-bottom-style:none;">
																		&nbsp;&nbsp;&nbsp;
																		<hx:commandExButton 
																			type="submit"
																			value="検索" styleClass="commandExButton_dat" id="search"
																			action="#{pc_Xrc01101T03.doSearchAction}">
																		</hx:commandExButton>
																		
																	</TD>
																</TR>
															</TBODY>
														</TABLE>

														<TABLE
															border="0"
															cellpadding="0"
															cellspacing="0"
															width="100%">
															<TBODY>
																<TR>
																	<TD align="right">
																		<h:outputText
																			styleClass="outputText"
																			id="htmlCount"
																			value="#{pc_Xrc01101T03.propTable.listCount}">
																		</h:outputText>
																		<h:outputText
																			styleClass="outputText"
																			id="lblCount" value="件">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD>
																		<DIV style="height:380px; width=100%;" id="listScroll" onscroll="setScrollPosition('htmlHidScroll',this);" class="listScroll">
																			<h:dataTable
																				footerClass="footerClass"
																				rowClasses="#{pc_Xrc01101T03.propTable.rowClasses}"
																				headerClass="headerClass"
																				styleClass="meisai_scroll"
																				id="htmlTable"
																				value="#{pc_Xrc01101T03.propTable.list}"
																				var="varlist"
																				width="850px">
																				
																				<h:column id="column1" >
																					<f:facet name="header" >
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propHaihonDate.labelName}"
																							id="lblHaihonDate">
																						</h:outputText>
																					</f:facet>
																				<DIV align=right>
																					<h:outputText
																						id="htmlHaihonDate"
																						value="#{varlist.haihonDate}"
																						styleClass="outputText">
																					</h:outputText>
																				</DIV>
																					<f:attribute value="70px" name="width" />
																				</h:column>
																				
																				<h:column id="column2">
																					<f:facet name="header">
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propGaksekiCd.labelName}"
																							id="lblGaksekiNo">
																						</h:outputText>
																					</f:facet>
																				<DIV align=right>
																					<h:outputText
																						id="htmlGaksekiNo"
																						value="#{varlist.gaksekiNo}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="70px" name="width" />
																				
																				</DIV>
																			
																				</h:column>
																				<h:column id="column3">
																					<f:facet name="header">
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propGakseiNm.labelName}"
																							id="lblGakseiNmOut">
																						</h:outputText>
																					</f:facet>
																					<h:outputText
																						id="htmlGakseiNmOut"
																						value="#{varlist.gakseiNmOut.displayValue}"
																						title="#{varlist.gakseiNmOut.value}"
																						styleClass="outputText">
																					</h:outputText>
																						<f:attribute value="200px" name="width" />
																						<f:attribute value="text-align: left" name="style" />
																				</h:column>
																				<h:column id="column4">
																					<f:facet name="header">
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propKmkCode.labelName}"
																							id="lblKmkOutCd">
																						</h:outputText>
																					</f:facet>
																					<h:outputText
																						id="htmlKmkCd"
																						value="#{varlist.kmkCd}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="70px" name="width" />
																				</h:column>
																				<h:column id="column5">
																					<f:facet name="header">
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propKmkName.labelName}"
																							id="lblKmkNameOut">
																						</h:outputText>
																					</f:facet>
																					<h:outputText
																						id="htmlKmkNameOut"
																						value="#{varlist.kmkNameOut.displayValue}"
																						title="#{varlist.kmkNameOut.value}"
																						styleClass="outputText">
																					</h:outputText>
																						<f:attribute value="180px" name="width" />
																						<f:attribute value="text-align: left" name="style" />
																				</h:column>
																			
																				<h:column id="column6">
																					<f:facet name="header">
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propEdaban.labelName}"
																							id="lblEdaban">
																						</h:outputText>
																					</f:facet>
																					<h:outputText
																						id="htmlEdaban"
																						value="#{varlist.edaban}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="30px" name="width" />
																				</h:column>
																				<h:column id="column7">
																					<f:facet name="header">
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propEdition.labelName}"
																							id="lblEdition">
																						</h:outputText>
																					</f:facet>
																					<h:outputText
																						id="htmlEdition"
																						value="#{varlist.edition}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="30px" name="width" />
																				</h:column>
																				<h:column id="column8">
																					<f:facet name="header">
																						<h:outputText
																							styleClass="outputText"
																							value="#{pc_Xrc01101T03.propHaifuKbn.labelName}"
																							id="lblHaifuKbn">
																						</h:outputText>
																					</f:facet>
																					<h:outputText
																						id="htmlHaifuKbn"
																						value="#{varlist.haifuKbn}"
																						styleClass="outputText">
																					</h:outputText>
																					<f:attribute value="70px" name="width" />
																				</h:column>
																			</h:dataTable>
																		</DIV>
																	</TD>
																</TR>
																<TR>
																	<TD align=right>
																		<h:outputText
																			styleClass="outputText"
																			id="lblSum" 
																			value="#{pc_Xrc01101T03.propSumCount.labelName}">
																		</h:outputText>
																		<h:outputText
																			styleClass="outputText"
																			id="htmlSum"
																			value="#{pc_Xrc01101T03.propSumCount.integerValue}">
																		</h:outputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
												<TR>
													<TD height="10px"></TD>
												</TR>
									
												<TR>
													<TD height="10px"></TD>
												</TR>
												<TR>
													<TD height="10px"></TD>
												</TR>
											</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			</DIV> 	
			<!--↑outer↑-->

			<h:inputHidden
				value="#{pc_Xrc01101T03.propTable.scrollPosition}"
				id="htmlHidScroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrc01101T03.propActiveControlSearch.value}"
				id="htmlActiveControlSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrc01101T03.propExecutableSearch.integerValue}"
				id="propExecutableSearch">
			<f:convertNumber />
			</h:inputHidden>

			<!-- フッダーインクルード -->
			<jsp:include page ="../inc/footer.jsp" />

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
