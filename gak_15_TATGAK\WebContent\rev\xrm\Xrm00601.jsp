
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00601.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>Xrm00601.jsp</TITLE>

<SCRIPT type="text/javascript">


	function fncInit() {
	    	//ＰＤＦ作成ボタンの制御
    	if(document.getElementById('form1:htmlHidErrorKbn').value == '1'){
    		document.getElementById('form1:pdfout').disabled = true;
    	}
	}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncInit();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm00601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm00601.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrm00601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm00601.screenName}"></h:outputText></div>

			<!--↓OUTER↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"></DIV>

			<!--↓CONTENT↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="900px">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="3" width="80%" cellpadding="0" class="table"
							cellspacing="0">
							<TBODY>

								<!-- 学費年度 -->
								<TR>
									<TH nowrap class="v_a" width="150px"><h:outputText
										styleClass="outputText" id="lblGhYear"
										value="#{pc_Xrm00601.propGhYear.labelName}"
										style="#{pc_Xrm00601.propGhYear.labelStyle}">
									</h:outputText></TH>
									<TD width="*" colspan=2><h:inputText styleClass="inputText"
										id="htmlGhYear" size="4"
										value="#{pc_Xrm00601.propGhYear.value}"
										style="#{pc_Xrm00601.propGhYear.style}"
										disabled="#{pc_Xrm00601.propGhYear.disabled}" tabindex="1">
										<hx:inputHelperAssist imeMode="inactive"
											errorClass="inputText_Error" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
								</TR>

								<!-- 就学種別 -->
								<TR>
									<TH width="150px" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblSyugakSbt"
										value="#{pc_Xrm00601.propSyugakSbt.labelName}"
										style="#{pc_Xrm00601.propSyugakSbt.labelStyle}">
									</h:outputText></TH>

									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSyugakSbt"
										disabled="#{pc_Xrm00601.propSyugakSbt.disabled}"
										value="#{pc_Xrm00601.propSyugakSbt.stringValue}"
										style="width:210px;">
										<f:selectItems value="#{pc_Xrm00601.propSyugakSbt.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<%--業務コード  --%>
								<TR>
									<TH width="150px" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblGyoumCd"
										value="#{pc_Xrm00601.propGyoumCd.labelName}"
										style="#{pc_Xrm00601.propGyoumCd.labelStyle}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGyoumCd" 
										style="#{pc_Xrm00601.propGyoumCd.style}"
								        disabled="#{pc_Xrm00601.propGyoumCd.disabled}" 
										value="#{pc_Xrm00601.propGyoumCd.stringValue}" tabindex="7">
										<f:selectItems value="#{pc_Xrm00601.propGyoumCd.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>

					<TR>

						<TD height="20px"></TD>

					</TR>

					<TR>
						<TD>
						<TABLE class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD width="100%"><hx:commandExButton type="submit"
										value="PDF作成" styleClass="commandExButton_out" id="pdfout"
										confirm="#{msg.SY_MSG_0019W}"
										action="#{pc_Xrm00601.doPdfoutAction}" tabindex="12">
									</hx:commandExButton>&nbsp;</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Xrm00601.propErrorKbn.stringValue}"
				id="htmlHidErrorKbn"></h:inputHidden>


		</h:form>
	</hx:scriptCollector>
	</BODY>

	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
