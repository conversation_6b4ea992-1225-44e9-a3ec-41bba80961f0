<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrl00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00401.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrl00401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00401.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<br/>
			<br/>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="100%">
						<CENTER><TABLE class="table" width="500" style="margin:0 auto">
							<TBODY>
								<TR>
									<TD style="border: medium none;background-color:#f7f7f7;">
										<h:outputText styleClass="outputText" id="lblFromNendo"
											 value="コピー元" >
										</h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_a" width="100">
										<h:outputText styleClass="outputText" id="lblFromTeateNendo"
											 value="#{pc_Xrl00401.propTeateNendo.labelName}" 
											 style="#{pc_Xrl00401.propTeateNendo.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="100"><h:inputText styleClass="inputText"
										id="txtFromTeateNendo"
										value="#{pc_Xrl00401.propFromTeateNendo.dateValue}"
										style="#{pc_Xrl00401.propFromTeateNendo.style}"
										maxlength="#{pc_Xrl00401.propFromTeateNendo.maxLength}" size="6">
										<hx:inputHelperAssist errorClass="inputText_Error"
						    			imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>
								</TR>
								<TR style="border: medium none; height: 40px;">
								</TR>
								<TR>
									<TD style="border: medium none;background-color:#f7f7f7;"> 
										<h:outputText styleClass="outputText" id="lblToNendo"
											 value="コピー先">
										</h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_a" width="100">
										<h:outputText styleClass="outputText" id="lblToTeateNendo"
											 value="#{pc_Xrl00401.propTeateNendo.labelName}" 
											 style="#{pc_Xrl00401.propTeateNendo.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="100"><h:inputText styleClass="inputText"
										id="txtToTeateNendo"
										value="#{pc_Xrl00401.propToTeateNendo.dateValue}"
										style="#{pc_Xrl00401.propToTeateNendo.style}"
										maxlength="#{pc_Xrl00401.propToTeateNendo.maxLength}" size="6">
										<hx:inputHelperAssist errorClass="inputText_Error"
						    			imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>
								</TR>
								<TR style="border: medium none; height: 60px;">
								</TR>
								<TR>
									
								</TR>
							</TBODY>
						</TABLE>
						<TABLE class="table" width="500px">
							<TBODY>
								<TR>
									<TH nowrap class="v_e" style="padding-left:30px ;padding-bottom:18px"><h:outputText
										styleClass="outputText" id="lblcheck" style="width:210px" value="チェックリスト出力指定"></h:outputText></TH>
									<TD width="290"><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="infoCheck" value="#{pc_Xrl00401.propInfoCheck.checked}"></h:selectBooleanCheckbox>正常データ
										<br/>
										<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="errorCheck" value="#{pc_Xrl00401.propErrorCheck.checked}"></h:selectBooleanCheckbox>エラーデータ
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</CENTER>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<br/>
			<TABLE width="500" class="button_bar" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TD width="500"><hx:commandExButton
							type="submit" value="実　行" styleClass="commandExButton_out"
							id="exec" confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Xrl00401.doExecAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

