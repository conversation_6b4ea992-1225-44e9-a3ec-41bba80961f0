<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00701.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00701.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 --> <!-- ↑ここにボタンを配置 -->
			</DIV>

			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="500">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblNendo" value="#{pc_Xrh00701.propNendo.labelName}"
							style="#{pc_Xrh00701.propNendo.labelStyle}">
						</h:outputText></TH>
						<TD><h:inputText id="htmlNendo" styleClass="inputText"
							tabindex="1"
							readonly="#{pc_Xrh00701.propNendo.readonly}"
							style="#{pc_Xrh00701.propNendo.style}"
							value="#{pc_Xrh00701.propNendo.dateValue}"
							disabled="#{pc_Xrh00701.propNendo.disabled}" size="4">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
						<TD width="120" nowrap
							style="background-color: transparent;border-bottom-style: none; border-top-style: none; border-right-style: none; text-align: right">
						<hx:commandExButton type="submit" value="選択"
							styleClass="commandExButton" id="search" tabindex="2"
							action="#{pc_Xrh00701.doSearchAction}"
							disabled="#{pc_Xrh00701.propSearchButton.disabled}"
							style="#{pc_Xrh00701.propSearchButton.style}">
						</hx:commandExButton> <hx:commandExButton type="submit" value="解除"
							tabindex="3" styleClass="commandExButton" id="clear"
							action="#{pc_Xrh00701.doClearAction}"
							disabled="#{pc_Xrh00701.propClearButton.disabled}"
							style="#{pc_Xrh00701.propClearButton.style}">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="500">
				<TBODY>
					<TR>
						<TH nowrap class="v_b" width="150"><h:outputText
							styleClass="outputText" id="lblSikenNm"
							value="#{pc_Xrh00701.propSikenNm.labelName}"
							style="#{pc_Xrh00701.propSikenNm.labelStyle}">
						</h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu" id="htmlSikenNm"
							tabindex="4"
							value="#{pc_Xrh00701.propSikenNm.value}"
							style="#{pc_Xrh00701.propSikenNm.style}; width:300px"
							disabled="#{pc_Xrh00701.propSikenNm.disabled}">
							<f:selectItems value="#{pc_Xrh00701.propSikenNm.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE width="500" border="0" cellpadding="0" cellspacing="0"
				class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="csvout" value="CSV作成"
							tabindex="5"
							action="#{pc_Xrh00701.doCsvOutAction}"
							disabled="#{pc_Xrh00701.propCsv.disabled}"
							rendered="#{pc_Xrh00701.propCsv.rendered}"
							style="#{pc_Xrh00701.propCsv.style}">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
			</DIV>

			</DIV>
			<!-- フッターインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>

	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
