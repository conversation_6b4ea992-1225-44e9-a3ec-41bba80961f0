<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00102.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<f:subview id="Xrx00102">
  <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00102.onPageLoadBegin}">
    <%-- ↓ コンテンツ部 ↓ --%>
    <hx:jspPanel>
      <DIV style="width:870px">
      									<hx:jspPanel id="pnlAll" rendered="#{pc_Xrx00102.propPnlAll.rendered}">
      									<hx:jspPanel id="pnlKigen" rendered="#{pc_Xrx00102.propPnlKigen.rendered}">
      									<TABLE class="table" width="840">
      										<TBODY>
												<TR>
													<TH nowrap class="v_e" width="180">
														<h:outputText styleClass="outputText" id="htmlDspZaisekiKgn"
														value="在籍期限"></h:outputText></TH>
													<TD width="200" style="border-right-style:none;"><h:outputText styleClass="outputText" id="htmlZaisekiKgn"
														value="#{pc_Xrx00102.propZaisekiKgn.stringValue}"></h:outputText></TD>
													<TD style="border-left-style:none;"><h:outputText styleClass="outputText" id="htmlNengenMsg"
														value="#{pc_Xrx00102.propNengenMsg.stringValue}"
														style="#{pc_Xrx00102.propNengenMsg.style}"></h:outputText></TD>
												</TR>
											</TBODY>
      									</TABLE>
      									<br/>
      									</hx:jspPanel>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="840">
											<TR>
												<TD>
												<div class="listScroll" id="listScroll" style="height: 148px"
													onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
													border="1" cellpadding="2" cellspacing="0"
													columnClasses="columnClass1" headerClass="headerClass"
													footerClass="footerClass"
													rowClasses="#{pc_Xrx00102.propIdoSinseiList.rowClasses}"
													styleClass="meisai_scroll" id="htmlIdoSinseiList"
													value="#{pc_Xrx00102.propIdoSinseiList.list}" var="varlist">
													<h:column id="column1">
														<f:facet name="header">
															<h:outputText id="htmlIdoNo_head" styleClass="outputText"
																value="異動ＮＯ"></h:outputText>
														</f:facet>
														<f:attribute value="65" name="width" />
														<f:attribute value="text-align:right" name="style" />
														<h:outputText styleClass="outputText" id="htmlIdoNo_list"
															value="#{varlist.idoNo}">
														</h:outputText>
													</h:column>
													<h:column id="column2">
														<f:facet name="header">
															<hx:jspPanel id="jspPanel1">
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	width="100%" height="100%">
																	<TBODY>
																		<TR>
																			<TH
																				style="border-right-style: none; border-style: none; text-align: center;"
																				width="305" colspan="2">異動出学種別</TH>
																		</TR>
																		<TR>
																			<TH
																				style="border-bottom-style: none; text-align: center;"
																				width="75"><h:outputText styleClass="outputText"
																				id="htmlIdoSyutgakSbtCd_head" value="コード"></h:outputText>
																			</TH>
																			<TH
																				style=" border-right-style: none; border-bottom-style: none; text-align: center;"
																				width="240"><h:outputText styleClass="outputText"
																				id="htmlIdoSyutgakSbtName_head" value="名称"></h:outputText>
																			</TH>
																		</TR>
																	</TBODY>
																</TABLE>
															</hx:jspPanel>
														</f:facet>
														<hx:jspPanel id="jspPanel2">
															<TABLE border="0" cellpadding="0"
																cellspacing="0" width="100%" height="100%"
																style="border-bottom-style:none;border-top-style:none;">
																<TBODY>
																	<TR>
																		<TD
																		
																			style="border-top-style:none; border-bottom-style: none; border-left-style: none;"
																			width="75"><h:outputText styleClass="outputText"
																			id="htmlIdoSyutgakSbtCd_list"
																			value="#{varlist.propIdoSyutgakSbtCd.stringValue}"></h:outputText></TD>
																		<TD
																			style="border-top-style:none; border-bottom-style: none; border-right-style: none;"
																			width="240"><h:outputText styleClass="outputText"
																			id="htmlIdoSyutgakSbtName_list"
																			value="#{varlist.idoSyutgakSbtName.displayValue}"
																			title="#{varlist.idoSyutgakSbtName.value}"></h:outputText>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</hx:jspPanel>
													</h:column>
													<h:column id="column3">
														<f:facet name="header">
															<hx:jspPanel id="jspPanel3">
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	width="100%" height="100%">
																	<TBODY>
																		<TR>
																			<TH
																				style="border-right-style: none; border-style: none; text-align: center;"
																				width="400" colspan="4">異動／出学</TH>
																		</TR>
																		<TR>
																			<TH
																				style="border-bottom-style: none; text-align: center;"
																				width="100"><h:outputText styleClass="outputText"
																				id="htmlIdoSyutgakStartDate_head" value="開始日"></h:outputText>
																			</TH>
																			<TH
																				style=" border-bottom-style: none; text-align: center;"
																				width="100"><h:outputText styleClass="outputText"
																				id="htmlIdoSyutgakEndDae_head" value="終了日"></h:outputText>
																			</TH>
																			<TH
																				style="border-bottom-style: none; text-align: center;"
																				width="100"><h:outputText styleClass="outputText"
																				id="htmlIdoSyutgakKekkaKbnName_head" value="申請結果"></h:outputText>
																			</TH>
																			<TH
																				style=" border-right-style: none; border-bottom-style: none; text-align: center;"
																				width="100"><h:outputText styleClass="outputText"
																				id="htmlIdoSyutgakKikan_head" value="期間"></h:outputText>
																			</TH>
																		</TR>
																	</TBODY>
																</TABLE>
															</hx:jspPanel>
														</f:facet>
														<hx:jspPanel id="jspPanel4">
															<TABLE border="0" cellpadding="0"
																cellspacing="0" width="100%" height="100%"
																style="border-bottom-style:none;border-top-style:none;">
																<TBODY>
																	<TR>
																		<TD
																			style="border-top-style:none; border-bottom-style: none; border-left-style: none;"
																			width="100"><h:outputText styleClass="outputText" id="htmlIdoSyutgakStartDate_list"
																			value="#{varlist.propIdoSyutgakStartDate.dateValue}"><f:convertDateTime /></h:outputText></TD>
																		<TD
																			style="border-top-style:none; border-bottom-style: none;"
																			width="100"><h:outputText styleClass="outputText" id="htmlIdoSyutgakEndDate_list"
																			value="#{varlist.propIdoSyutgakEndDate.dateValue}"><f:convertDateTime /></h:outputText></TD>
																		<TD
																			style="border-top-style:none; border-bottom-style: none;"
																			width="100"><h:outputText styleClass="outputText" id="htmlIdoSyutgakKekkaKbnName_list"
																			value="#{varlist.idoSyutgakKekkaKbnName}"></h:outputText></TD>
																		<TD
																			style="border-top-style:none; border-bottom-style: none; text-align: right"
																			width="100"><h:outputText styleClass="outputText" id="htmlIdoSyutgakKikan_list"
																			value="#{varlist.idoSyutgakKikan}"></h:outputText></TD>
																	</TR>
																</TBODY>
															</TABLE>
														</hx:jspPanel>
													</h:column>
													<h:column id="column4">
														<f:facet name="header">
														</f:facet>
														<f:attribute value="40" name="width" />
														<hx:commandExButton type="submit" value="選択"
															styleClass="commandExButton" id="listSelect"
															action="#{pc_Xrx00102.doListSelectAction}"></hx:commandExButton>
													</h:column>
												</h:dataTable></div>
												</TD>
											</TR>
										</TABLE>
										<BR>
										<hx:jspPanel id="pnlDetail" rendered="#{pc_Xrx00102.propPnlDetail.rendered}">
										<TABLE class="table" width="840">
											<TBODY>
												<TR>
													<TH nowrap class="v_d">
													<!-- 異動出学種別区分 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakSbtKbn"
														value="#{pc_Xrx00102.propIdoSyutgakSbtKbn.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakSbtKbn.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlIdoSyutgakKbn"
														value="#{pc_Xrx00102.propIdoSyutgakKbn.stringValue}"
														style="#{pc_Xrx00102.propIdoSyutgakKbn.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_d">
													<!-- 異動出学種別コード -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakSbtName"
														value="異動出学種別"
														style="#{pc_Xrx00102.propIdoSyutgakSbtName.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlIdoSyutgakSbtName"
														value="#{pc_Xrx00102.propIdoSyutgakSbtName.stringValue}"
														style="#{pc_Xrx00102.propIdoSyutgakSbtName.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_d">
													<!-- 異動出学理由 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakRiyu"
														value="#{pc_Xrx00102.propIdoSyutgakRiyu.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakRiyu.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlIdoSyutgakRiyu"
														value="#{pc_Xrx00102.propIdoSyutgakRiyu.stringValue}"
														style="#{pc_Xrx00102.propIdoSyutgakRiyu.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_d">
													<!-- 異動理由主免 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoRiyuSyu"
														value="異動理由主免"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlIdoRiyuSyu"
														value="#{pc_Xrx00102.propIdoRiyuSyu.stringValue}"
														style="#{pc_Xrx00102.propIdoRiyuSyu.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_d">
													<!-- 異動理由副免 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoRiyuFuku"
														value="異動理由副免"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlIdoRiyuFuku"
														value="#{pc_Xrx00102.propIdoRiyuFuku.stringValue}"
														style="#{pc_Xrx00102.propIdoRiyuFuku.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_e" width="180">
													<!-- 異動出学年度 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakNendo"
														value="#{pc_Xrx00102.propIdoSyutgakNendo.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakNendo.labelStyle}"></h:outputText></TH>
													<TD width="240"><h:outputText styleClass="outputText" id="htmlIdoSyutgakNendo"
														value="#{pc_Xrx00102.propIdoSyutgakNendo.stringValue}"
														style="#{pc_Xrx00102.propIdoSyutgakNendo.style}"></h:outputText></TD>
													<TH nowrap class="v_e" width="180">
													<!-- 異動出学学期NO -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakGakki"
														value="#{pc_Xrx00102.propIdoSyutgakGakki.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakGakki.labelStyle}"></h:outputText></TH>
													<TD width="240"><h:outputText styleClass="outputText" id="htmlIdoSyutgakGakki"
														value="#{pc_Xrx00102.propIdoSyutgakGakki.stringValue}"
														style="#{pc_Xrx00102.propIdoSyutgakGakki.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_f">
													<!-- 異動出学願提出日 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakNegaiDate"
														value="#{pc_Xrx00102.propIdoSyutgakNegaiDate.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakNegaiDate.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlIdoSyutgakNegaiDate"
														value="#{pc_Xrx00102.propIdoSyutgakNegaiDate.dateValue}"
														style="#{pc_Xrx00102.propIdoSyutgakNegaiDate.style}"><f:convertDateTime /></h:outputText></TD>
													<TH nowrap class="v_f">
													<!-- 異動出学申請結果 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakKekkaKbn"
														value="#{pc_Xrx00102.propIdoSyutgakKekkaKbn.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakKekkaKbn.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlIdoSyutgakKekkaKbn"
														value="#{pc_Xrx00102.propIdoSyutgakKekkaKbn.stringValue}"
														style="#{pc_Xrx00102.propIdoSyutgakKekkaKbn.style}"></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_g">
													<!-- 異動出学申請結果日 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakKekkaDate"
														value="#{pc_Xrx00102.propIdoSyutgakKekkaDate.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakKekkaDate.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlIdoSyutgakKekkaDate"
														value="#{pc_Xrx00102.propIdoSyutgakKekkaDate.dateValue}"
														style="#{pc_Xrx00102.propIdoSyutgakKekkaDate.style}"><f:convertDateTime /></h:outputText></TD>
													<TH nowrap class="v_g">
													<!-- 異動出学開始日 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakStartDate"
														value="#{pc_Xrx00102.propIdoSyutgakStartDate.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakStartDate.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlIdoSyutgakStartDate"
														value="#{pc_Xrx00102.propIdoSyutgakStartDate.dateValue}"
														style="#{pc_Xrx00102.propIdoSyutgakStartDate.style}"><f:convertDateTime /></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_a">
													<!-- 異動出学終了予定日 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakEndYoteiDate"
														value="#{pc_Xrx00102.propIdoSyutgakEndYoteiDate.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakEndYoteiDate.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlIdoSyutgakEndYoteiDate"
														value="#{pc_Xrx00102.propIdoSyutgakEndYoteiDate.dateValue}"
														style="#{pc_Xrx00102.propIdoSyutgakEndYoteiDate.style}"><f:convertDateTime /></h:outputText></TD>
													<TH nowrap class="v_a">
													<!-- 異動出学終了日 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakEndDate"
														value="#{pc_Xrx00102.propIdoSyutgakEndDate.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakEndDate.labelStyle}"></h:outputText></TH>
													<TD><h:outputText styleClass="outputText" id="htmlIdoSyutgakEndDate"
														value="#{pc_Xrx00102.propIdoSyutgakEndDate.dateValue}"
														style="#{pc_Xrx00102.propIdoSyutgakEndDate.style}"><f:convertDateTime /></h:outputText></TD>
												</TR>
												<TR>
													<TH nowrap class="v_b">
													<!-- 異動出学期間 -->
														<h:outputText styleClass="outputText" id="htmlDspIdoSyutgakKikan"
														value="#{pc_Xrx00102.propIdoSyutgakKikan.labelName}"
														style="#{pc_Xrx00102.propIdoSyutgakKikan.labelStyle}"></h:outputText></TH>
													<TD colspan="3"><h:outputText styleClass="outputText" id="htmlIdoSyutgakKikan"
														value="#{pc_Xrx00102.propIdoSyutgakKikan.stringValue}"
														style="#{pc_Xrx00102.propIdoSyutgakKikan.style}"></h:outputText></TD>
												</TR>
											</TBODY>
										</TABLE>
										</hx:jspPanel>
										</hx:jspPanel>
      </DIV>
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
  </hx:scriptCollector>
</f:subview>