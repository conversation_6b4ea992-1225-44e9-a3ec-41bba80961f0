<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz00301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz00301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0" width="400">
				<TBODY>
					<TR>
						<TD align="center" width="320">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="320">
								<TBODY>
									<TR>
										<TH class="v_a" width="100"><h:outputText
											styleClass="outputText" id="lblKjnNendo"
											value="#{pc_Ssz00301.propKjnNendo.labelName}"
											style="#{pc_Ssz00301.propKjnNendo.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:inputText styleClass="inputText"
											id="htmlKjnNendo" size="5"
											value="#{pc_Ssz00301.propKjnNendo.dateValue}"
											style="#{pc_Ssz00301.propKjnNendo.style}"
											disabled="#{pc_Ssz00301.propKjnNendo.disabled}">
											<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
											<f:convertDateTime pattern="yyyy" /></h:inputText></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
						<TD align="center" width="80">
						<TABLE border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD align="left" width="50"><hx:commandExButton
										type="submit" value="選択" styleClass="commandExButton"
										id="select" action="#{pc_Ssz00301.doSelectAction}"
										disabled="#{pc_Ssz00301.propSelect.disabled}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="400">
				<TBODY>
					<TR>
						<TD align="center" width="320">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="320">
								<TBODY>
									<TR>
										<TH class="v_a" width="100"><h:outputText
											styleClass="outputText" id="lblKjnNendoOutput"
											value="#{pc_Ssz00301.propKjnNendoOutput.labelName}"
											style="#{pc_Ssz00301.propKjnNendoOutput.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:outputText styleClass="outputText"
											id="htmlKjnNendoOutput"
											value="#{pc_Ssz00301.propKjnNendoOutput.stringValue}"
											style="#{pc_Ssz00301.propKjnNendoOutput.style}"></h:outputText></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
						<TD width="80"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="400">
				<TBODY>
					<TR>
						<TD align="left" width="320">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="320">
								<TBODY>
									<TR>
										<TH class="v_a" width="100"><h:outputText
											styleClass="outputText" id="lblKjnGakki"
											value="#{pc_Ssz00301.propKjnGakki.labelName}"
											style="#{pc_Ssz00301.propKjnGakki.labelStyle}"></h:outputText></TH>
										<TD width="220"><h:selectOneMenu styleClass="selectOneMenu"
											value="#{pc_Ssz00301.propKjnGakki.value}"
											disabled="#{pc_Ssz00301.propKjnGakki.disabled}"
											id="htmlKjnGakki" style="width:175px;">
											<f:selectItems value="#{pc_Ssz00301.propKjnGakki.list}" />
										</h:selectOneMenu></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
						<TD width="80"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz00301.doRegisterAction}"
							disabled="#{pc_Ssz00301.propRegister.disabled}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

