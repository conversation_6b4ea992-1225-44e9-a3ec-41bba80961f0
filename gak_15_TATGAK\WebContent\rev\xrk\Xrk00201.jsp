<%-- 
	証明書文面登録
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrk/Xrk00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrk00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrk00201.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrk00201.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrk00201.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrk00201.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="700">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText
							styleClass="outputText" value="#{pc_Xrk00201.propXrkSyomBmn.listCount == null ? 0 : pc_Xrk00201.propXrkSyomBmn.listCount}" style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="htmlCountlbl" value="件" style="font-size: 8pt"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="700">
				<TBODY>
					<TR>
						<TD>

						<div class="listScroll" style="height:254px;" id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrk00201.propXrkSyomBmn.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xrk00201.propXrkSyomBmn.list}" var="varlist">
							
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblPrintId_head" styleClass="outputText"
										value="帳票ID">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblPrintId"
									value="#{varlist.printId}"></h:outputText>
								<f:attribute value="105" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: middle" name="style" />
							</h:column>

							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblSheetNo_head" styleClass="outputText"
										value="シートNO">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblSheetNo"
									value="#{varlist.sheetNo}"></h:outputText>
								<f:attribute value="75" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							

							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblPatternNo_head" styleClass="outputText"
										value="パターンNO">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblPatternNo"
									value="#{varlist.patternNo}"></h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblTitle_head" styleClass="outputText"
										value="タイトル">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblTitle"
									title="#{varlist.propTitle.value}"
									value="#{varlist.propTitle.displayValue}"></h:outputText>
								<f:attribute value="200" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblPrintTitle_head" styleClass="outputText"
										value="帳票タイトル">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblPrintTitle"
									title="#{varlist.propPrintTitle.value}"
									value="#{varlist.propPrintTitle.displayValue}"></h:outputText>
								<f:attribute value="200" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="30" name="width">
								</f:attribute>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style">
								</f:attribute>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Xrk00201.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" class="table" width="700" cellpadding="0"
				cellspacing="0">
				<TBODY>
					<TR>
						<!-- 帳票ID -->
						<TH nowrap class="v_c" width="150">
							<h:outputText styleClass="outputText" id="lblPropPrintId"
							value="#{pc_Xrk00201.propPrintId.labelName}"
							style="#{pc_Xrk00201.propPrintId.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlPrintId"
                            	value="#{pc_Xrk00201.propPrintId.value}"
                            	disabled="#{pc_Xrk00201.propPrintId.disabled}"
                            	style="width:200px">
                            	<f:selectItems value="#{pc_Xrk00201.propPrintId.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>
					
					<TR>
						<!-- シートNO -->
						<TH nowrap class="v_c" width="150">
							<h:outputText styleClass="outputText" id="lblPropSheetNo"
							value="#{pc_Xrk00201.propSheetNo.labelName}"
							style="#{pc_Xrk00201.propSheetNo.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlSheetNo"
                            	value="#{pc_Xrk00201.propSheetNo.value}"
                            	disabled="#{pc_Xrk00201.propSheetNo.disabled}"
                            	style="width:200px">
                            	<f:selectItems value="#{pc_Xrk00201.propSheetNo.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>
					
					<TR>
						<!-- パターンNO -->
						<TH nowrap class="v_c" width="150">
							<h:outputText styleClass="outputText" id="lblPropPatternNo"
							value="#{pc_Xrk00201.propPatternNo.labelName}"
							style="#{pc_Xrk00201.propPatternNo.labelStyle}"></h:outputText></TH>
                        <TD nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlPatternNo"
                            	value="#{pc_Xrk00201.propPatternNo.value}"
                            	disabled="#{pc_Xrk00201.propPatternNo.disabled}"
                            	style="width:200px">
                            	<f:selectItems value="#{pc_Xrk00201.propPatternNo.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>

					<TR>
						<!-- タイトル -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropTitle" 
							value="#{pc_Xrk00201.propTitle.labelName}"
							style="#{pc_Xrk00201.propTitle.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlTitle"
							value="#{pc_Xrk00201.propTitle.stringValue}"
							maxlength="#{pc_Xrk00201.propTitle.maxLength}" size="50"
							style="#{pc_Xrk00201.propTitle.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"/>
						</h:inputText></TD>
					</TR>

					<TR>
						<!-- 帳票タイトル -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblPropPrintTitle" 
							value="#{pc_Xrk00201.propPrintTitle.labelName}"
							style="#{pc_Xrk00201.propPrintTitle.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlPrintTitle"
							value="#{pc_Xrk00201.propPrintTitle.stringValue}"
							maxlength="#{pc_Xrk00201.propPrintTitle.maxLength}" size="50"
							style="#{pc_Xrk00201.propPrintTitle.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"/>
						</h:inputText></TD>
					</TR>
					
					<TR>
						<!-- 手数料 -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblPropFee" 
							value="#{pc_Xrk00201.propFee.labelName}"
							style="#{pc_Xrk00201.propFee.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlFee"
							value="#{pc_Xrk00201.propFee.integerValue}"
							maxlength="#{pc_Xrk00201.propFee.maxLength}" size="9"
							style="#{pc_Xrk00201.propFee.style}">
							<f:convertNumber type="number" pattern="########0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>

				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register" 
							action="#{pc_Xrk00201.doRegisterAction}" 
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}" 
							action="#{pc_Xrk00201.doDeleteAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrk00201.doClearAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrk00201.propXrkSyomBmn.scrollPosition}"
				id="scroll"></h:inputHidden>
			<hx:inputHelperSetFocus target="htmlPrintId"></hx:inputHelperSetFocus>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

