<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01602.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
	
<%@ page import="com.jast.gakuen.framework.util.*"%>
<%@ page import="com.jast.gakuen.rev.xrh.Xrh01602"%>
<%@ page import="com.jast.gakuen.framework.constant.ActionConst" %>

<% Xrh01602 pc = (Xrh01602)UtilSystem.getManagedBean(Xrh01602.class); %>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pCob0101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
function onClickReturnDisp(id){
	return true;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<% pc.setPageLoadAction(ActionConst.ACTION_INIT); %>
	<BODY onload="">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01602.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page="../inc/childHeader.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh01602.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01602.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01602.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column">
					
					<TABLE border="0" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
							<TH width="50" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="年度：">
								</h:outputText>
							</TH>
							<TD width="160" align="left">
								<h:outputText styleClass="outputText"
									id="htmlNendo"
									value="#{pc_Xrh01602.propNendo.stringValue}" 
									style="#{pc_Xrh01602.propNendo.labelStyle}">
								</h:outputText>
							</TD>
							<TH width="100" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="科目試験回数：">
								</h:outputText>
							</TH>
							<TD width="230" align="left">
								<h:outputText styleClass="outputText"
									id="htmlKamokSikenCnt"
									value="#{pc_Xrh01602.propKamokSikenCnt.stringValue}"
									style="#{pc_Xrh01602.propKamokSikenCnt.labelStyle}">
								</h:outputText>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
					
					<BR>
					<HR noshade width="600px">
					<BR>		
							
					<TABLE border="0" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
							<TH width="90">
							</TH>
							<TH width="120" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="結果通知">
								</h:outputText>
							</TH>
							<TH width="100" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="発行可能：">
								</h:outputText>
							</TH>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlHakkoavailCnt"
									value="#{pc_Xrh01602.propHakkoavailCnt.stringValue}" 
									style="#{pc_Xrh01602.propHakkoavailCnt.labelStyle}">
								</h:outputText> 件
							</TD>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlHakkoavailPer"
									value="#{pc_Xrh01602.propHakkoavailPer.stringValue}" 
									style="#{pc_Xrh01602.propHakkoavailPer.labelStyle}">
								</h:outputText> ％
							</TD>
							<TD width="90">
							</TD>						
						</TR>
						<TR>
							<TH width="90">
							</TH>
							<TH width="120">
							</TH>
							<TH width="100" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="発行未：">
								</h:outputText>
							</TH>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlHakkomiCnt"
									value="#{pc_Xrh01602.propHakkomiCnt.stringValue}" 
									style="#{pc_Xrh01602.propHakkomiCnt.labelStyle}">
								</h:outputText> 件
							</TD>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlHakkomiPer"
									value="#{pc_Xrh01602.propHakkomiPer.stringValue}" 
									style="#{pc_Xrh01602.propHakkomiPer.labelStyle}">
								</h:outputText> ％
							</TD>
							<TD width="90">
							</TD>						
						</TR>	
							
						<TR>
							<TH width="90">
							</TH>
							<TH width="120">
							</TH>
							<TH width="100" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="発行済：">
								</h:outputText>
							</TH>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlHakkozumiCnt"
									value="#{pc_Xrh01602.propHakkozumiCnt.stringValue}" 
									style="#{pc_Xrh01602.propHakkozumiCnt.labelStyle}">
								</h:outputText> 件
							</TD>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlHakkozumiPer"
									value="#{pc_Xrh01602.propHakkozumiPer.stringValue}" 
									style="#{pc_Xrh01602.propHakkozumiPer.labelStyle}">
								</h:outputText> ％
							</TD>
							<TD width="90">
							</TD>						
						</TR>	
							
						<TR>
							<TH width="90">
							</TH>
							<TH width="120" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="評価入力">
								</h:outputText>
							</TH>
							<TH width="100" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="総科目数：">
								</h:outputText>
							</TH>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlKamokCnt"
									value="#{pc_Xrh01602.propKamokCnt.stringValue}" 
									style="#{pc_Xrh01602.propKamokCnt.labelStyle}">
								</h:outputText> 件
							</TD>
							<TD width="90">
							</TD>
							<TD width="90">
							</TD>						
						</TR>	
							
						<TR>
							<TH width="90">
							</TH>
							<TH width="120">
							</TH>
							<TH width="100" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="入力未科目：">
								</h:outputText>
							</TH>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlNyuryokmiKamokCnt"
									value="#{pc_Xrh01602.propNyuryokmiKamokCnt.stringValue}" 
									style="#{pc_Xrh01602.propNyuryokmiKamokCnt.labelStyle}">
								</h:outputText> 件
							</TD>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlNyuryokmiKamokPer"
									value="#{pc_Xrh01602.propNyuryokmiKamokPer.stringValue}" 
									style="#{pc_Xrh01602.propNyuryokmiKamokPer.labelStyle}">
								</h:outputText> ％
							</TD>
							<TD width="90">
							</TD>						
						</TR>	
						
						<TR>
							<TH width="90">
							</TH>
							<TH width="120">
							</TH>
							<TH width="100" style="text-align:left">
								<h:outputText styleClass="outputText"
									value="入力済科目：">
								</h:outputText>
							</TH>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlNyuryokzumiKamokCnt"
									value="#{pc_Xrh01602.propNyuryokzumiKamokCnt.stringValue}" 
									style="#{pc_Xrh01602.propNyuryokzumiKamokCnt.labelStyle}">
								</h:outputText> 件
							</TD>
							<TD width="90" style="text-align:right">
								<h:outputText styleClass="outputText"
									id="htmlNyuryokzumiKamokPer"
									value="#{pc_Xrh01602.propNyuryokzumiKamokPer.stringValue}" 
									style="#{pc_Xrh01602.propNyuryokzumiKamokPer.labelStyle}">
								</h:outputText> ％
							</TD>	
							<TD width="90">
							</TD>						
						</TR>
						</TBODY>
					</TABLE>
					
					<BR>
					<HR noshade width="600px">
					<BR>
	
				</DIV>
			</DIV>
		</DIV>
		<!-- フッターインクルード -->
		<jsp:include page="../inc/childFooter.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
				
