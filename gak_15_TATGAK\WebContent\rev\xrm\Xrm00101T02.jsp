<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00101T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<TITLE>Xrm00101T02.jsp</TITLE>

<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript">

	//納付金配当一覧　全選択
	function func_check_on(thisObj, thisEvent) {
		check('htmlPayhList','htmlPayChecked');
	}
	function onClickChkKigen(id) {
	// 201411納入期限チェックボックスをクリック時の処理追加
		indirectClick(id);
	}

	//納付金配当一覧　全解除
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPayhList','htmlPayChecked');
	}

	function fncButtonActive(){

		var codeRegSearch = null;

		//選択ボタン
		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch').value;
		if(codeRegSearch == 1){
			document.getElementById('form1:search').disabled = true;
		} else {
			document.getElementById('form1:unselect').disabled = true;
			document.getElementById('form1:takein').disabled = true;
			document.getElementById('form1:popGakSearch').disabled = true;
			document.getElementById('form1:addition').disabled = true;
			document.getElementById('form1:allExclusion').disabled = true;
		}

		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch2').value;
		if(codeRegSearch == 1){
		} else {
			document.getElementById('form1:pdfout').disabled = false;
		}

		window.attachEvent('onload', endload);		

		//学籍氏名取得Ajax呼び出し
		funcAjaxGakusekiCd(document.getElementById('form1:htmlGakusekiCd'), '1' ,'form1:htmlNameHd');

		//フォーカスの設定
		setFocus();
	}

	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}

	//学費学生検索画面へ遷移
	function openPGhz0301Window() {
		openPGhz0301("<%=com.jast.gakuen.rev.gh.PGhz0301.getWindowOpenOption() %>");
		return true;
	}

	function studOnblur(thisObj, targetLabel) {
		
		//学籍氏名取得Ajax呼び出し
		funcAjaxGakusekiCd(thisObj, '1', targetLabel);
		
		return true;
	}

	//フォーカスの設定
	function setFocus(){

		var id = null;

		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;

		//リスト内選択ボタンフォーカス時
		if ((id != null) && (id != "")) {
			document.getElementById(id).focus();
		}

		//初期化
		document.getElementById('form1:htmlScrollPos').value = "";

	}

	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('search');
	}

	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}
	
	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}
	
	function onClickChk(id) {
		indirectClick(id);
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncButtonActive();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm00101T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm00101T02.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrm00101T02.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm00101T02.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV id="content">
			<DIV class="column" align="center">

			<TABLE width="900px">
				<TBODY>
					<TR>
						<td>
						<TABLE width="100%" border="0" class="table">
							<TBODY>

								<TR>
									<!-- 発行日付 -->
									<TH nowrap class="v_a" width="100px"><h:outputText
										styleClass="outputText" id="lblHakkouDate"
										value="#{pc_Xrm00101T02.propHakkouDate.labelName}"
										style="#{pc_Xrm00101T02.propHakkouDate.labelStyle}">
									</h:outputText></TH>
									<TD width="400px"><h:inputText styleClass="inputText"
										id="htmlHakkouDate"
										value="#{pc_Xrm00101T02.propHakkouDate.dateValue}"
										style="#{pc_Xrm00101T02.propHakkouDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<!-- 通信欄 -->
									<TD rowspan=3>
									<TABLE width="300" border="0" class="table">
										<TBODY>
											<TR>
												<TH align="center" nowrap class="v_a" width="150px"><h:outputText
													styleClass="outputText" id="lblTsushinText"
													value="#{pc_Xrm00101T02.propTsushinText.labelName}"
													style="#{pc_Xrm00101T02.propTsushinText.labelStyle}"
													>
												</h:outputText></TH>
												<TD width="*"><h:inputTextarea styleClass="inputTextarea"
													id="htmlTsushinText" cols="50" rows="6"
													onchange="onCangeData();"
													disabled="#{pc_Xrm00101T02.propTsushinText.disabled}"
													value="#{pc_Xrm00101T02.propTsushinText.stringValue}"
													readonly="#{pc_Xrm00101T02.propTsushinText.readonly}"
													style="#{pc_Xrm00101T02.propTsushinText.style}">
												</h:inputTextarea></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<!-- 通信区分 -->
									<TH nowrap class="v_a"><h:outputText styleClass="outputText"
										id="lblTushinTxt"
										value="#{pc_Xrm00101T02.propTushinKbn.labelName}"
										style="#{pc_Xrm00101T02.propTushinKbn.labelStyle}">
									</h:outputText></TH>
									<TD valign="middle" width="*"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlTusinKbn"
										onclick="return onClickChk('linkKikanUseChkBox');"
										value="#{pc_Xrm00101T02.propTushinKbn.checked}"
										disabled="#{pc_Xrm00101T02.propTushinKbn.disabled}">
									</h:selectBooleanCheckbox>定型文を利用せず通信欄を直接入力する。</TD>
									<h:commandLink styleClass="commandLink" id="linkKikanUseChkBox"
										action="#{pc_Xrm00101T02.doChkAction}">
										<h:outputText id="htmlLinkKikanUseChkBox"
											styleClass="outputText">
										</h:outputText>
									</h:commandLink>

								</TR>
								<TR>
									<!-- 出力区分 -->
									<TH class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblOutPutTxt" value="出力区分" style="">
									</h:outputText></TH>
									<TD nowrap><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="radio1"
										value="#{pc_Xrm00101T02.propOutPutKbn.value}"
										layout="pageDirection" style="}">
										<f:selectItem itemValue="1" itemLabel="請求書を発行する。" />
										<f:selectItem itemValue="2" itemLabel="未出力の請求書を発行する。" />
										<f:selectItem itemValue="3" itemLabel="請求書を再発行する。" />
									</h:selectOneRadio></TD>
								</TR>
							<TR>
								<!-- 期限区分 -->
								<TH class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblNonyuTxt"
										value="#{pc_Xrm00101T02.propKigenkbn.labelName}"
										style="">
								</h:outputText></TH>
									<TD valign="middle" width="*"><h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlNonyuKbn"
											onclick="return onClickChkKigen('linkKigenUseChkBox');"
											value="#{pc_Xrm00101T02.propKigenkbn.checked}"
											disabled="#{pc_Xrm00101T02.propKigenkbn.disabled}">
										</h:selectBooleanCheckbox>納入期限を直接入力する。
										<h:commandLink
										styleClass="onClickChkKigen"
										id="linkKigenUseChkBox"
										action="#{pc_Xrm00101T02.doChkKigenAction}">
										<h:outputText
										id="htmlLinkKigenUseChkBox"
										styleClass="outputText">
										</h:outputText>
										</h:commandLink>
										
									</TD>
								<!-- 納入期限 -->	
								<TD>
									<TABLE width="600"  class="table">
									<TBODY>
								<TH width="150px><h:outputText
										styleClass="outputText" id="lblNonyukigen"
										value="#{pc_Xrm00101T02.propNonyuDate.labelName}"
										style="#{pc_Xrm00101T02.propNonyuDate.labelStyle}">
								</h:outputText></TH>
								<TD width="145px"><h:inputText styleClass="inputText"
										id="nonyuDate" disabled="#{pc_Xrm00101T02.propNonyuDate.disabled}" 
										value="#{pc_Xrm00101T02.propNonyuDate.dateValue}"
										style="#{pc_Xrm00101T02.propNonyuDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
								</h:inputText></TD>
								<!-- 有効期限-->
									
								<TH width="150px><h:outputText
										styleClass="outputText" id="lblYukokigen"
										value="#{pc_Xrm00101T02.propYukoDate.labelName}"
										style="#{pc_Xrm00101T02.propYukoDate.labelStyle}">
								</h:outputText></TH>
								<TD width="145px"><h:inputText styleClass="inputText"
										id="YukoDate"  disabled="#{pc_Xrm00101T02.propYukoDate.disabled}" 
										value="#{pc_Xrm00101T02.propYukoDate.dateValue}"
										style="#{pc_Xrm00101T02.propYukoDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
								</h:inputText></TD>								
								</TBODY>
							</TABLE>
							</TD>
								</TR>
									
								

							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="5px"></TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR align="left">
									<TD><hx:commandExButton type="submit" value="納付金指定"
										styleClass="tab_head_off" id="htmlPayTab"
										action="#{pc_Xrm00101T02.doLinkPayTabAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="納付金・学生指定" styleClass="tab_head_on"
										id="htmlPayGakTab"></hx:commandExButton><hx:commandExButton
										type="submit" value="学生指定" styleClass="tab_head_off"
										id="htmlGakseiTab"
										action="#{pc_Xrm00101T02.doLinkGakseiTabAction}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TD>

									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										class="tab_body">
										<TBODY>
											<TR align="center">
												<TD height="20px">

												<TABLE width="850px">
													<TR>
														<TD height="5px"></TD>
													</TR>
													<TR>
														<TD>
														<TABLE width="100%" border="0" class="table">
															<TBODY>
																<TR>
																	<!-- 学費年度 -->
																	<TH nowrap class="v_a" width="150px"><h:outputText
																		styleClass="outputText" id="lblGhNendo"
																		value="#{pc_Xrm00101T02.propGhNendo.labelName}"
																		style="#{pc_Xrm00101T02.propGhNendo.labelStyle}">
																	</h:outputText></TH>
																	<TD valign="middle" width="*"><h:inputText
																		styleClass="inputText" id="htmlGhNendo"
																		value="#{pc_Xrm00101T02.propGhNendo.dateValue}"
																		maxlength="#{pc_Xrm00101T02.propGhNendo.maxLength}"
																		style="#{pc_Xrm00101T02.propGhNendo.style}"
																		disabled="#{pc_Xrm00101T02.propGhNendo.disabled}"
																		size="4">
																		<hx:inputHelperAssist imeMode="inactive"
																			errorClass="inputText_Error" promptCharacter="_" />
																		<f:convertDateTime pattern="yyyy" />
																	</h:inputText></TD>
																	<TD rowspan=3><hx:commandExButton type="submit"
																		value="選択" styleClass="cmdBtn_dat_s" id="search"
																		action="#{pc_Xrm00101T02.doSearchAction}"
																		disabled="#{pc_Xrm00101T02.propGhNendo.disabled}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="解除" styleClass="cmdBtn_etc_s" id="unselect"
																		action="#{pc_Xrm00101T02.doUnselectAction}"
																		disabled="#{!pc_Xrm00101T02.propGhNendo.disabled}">
																	</hx:commandExButton></TD>
																</TR>
																<%--納付金種別--%>
																<TR>
																	<TH width="150px" nowrap class="v_c"><h:outputText
																		styleClass="outputText" id="lblSyubetuChkList"
																		value="#{pc_Xrm00101T02.propChkListLbl.name}"
																		style="#{pc_Xrm00101T02.propChkListLbl.labelStyle}">
																	</h:outputText></TH>

																	<TD width="550" nowrap class="clear_border"><h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox"
																		id="htmlChkListSeika"
																		value="#{pc_Xrm00101T02.propChkListSeika.checked}"
																		disabled="#{pc_Xrm00101T02.propChkListSeika.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="lblChkListSeika"
																		value="#{pc_Xrm00101T02.propChkListSeika.name}"
																		style="#{pc_Xrm00101T02.propChkListSeika.style}">
																	</h:outputText> <h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox"
																		id="htmlChkListKamokutori"
																		value="#{pc_Xrm00101T02.propChkListKamokutori.checked}"
																		disabled="#{pc_Xrm00101T02.propChkListKamokutori.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="lblChkListKamokutorir"
																		value="#{pc_Xrm00101T02.propChkListKamokutori.name}"
																		style="#{pc_Xrm00101T02.propChkListKamokutori.style}">
																	</h:outputText> <h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox"
																		id="htmlChkListToitu"
																		value="#{pc_Xrm00101T02.propChkListToitu.checked}"
																		disabled="#{pc_Xrm00101T02.propChkListToitu.disabled}">
																	</h:selectBooleanCheckbox> <h:outputText
																		styleClass="outputText" id="lblChkListToitu"
																		value="#{pc_Xrm00101T02.propChkListToitu.name}"
																		style="#{pc_Xrm00101T02.propChkListToitu.style}">
																	</h:outputText></TD>
																</TR>


																<%--業務コード--%>
																<TR>
																	<TH width="150px" nowrap class="v_b"><h:outputText
																		styleClass="outputText" id="propgyoumCD"
																		value="#{pc_Xrm00101T02.propgyoumCD.name}"
																		style="#{pc_Xrm00101T02.propgyoumCD.name}">
																	</h:outputText></TH>
																	<TD><h:selectOneMenu styleClass="selectOneMenu"
																		id="propgyoumList" style="width:450px;"
																		value="#{pc_Xrm00101T02.propgyoumList.value}"
																		tabindex="7"
																		disabled="#{pc_Xrm00101T02.propgyoumList.disabled}">
																		<f:selectItems
																			value="#{pc_Xrm00101T02.propgyoumList.list}" />
																	</h:selectOneMenu></TD>
																</TR>

															</TBODY>
														</TABLE>

														<TABLE border="0" cellpadding="0" cellspacing="0"
															width="100%">
															<TBODY>
																<TR>
																	<TD align="right"><h:outputText styleClass="outputText"
																		id="htmlCount"
																		value="#{pc_Xrm00101T02.propPayhList.listCount}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblCount" value="件">
																	</h:outputText></TD>
																</TR>
																<TR>
																	<TD align="center">
																	<DIV style="height: 168px; width=100%;" id="listScroll"
																		onscroll="setScrollPosition('htmlHidScroll',this);"
																		class="listScroll"><h:dataTable
																		footerClass="footerClass"
																		rows="#{pc_Xrm00101T02.propPayhList.rows}"
																		rowClasses="#{pc_Xrm00101T02.propPayhList.rowClasses}"
																		headerClass="headerClass" styleClass="meisai_scroll"
																		id="htmlPayhList"
																		value="#{pc_Xrm00101T02.propPayhList.list}"
																		var="varlist" width="850px">
																		<h:column id="column1">
																			<f:facet name="header">
																			</f:facet>
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox"
																				id="htmlPayChecked" value="#{varlist.payChecked}"
																				rendered="#{varlist.rendered}">
																			</h:selectBooleanCheckbox>
																			<f:attribute value="30px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column2">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T02.propPayhListPayCd.labelName}"
																					id="lblPayCd">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmpPayCd" value="#{varlist.payCd}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="95px" name="width" />
																		</h:column>
																		<h:column id="column3">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T02.propPayhListPatternCd.labelName}"
																					id="lblPatternCd">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPatternCd"
																				value="#{varlist.patternCd}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="95px" name="width" />
																		</h:column>
																		<h:column id="column4">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T02.propPayhListBunnoKbnCd.labelName}"
																					id="lblBunnoKbnCd">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlBunnoKbnCd"
																				value="#{varlist.bunnoKbnCd}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="95px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column5">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T02.propPayhListPayName.labelName}"
																					id="lblPayName">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPayName"
																				value="#{varlist.payName}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="*" name="width" />
																		</h:column>
																		<h:column id="column6">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T02.propPayhListBunkatsuNo.labelName}"
																					id="lblBunkatsuNo">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlBunkatsuNo"
																				value="#{varlist.bunkatsuNo}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="65px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column7">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T02.propPayhListPayLimit.labelName}"
																					id="lblPayLimit">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPayLimit"
																				value="#{varlist.payLimit}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="70px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																	</h:dataTable></DIV>
																	</TD>
																</TR>
																<TR>
																	<TD>
																	<TABLE border="0" cellpadding="2" cellspacing="0"
																		class="meisai_scroll" width="100%">
																		<TBODY>
																			<TR>
																				<TD class="footerClass">
																				<TABLE class="panelBox">
																					<TBODY>
																						<TR>
																							<TD><%-- 全選択・全解除 --%> <hx:jspPanel id="jspPanel1">
																								<INPUT type="button" name="check" value="on"
																									onclick="return func_check_on(this, event);"
																									class="check">
																								<INPUT type="button" name="uncheck" value="off"
																									onclick="return func_check_off(this, event);"
																									class="uncheck">
																							</hx:jspPanel></TD>
																						</TR>
																					</TBODY>
																				</TABLE>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>

														</TD>
													</TR>
													<TR>
														<TD height="10px"></TD>
													</TR>
													<TR>
														<TD>

														<TABLE border="0" cellpadding="0" cellspacing="0"
															class="table" width="100%">
															<TBODY>
																<TR>
																	<TH width="150px" class="v_e">
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		width="100%">
																		<TBODY>
																			<TR>
																				<TH class="clear_border"><!-- 入力ファイル --> <h:outputText
																					styleClass="outputText" id="lblinputFile"
																					value="#{pc_Xrm00101T02.propInputFile.labelName}">
																				</h:outputText></TH>
																			</TR>
																			<TR>
																				<TH class="clear_border"><!-- 前回ファイル --> <h:outputText
																					styleClass="outputText" id="lblInputFileOld"
																					value="#{pc_Xrm00101T02.propInputFileOld.labelName}">
																				</h:outputText></TH>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TH>
																	<TD width="*" nowrap>

																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		width="100%">
																		<TBODY>
																			<TR>
																				<TD class="clear_border"><hx:fileupload
																					styleClass="fileupload" id="htmlInputFile"
																					value="#{pc_Xrm00101T02.propInputFile.value}"
																					disabled="#{pc_Xrm00101T02.propGakusekiCd.disabled}"
																					style="#{pc_Xrm00101T02.propGakusekiCd.style} width:580px">
																					<hx:fileProp name="fileName"
																						value="#{pc_Xrm00101T02.propInputFile.fileName}" />
																					<hx:fileProp name="contentType"
																						value="#{pc_Xrm00101T02.propInputFile.contentType}" />
																				</hx:fileupload> <hx:commandExButton type="submit"
																					value="取込" styleClass="commandExButton" id="takein"
																					action="#{pc_Xrm00101T02.doTakeinAction}">
																				</hx:commandExButton></TD>
																			</TR>
																			<TR>
																				<TD class="clear_border"><h:outputText
																					styleClass="outputText" id="htmlInputFileOld"
																					value="#{pc_Xrm00101T02.propInputFileOld.value}">
																				</h:outputText></TD>
																			</TR>
																		</TBODY>
																	</TABLE>

																	</TD>
																</TR>
																<TR>
																	<TH width="150px" nowrap class="v_f"><!-- 学籍番号 --> <h:outputText
																		styleClass="outputText" id="lblGakusekiCd"
																		style="#{pc_Xrm00101T02.propGakusekiCd.labelStyle}"
																		value="#{pc_Xrm00101T02.propGakusekiCd.labelName}">
																	</h:outputText></TH>
																	<TD nowrap width="*"><h:inputText
																		styleClass="inputText" id="htmlGakusekiCd" size="10"
																		value="#{pc_Xrm00101T02.propGakusekiCd.value}"
																		maxlength="#{pc_Xrm00101T02.propGakusekiCd.maxLength}"
																		disabled="#{pc_Xrm00101T02.propGakusekiCd.disabled}"
																		style="#{pc_Xrm00101T02.propGakusekiCd.style}"
																		onblur="return studOnblur(this, 'form1:htmlNameHd');">
																	</h:inputText> <hx:commandExButton type="submit"
																		styleClass="commandExButton_search"
																		onclick="return openPGhz0301Window();"
																		id="popGakSearch"
																		action="#{pc_Xrm00101T02.doPopGakSearchAction}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="追加" styleClass="commandExButton" id="addition"
																		action="#{pc_Xrm00101T02.doAdditionAction}">
																	</hx:commandExButton> <h:inputText
																		styleClass="likeOutput" id="htmlNameHd" size="61"
																		readonly="#{pc_Xrm00101T02.propName.readonly}"
																		value="#{pc_Xrm00101T02.propName.stringValue}"
																		tabindex="-1">
																	</h:inputText></TD>
																</TR>
															</TBODY>
														</TABLE>

														</TD>
													</TR>
													<TR>
														<TD>

														<TABLE border="0" cellpadding="0" cellspacing="0"
															width="100%">
															<TBODY>
																<TR>
																	<TD width="600px">

																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		width="100%">
																		<TBODY>
																			<TR>
																				<TD align="left"><h:outputText
																					styleClass="outputText" id="lblListName"
																					value="#{pc_Xrm00101T02.propListName.labelName}">
																				</h:outputText></TD>
																			</TR>
																			<TR>
																				<TD><h:selectManyListbox
																					styleClass="selectManyListbox" id="htmlGakseiList"
																					style="width:100%" size="5"
																					value="#{pc_Xrm00101T02.propGakseiList.value}">
																					<f:selectItems
																						value="#{pc_Xrm00101T02.propGakseiList.list}" />
																				</h:selectManyListbox></TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																	<TD width="*" align="left"><BR>
																	<hx:commandExButton type="submit" value="除外"
																		styleClass="commandExButton" id="exclusion"
																		action="#{pc_Xrm00101T02.doExclusionAction}">
																	</hx:commandExButton> <BR>
																	<h:outputText styleClass="note" id="lblExclusionCmt"
																		value="(複数選択可)">
																	</h:outputText> <BR>
																	<hx:commandExButton type="submit" value="全て除外"
																		styleClass="commandExButton" id="allExclusion"
																		action="#{pc_Xrm00101T02.doAllExclusionAction}">
																	</hx:commandExButton> <BR>
																	</TD>
																</TR>
																<TR>
																	<TD width="600px" align="right"><h:outputText
																		styleClass="outputText" id="lblSumCount"
																		value="#{pc_Xrm00101T02.propSumCount.labelName}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="htmlSumCount"
																		value="#{pc_Xrm00101T02.propSumCount.value}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblSumCountName" value="件">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblNormalCount"
																		value="#{pc_Xrm00101T02.propNormalCount.labelName}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="htmlNormalCount"
																		value="#{pc_Xrm00101T02.propNormalCount.value}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblNormalCountName" value="件">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblErrorCount"
																		value="#{pc_Xrm00101T02.propErrorCount.labelName}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="htmlErrorCount"
																		value="#{pc_Xrm00101T02.propErrorCount.value}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblErrorCountName" value="件">
																	</h:outputText></TD>
																	<TD width="*"></TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="100%" border="0" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD width="100%"><font color="#FF0000">CSVは三菱UFJファクターに必ず送信する事。</font>
									</TD>
								</TR>
								<TR>
									<TD width="100%"><hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_out" id="pdfout"
										confirm="#{msg.SY_MSG_0001W}"
										action="#{pc_Xrm00101T02.doPdfoutAction}">
									</hx:commandExButton>&nbsp;</TD>
								</TR>
							</TBODY>
						</TABLE>

						</TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Xrm00101T02.propPayhList.scrollPosition}"
				id="htmlHidScroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm00101T02.propActiveControlSearch.value}"
				id="htmlActiveControlSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm00101T02.propActiveControlSearch2.value}"
				id="htmlActiveControlSearch2">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm00101T02.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Xrm00101T02.propScrollPos.stringValue}"
				id="htmlScrollPos">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Xrm00101T02.propExecutableBtnAdd.integerValue}" id="htmlExecutableBtnAdd"><f:convertNumber /></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
