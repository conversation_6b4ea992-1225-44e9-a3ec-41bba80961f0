<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx04102.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>


<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.jast.gakuen.rev.xrx.Xrx04102"%>
<%@page import="com.jast.gakuen.rev.xrx.action.bean.Xrx04102L01Bean"%>
<%@page import="com.jast.gakuen.rev.xrx.constant.code.ShinseiKmkSbt"%>
<%@page import="com.jast.gakuen.rev.xrx.constant.code.ShinseiKmkTitle"%>
<%@page import="java.util.ArrayList"%>
<%@page import="java.util.LinkedHashMap"%>
<%@page import="com.jast.gakuen.framework.util.UtilSystem"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xrx04102.jsp</TITLE>

<SCRIPT type="text/javascript">
	function init(){
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:htmlGakuseiNm');
	}
	function doGakuseiAjax(thisObj, thisEven, targetLabel){
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;
	
	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;
	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}
	function confirmOk() {

		var val = document.getElementById('form1:htmlExecute').value;
		var target;
		if(val === '2'){
			target = "delete";
		} else if(val === '3'){
			target = "update";
		}		
		document.getElementById('form1:htmlExecute').value = "1";
		
		indirectClick(target);
	}
	function confirmCancel(){
		return true;
	}
	//-------------------------------------------
	// 科目検索子画面オープン
	//-------------------------------------------
	var kamokWindowName = "pXrc0201";
	function openSubKamokWindow(thisObj, thisEvent) {
		var target = thisObj.id;
		var gaksekiVal = document.getElementById("form1:htmlGakusekiCd").value;
		var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0201.jsp?retFieldName=" 
			+ target.substring(0, target.lastIndexOf('_')) + "&paramGaksekiCd=" + gaksekiVal;
		
		var ajaxServlet = "rev/xrx/XrxRemoveFromSessionAJAX";
		var args = new Array();
		args['pcClass'] = 'com.jast.gakuen.rev.xrc.PXrc0201';
		var engine      = new AjaxEngine();
		engine.setCallbackMethod(
		function(value) {
			var windowPointer = openModalWindow(url, kamokWindowName, "<%=com.jast.gakuen.rev.xrc.PXrc0201.getWindowOpenOption() %>");
			focus();
		});
		engine.send(ajaxServlet,null,args);
		return false;	
	}

	//-------------------------------------------
	// 科目名称取得 (AJAX)
	//-------------------------------------------
	function doKamokuAjax(thisObj, thisEvent) {
		var servlet = "rev/xrc/XrcKmkAJAX";
		var target = thisObj.id + "_name";
		var args = new Array();
		args['code'] = thisObj.value;
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="init();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx04102.onPageLoadBegin}" >
<gakuen:itemStateCtrl managedbean="pc_Xrx04102">
<h:form styleClass="form" id="form1">
<!-- ヘッダーインクルード -->
<jsp:include page="../inc/header.jsp" />
<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;"><hx:commandExButton type="submit"
  value="閉じる" styleClass="commandExButton" id="closeDisp"
  action="#{pc_Xrx04102.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrx04102.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrx04102.screenName}"></h:outputText>
</div>
<h:inputHidden
	value="#{pc_Xrx04102.propExecuteUpdate.integerValue}"
	id="htmlExecute">
	<f:convertNumber/>
</h:inputHidden>
<!--↓outer↓-->
<DIV class="outer">
<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
  id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
  styleClass="outputText" escape="false">
</h:outputText></FIELDSET>
<!--↓content↓-->
<DIV class="head_button_area">
<hx:commandExButton type="submit"
								  value=" 戻る " styleClass="commandExButton" id="returnDisp"
								  action="#{pc_Xrx04102.doReturnDispAction}"></hx:commandExButton>

<DIV id="content">
<DIV class="column" align="center">
<TABLE border="0" cellpadding="5">
	<TBODY>
		<TR>
			<TD width="800">
			<!-- 定型出力項目-->
			<TABLE class="table" width="100%" style="margin-top:7px;">
				<TBODY>
					<TR>
						<TH class="v_a" width="200">
							<h:outputText styleClass="outputText" id="lblSsiName"
                  				value="#{pc_Xrx04102.propSsiName.labelName}"
                  				style="#{pc_Xrx04102.propSsiName.labelStyle}"></h:outputText>
						</TH>
						<TD width="600">
							<h:inputText styleClass="likeOutput" id="htmlSsiName"
								size="80"
                  				value="#{pc_Xrx04102.propSsiName.value}"
                  				readonly="#{pc_Xrx04102.propSsiName.readonly}">
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_a" width="200">
							<h:outputText styleClass="outputText" id="lblGaksekiNo"
				                value="#{pc_Xrx04102.propGakusekiCd.labelName}"
				                style="#{pc_Xrx04102.propGakusekiCd.labelStyle}"></h:outputText>
						</TH>
						<TD><h:inputText styleClass="inputText"
		                  id="htmlGakusekiCd" size="18"
		                  maxlength="#{pc_Xrx04102.propGakusekiCd.maxLength}"
		                  value="#{pc_Xrx04102.propGakusekiCd.stringValue}"
		                  style="#{pc_Xrx04102.propGakusekiCd.style}"
		                  readonly="#{pc_Xrx04102.propGakusekiCd.readonly}"
		   				  disabled="#{pc_Xrx04102.propGakusekiCd.disabled}"
		                  maxlength="#{pc_Xrx04102.propGakusekiCd.maxLength}"
		                  onblur="return doGakuseiAjax(this, event, 'form1:htmlGakuseiNm');"></h:inputText>
		                <hx:commandExButton type="button" value="検"
		                  styleClass="commandExButton_search" id="select"
		                  disabled="#{pc_Xrx04102.propGakusekiCd.disabled}"
		                  readonly="#{pc_Xrx04102.propGakusekiCd.readonly}"
		                  onclick="openSubWindow('form1:htmlGakusekiCd');"></hx:commandExButton>
		                <h:inputText id="htmlGakuseiNm" tabindex="-1"
		                  styleClass="likeOutput" size="40"
		                  value="#{pc_Xrx04102.propGakuseiNm.stringValue}"
		                  readonly="#{pc_Xrx04102.propGakuseiNm.readonly}"></h:inputText>
		              </TD>
					</TR>
					<TR>
						<TH class="v_a" width="200">
							<h:outputText styleClass="outputText" id="lblSsiDate"
                  				value="#{pc_Xrx04102.propSsiDate.labelName}"
                  				style="#{pc_Xrx04102.propSsiDate.labelStyle}"></h:outputText>
						</TH>
						<TD width="600">
							<h:inputText styleClass="inputText"
								id="htmlSsiDate" size="12"
								readonly="#{pc_Xrx04102.propSsiDate.readonly}"
								disabled="#{pc_Xrx04102.propSsiDate.disabled}"
								value="#{pc_Xrx04102.propSsiDate.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"promptCharacter="_" />
							</h:inputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- 申請NO-->
			<hx:jspPanel rendered="#{!pc_Xrx04102.isCreate}">
			<TABLE class="table" width="100%" style="margin-top:7px;">
				<TBODY>
					<TR>
						<TH class="v_a" width="200">
							<h:outputText styleClass="outputText" id="lblSsiNo"
                  				value="#{pc_Xrx04102.propSsiNo.labelName}"></h:outputText>
						</TH>
						<TD>
							<h:outputText styleClass="outputText" id="htmlSsiNo"
                  				value="#{pc_Xrx04102.propSsiNo.value}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</hx:jspPanel>
			<TABLE class="table" width="100%" style="margin-top:7px;">
				<TBODY>
					<%
									Xrx04102 pc = (Xrx04102)UtilSystem.getManagedBean(Xrx04102.class);
									ArrayList layoutList = pc.getLayoutList();
									String curGyo = null;
									for(int i = 0; i < layoutList.size(); i++){
										Xrx04102L01Bean bean = (Xrx04102L01Bean)layoutList.get(i);
										String id = bean.getId();
										String gyoEdano = bean.getGyoEdano();
										String gyoRowno = bean.getGyoRowno();
										String gyoHeight = bean.getGyoHeight();
										String retsuEdano = bean.getRetsuEdano();
										String komokuName = bean.getKomokuName();
										String komokuWidth = bean.getKomokuWidth();
										String sikibetsu = bean.getSikibetsu();
										String variableNo = bean.getVariableNo();
										String dataWidth = bean.getDataWidth();
										String maxByte = bean.getMaxByte();
										String dispTarget = bean.getDispTarget();
										boolean hissu = bean.isHissu();
										String kmkZks = bean.getKmkZks();
										LinkedHashMap listItem = bean.getListItem();
										String value = bean.getValue();
										boolean checkValue = bean.isCheckValue();
										
										int hei = bean.isNullOrEmpty(gyoHeight) ? 20 : Integer.parseInt(gyoHeight) < 20 ? 20 : Integer.parseInt(gyoHeight);
										
										if(ShinseiKmkTitle.EMPTY.getCode().equals(sikibetsu)){
											continue;
										}
										
										out.print("<TR valign=\"middle\"");
										out.print(" style=\"");
										out.print("height:" + String.valueOf(hei) + "px;");
										out.print("\">");
										
										out.print("<TH style=\"word-break:break-all;\"");
										if(ShinseiKmkSbt.TITLE.getCode().equals((dispTarget))){
											out.print(" colspan=\"2\"");
										} else {
											out.print(" width=\"200px\"");
										}
										out.print(">");
										out.print("<span class=\"outputText\" ");
										out.print(bean.isHissu() ? "style=\"background-image: url(../../rev/image/ja/hissu_1.gif);\">" : ">");
										out.print(komokuName);
										out.print("</span>");
										out.print("</TH>");
										
										if(!ShinseiKmkSbt.TITLE.getCode().equals((dispTarget))){
											out.print("<TD style=\"padding-right:4px;\">");
											out.print("<Div style=\"width: 100%;\">");
											if(!ShinseiKmkTitle.YOYAKUGO.getCode().equals(sikibetsu)){
												out.print(bean.getInputTag());
											} else {
												out.print("<span class=\"outputText\">");
												out.print(value);
												out.print("</span>");
											}
											out.print("</Div>");
											out.print("</TD>");
										}
										out.print("</TR>");
									}
								%>
				</TBODY>
	        </TABLE>
	        <!-- 申請コメント-->
			<TABLE class="table" width="100%" style="margin-top:7px;">
				<TBODY>
					<TR>
						<TH class="v_a" width="200">
							<h:outputText styleClass="outputText" id="lblSsiComment"
                  				value="#{pc_Xrx04102.propComment.labelName}"
                  				style="#{pc_Xrx04102.propComment.labelStyle}"></h:outputText>
						</TH>
						<TD style="padding-right:4px;">
							<Div style="width: 100%;">
							<h:inputTextarea styleClass="inputTextarea"
								id="htmlSsiComment" rows="3"
								value="#{pc_Xrx04102.propComment.stringValue}"
								style="width: 100%;"></h:inputTextarea>
							</Div>
						</TD>
					</TR>	
				</TBODY>
			</TABLE>
			<!-- ボタン類-->
			<TABLE cellpadding="1" cellspacing="1" class="button_bar" align="center" width="100%" style="margin-top:7px;">
	          <TR>
	            <TD align="center" valign="middle">
	              <hx:commandExButton type="submit" value="確定"
	                styleClass="commandExButton_dat" id="update"
	                action="#{pc_Xrx04102.doUpdateAction}"></hx:commandExButton>
	              <hx:commandExButton type="submit" value="削除"
	                styleClass="commandExButton_etc" id="delete"
	                action="#{pc_Xrx04102.doDeleteAction}"
	                rendered="#{!pc_Xrx04102.isCreate && !pc_Xrx04102.isReadOnly && pc_Xrx04102.isHagaki}"></hx:commandExButton>
	            </TD>
	          </TR>
	        </TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>
</DIV>
</DIV>
</DIV>
</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
<SCRIPT type="text/javascript">
<%
	Xrx04102 pc = (Xrx04102)UtilSystem.getManagedBean(Xrx04102.class);
	out.print(pc.getDateScript());
%>
</SCRIPT>
</BODY>

<jsp:include page ="../inc/common.jsp" />

</f:view>
<SCRIPT LANGUAGE="JavaScript">
  window.attachEvent('onload', endload);

  function endload() {
    changeScrollPosition('htmlHidScroll', 'listScroll');
  }
</SCRIPT>

</HTML>
