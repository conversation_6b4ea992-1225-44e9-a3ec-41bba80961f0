<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/PXrh0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>PXrh0101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function confirmOk() {	
	document.getElementById('form1:propClearButton').value = "1";
	indirectClick('search');
}

function confirmCancel() {	
	document.getElementById('form1:propClearButton').value = "0";
}

//親画面に試験地コードを返却
function doSelect(thisObj, thisEvent) {
	
	var buttonid = thisObj.id;
	// 正規表現にて選択行のindex値を取得
	var point_start = buttonid.search(":[0-9]*:");
	var point_end = buttonid.search(":btnSelect");
	var index = buttonid.substring(point_start+1,point_end);
	var sikentiCd = document.getElementById("form1:htmlSikentiList:"+ index +":lblSikentiCdList").innerHTML;
	// 試験地コード返却フィールドID
	var retFieldName = document.getElementById("form1:htmlRetFieldName").value;
	if (window.opener) {
		window.opener.document.getElementById(retFieldName).value = sikentiCd;
		window.opener.document.getElementById(retFieldName).onblur(); // 親ウィンドウのAjaxを実行して名称をセット
	}
	window.open('about:blank','_self').close();
	return true;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_PXrh0101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_PXrh0101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_PXrh0101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_PXrh0101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<!-- ↓ここに戻る／閉じるボタンを配置 --> 
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE class="table" width="620">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="110">
										<h:outputText
											styleClass="outputText" id="lblSikenticd"
					                		value="#{pc_PXrh0101.propSikenticd.labelName}"
											style="#{pc_PXrh0101.propSikenticd.labelStyle}">
										</h:outputText></TH>
									<TD>
										<h:inputText styleClass="inputText" id="htmlSikenticd"
											size="5" 
					                		maxlength="#{pc_PXrh0101.propSikenticd.maxLength}"
											value="#{pc_PXrh0101.propSikenticd.stringValue}"
											style="#{pc_PXrh0101.propSikenticd.style}"
											tabindex="1">
											<hx:inputHelperAssist errorClass="inputText_Error"
												imeMode="disabled"/>
										</h:inputText></TD>
									<TD>（前方一致）</TD>
								</TR>
								<TR>
									<TH nowrap class="v_b">
										<h:outputText
											styleClass="outputText" id="lblSikentinm"
					                		value="#{pc_PXrh0101.propSikentinm.labelName}"
											style="#{pc_PXrh0101.propSikentinm.labelStyle}">
										</h:outputText></TH>
									<TD>
										<h:inputText styleClass="inputText" id="htmlSikentinm"
											size="30" 
					                		maxlength="#{pc_PXrh0101.propSikentinm.maxLength}"
											value="#{pc_PXrh0101.propSikentinm.stringValue}"
											style="#{pc_PXrh0101.propSikentinm.style}"
											tabindex="2">
										</h:inputText></TD>
									<TD>
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlSikentiFindType"
											value="#{pc_PXrh0101.propSikentiFindType.value}" 
											tabindex="3">
											<f:selectItem itemValue="1" itemLabel="部分一致" />
											<f:selectItem itemValue="0" itemLabel="前方一致" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE cellspacing="1" cellpadding="1" class="button_bar" width="620">
							<TBODY>
								<TR align="right">
									<TD align="center">
									<hx:commandExButton
										type="submit" value="検　索" styleClass="commandExButton_dat"
										id="search"
										action="#{pc_PXrh0101.doSearchAction}" tabindex="4">
									</hx:commandExButton>
									<hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear"
										action="#{pc_PXrh0101.doClearAction}" tabindex="5">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE cellpadding="0" cellspacing="0" width="620">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText styleClass="outputText"
										id="lblCount" value="#{pc_PXrh0101.propSikentilist.listCount}件"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TD colspan="3">
										<div class="listScroll" style="width: 621px; height: 300px"><h:dataTable
											width="604px" border="1" cellpadding="2" cellspacing="0"
											columnClasses="columnClass1" headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_PXrh0101.propSikentilist.rowClasses}"
											styleClass="meisai_scroll" id="htmlSikentiList"
											value="#{pc_PXrh0101.propSikentilist.list}" var="varlist">
											<h:column id="column1">
												<f:facet name="header">
													<h:outputText id="lblSikentiCdHead" styleClass="outputText"
														value="試験地コード"></h:outputText>
												</f:facet>
												<f:attribute value="100" name="width" />
												<h:outputText styleClass="outputText" id="lblSikentiCdList"
													value="#{varlist.sikentiCd}"></h:outputText>
											</h:column>
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="試験地名称"
														id="lblSikentiNameHead"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="lblSikentiNameList"
													value="#{varlist.sikentiName}"></h:outputText>
												<f:attribute value="464" name="width" />
											</h:column>
											<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														id="lblButton_select_head"></h:outputText>
												</f:facet>
												<f:attribute value="30" name="width" />
												<hx:commandExButton type="button"
													value="選択" styleClass="commandExButton" id="btnSelect"
													tabindex="6"
													onclick="return doSelect(this, event);"
													action="#{pc_PXrh0101.doCloseDispAction}">
												</hx:commandExButton>
											</h:column>
										</h:dataTable>
										</div>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlRetFieldName"
				value="#{pc_PXrh0101.propRetFieldName.value}"></h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 -->
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/childFooter.jsp" />
			<h:inputHidden value="#{pc_PXrh0101.propClearButton.integerValue}"
				id="propClearButton">
				<f:convertNumber type="number" />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
