<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00601.jsp</TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	
<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlListCountDialogFlag').value = "1";
	indirectClick('htmlSearchButton');
}

function confirmCancel() {
	document.getElementById('form1:htmlListCountDialogFlag').value = "0";
}
function changeTextState() {
	document.getElementById('form1:htmlSeikyuDataSakuseiZumiTxt').disabled = !document.getElementById('form1:htmlSeikyuDataSakuseiZumiChk').checked;
	return true;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="changeTextState();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00601.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
styleClass="commandExButton" id="closeDisp"
action="#{pc_Xrb00601.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00601.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00601.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
 <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
  <TBODY>
   <TR>
    <TD>
     <TABLE class="table" width="700">
      <TBODY>
       <TR align="center">
        <TH nowrap class="v_a" width="160">
		 <!-- 申請日 -->
		 <h:outputText styleClass="outputText" id="lblSinseiDate" 
		  value="#{pc_Xrb00601.propSinseiDateFrom.labelName}">
		 </h:outputText>
	    </TH>
		<TD>
		 <!-- 申請日FROM -->
		 <h:inputText
		  styleClass="inputText"
		  id="htmlSinseiDateFrom"
		  size="10"
		  disabled="#{pc_Xrb00601.propSinseiDateFrom.disabled}"
		  value="#{pc_Xrb00601.propSinseiDateFrom.dateValue}"
		  style="#{pc_Xrb00601.propSinseiDateFrom.style}"
		  readonly="#{pc_Xrb00601.propSinseiDateFrom.readonly}">
		  <f:convertDateTime />
		  <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
		  <hx:inputHelperDatePicker />
		 </h:inputText>
		 ～
		 <!-- 申請日TO -->
		 <h:inputText
		  styleClass="inputText"
		  id="htmlSinseiDateTo"
		  size="10"
		  disabled="#{pc_Xrb00601.propSinseiDateTo.disabled}"
		  value="#{pc_Xrb00601.propSinseiDateTo.dateValue}"
		  style="#{pc_Xrb00601.propSinseiDateTo.style}"
		  readonly="#{pc_Xrb00601.propSinseiDateTo.readonly}">
		  <f:convertDateTime />
		  <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
		  <hx:inputHelperDatePicker />
		 </h:inputText>
		</TD>
	   </TR>
	   <TR>
		<TH nowrap class="v_a" width="160">
		 <!-- 学籍番号 -->
		 <h:outputText styleClass="outputText" id="lblGakusekiCd"
          value="#{pc_Xrb00601.propGakusekiCode.labelName}"
          style="#{pc_Xrb00601.propGakusekiCode.labelStyle}">
         </h:outputText>
		</TH>
		<TD>
		 <h:inputText
		  styleClass="inputText"
          id="htmlGakusekiCd" size="10"
          maxlength="#{pc_Xrb00601.propGakusekiCode.maxLength}"
          disabled="#{pc_Xrb00601.propGakusekiCode.disabled}"
          value="#{pc_Xrb00601.propGakusekiCode.stringValue}"
          style="#{pc_Xrb00601.propGakusekiCode.style}"
          readonly="#{pc_Xrb00601.propGakusekiCode.readonly}">
         </h:inputText>
		</TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
		 <!-- 変更区分 -->
		 <h:outputText styleClass="outputText" id="lblHenkoKbn"
          value="#{pc_Xrb00601.propHenkoKbn.labelName}"
          style="#{pc_Xrb00601.propHenkoKbn.labelStyle}">
         </h:outputText>
		</TH>
		<TD>
		 <h:selectManyCheckbox
		  id="htmlHenkoKbn"
          disabledClass="selectManyCheckbox_Disabled"
          styleClass="selectManyCheckbox"
          value="#{pc_Xrb00601.propHenkoKbn.value}"
          disabled="#{pc_Xrb00601.propHenkoKbn.disabled}">
          <f:selectItems value="#{pc_Xrb00601.propHenkoKbn.list}" />
         </h:selectManyCheckbox>
		</TD>
	   </TR>
	   <TR>
		<TH nowrap class="v_a">
		 <!-- 請求データ作成済 -->
		 <h:outputText styleClass="outputText" id="lblSeikyuDataSakuseiZumi"
          value="#{pc_Xrb00601.propSeikyuDataSakuseiZumiTxt.labelName}"
          style="#{pc_Xrb00601.propSeikyuDataSakuseiZumiTxt.labelStyle}">
         </h:outputText>
		</TH>
		<TD>
		 <h:selectBooleanCheckbox
		  id="htmlSeikyuDataSakuseiZumiChk"
          styleClass="selectBooleanCheckbox"
          value="#{pc_Xrb00601.propSeikyuDataSakuseiZumiChk.checked}"
          disabled="#{pc_Xrb00601.propSeikyuDataSakuseiZumiChk.disabled}"
          onclick="changeTextState();">
         </h:selectBooleanCheckbox>
		 <h:inputText
		  styleClass="inputText"
          id="htmlSeikyuDataSakuseiZumiTxt" size="10"
          maxlength="#{pc_Xrb00601.propSeikyuDataSakuseiZumiTxt.maxLength}"
          disabled="#{pc_Xrb00601.propSeikyuDataSakuseiZumiTxt.disabled}"
          value="#{pc_Xrb00601.propSeikyuDataSakuseiZumiTxt.dateValue}"
          style="#{pc_Xrb00601.propSeikyuDataSakuseiZumiTxt.style}"
          readonly="#{pc_Xrb00601.propSeikyuDataSakuseiZumiTxt.readonly}">
          <f:convertDateTime pattern="yyyy"/>
          <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
         </h:inputText>
         年
        </TD>
       </TR>
	   <TR>
		<TH nowrap class="v_a">
		 <!-- 振込依頼人コード -->
		 <h:outputText styleClass="outputText" id="lblHurikomiIraininCode"
          value="#{pc_Xrb00601.propHurikomiIraininCode.labelName}"
          style="#{pc_Xrb00601.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
		</TH>
		<TD>
		 <h:inputText
		  styleClass="inputText"
          id="htmlHurikomiIraininCode" size="10"
          maxlength="#{pc_Xrb00601.propHurikomiIraininCode.maxLength}"
          disabled="#{pc_Xrb00601.propHurikomiIraininCode.disabled}"
          value="#{pc_Xrb00601.propHurikomiIraininCode.stringValue}"
          style="#{pc_Xrb00601.propHurikomiIraininCode.style}"
          readonly="#{pc_Xrb00601.propHurikomiIraininCode.readonly}">
         </h:inputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE width="700">
      <TBODY>
       <TR>
        <TD>
         <hx:commandExButton
          type="submit"
          value="検索"
          styleClass="commandExButton_dat"
          id="htmlSearchButton"
          disabled="#{pc_Xrb00601.propSearchButton.disabled}"
          action="#{pc_Xrb00601.doSearchAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="クリア"
          styleClass="commandExButton_dat"
          id="htmlClearButton"
          disabled="#{pc_Xrb00601.propClearButton.disabled}"
          action="#{pc_Xrb00601.doClearAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE border="0" cellpadding="0" cellspacing="0" width="700">
	  <TBODY>
	   <TR>
		<TD>
		 <div class="listScroll" style="height: 300px">
          <h:dataTable
           border="1"
           cellpadding="2"
           cellspacing="0"
           headerClass="headerClass"
           footerClass="footerClass"
           columnClasses="columnClass1"
           rowClasses="#{pc_Xrb00601.propSinseiList.rowClasses}"
           styleClass="meisai_scroll"
           id="htmlSisneiList"
           var="varlist"
           value="#{pc_Xrb00601.propSinseiList.list}">
           <h:column id="column1">
            <f:facet name="header">
             <h:outputText id="lblSinseiDate_head"
              styleClass="outputText" value="申請日" escape="false">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblSinseiDate_list" value="#{varlist.propSinseiDate.value}">
            </h:outputText>
            <f:attribute value="70" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column2">
            <f:facet name="header">
             <h:outputText id="lblGakusekiCode_head"
              styleClass="outputText" value="学籍番号">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblGakusekiCode_list" value="#{varlist.propGakusekiCode.value}">
            </h:outputText>
            <f:attribute value="110" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column3">
            <f:facet name="header">
             <h:outputText id="lblGakuseiName_head"
              styleClass="outputText" value="氏名">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblGakuseiName_list" value="#{varlist.propGakuseiName.value}">
            </h:outputText>
            <f:attribute value="160" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column4">
            <f:facet name="header">
             <h:outputText id="lblHenkoKbn_head"
              styleClass="outputText" value="変更区分">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblHenkoKbn_list" value="#{varlist.propHenkoKbn.value}">
            </h:outputText>
            <f:attribute value="160" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column5">
            <f:facet name="header">
             <h:outputText id="lblHurikomiIRaininCode_head"
              styleClass="outputText" value="振込依頼人コード">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblHurikomiIRaininCode_list" value="#{varlist.propHurikomiIraininCode.value}">
            </h:outputText>
            <f:attribute value="160" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column6">
            <hx:commandExButton type="submit" value="選択"
             styleClass="commandExButton" id="htmlSelectButton"
             rendered="#{varlist.rendered}"
             style="width: 40px"
             action="#{pc_Xrb00601.doSelectAction}">
            </hx:commandExButton>
            <f:attribute value="40" name="width" />
           </h:column>
          </h:dataTable>
		 </div>
		</TD>
	   </TR>
      </TBODY>
     </TABLE>
    </TD>
   </TR>
  </TBODY>
 </TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden value="#{pc_Xrb00601.propListCountDialogFlag.integerValue}" id="htmlListCountDialogFlag">
 <f:convertNumber />
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

