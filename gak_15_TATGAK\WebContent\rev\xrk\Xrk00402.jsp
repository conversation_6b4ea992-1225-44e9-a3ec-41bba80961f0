<%-- 
	発行履歴修正
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrk/Xrk00402.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrk00402.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
	function init() {
		//func_1(thisObj, thisEven, targetLabel);
	}
	function func_1(thisObj, thisEven, targetLabel) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		//20161021 Exceed zhao 卒業正仕様追加 Start
		//var servlet = "rev/co/CobGakseiAJAX";
		//var target = "form1:htmlGakusekiName";
		//var args = new Array();
		//args['code1'] = thisObj.value;
		//args['code2'] = "";
		//args['code3'] = "";
		//var ajaxUtil = new AjaxUtil();
		//ajaxUtil.getPluralValue(servlet,target,args);
		//return true;
    	var servlet = "rev/xrk/XrkCobGakusekiAJAX";
	    var args = new Array();
	    args['code1'] = thisObj.value;
	
	    var ajaxUtil = new AjaxUtil();
	    ajaxUtil.getPluralValueSetMethod(servlet, targetLabel, args, "setZaiSotinfo");
		//20161021 Exceed zhao 卒業正仕様追加 End
	}
	
	//20161021 Exceed zhao 卒業正仕様追加 Start
	//在学生・卒業生情報を取得する（CallBack関数）
	function setZaiSotinfo (value) {
	
		var gakuseiName = value['gakuseiName'];

		if (gakuseiName != "") {
			document.getElementById('form1:htmlGakusekiName').value = gakuseiName;
			
			var gakuseiKbn = value['syuturyokuKbu'];
			
			if (gakuseiKbn == 0) {
				document.getElementsByName('form1:htmlOutputTarget')[1].checked = true;
			} else if (gakuseiKbn == 1) {
				document.getElementsByName('form1:htmlOutputTarget')[2].checked = true;
			}
		} else {
			document.getElementsByName('form1:htmlOutputTarget')[1].checked = true;
		    document.getElementById('form1:htmlGakusekiName').value = "";
		}
	}
	//20161021 Exceed zhao 卒業正仕様追加 End
	
	function func_2(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		//var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakuSekiCd";
		//20161021 Exceed zhao 卒業正仕様追加 Start
 		var url="${pageContext.request.contextPath}/faces/rev/co/";
		var outputTarget = document.getElementsByName('form1:htmlOutputTarget');
		
	    if (outputTarget[1].checked) {
	      url = url + "pCob0101.jsp"
	                + "?retFieldName=form1:htmlGakuSekiCd"
	                + "&gakuseiSearchKbn=0";
	      openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	    } else if (outputTarget[2].checked) {
	      url = url + "pCob0201.jsp"
	                + "?retFieldName=form1:htmlGakuSekiCd"
	                + "&retFieldName2=form1:htmlHidSotNendo"
	                + "&retFieldName3=form1:htmlHidSotGakki"
	                + "&gakuseiSearchKbn=0";  
	      openModalWindow(url, "PCob0201", "<%=com.jast.gakuen.rev.co.PCob0201.getWindowOpenOption() %>");
	    } else {
	      // 何もしない
	    }
		
		return false;
		//20161021 Exceed zhao 卒業正仕様追加 End
	}
	
	function callBackMethod(value){
		try{
			document.getElementById("form1:htmlGakusekiName").value = value["name"];
		} catch (e) {
		}
	}
	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfilm").value = "0";
		} catch (e) {
		}
	}
	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfilm").value = "1";
			indirectClick(document.getElementById("form1:htmlAction").value);
		} catch (e) {
		}
	}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="init();">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrk00402.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrk00402.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrk00402.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrk00402.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
  styleClass="commandExButton" id="returnDisp"
  action="#{pc_Xrk00402.doReturnAction}">
</hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="950">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText
							styleClass="outputText" value="#{pc_Xrk00402.propXrkSyomHakRrk.listCount == null ? 0 : pc_Xrk00402.propXrkSyomHakRrk.listCount}" style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="htmlCountlbl" value="件" style="font-size: 8pt"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="950">
				<TBODY>
					<TR>
						<TD>

						<div class="listScroll" style="height:254px;" id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrk00402.propXrkSyomHakRrk.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xrk00402.propXrkSyomHakRrk.list}" var="varlist">
							
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblHakkoNendo_head" styleClass="outputText"
										value="発行年度">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblHakkoNendo"
									value="#{varlist.hakkoNendo}"></h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblPatternName_head" styleClass="outputText"
										value="パターン">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblPatternName"
									title="#{varlist.patternName.value}"
									value="#{varlist.patternName.displayValue}"></h:outputText>
								<f:attribute value="170" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							

							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="lblHakkoNo_head" styleClass="outputText"
										value="発行番号">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblHakkoNo"
									value="#{varlist.hakkoNo}"></h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column4">
								<f:facet name="header">
									<h:outputText id="lblGaksekiNo_head" styleClass="outputText"
										value="学籍番号">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblGakusekiNo"
									value="#{varlist.gakusekiNo}"></h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText id="lblGakusekiName_head" styleClass="outputText"
										value="学生氏名">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblGakusekiName"
									title="#{varlist.gakusekiName.value}"
									value="#{varlist.gakusekiName.displayValue}"></h:outputText>
								<f:attribute value="170" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column6">
								<f:facet name="header">
									<h:outputText id="lblHakkoDate_head" styleClass="outputText"
										value="発行日付">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblHakkoDate"
									value="#{varlist.hakkoDate}">
									<f:convertDateTime />
								</h:outputText>
								<f:attribute value="90" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column7">
								<f:facet name="header">
									<h:outputText id="lblSakuseiFlg_head" styleClass="outputText"
										value="作成済">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblSakuseiFlg"
									value="#{varlist.sakuseiFlg}"></h:outputText>
								<f:attribute value="60" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column8">
								<f:facet name="header">
									<h:outputText id="lblmokuteki_head" styleClass="outputText"
										value="使用目的">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblMokuteki"
									title="#{varlist.mokuteki.value}"
									value="#{varlist.mokuteki.displayValue}"></h:outputText>
								<f:attribute value="170" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							
							<h:column id="column9">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="30" name="width">
								</f:attribute>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style">
								</f:attribute>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Xrk00402.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="table" width="950" cellpadding="0"
				cellspacing="0">
				<TBODY>
					<TR>
						<!-- 発行年度 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropHakkoNendo" 
							value="#{pc_Xrk00402.propHakkoNendo.labelName}"
							style="#{pc_Xrk00402.propHakkoNendo.labelStyle}"></h:outputText></TH>
						<TD colspan="3" width="500"><h:inputText styleClass="inputText"
							id="htmlHakkoNendo"
							value="#{pc_Xrk00402.propHakkoNendo.stringValue}"
							maxlength="#{pc_Xrk00402.propHakkoNendo.maxLength}" size="4"
                        	disabled="#{pc_Xrk00402.propHakkoNendo.disabled}"
							style="#{pc_Xrk00402.propHakkoNendo.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"/>
						</h:inputText></TD>
					</TR>
					
					<TR>
						<!-- パターン -->
						<TH nowrap class="v_c" width="150">
							<h:outputText styleClass="outputText" id="lblPropPatternNo"
							value="#{pc_Xrk00402.propPatternNo.labelName}"
							style="#{pc_Xrk00402.propPatternNo.labelStyle}"></h:outputText></TH>
                        <TD colspan="3" nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlPatternNo"
                            	value="#{pc_Xrk00402.propPatternNo.value}"
                            	disabled="#{pc_Xrk00402.propPatternNo.disabled}"
                            	style="width:275px">
                            	<f:selectItems value="#{pc_Xrk00402.propPatternNo.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>

					<TR>
						<!-- 発行番号 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropHakkoNo" 
							value="#{pc_Xrk00402.propHakkoNo.labelName}"
							style="#{pc_Xrk00402.propHakkoNo.labelStyle}"></h:outputText></TH>
						<TD colspan="3" width="500"><h:inputText styleClass="inputText"
							id="htmlHakkoNo"
							value="#{pc_Xrk00402.propHakkoNo.stringValue}"
							maxlength="#{pc_Xrk00402.propHakkoNo.maxLength}" size="7"
                        	disabled="#{pc_Xrk00402.propHakkoNo.disabled}"
							style="#{pc_Xrk00402.propHakkoNo.style}">
							<f:convertNumber type="number" pattern="######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					
					<TR>
						<!-- 学籍番号 -->
						<TH  class="v_d" width="200"><h:outputText styleClass="outputText"
							id="lblPropGakusekiCd" value="#{pc_Xrk00402.propGakusekiCd.labelName}"
							style="#{pc_Xrk00402.propHakkoNo.labelStyle}">
							</h:outputText></TH>
					    <TD colspan="1" width="10"><h:inputText
		                    styleClass="inputText" id="htmlGakusekiCd"
		                    value="#{pc_Xrk00402.propGakusekiCd.stringValue}"
		                   	style="ime-mode:disabled"
		                    maxlength="#{pc_Xrk00402.propGakusekiCd.maxLength}"
		                    onblur="return func_1(this, event, 'form1:htmlGakusekiName');" size="10"></h:inputText>
		                </TD>
		                <TD colspan="1" width="110">    
		                    <h:selectOneRadio
	                          disabledClass="selectOneRadio_Disabled"
	                          styleClass="selectOneRadio"
	                          id="htmlOutputTarget"
	                  		  disabled="#{pc_Xrk00402.propOutputTarget.disabled}"
	                          value="#{pc_Xrk00402.propOutputTarget.value}">
	                          <f:selectItem itemValue="0" itemLabel="在学生" />
	                          <f:selectItem itemValue="1" itemLabel="卒業生" />
	                        </h:selectOneRadio>
	                     </TD>
	                     <TD colspan="1" width="380">     
		                    <hx:commandExButton
		                    type="button" styleClass="commandExButton_search" id="search"
		                    onclick="return func_2(this, event);"></hx:commandExButton></TD>
					</TR>
					
					<TR>
						<!-- 学生氏名 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropGakusekiName" 
							value="#{pc_Xrk00402.propGakusekiName.labelName}"
							style="#{pc_Xrk00402.propGakusekiName.labelStyle}"></h:outputText></TH>
						<TD colspan="3" width="500"><h:inputText styleClass="inputText"
							id="htmlGakusekiName"
							value="#{pc_Xrk00402.propGakusekiName.stringValue}"
							maxlength="#{pc_Xrk00402.propGakusekiName.maxLength}" size="40"
							style="#{pc_Xrk00402.propGakusekiName.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"/>
						</h:inputText></TD>
						
					</TR>

					<TR>
						<!-- 発行日付 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropHakkoDate" 
							value="#{pc_Xrk00402.propHakkoDate.labelName}"
							style="#{pc_Xrk00402.propHakkoDate.labelStyle}"></h:outputText></TH>
							
						<TD colspan="3" width="500"><h:inputText styleClass="inputText"
                    		id="htmlHakkoDate"
                    		value="#{pc_Xrk00402.propHakkoDate.dateValue}">
                    		<f:convertDateTime />
                    		<hx:inputHelperDatePicker />
                    		<hx:inputHelperAssist errorClass="inputText_Error"
                      		promptCharacter="_"></hx:inputHelperAssist>
                  			</h:inputText></TD>
					</TR>
					<TR>
						<!-- 出力日付 -->
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPropShutuDate" 
							value="#{pc_Xrk00402.propShutuDate.labelName}"
							style="#{pc_Xrk00402.propShutuDate.labelStyle}"></h:outputText></TH>
						<TD colspan="3" width="500"><h:inputText styleClass="inputText"
                    		id="htmlShutuDate"
                    		value="#{pc_Xrk00402.propShutuDate.dateValue}">
                    		<f:convertDateTime />
                    		<hx:inputHelperDatePicker />
                    		<hx:inputHelperAssist errorClass="inputText_Error"
                      		promptCharacter="_"></hx:inputHelperAssist>
                  			</h:inputText></TD>
					</TR>
					
					<TR>
						<!-- 作成済 -->
						<TH class="v_e" width="200"><h:outputText
							styleClass="outputText" id="lblPropSakuseiFlg" 
							value="#{pc_Xrk00402.propSakuseiFlg.labelName}"
							style="#{pc_Xrk00402.propSakuseiFlg.labelStyle}"></h:outputText></TH>
							
						<TD colspan="3" width="500"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlSakuseiFlg"
							value="#{pc_Xrk00402.propSakuseiFlg.checked}"></h:selectBooleanCheckbox>
							<h:outputText styleClass="outputText" id="lblSakuseiZumi" value="作成済"></h:outputText></TD>
					</TR>
					
					<TR>
						<!-- 使用目的 -->
						<TH nowrap class="v_d" width="150">
							<h:outputText styleClass="outputText" id="lblPropMokuteki"
							value="#{pc_Xrk00402.propMokuteki.labelName}"
							style="#{pc_Xrk00402.propMokuteki.labelStyle}"></h:outputText></TH>
                        <TD colspan="3" nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlMokuteki"
                            	value="#{pc_Xrk00402.propMokuteki.value}"
                            	disabled="#{pc_Xrk00402.propMokuteki.disabled}"
                            	style="width:200px">
                            	<f:selectItems value="#{pc_Xrk00402.propMokuteki.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>
					
					<TR>
						<!-- 証明書種類 -->
						<TH nowrap class="v_d" width="150">
							<h:outputText styleClass="outputText" id="lblPropShomShurui"
							value="#{pc_Xrk00402.propShomShurui.labelName}"
							style="#{pc_Xrk00402.propShomShurui.labelStyle}"></h:outputText></TH>
                        <TD colspan="3" nowrap>
                        	<h:selectOneMenu styleClass="selectOneMenu"
                            	id="htmlShomShurui"
                            	value="#{pc_Xrk00402.propShomShurui.value}"
                            	disabled="#{pc_Xrk00402.propShomShurui.disabled}"
                            	style="width:200px">
                            	<f:selectItems value="#{pc_Xrk00402.propShomShurui.list}" />
	                        </h:selectOneMenu>
	                    </TD>
					</TR>

				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register" 
							action="#{pc_Xrk00402.doRegisterAction}" 
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}" 
							action="#{pc_Xrk00402.doDeleteAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrk00402.doClearAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrk00402.propXrkSyomHakRrk.scrollPosition}"id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrk00402.propHtmlHidSotNendo.value}" id="htmlHidSotNendo"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrk00402.propHtmlHidSotGakki.value}" id="htmlHidSotGakki"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

