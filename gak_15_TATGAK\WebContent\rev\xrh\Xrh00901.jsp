<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
		// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}
	
	// 学生氏名を取得する
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	//ページ初期化
	function reloadPage( thisEvent ){
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), thisEvent, 'form1:lblName');
	}
 	
 	function doKamokuAjax(thisObj, thisEvent, targetLabel, targetLabel2) {
	// 科目名称,単位数を取得する
		var servlet = "rev/xrf/XrfKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;

	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);


		// 単位数
		if ( targetLabel2 != "" ) {
		    var args2 = new Array();
		    args2['code'] = thisObj.value;
		    args2['tanisu'] = "GET";
		    args2['addString'] = " 単位";

			ajaxUtil.getCodeName(servlet, targetLabel2, args2);
		}
	}
	
	function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}
 	
 	function openKaisaitiSearchWindow(thisObj, thisEvent) {
		var url="${pageContext.request.contextPath}/faces/rev/xrh/pXrh0101.jsp?retFieldName=form1:htmlSikentiCd";
		openModalWindow(url, "pXrh0101", "<%= com.jast.gakuen.rev.xrh.PXrh0101.getWindowOpenOption() %>");
		return true;
	}

	function doSikentiAjax(thisObj, thisEvent) {
		var servlet = "rev/xrh/XrhMeiSikentiNmAJAX";
		var target = "form1:lblSikentiNm";
		var args = new Array();
		args['code'] = thisObj.value;
	
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}

	function confirmRemoveAll(msg) {
		var args = new Array();
		args[0] = "表示内容";
		return confirm(messageCreate(msg, args));
	}

 	function confirmOk(){
 		if(document.getElementById("form1:propExecutable").value == 1){
 			indirectClick("register");
 		}
 	}
 	
 	function confirmCancel(){
 		if(document.getElementById("form1:propExecutable").value == 1){
			document.getElementById("form1:propExecutable").value = 2;
 			indirectClick("register");
 		}
 	}
 	
	function loadAction(event){
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:lblName');
		doKamokuAjax(document.getElementById('form1:htmlKamokCd'), event, 'form1:lblKamokNm', '');
		doSikentiAjax(document.getElementById('form1:htmlSikenticd'), event);
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00901.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00901.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00901.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00901.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="0" cellspacing="0" >
			<TBODY>
			<TR>
			<TD>
				<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblNendo"
								value="#{pc_Xrh00901.propNendo.labelName}" 
								style="#{pc_Xrh00901.propNendo.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="472">
							<h:inputText id="htmlNendo" styleClass="inputText" 
								readonly="#{pc_Xrh00901.propNendo.readonly}" 
								value="#{pc_Xrh00901.propNendo.dateValue}"
								disabled="#{pc_Xrh00901.propNendo.disabled}" size="4" tabindex="1">
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<f:convertDateTime pattern="yyyy" />
								</h:inputText>
						</TD>
						
						<TD rowspan="4" width="154" align="center"
							style="background-color: transparent; text-align: center"
							class="clear_border">
							<hx:commandExButton type="submit"
								value="選択"
								styleClass="commandExButton" id="select"
								disabled="#{pc_Xrh00901.propSelect.disabled}"
								rendered="#{pc_Xrh00901.propSelect.rendered}"
								style="#{pc_Xrh00901.propSelect.style}"
								action="#{pc_Xrh00901.doSelectAction}" tabindex="6">
							</hx:commandExButton>
							<hx:commandExButton type="submit"
								value="解除"
								styleClass="commandExButton" id="unSelect"
								disabled="#{pc_Xrh00901.propUnSelect.disabled}"
								rendered="#{pc_Xrh00901.propUnSelect.rendered}"
								style="#{pc_Xrh00901.propUnSelect.style}"
								action="#{pc_Xrh00901.doUnSelectAction}" tabindex="7">
							</hx:commandExButton>
						</TD>
					</TR>
					
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								value="#{pc_Xrh00901.propKamokSikenCnt.labelName}"
		                		style="#{pc_Xrh00901.propKamokSikenCnt.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="200">
							<h:inputText id="htmlKamokSikenCnt" styleClass="inputText"
										readonly="#{pc_Xrh00901.propKamokSikenCnt.readonly}"
										value="#{pc_Xrh00901.propKamokSikenCnt.integerValue}"
										disabled="#{pc_Xrh00901.propKamokSikenCnt.disabled}" size="4"
										tabindex="2">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText>
						</TD>
					</TR>
					
					<TR>
						<TH nowrap class="v_a" width="150">
							<!--学籍番号 -->
						    <h:outputText 
		                		styleClass="outputText" 
		                		id="lblGakusekiCd"
		                		value="#{pc_Xrh00901.propGakusekiCd.labelName}"
		                		style="#{pc_Xrh00901.propGakusekiCd.labelStyle}">
						    </h:outputText>
						</TH>
						<TD>
							<h:inputText id="htmlGakusekiCd" styleClass="inputText"
										readonly="#{pc_Xrh00901.propGakusekiCd.readonly}"
										disabled="#{pc_Xrh00901.propGakusekiCd.disabled}"
										maxlength="#{pc_Xrh00901.propGakusekiCd.maxLength}" size="20"
										tabindex="3" value="#{pc_Xrh00901.propGakusekiCd.stringValue}"
										onblur="return doGakuseiAjax(this, event, 'form1:lblName');">
									</h:inputText>
							<hx:commandExButton type="button"
										styleClass="commandExButton_search" id="search"
										disabled="#{pc_Xrh00901.propSearchGakuseki.disabled}"
										rendered="#{pc_Xrh00901.propSearchGakuseki.rendered}"
										style="#{pc_Xrh00901.propSearchGakuseki.style}"
										onclick="openSubWindow('form1:htmlGakusekiCd');" tabindex="4">
									</hx:commandExButton>
							<h:outputText
		              			styleClass="outputText"
		              			id="lblName"
		              			value="#{pc_Xrh00901.propName.stringValue}">
		              		</h:outputText>
		              	</TD>
		             </TR>
					 
					 <TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
									value="ソート順">
							</h:outputText>
						</TH>
						<TD>
							<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSort"
										value="#{pc_Xrh00901.propSort.stringValue}"
										style="#{pc_Xrh00901.propSort.style}"
										disabled="#{pc_Xrh00901.propSort.disabled}" tabindex="5">
										<f:selectItem itemValue="0" itemLabel="試験地、試験日" />
										<f:selectItem itemValue="1" itemLabel="試験日、試験地" />
									</h:selectOneRadio>
						</TD>
					 </TR>
				</TBODY>
				</TABLE>
				
				<TABLE width="800" border="0" style="margin-top:8px">
				<TBODY>
					<TR>
						<TD align="right" height="16">
							<h:outputText styleClass="outputText"
								id="lblListCount"
								value="#{pc_Xrh00901.propListCount.stringValue}"
								style="#{pc_Xrh00901.propListCount.style}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TD>
							<DIV id="listScroll" class="listScroll" style="height: 256px;">
								<h:dataTable 
									columnClasses="columnClass" 
									headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Xrh00901.propList.rowClasses}"
									styleClass="meisai_scroll" id="htmlList"
									value="#{pc_Xrh00901.propList.list}" var="varlist">
									
									<h:column id="column1">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="試験日"
												id="lblListSikentiDate">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListSikenDate"
											style="#{pc_Xrh00901.propListSikenDate.style}"
											value="#{varlist.sikenDate.stringValue}">
										</h:outputText>
										<f:attribute value="106" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
											
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="試験地"
												id="lblListSikenti">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListSikenti"
											style="#{pc_Xrh00901.propListSikenti.style}"
											value="#{varlist.sikenti.displayValue}"
											title="#{varlist.sikenti.stringValue}">
										</h:outputText>
										<f:attribute value="100" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
											
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="時限"
												id="lblListJigen">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListJigen"
											style="#{pc_Xrh00901.propListJigen.style}"
											value="#{varlist.jigen.stringValue}">
										</h:outputText>
										<f:attribute value="58" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>
											
									<h:column id="column4">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="科目"
												id="lblListKamok">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKamok"
											style="#{pc_Xrh00901.propListKamok.style}"
											value="#{varlist.kamok.displayValue}"
											title="#{varlist.kamok.stringValue}">
										</h:outputText>
										<f:attribute value="262" name="width" />
										<f:attribute value="text-align: left" name="style" />
									</h:column>
											
									<h:column id="column5">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="無効理由"
												id="lblListMukoriyu">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListMukoriyu"
											value="#{varlist.mukoriyu.displayValue}"
											title="#{varlist.mukoriyu.stringValue}">
										</h:outputText>
										<f:attribute value="210" name="width" />
									</h:column>
											
									<h:column id="column6">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit"
											value="選択"
											styleClass="commandExButton" 
											id="listEdit"
											action="#{pc_Xrh00901.doEditAction}" tabindex="8">
										</hx:commandExButton>
										<f:attribute value="true" name="nowrap" />
										<f:attribute value="36" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>

								</h:dataTable>
							</DIV>
						</TD>
					</TR>
				</TBODY>
				</TABLE>
								
				<BR>
				
				<TABLE width="800" border="0" cellpadding="0" cellspacing="0" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblSikenbiYobi"
								value="#{pc_Xrh00901.propSikenbiYobi.labelName}" 
								style="#{pc_Xrh00901.propSikenbiYobi.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="240">
							<h:selectOneMenu 
								styleClass="selectOneMenu" 
								id="htmlSikenbiYobi"
								value="#{pc_Xrh00901.propSikenbiYobi.stringValue}"
								style="#{pc_Xrh00901.propSikenbiYobi.style};width:150px"
								disabled="#{pc_Xrh00901.propSikenbiYobi.disabled}" tabindex="9">
								<f:selectItems 
									value="#{pc_Xrh00901.propSikenbiYobi.list}" />
							</h:selectOneMenu>
						</TD>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblUketukeNo"
								value="#{pc_Xrh00901.propUketukeNo.labelName}">
							</h:outputText>
						</TH>
						<TD width="240">
							<h:inputText id="htmlUketukeNo" styleClass="inputText" 
                      			maxlength="#{pc_Xrh00901.propUketukeNo.maxLength}"
								readonly="#{pc_Xrh00901.propUketukeNo.readonly}" 
								value="#{pc_Xrh00901.propUketukeNo.stringValue}"
								disabled="#{pc_Xrh00901.propUketukeNo.disabled}" size="18" tabindex="10">
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblJigen"
								value="#{pc_Xrh00901.propJigen.labelName}" 
								style="#{pc_Xrh00901.propJigen.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="180">
							<h:selectOneMenu 
								styleClass="selectOneMenu" 
								id="htmlJigen"
								value="#{pc_Xrh00901.propJigen.stringValue}"
								style="#{pc_Xrh00901.propJigen.style};width:150px"
								disabled="#{pc_Xrh00901.propJigen.disabled}" tabindex="11">
								<f:selectItems 
									value="#{pc_Xrh00901.propJigen.list}" />
							</h:selectOneMenu>
						</TD>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblSeiriNo"
								value="#{pc_Xrh00901.propSeiriNo.labelName}">
							</h:outputText>
						</TH>
						<TD width="200">
							<h:inputText id="htmlSeiriNo" styleClass="inputText" 
                      			maxlength="#{pc_Xrh00901.propSeiriNo.maxLength}"
								readonly="#{pc_Xrh00901.propSeiriNo.readonly}" 
								value="#{pc_Xrh00901.propSeiriNo.stringValue}"
								disabled="#{pc_Xrh00901.propSeiriNo.disabled}" size="18" tabindex="12">
							</h:inputText>
						</TD>
					</TR>
					
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblKamokCd"
								value="#{pc_Xrh00901.propKamokCd.labelName}" 
								style="#{pc_Xrh00901.propKamokCd.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="3">
							<h:inputText id="htmlKamokCd" styleClass="inputText" 
                        		size="20"
                      			maxlength="#{pc_Xrh00901.propKamokCd.maxLength}"
								disabled="#{pc_Xrh00901.propKamokCd.disabled}"
								value="#{pc_Xrh00901.propKamokCd.stringValue}"
                        		onblur="return doKamokuAjax(this, event, 'form1:lblKamokNm', '');" 
                        		tabindex="13">
							</h:inputText>
							<hx:commandExButton type="button"
								styleClass="commandExButton_search" id="searchKamok"
								onclick="return openKamokuSearchWindow(this, event);"
								disabled="#{pc_Xrh00901.propSearchKamok.disabled}" tabindex="14">
							</hx:commandExButton>	
      						<h:outputText
      							styleClass="outputText" id="lblKamokNm"
  								value="#{pc_Xrh00901.propKamokNm.stringValue}">
  							</h:outputText>				
						</TD>
					</TR>
					
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblSikentiCd"
								value="#{pc_Xrh00901.propSikentiCd.labelName}" 
								style="#{pc_Xrh00901.propSikentiCd.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="3">
							<h:inputText id="htmlSikentiCd" styleClass="inputText" 
                        		size="20"
                      			maxlength="#{pc_Xrh00901.propSikentiCd.maxLength}"
								disabled="#{pc_Xrh00901.propSikentiCd.disabled}"
								value="#{pc_Xrh00901.propSikentiCd.stringValue}" 
                        		onblur="return doSikentiAjax(this, event);"
								tabindex="15">
								<hx:inputHelperAssist errorClass="inputText_Error"
									imeMode="disabled"/>
							</h:inputText>
							<hx:commandExButton type="button"
								styleClass="commandExButton_search" 
								id="searchSikenti"
								onclick="return openKaisaitiSearchWindow(this, event);"
								disabled="#{pc_Xrh00901.propSearchSikenti.disabled}" tabindex="16">
							</hx:commandExButton>	
      						<h:outputText
      							styleClass="outputText" id="lblsikentiNm"
  								value="#{pc_Xrh00901.propSikentiNm.stringValue}">
  							</h:outputText>	
						</TD>	
					</TR>	
						
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblMukoriyu"
								value="#{pc_Xrh00901.propMukoriyu.labelName}" 
								style="#{pc_Xrh00901.propMukoriyu.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="180" colspan="3">
							<h:selectOneMenu 
								styleClass="selectOneMenu" 
								id="htmlMukoriyu"
								value="#{pc_Xrh00901.propMukoriyu.stringValue}"
								style="#{pc_Xrh00901.propMukoriyu.style};width:300px"
								disabled="#{pc_Xrh00901.propMukoriyu.disabled}" tabindex="17">
								<f:selectItems 
									value="#{pc_Xrh00901.propMukoriyu.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
				</TBODY>
				</TABLE>
								
				<BR>
								
				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" 
					class="button_bar">
				<TBODY>
					<TR>
					<TD>
						<hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="register"
							value="確定"
							action="#{pc_Xrh00901.doRegisterAction}"
							disabled="#{pc_Xrh00901.propRegister.disabled}"
							rendered="#{pc_Xrh00901.propRegister.rendered}"
							style="#{pc_Xrh00901.propRegister.style}" tabindex="18">
						</hx:commandExButton>
								
						<hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="delete"
							value="削除"
							action="#{pc_Xrh00901.doDeleteAction}"
							disabled="#{pc_Xrh00901.propDelete.disabled}"
							rendered="#{pc_Xrh00901.propDelete.rendered}"
							onclick="return confirm('#{msg.SY_MSG_0004W}');"
							style="#{pc_Xrh00901.propDelete.style}" tabindex="19">
						</hx:commandExButton>
								
						<hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="clear"
							value="クリア"
							action="#{pc_Xrh00901.doClearAction}"
							disabled="#{pc_Xrh00901.propClear.disabled}"
							rendered="#{pc_Xrh00901.propClear.rendered}"
							onclick="return confirmRemoveAll('#{msg.SY_MSG_0006W}');"
							style="#{pc_Xrh00901.propClear.style}" tabindex="20">
						</hx:commandExButton>
				  	</TD>
				  	</TR>
				</TBODY>
				</TABLE>
		</TD>
		</TR>
		</TBODY>
		</TABLE>				
		</DIV>
		</DIV>	
		</DIV>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		<h:inputHidden
				value="#{pc_Xrh00901.propExecutable.integerValue}"
				id="propExecutable">
		</h:inputHidden>
		
		<h:inputHidden
				value="#{pc_Xrh00901.propChkCnt.integerValue}"
				id="htmlPropChkCnt">
		</h:inputHidden>

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
				