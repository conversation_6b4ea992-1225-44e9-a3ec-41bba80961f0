<%-- 
	入出庫登録(発注管理)
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00503.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc00503.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
	// キャンセルボタンを押下時の処理
	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfirm").value = "";
			document.getElementById("form1:htmlAction").value = "";
		} catch (e) {
		}
	}
	// OKボタンを押下時の処理
	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfirm").value = "1";
			indirectClick(document.getElementById("form1:htmlAction").value);
		} catch (e) {
		}
	}	

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc00503.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc00503.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc00503.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc00503.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer" align="">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Xrc00503.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content" class="outer">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE width="900" class="table">
							<TBODY>
								<TR>
									<TH width="150"><h:outputText 
										styleClass="outputText" id="text1" 
										value="物品コード"
										style="#{pc_Xrc00503.propBuppinCd.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblBuppinCd" 
										value="#{pc_Xrc00503.propBuppinCd.stringValue}"
										style="#{pc_Xrc00503.propBuppinCd.style}"></h:outputText></TD>
									<TH width="130"><h:outputText
										styleClass="outputText" id="text2" 
										value="物品名称"></h:outputText></TH>
									<TD width="470" colspan="3"><h:outputText styleClass="outputText"
										id="lblBuppinName"
										value="#{pc_Xrc00503.propBuppinName.stringValue}"
										style="#{pc_Xrc00503.propBuppinName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_a"><h:outputText 
										styleClass="outputText" id="text3" 
										value="物品区分"
										style="#{pc_Xrc00503.propBuppinKbnName.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblBuppinKbnName" 
										value="#{pc_Xrc00503.propBuppinKbnName.stringValue}"
										style="#{pc_Xrc00503.propBuppinKbnName.style}"></h:outputText></TD>
									<TH width="150"><h:outputText 
										styleClass="outputText" id="text4" 
										value="廃止フラグ"
										style="#{pc_Xrc00503.propHaisiFlgName.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblTaxFlgName" 
										value="#{pc_Xrc00503.propHaisiFlgName.stringValue}"
										style="#{pc_Xrc00503.propHaisiFlgName.style}"></h:outputText></TD>
									<TH width="150" ><h:outputText
										styleClass="outputText" id="text5" 
										value="配本版"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblHaihonEd"
										value="#{pc_Xrc00503.propHaihonEd.stringValue}"
										style="#{pc_Xrc00503.propHaihonEd.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD></TD><TD></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" nowrap align="right"><h:outputText
							styleClass="outputText" id="lblCountEd" 
							value="#{pc_Xrc00503.propBpnEd.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text6" value="件"></h:outputText></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" align="center">
                    	<div class="listScroll" style="height:80px;"
							id="listScroll" onscroll="setScrollPosition('scrollEd',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrc00503.propBpnEd.rowClasses}"
							styleClass="meisai_scroll" id="htmlBpnEd"
							value="#{pc_Xrc00503.propBpnEd.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text7" styleClass="outputText" value="版"></h:outputText>
								</f:facet>
								<f:attribute value="35" name="width" />
								<h:outputText styleClass="outputText" id="text8"
									value="#{varlist.edition}"></h:outputText>
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column2">								
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="書名" id="text9"></h:outputText>
								</f:facet>
								<f:attribute value="270" name="width" />
								<h:outputText styleClass="outputText" id="text10"
									value="#{varlist.syosekiName.displayValue}"
									title="#{varlist.syosekiName.value}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="著者名" id="text11"></h:outputText>
								</f:facet>
								<f:attribute value="270" name="width" />
								<h:outputText styleClass="outputText" id="text12"
									value="#{varlist.chosyaName.displayValue}"
									title="#{varlist.chosyaName.value}"></h:outputText>
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="出版社名" id="text13"></h:outputText>
								</f:facet>
								<f:attribute value="265" name="width" />
								<h:outputText styleClass="outputText" id="text14"
									value="#{varlist.syupansyaName.displayValue}"
									title="#{varlist.syupansyaName.value}"></h:outputText>
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="35" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="selectEd"
									action="#{pc_Xrc00503.doSelectEdAction}"></hx:commandExButton>
								<f:attribute value="center" name="align" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
						</h:dataTable>
						<div>
						</TD>
					</TR>
					<TR>
						<TD></TD><TD></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" nowrap align="right"><h:outputText
							styleClass="outputText" id="lblCountImp" 
							value="#{pc_Xrc00503.propBpntRrk.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text23" value="件"></h:outputText></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" align="center">
                    	<div class="listScroll" style="height:80px;"
							id="listScroll" onscroll="setScrollPosition('scrollImp',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrc00503.propBpntRrk.rowClasses}"
							styleClass="meisai_scroll" id="htmlBpntRrk"
							value="#{pc_Xrc00503.propBpntRrk.list}" var="varlist">
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText id="text15" styleClass="outputText" value="刷"></h:outputText>
								</f:facet>
								<f:attribute value="35" name="width" />
								<h:outputText styleClass="outputText" id="text16"
									value="#{varlist.impression}"></h:outputText>
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column7">								
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="発注日" id="text17"></h:outputText>
								</f:facet>
								<f:attribute value="80" name="width" />
								<h:outputText styleClass="outputText" id="text18"
									value="#{varlist.hakkoDate}"></h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="入出庫先種別＿発注" id="text19"></h:outputText>
								</f:facet>
								<f:attribute value="650" name="width" />
								<h:outputText styleClass="outputText" id="text20"
									value="#{varlist.nsksakiSbtCdOrder}"></h:outputText>
							</h:column>
							<h:column id="column9">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="完了フラグ" id="text21"></h:outputText>
								</f:facet>
								<f:attribute value="80" name="width" />
								<h:outputText styleClass="outputText" id="text22"
									value="#{varlist.kanryoFlgName}"></h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column10">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="40" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="selectImp"
									action="#{pc_Xrc00503.doSelectImpAction}"></hx:commandExButton>
								<f:attribute value="center" name="align" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
						</h:dataTable>
						<div>
						</TD>
					</TR>
					<TR>
						<TD><BR></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE class="table" width="896">
							<TBODY>
								<TR>
									<TH width="165"><h:outputText
										styleClass="outputText" id="lblEdition"
										style="#{pc_Xrc00503.propEdition.labelStyle}"
										value="#{pc_Xrc00503.propEdition.labelName}"></h:outputText></TH>
                              		<TD width="100"><h:outputText styleClass="outputText"
										id="htmlEdition" 
										value="#{pc_Xrc00503.propEdition.integerValue}"
										style="#{pc_Xrc00503.propEdition.style}"></h:outputText></TD>
									<TH width="100"><h:outputText
										styleClass="outputText" id="lblSyosekiName"
										style="#{pc_Xrc00503.propSyosekiName.labelStyle}"
										value="#{pc_Xrc00503.propSyosekiName.labelName}"></h:outputText></TH>
                              		<TD width="530"><h:outputText styleClass="outputText"
										id="htmlSyosekiName" 
										value="#{pc_Xrc00503.propSyosekiName.stringValue}"
										style="#{pc_Xrc00503.propSyosekiName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblImpression"
										value="#{pc_Xrc00503.propImpression.labelName}"
										style="#{pc_Xrc00503.propImpression.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlImpression"
										value="#{pc_Xrc00503.propImpression.integerValue}"
										style="#{pc_Xrc00503.propImpression.style}"
										size="1" 
										disabled="#{pc_Xrc00503.propImpression.disabled}">
										<f:convertNumber type="number" pattern="#0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
									<TH><h:outputText 
									    styleClass="outputText" id="lblHakkoDate"
										value="#{pc_Xrc00503.propHakkoDate.labelName}"
										style="#{pc_Xrc00503.propHakkoDate.labelStyle}"></h:outputText>
									 </TH>
									<TD><h:inputText id="htmlHakkoDate"
										styleClass="inputText" size="13"
										value="#{pc_Xrc00503.propHakkoDate.dateValue}"
										disabled="#{pc_Xrc00503.propHakkoDate.disabled}">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" /></h:inputText>
									</TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblNsksakiSbtCdOrder"
										value="#{pc_Xrc00503.propNsksakiSbtCdOrder.labelName}"
										style="#{pc_Xrc00503.propNsksakiSbtCdOrder.labelStyle}"></h:outputText>
									</TH>
									<TD colspan="4"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNsksakiSbtCdOrder" 
										value="#{pc_Xrc00503.propNsksakiSbtCdOrder.stringValue}"
										style="#{pc_Xrc00503.propNsksakiSbtCdOrder.style};width:400px"
										disabled="#{pc_Xrc00503.propNsksakiSbtCdOrder.disabled}">
										<f:selectItems value="#{pc_Xrc00503.propNsksakiSbtCdOrder.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblSatusuOrder"
										value="#{pc_Xrc00503.propSatusuOrder.labelName}"
										style="#{pc_Xrc00503.propSatusuOrder.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlSatusuOrder"
										value="#{pc_Xrc00503.propSatusuOrder.integerValue}"
										style="#{pc_Xrc00503.propSatusuOrder.style}"
										size="4" 
										disabled="#{pc_Xrc00503.propSatusuOrder.disabled}">
										<f:convertNumber type="number" pattern="######0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
									<TH><h:outputText
										styleClass="outputText" id="lblKingakuOrder"
										value="#{pc_Xrc00503.propKingakuOrder.labelName}"
										style="#{pc_Xrc00503.propKingakuOrder.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlKingakuOrder"
										value="#{pc_Xrc00503.propKingakuOrder.integerValue}"
										style="#{pc_Xrc00503.propKingakuOrder.style}"
										size="6" 
										disabled="#{pc_Xrc00503.propKingakuOrder.disabled}">
										<f:convertNumber type="number" pattern="########0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblSatusuDeliv"
										value="#{pc_Xrc00503.propSatusuDeliv.labelName}"
										style="#{pc_Xrc00503.propSatusuDeliv.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlSatusuDeliv"
										value="#{pc_Xrc00503.propSatusuDeliv.integerValue}"
										style="#{pc_Xrc00503.propSatusuDeliv.style}"
										size="4" 
										disabled="#{pc_Xrc00503.propSatusuDeliv.disabled}">
										<f:convertNumber type="number" pattern="######0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
									<TH><h:outputText
										styleClass="outputText" id="lblKanryoFlg"
										value="#{pc_Xrc00503.propKanryoFlg.labelName}"
										style="#{pc_Xrc00503.propKanryoFlg.labelName}"></h:outputText></TH>
									<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="htmlKanryoFlg"
										value="#{pc_Xrc00503.propKanryoFlg.checked}"
										style="#{pc_Xrc00503.propKanryoFlg.style}"
										disabled="#{pc_Xrc00503.propKanryoFlg.disabled}">
										</h:selectBooleanCheckbox></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblMemo"
										value="#{pc_Xrc00503.propMemo.labelName}"
										style="#{pc_Xrc00503.propMemo.labelStyle}"></h:outputText><hx:graphicImageEx
										styleClass="graphicImageEx" id="imageEx1" 
										url="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
									<TD colspan="4"><h:inputTextarea styleClass="inputTextarea"
										id="htmlMemo" 
										value="#{pc_Xrc00503.propMemo.stringValue}"
										style="#{pc_Xrc00503.propMemo.style}" cols="90" rows="3"
										disabled="#{pc_Xrc00503.propMemo.disabled}"></h:inputTextarea></TD>
								</TR>							
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<CENTER><TABLE class="button_bar" width="750">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="明細登録"
							styleClass="commandExButton_dat" id="exec"
							action="#{pc_Xrc00503.doExecAction}"
							disabled="#{pc_Xrc00503.propExecButton.disabled}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE></CENTER>
			<HR class="hr" noshade>
			<TABLE width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Xrc00503.doRegisterAction}"
							disabled="#{pc_Xrc00503.propRegisterButton.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Xrc00503.doDeleteAction}"
							disabled="#{pc_Xrc00503.propDeleteButton.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrc00503.doClearAction}"
							disabled="#{pc_Xrc00503.propClearButton.disabled}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrc00503.propBpnEd.scrollPosition}" id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrc00503.propConfirm.stringValue}" id="htmlConfirm"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrc00503.propAction.stringValue}" id="htmlAction"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition("scrollEd","listScroll")
changeScrollPosition("scrollImp","listScroll")
</SCRIPT>
</HTML>

