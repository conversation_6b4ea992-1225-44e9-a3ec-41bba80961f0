<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
function openPCos0401() {
	setTarget("xxxx");
	openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
	return true;
}

function confirmOk() {

	//重複起動ワーニング確認ＯＫ
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('exec');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00201.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrb00201.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00201.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00201.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 --><TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE width="600" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="160"><h:outputText
										styleClass="outputText" id="lblTorokDate"
										value="#{pc_Xrb00201.propTorokDate.labelName}" style="#{pc_Xrb00201.propTorokDate.labelStyle}"></h:outputText></TH>
									<TD align="left" valign="middle" width="440">
										<h:inputText styleClass="inputText"
										id="htmlTorokDate" size="10"
										value="#{pc_Xrb00201.propTorokDate.dateValue}" 
										style="#{pc_Xrb00201.propTorokDate.style}" >
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_c" width="160"><h:outputText
										styleClass="outputText" id="lblRsyuChk"
										value="#{pc_Xrb00201.propRsyuChk.labelName}" style="#{pc_Xrb00201.propRsyuChk.labelStyle}"></h:outputText></TH>
									<TD width="440"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRsyuChk"
										value="#{pc_Xrb00201.propRsyuChk.stringValue}"
										style="#{pc_Xrb00201.propRsyuChk.style}"
										layout="lineDirection">
										<f:selectItem itemValue='0' itemLabel="行う" />
										<f:selectItem itemValue='1' itemLabel="行わない" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center"><BR>
						</TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE width="600" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_d" width="160"><h:outputText
										styleClass="outputText" id="lblInputFile"
										value="#{pc_Xrb00201.propInputFile.labelName}" style="#{pc_Xrb00201.propInputFile.labelStyle}"></h:outputText><BR>
									<h:outputText styleClass="outputText" id="lblInputFileOld"
										value="#{pc_Xrb00201.propInputFileOld.labelName}" style="#{pc_Xrb00201.propInputFileOld.labelStyle}"></h:outputText>
									</TH>
									<TD width="440"><hx:fileupload styleClass="fileupload"
										id="htmlInputFile" value="#{pc_Xrb00201.propInputFile.value}"
										style="#{pc_Xrb00201.propInputFile.style};width:425px">
										<hx:fileProp name="fileName"
											value="#{pc_Xrb00201.propInputFile.fileName}" />
										<hx:fileProp name="contentType" />
									</hx:fileupload><BR><h:outputText styleClass="outputText" id="htmlInputFileOld"
										value="#{pc_Xrb00201.propInputFileOld.stringValue}" style="#{pc_Xrb00201.propInputFileOld.style}"></h:outputText>
									<BR>
									</TD>
								</TR>
								<TR>
									<TH width="160" class="v_e"><h:outputText
										styleClass="outputText" id="lblRegKbn"
										value="#{pc_Xrb00201.propRegKbn.labelName}"
										style="#{pc_Xrb00201.propRegKbn.labelStyle}"></h:outputText></TH>
									<TD width="440"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRegKbn"
										layout="pageDirection"
										value="#{pc_Xrb00201.propRegKbn.stringValue}"
										style="#{pc_Xrb00201.propRegKbn.style}">
										<f:selectItem itemValue="1"
											itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
										<f:selectItem itemValue="2"
											itemLabel="データを登録または更新します。同一データが存在すれば上書き更新します。" />
										<f:selectItem itemValue="3"
											itemLabel="データを更新します。入力ファイルに指定された項目のみ更新します。" />
										<f:selectItem itemValue="4" itemLabel="入力ファイルに指定されたデータを削除します。" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH width="160" class="v_f"><h:outputText
										styleClass="outputText" id="lblSetProcess"
										value="#{pc_Xrb00201.propSetProcess.labelName}" style="#{pc_Xrb00201.propSetProcess.labelStyle}"></h:outputText></TH>
									<TD width="440" style="padding-left: 2px"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlSetProcess"
										value="#{pc_Xrb00201.propSetProcess.checked}"
										style="#{pc_Xrb00201.propSetProcess.style}"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="text12"
										value="チェックのみ（データの登録/更新は行いません）"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g" width="160"><h:outputText
										styleClass="outputText" id="lblSetCheckListOut"
										value="#{pc_Xrb00201.propSetCheckListOut.labelName}" style="#{pc_Xrb00201.propSetCheckListOut.labelStyle}"></h:outputText></TH>
									<TD width="440"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="ltmlSetCheckListOut"
										layout="pageDirection"
										value="#{pc_Xrb00201.propSetCheckListOut.stringValue}" style="#{pc_Xrb00201.propSetCheckListOut.style}">
										<f:selectItem itemValue="0" itemLabel="正常データ" />
										<f:selectItem itemValue="1" itemLabel="エラーデータ" />
										<f:selectItem itemValue="2" itemLabel="ワーニングデータ　　※ 履修エラー　" />
									</h:selectManyCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center" height="40">
						<HR class="hr" noshade>
						</TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE width="600" border="0" cellpadding="0" cellspacing="0"
							class="button_bar">
							<TBODY>
								<TR charoff="0">
									<TD><hx:commandExButton type="submit" value="入力項目指定"
										styleClass="commandExButton_out" id="setinput"
										onclick="return openPCos0401();"
										action="#{pc_Xrb00201.doSetinputAction}"></hx:commandExButton><hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_dat" id="exec" action="#{pc_Xrb00201.doExecAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrb00201.propExecutableSearch.integerValue}"
			               id="htmlExecutableSearch"><f:convertNumber />
			</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

