<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssi00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssi00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	


</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssi00301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssi00301.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssi00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssi00301.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 --><BR>

<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
<TBODY>
  <TR>
    <TD width="30%"></TD>
    <TD>

		<TABLE border="0" cellpadding="0" cellspacing="0" width="400" class="table">
			<TBODY>
				<TR>
					<TH width="30%" class="v_a"><h:outputText styleClass="outputText" id="text1" value="求人対象年度"></h:outputText></TH>
						<TD width="30%"><h:inputText styleClass="inputText"
								id="htmlNyushiNendo"
								value="#{pc_Ssi00301.propKjnNendo.dateValue}"
								tabindex="1" size="4"
								style="#{pc_Ssi00301.propKjnNendo.style}">
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" imeMode="inactive" />
								<f:convertDateTime pattern="yyyy" />
							</h:inputText>
						</TD>
				</TR>
				<TR>
					<TH class="v_a" width="30%"><h:outputText
						styleClass="outputText" id="text15"
						value="#{pc_Ssi00301.propDateProsess.labelName}"
						style="#{pc_Ssi00301.propDateProsess.labelStyle}"></h:outputText></TH>
					<TD width="100"><h:inputText styleClass="inputText"
						id="htmlDateProcess" size="12"
						value="#{pc_Ssi00301.propDateProsess.dateValue}"
						style="#{pc_Ssi00301.propDateProsess.style}">
						<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" />
						<f:convertDateTime dateStyle="medium"/>
						<hx:inputHelperDatePicker />
					</h:inputText></TD>
				</TR>
			</TBODY>
		</TABLE>

		<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar" style="margin-top: 8px;">
			<TBODY>
				<TR>
					<TD><hx:commandExButton type="submit" value="実行"
						styleClass="commandExButton_dat" id="exec"
						confirm="#{msg.SY_MSG_0001W}" action="#{pc_Ssi00301.doExecAction}"></hx:commandExButton></TD>
				</TR>
			</TBODY>
		</TABLE>

	</TD>
    <TD width="30%"></TD>
  </TR>
</TBODY>
</TABLE>


<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />

		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

