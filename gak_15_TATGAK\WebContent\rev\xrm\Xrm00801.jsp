
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00801.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

<TITLE>Xrm00801.jsp</TITLE>
<SCRIPT type="text/javascript">
	function fncInit() {
		changedRadio();
		
    	//取込ボタンの制御 htmlHidErrorKbn
    	if(document.getElementById('form1:htmlHidErrorKbn').value == '1'){
    		document.getElementById('form1:pdfout').disabled = true;
    	}
    
	}
	
	function changedRadio(){
		var radioList = document.getElementsByName("form1:htmlIktOutTaisyou");
		var str = "";
		for(var i=0; i<radioList.length; i++){
			if (radioList[i].checked) {
				str = radioList[i].value;
				if(str == "1"){//日報の場合
					document.getElementById("form1:htmlFuriDateStart").disabled = false;
					document.getElementById("form1:htmlFuriDateEnd").disabled = false;
					document.getElementById("form1:htmlGhYear").disabled = true;
					document.getElementById("form1:propgatuki").disabled = true;
				}else if(str == "2"){//収納データリストの場合
					document.getElementById("form1:htmlFuriDateStart").disabled = false;
					document.getElementById("form1:htmlFuriDateEnd").disabled = false;
					document.getElementById("form1:htmlGhYear").disabled = true;
					document.getElementById("form1:propgatuki").disabled = true;
				}else if(str == "3"){//月報の場合
					document.getElementById("form1:htmlFuriDateStart").disabled = true;
					document.getElementById("form1:htmlFuriDateEnd").disabled = true;
					document.getElementById("form1:htmlGhYear").disabled = false;
					document.getElementById("form1:propgatuki").disabled = false;
				}
				break;
			}
		}
	}
		

	function func_check_on(thisObj, thisEvent) {
		check('htmlPayList','htmlSelectedList');
	}
	
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPayList','htmlSelectedList');
	}
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('search');
	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}
	
	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY onload="fncInit();">

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm00801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm00801.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrm00801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm00801.screenName}"></h:outputText></div>

			<!--↓OUTER↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"></DIV>

			<!--↓CONTENT↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="900px">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" width="80%" cellpadding="0" class="table"
							cellspacing="0">
							<TBODY>
								<!-- 統計種別 -->
								<TR>
									<TH nowrap class="v_a" width="150px"><h:outputText
										styleClass="outputText" id="lblIktOutTaisyou"
										value="#{pc_Xrm00801.propOutTaisyou.labelName}"
										style="#{pc_Xrm00801.propOutTaisyou.labelStyle}">
									</h:outputText></TH>
									<TD width="*" colspan=3>
										<h:selectOneRadio
											onclick="changedRadio();"
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlIktOutTaisyou"
											value="#{pc_Xrm00801.propOutTaisyou.value}"
											style="#{pc_Xrm00801.propOutTaisyou.style}" tabindex="25">
											<f:selectItem itemValue="1" itemLabel="日報" />
											<f:selectItem itemValue="2" itemLabel="収納データリスト" />
											<f:selectItem itemValue="3" itemLabel="月報" />
										</h:selectOneRadio>
									</TD>
								</TR>

								<%-- 預入日付 --%>
								<TR>
									<TH width="150px" class="v_a"><h:outputText
										styleClass="outputText" id="lblDate"
										value="#{pc_Xrm00801.propFuriDate.name}">
									</h:outputText></TH>
									<TD  width="*" colspan=3><h:inputText
													styleClass="inputText" id="htmlFuriDateStart"
													value="#{pc_Xrm00801.propFuriDateStart.dateValue}"
													style="#{pc_Xrm00801.propFuriDateStart.style}"
													tabindex="14" size="11">
													<f:convertDateTime />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText>
												<h:outputText
													styleClass="outputText" id="textFuri" value="～">
												</h:outputText>
												<h:inputText
													styleClass="inputText" id="htmlFuriDateEnd"
													value="#{pc_Xrm00801.propFuriDateEnd.dateValue}"
													style="#{pc_Xrm00801.propFuriDateEnd.style}" tabindex="15"
													size="11">
													<f:convertDateTime />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
												</h:inputText></TD>
											</TR>



											<!-- 学費年度 -->
											<TR>
												<TH nowrap class="v_a" width="150px"><h:outputText
													styleClass="outputText" id="lblGhYear"
													value="#{pc_Xrm00801.propGhYear.labelName}"
													style="#{pc_Xrm00801.propGhYear.labelStyle}">
												</h:outputText></TH>
												<TD width="*" colspan=0><h:inputText styleClass="inputText"
													id="htmlGhYear" size="4"
													value="#{pc_Xrm00801.propGhYear.value}"
													style="#{pc_Xrm00801.propGhYear.style}"
													disabled="#{pc_Xrm00801.propGhYear.disabled}" tabindex="1">
													<hx:inputHelperAssist imeMode="inactive"
														errorClass="inputText_Error" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
												</h:inputText></TD>
												<%--　月期  --%>
												<TH nowrap class="v_a" width="150px"><h:outputText
													styleClass="outputText" id="propgatukiCD"
													value="#{pc_Xrm00801.propgatukiCD.name}"
													style="#{pc_Xrm00801.propgatukiCD.labelStyle}">
												</h:outputText></TH>
												<TD><h:selectOneMenu styleClass="selectOneMenu"
													id="propgatuki" style="width:150px;"
													value="#{pc_Xrm00801.propgatukiList.value}" tabindex="7">
													<f:selectItems value="#{pc_Xrm00801.propgatukiList.list}" />
												</h:selectOneMenu></TD>
											</TR>
										</TBODY>
									</TABLE>
								<TR>

									<TD height="20px"></TD>

								</TR>

								<TR>
									<TD>
									<TABLE class="button_bar" width="100%">
										<TBODY>
											<TR>
												<TD width="100%"><hx:commandExButton type="submit"
													value="PDF作成" styleClass="commandExButton_out" id="pdfout"
													confirm="#{msg.SY_MSG_0019W}"
													action="#{pc_Xrm00801.doPdfoutAction}" tabindex="12">
												</hx:commandExButton>&nbsp;</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Xrm00801.propErrorKbn.stringValue}"
				id="htmlHidErrorKbn"></h:inputHidden>


		</h:form>
	</hx:scriptCollector>
	</BODY>


	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
