<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

	// バーコード入力：KeyDown処理
	function fncKeyEvt() {
		if (event.keyCode == 13) {
			var btnObj = document.getElementById('form1:readBarcode');
			indirectClick('barcodeRegister');
		}
	}	


	function func_1(thisObj, thisEvent, fieldName) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=" + fieldName + "&kyoShokuin=3";
		 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	}
	
	function getJinjiName(thisObj, thisEvent, target ) {
		// 教員名称を取得する
  		var servlet = "rev/co/CobJinjAJAX";
  		//var target = "form1:lblSyokuinName";
  		getCodeName(servlet, target, thisObj.value);
 	}
 	
	 function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}

 
	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
	// 科目名称を取得する
		var servlet = "rev/km/KmzKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	function func_2(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

		// 時間割リスト一括チェック

		check("htmlKadaiTantouList","htmlListCheckBox");
	}
	
	function func_3(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

		// 時間割リスト一括チェック解除

		uncheck("htmlKadaiTantouList","htmlListCheckBox");
	}
	
	//@@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 End 
	// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}

	// 学生氏名を取得する
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

 	function confirmOk(){
 		if(document.getElementById("form1:propExecutable").value == 1){
 			indirectClick("register");
 		}
 	}
 	
 	function confirmCancel(){
 	
 		document.getElementById("form1:propExecutable").value = 0;
 		
 	}
 	
	function onClickReturnDisp(id){
		return true;
	}

	function loadAction(event){
 		doGakuseiAjax(document.getElementById('form1:htmlGakusekiNo'), event, 'form1:lblName');
 		doKamokuAjax(document.getElementById('form1:htmlKamokuCd'), event, 'form1:lblKamokuName');
	}


    function isRegNum(obj,id){

		
	    var second=obj.value; /* 入力値 */

		var first =document.getElementById('form1:htmlHyouka1Cd').value;/* １回目の評価 */

		var hyoukaKaisuu =document.getElementById('form1:htmlHyoukaKaisu').value;/* 評価回数 */

		var msg = new String();
		msg += first;
		msg += '] ２回目：['
		msg +=obj.value;
		if(first != ''){
			if(hyoukaKaisuu == '2'){
				if(second != '|no select|'){
			
		        	if(first == second){ 
		        				document.getElementById('form1:register').disabled = false;
		                  		document.getElementById('form1:htmlBarcode').focus();
		          		return true;
		
		        	}else{
		        	
		        		document.getElementById('form1:register').disabled = true;
		         		return doPopupMsg(id,msg);

		         		
		        	}
		        }
		        document.getElementById('form1:register').disabled = true;
	        }else{
	        
	            document.getElementById('form1:register').disabled = false;
        		document.getElementById('form1:htmlBarcode').focus();
 
        	return true;
        	
        	}
        }
        return false;
    }

	// ポップアップメッセージを表示
	function doPopupMsg(id, msg) {
		var args = new Array();
		args[0] = msg;
		return confirm(messageCreate(id, args));
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<f:loadBundle basename="properties.messageXRF_ja" var="msgXRF"/>
	<BODY OnLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00502.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00502.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00502.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00502.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<hx:commandExButton type="submit"
				value="戻　る" styleClass="commandExButton" id="returnDisp"
				tabindex="13" action="#{pc_Xrf00502.doReturnDispAction}"
				onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
				</hx:commandExButton>
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="600" >
					<TBODY>
						<TR>
							<TH class="v_a" width="110">
								<!--バーコード -->
								<h:outputText styleClass="outputText" id="lblBarcode"
								value="#{pc_Xrf00502.propBarcode.labelName}"
								style="#{pc_Xrf00502.propBarcode.labelStyle}">
								</h:outputText>
							</TH>
							<TD>
								<h:inputText styleClass="inputText"
									id="htmlBarcode" size="22" tabindex="1"
									onkeydown="fncKeyEvt();" style="#{pc_Xrf00502.propBarcode.style}"
									maxlength="#{pc_Xrf00502.propBarcode.maxLength}"
									disabled="#{pc_Xrf00502.propBarcode.disabled}"
									value="#{pc_Xrf00502.propBarcode.stringValue}">
									<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" imeMode="disabled"/>
								</h:inputText>
							</TD>
							<TD rowspan="4" width="50"
								style="background-color: transparent; text-align: center"
								class="clear_border">
								<hx:commandExButton type="submit" value="読取"
									tabindex="7"
									disabled="#{pc_Xrf00502.propRead.disabled}"
									styleClass="cmdBtn_dat_s" id="readBarcode"
									action="#{pc_Xrf00502.doReadBarcodeAction}">
								</hx:commandExButton>
								<hx:commandExButton type="submit" value="バーコード登録"
									styleClass="commandExButton" id="barcodeRegister"
									style="display:none;"
									action="#{pc_Xrf00502.doBarcodeRegisterAction}">
								</hx:commandExButton>
							</TD>
						</TR>
						<TH nowrap class="v_a" width="110">
							<!--学籍番号 -->
							<h:outputText styleClass="outputText" id="lblGaksekiNo"
								value="#{pc_Xrf00502.propGakusekiNo.labelName}"
								style="#{pc_Xrf00502.propGakusekiNo.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText styleClass="inputText"
								id="htmlGakusekiNo" size="10"
									tabindex="2"
									value="#{pc_Xrf00502.propGakusekiNo.stringValue}"
									readonly="#{pc_Xrf00502.propGakusekiNo.readonly}"
									disabled="#{pc_Xrf00502.propGakusekiNo.disabled}"
									style="#{pc_Xrf00502.propGakusekiNo.style}"
									maxlength="#{pc_Xrf00502.propGakusekiNo.maxLength}"
									onblur="return doGakuseiAjax(this, event, 'form1:lblName');"
									>
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="disabled"/>
								</h:inputText>
								<hx:commandExButton type="button"
									tabindex="3"
									disabled="#{pc_Xrf00502.propSearchGakusekiNo.disabled}"
									styleClass="commandExButton_search" id="searchGakuseiNo"
									onclick="openSubWindow('form1:htmlGakusekiNo');"
								>
								</hx:commandExButton>
								<h:outputText styleClass="outputText"
									id="lblName"
									value="#{pc_Xrf00502.propName.stringValue}">
								</h:outputText>
							</TD>
						</TR>
						<TR> 
							<TH class="v_a" width="110">
								<!--科目コード -->
								<h:outputText styleClass="outputText"
									id="lblKamokuCd"
									value="#{pc_Xrf00502.propKamokuCd.labelName}" 
									style="#{pc_Xrf00502.propKamokuCd.labelStyle}">
								</h:outputText>
							</TH>
							<TD>
								<h:inputText id="htmlKamokuCd" styleClass="inputText" 
									size="10"
									tabindex="4"
									onblur="return doKamokuAjax(this, event, 'form1:lblKamokuName');"
									value="#{pc_Xrf00502.propKamokuCd.stringValue}"
									readonly="#{pc_Xrf00502.propKamokuCd.readonly}"
									maxlength="#{pc_Xrf00502.propKamokuCd.maxLength}"
									disabled="#{pc_Xrf00502.propKamokuCd.disabled}"
									style="#{pc_Xrf00502.propKamokuCd.style}">
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="disabled"/>
								</h:inputText>
								<hx:commandExButton type="button"
									tabindex="5"
									disabled="#{pc_Xrf00502.propSearchKamoku.disabled}"
									styleClass="commandExButton_search" id="searchKamoku"
									onclick="return openKamokuSearchWindow(this, event);">
								</hx:commandExButton>
								<h:outputText
									styleClass="outputText" id="lblKamokuName"
									value="#{pc_Xrf00502.propKamokuName.stringValue}">
								</h:outputText>
							</TD>
						</TR>
						<TR>
							<TH nowrap class="v_a" width="110">
								<!--分冊 -->
								<h:outputText styleClass="outputText" id="lblBunsatu"
									value="#{pc_Xrf00502.propBunsatu.labelName}"
									style="#{pc_Xrf00502.propBunsatu.labelStyle}">
								</h:outputText>
							</TH>
							<TD>
								<h:inputText styleClass="inputText"
									id="htmlBunsatu" size="2"
									tabindex="6"
									value="#{pc_Xrf00502.propBunsatu.stringValue}"
									readonly="#{pc_Xrf00502.propBunsatu.readonly}"
									disabled="#{pc_Xrf00502.propBunsatu.disabled}"
									maxlength="#{pc_Xrf00502.propBunsatu.maxLength}"
									style="#{pc_Xrf00502.propBunsatu.style}">
									<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="disabled"/>
								</h:inputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<BR>
				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="600" >
					<TBODY>
						<TR>
							<TH nowrap class="v_a" width="110">
									<!--評価回数 -->
								<h:outputText styleClass="outputText" id="lblHyoukaKaisu"
									value="#{pc_Xrf00502.propHyoukaKaisu.labelName}"
									style="#{pc_Xrf00502.propHyoukaKaisu.labelStyle}">
								</h:outputText>
							</TH>
							<TD colspan="3">
								<h:selectOneMenu styleClass="selectOneMenu" id="htmlHyoukaKaisu"
									tabindex="8"
									value="#{pc_Xrf00502.propHyoukaKaisu.stringValue}"
									style="#{pc_Xrf00502.propHyoukaKaisu.style};width:128px"
									disabled="#{pc_Xrf00502.propHyoukaKaisu.disabled}">
									<f:selectItems value="#{pc_Xrf00502.propHyoukaKaisu.list}" />
								</h:selectOneMenu>
							</TD>     
						</TR>
						<TR>
							<TH nowrap class="v_a" width="110">
								<!--評価 -->
								<h:outputText styleClass="outputText" id="lblHyouka"
								value="#{pc_Xrf00502.propHyouka.labelName}"
								style="#{pc_Xrf00502.propHyouka.labelStyle}">
								</h:outputText>
							</TH>
							<TD width="128">
								<h:selectOneMenu styleClass="selectOneMenu" id="htmlHyouka"
									tabindex="9"
									value="#{pc_Xrf00502.propHyouka.stringValue}"
									style="#{pc_Xrf00502.propHyouka.style};width:128px"
									disabled="#{pc_Xrf00502.propHyouka.disabled}"
									onchange="isRegNum(this,'#{msgXRF.XRF_MSG_0010E}')">
									<f:selectItems value="#{pc_Xrf00502.propHyouka.list}" />
								</h:selectOneMenu>
							</TD>
							<TH nowrap class="v_a" width="100">
								<!--1回目評価 -->
								<h:outputText styleClass="outputText" id="lbl1Hyouka"
									value="#{pc_Xrf00502.prop1Hyouka.labelName}"
									style="#{pc_Xrf00502.prop1Hyouka.labelStyle}">
								</h:outputText>
							</TH>
							<TD>
								<h:outputText
									styleClass="outputText"
									id="html1Hyouka"
									style="width:300px"
									value="#{pc_Xrf00502.prop1Hyouka.stringValue}"
									>
								</h:outputText>
							</TD>
						</TR>			            	
						<TR>
							<TH class="v_a" width="110">
								<h:outputText styleClass="outputText" id="lblNyuryokubi"
									value="#{pc_Xrf00502.propNyuryokubi.labelName}"
									style="#{pc_Xrf00502.propNyuryokubi.labelStyle}">
								</h:outputText>
							</TH>
							<TD width="200">
								<h:inputText id="htmlNyuryokubi"
									styleClass="inputText"
									style="width:128"
									tabindex="10"
									value="#{pc_Xrf00502.propNyuryokubi.dateValue}">
									<f:convertDateTime />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
									<hx:inputHelperDatePicker />
								</h:inputText>
							</TD>
							<TH nowrap class="v_a" width="120">
								<!--1回目評価入力日 -->
								<h:outputText styleClass="outputText" id="lbl1Nyuryokubi"
									value="#{pc_Xrf00502.prop1Nyuryokubi.labelName}"
									style="#{pc_Xrf00502.prop1Nyuryokubi.labelStyle}">
								</h:outputText>
							</TH>
							<TD>
								<h:outputText
									styleClass="outputText"
									id="html1Nyuryokubi"
									style="width:300px"
									value="#{pc_Xrf00502.prop1Nyuryokubi.dateValue}">
									<f:convertDateTime pattern="yyyy/MM/dd" />
								</h:outputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>

				<BR>

				<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
							<TD>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="register"
									tabindex="11"
									confirm="#{msg.SY_MSG_0002W}"
									disabled="#{pc_Xrf00502.propKakutei.disabled}"
									action="#{pc_Xrf00502.doRegisterAction}"
									value="確定">
								</hx:commandExButton>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="clear"
									tabindex="12"
									action="#{pc_Xrf00502.doClearAction}"
									value="クリア">
								</hx:commandExButton>
							</TD>	
						</TR>
					</TBODY>
				</TABLE>
				</DIV>
			</DIV>
			
		</DIV>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		<h:inputHidden
				value="#{pc_Xrf00502.propExecutable.integerValue}"
				id="propExecutable">
		</h:inputHidden>				
		<h:inputHidden
			id="htmlOldBarcode"
			value="#{pc_Xrf00502.propOldBarcode.stringValue}">
		</h:inputHidden>
		<h:inputHidden
			id="htmlBarcodeRegisterFlg"
			value="#{pc_Xrf00502.propBarcodeRegisterFlg.integerValue}">
		</h:inputHidden>
		<h:inputHidden
			id="htmlHyouka1Cd"
			value="#{pc_Xrf00502.propHyouka1Cd.stringValue}">
		</h:inputHidden>	
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
