
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
var katudoKbnName0 = document.getElementById('form1:htmlSyusyokuKatudoKbnName0');
var katudoKbnName1 = document.getElementById('form1:htmlSyusyokuKatudoKbnName1');
var katudoKbnName2 = document.getElementById('form1:htmlSyusyokuKatudoKbnName2');
var katudoKbnName3 = document.getElementById('form1:htmlSyusyokuKatudoKbnName3');
var katudoKbnName4 = document.getElementById('form1:htmlSyusyokuKatudoKbnName4');
var katudoKbnName9 = document.getElementById('form1:htmlSyusyokuKatudoKbnName9');

//就職活動区分０標準名称
katudoKbnName0.value = '就職活動前';
//就職活動区分１標準名称
katudoKbnName1.value = '進路希望申請済';
//就職活動区分２標準名称
katudoKbnName2.value = '就職活動中';
//就職活動区分３標準名称
katudoKbnName3.value = '内定受諾';
//職活動区分４標準名称
katudoKbnName4.value = '進路先決定';
//就職活動区分９標準名称
katudoKbnName9.value = '非就職';
indirectClick('register');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz01301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz01301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz01301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz01301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD width="64%">
						<TABLE class="table" width="100%">
							<TBODY>
								<TR>
									<TH width="50"><h:outputText styleClass="outputText" id="text1"
										value="区分"></h:outputText></TH>
									<TH width="158"><h:outputText styleClass="outputText"
										style="#{pc_Ssz01301.propSyusyokuKatudoKbnName0.labelStyle}"
										id="text2" value="活動区分名称(全10)"></h:outputText></TH>
									<TH width="400"><h:outputText styleClass="outputText"
										id="text3" value="活動区分を表す意味"></h:outputText></TH>
								</TR>
								<TR>
									<TH width="50" class="v_a"><h:outputText 
										styleClass="outputText" id="text16" value="０："></h:outputText></TH>
									<TD width="158"><h:inputText styleClass="inputText"
										id="htmlSyusyokuKatudoKbnName0" size="20"
										value="#{pc_Ssz01301.propSyusyokuKatudoKbnName0.stringValue}"
										style="#{pc_Ssz01301.propSyusyokuKatudoKbnName0.style}"
										maxlength="#{pc_Ssz01301.propSyusyokuKatudoKbnName0.maxLength}"></h:inputText></TD>
									<TD width="400"><h:outputText styleClass="outputText"
										id="text4" value="就職活動を何も行っていない状態を表す名称を設定します。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="50" class="v_b"><h:outputText 
										styleClass="outputText" id="text17" value="１："></h:outputText></TH>
									<TD width="158"><h:inputText styleClass="inputText"
										id="htmlSyusyokuKatudoKbnName1" size="20"
										value="#{pc_Ssz01301.propSyusyokuKatudoKbnName1.stringValue}"
										style="#{pc_Ssz01301.propSyusyokuKatudoKbnName1.style}"
										maxlength="#{pc_Ssz01301.propSyusyokuKatudoKbnName1.maxLength}"></h:inputText></TD>
									<TD width="400"><h:outputText styleClass="outputText"
										id="text5" value="学生が進路希望の登録を完了している状態を表す名称を設定します。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="50" class="v_c"><h:outputText
										styleClass="outputText" id="text18" value="２："></h:outputText></TH>
									<TD width="158"><h:inputText styleClass="inputText"
										id="htmlSyusyokuKatudoKbnName2" size="20"
										value="#{pc_Ssz01301.propSyusyokuKatudoKbnName2.stringValue}"
										style="#{pc_Ssz01301.propSyusyokuKatudoKbnName2.style}"
										maxlength="#{pc_Ssz01301.propSyusyokuKatudoKbnName2.maxLength}"></h:inputText></TD>
									<TD width="400"><h:outputText styleClass="outputText"
										id="text6" value="求人に対し応募を行った状態を表す名称を設定します。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="50" class="v_d"><h:outputText 
										styleClass="outputText" id="text19" value="３："></h:outputText></TH>
									<TD width="158"><h:inputText styleClass="inputText"
										id="htmlSyusyokuKatudoKbnName3" size="20"
										value="#{pc_Ssz01301.propSyusyokuKatudoKbnName3.stringValue}"
										style="#{pc_Ssz01301.propSyusyokuKatudoKbnName3.style}"
										maxlength="#{pc_Ssz01301.propSyusyokuKatudoKbnName3.maxLength}"></h:inputText></TD>
									<TD width="400"><h:outputText styleClass="outputText"
										id="text7" value="企業から内定をもらった状態を表す名称を設定します。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="50" class="v_e"><h:outputText 
										styleClass="outputText" id="text20" value="４："></h:outputText></TH>
									<TD width="158"><h:inputText styleClass="inputText"
										id="htmlSyusyokuKatudoKbnName4" size="20"
										value="#{pc_Ssz01301.propSyusyokuKatudoKbnName4.stringValue}"
										style="#{pc_Ssz01301.propSyusyokuKatudoKbnName4.style}"
										maxlength="#{pc_Ssz01301.propSyusyokuKatudoKbnName4.maxLength}"></h:inputText></TD>
									<TD width="400"><h:outputText styleClass="outputText"
										id="text8" value="進路が決定した状態を表す名称を設定します。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="50" class="v_f"><h:outputText 
										styleClass="outputText" id="text21" value="９："></h:outputText></TH>
									<TD width="158"><h:inputText styleClass="inputText"
										id="htmlSyusyokuKatudoKbnName9" size="20"
										value="#{pc_Ssz01301.propSyusyokuKatudoKbnName9.stringValue}"
										style="#{pc_Ssz01301.propSyusyokuKatudoKbnName9.style}"
										maxlength="#{pc_Ssz01301.propSyusyokuKatudoKbnName9.maxLength}"></h:inputText></TD>
									<TD width="400"><h:outputText styleClass="outputText"
										id="text9" value="就職を行わない状態を表す名称を設定します。"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="18%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0003W}"
							action="#{pc_Ssz01301.doRegisterAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="標準名称" styleClass="commandExButton_etc"
							id="edit" onclick="return func_1(this, event);"
							confirm="#{msg.SY_MSG_0005W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

