@echo off
REM TITLE=

call env.bat

set CLASSPATH=%CLASSPATH%;%GAK_WEBINF_ROOT%/classes;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/db2jcc.jar;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/db2jcc_license_cu.jar;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/db2jcc_license_cisuz.jar;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/dbunit-2.1.jar;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/log4j-1.2.9.jar;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/gakuen.jar;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/ojdbc14.jar;
set CLASSPATH=%CLASSPATH%%GAK_WEBINF_ROOT%/lib/poi-2.5.1-final-20040804.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/svf.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/svfformapi.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/svfgrph.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/svfpdfparser.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/svfpstat.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/BSPro.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/jai_codec.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/jai_core.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/jdom.jar;
set CLASSPATH=%CLASSPATH%%SVF_LIB%/xercesImpl.jar;
set CLASSPATH=%CLASSPATH%%IBM_LIB%/xml.jar;

echo Pdf File Create Start.
java  -Xrs -Xms128m -Xmx512m com.jast.gakuen.rev.xrh.batch.Xbh001  >  %LOG_ROOT% 2>&1

if errorlevel 1 (
	echo Pdf File Create Prossing Terminated Abnormally !!
	echo Please Read %LOG_ROOT% !!
	exit /b 1
)

echo Pdf File Create End.
exit /b 0
