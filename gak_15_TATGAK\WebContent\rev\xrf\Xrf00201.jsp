<%-- 
	レポート科目分冊担当教員登録
	
	<AUTHOR>
--%>



<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<SCRIPT type="text/javascript">

function numOnly(thisObj, thisKey) {
	var inCode;

// JSP の呼び出し							onkeydown="return numOnly( this, event.keyCode );"
//	inCode = String.fromCharCode( thisEvent.keyCode );
	inCode = thisKey;

	if("0123456789\b\r".indexOf( inCode, 0 ) < 0) {
		 return false;
	}

	return true;
}


function confirmOk() {
	var phase;
	phase = parseInt(document.getElementById('form1:htmlBackPhase').value);
	if (phase == 0) {
		document.getElementById('form1:htmlBackPhase').value = 1;
		//indirectClick('register');
	}
	
	indirectClick('register');
}

function confirmCancel() {
	//document.getElementById('form1:htmlBackPhase').value = 9;
	//indirectClick('register');
	document.getElementById('form1:htmlBackPhase').value = 0;
}

function openKamokuSubWindow(field1) {
// 科目検索画面
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;
	openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	return false;
}

function openKamokuSubWindow2(field1) {
// 科目検索画面
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;
	openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	return false;
}

function openKamokuSubWindow3(thisObj, thisEvent) {
// 教員検索画面
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlKyoinCd";
	openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return false;
}


//function doKamokuAjaxWork(thisObj, thisEvent, targetLabel) {
//// 科目名称を取得する
//	var servlet = "rev/km/KmzKmkAJAX";
//    var args = new Array();
//    args['code'] = thisObj.value;
//    
//    var ajaxUtil = new AjaxUtil();
//	ajaxUtil.getCodeName(servlet, targetLabel, args);
//}

function doKamokuAjax(thisObj, thisEvent, targetLabel, targetLabel2) {
// 科目名称,単位数を取得する
	var servlet = "rev/xrf/XrfKmkAJAX";
    var args = new Array();
    args['code'] = thisObj.value;

    args['func'] = "doKamokuAjax-1";
    
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);


	// 単位数
	if ( targetLabel2 != "" ) {
	    var args2 = new Array();
	    args2['code'] = thisObj.value;
	    args2['tanisu'] = "GET";
	    args2['addString'] = " 単位";

	    args['func'] = "doKamokuAjax-2";

		ajaxUtil.getCodeName(servlet, targetLabel2, args2);
	}
}


function doKamokuAjax2(thisObj, thisEvent, targetLabel) {
// 科目名称を取得する
	var servlet = "rev/xrf/XrfKmkAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    
    args['func'] = "doKamokuAjax2-1";

    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);

}

function doKyoinAjax(thisObj, thisEvent, target) {
//教員名を取得
  var servlet = "rev/co/CobJinjAJAX";
  getCodeName(servlet, target, thisObj.value);
  return false;
}

function func_kadai_click(thisObj, thisEvent) {
// 課題フラグチェックボックスの処理
	var selObj1 = document.getElementById('form1:htmlKadaiFlag');
	
	var cmp = hX.getComponentById('form1:htmlTegakiFlag');
	var cmp2 = hX.getComponentById('form1:htmlKadaiIraibi');
	
	if( !selObj1.checked ) {
		document.getElementById('form1:htmlTegakiFlag').disabled = true;
		document.getElementById('form1:htmlKadaiIraibi').disabled = true;
		//cmp.redraw();
		cmp2.redraw();
	} 
	else {
		document.getElementById('form1:htmlTegakiFlag').disabled = false;
		document.getElementById('form1:htmlKadaiIraibi').disabled = false;
		//cmp.redraw();
		cmp2.redraw();
	}	

	
	
}	



function loadAction(event) {
// 画面ロード時の学生名称・教員名称の再取得
	doKamokuAjax(document.getElementById('form1:htmlKamokCd'), event, 'form1:lblPreKamokName', 'form1:lblPreKamokTani');
	doKamokuAjax2(document.getElementById('form1:htmlKamokCd2'), event, 'form1:lblPreKamokName2');
	doKyoinAjax(document.getElementById('form1:htmlKyoinCd'), event, 'form1:lblPreKyoinName');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>
			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
				<TBODY>
					<TR>
						<TD width="718">
						</TD>
 						<TD  align="center"  valign="top" rowspan="2" colspan="4" width="119" >
							<hx:commandExButton type="submit" value="選択"
							styleClass="commandExButton" id="selectNendo"
							action="#{pc_Xrf00201.doSelectNendoAction}"
							disabled="#{pc_Xrf00201.propSelectNendo.disabled}" tabindex="2">
						</hx:commandExButton>
							<hx:commandExButton type="submit" value="解除" id="unSelectNendo"
							styleClass="commandExButton"
							action="#{pc_Xrf00201.doUnSelectNendoAction}"
							disabled="#{pc_Xrf00201.propUnSelectNendo.disabled}" tabindex="3">
						</hx:commandExButton></TD>

						<TR>
							<TD  valign="top" height="34" >

								<TABLE border="0" cellpadding="0" cellspacing="0"  class="table" height="30">
									<TBODY>
										<TR>
											<TH class="v_a" width="200">
												<h:outputText
													styleClass="outputText" id="lblNyugkNendo"
													value="#{pc_Xrf00201.propNyugkNendo.labelName}"
													style="#{pc_Xrf00201.propNyugkNendo.labelStyle}">
												</h:outputText>
											</TH>
											<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNyugkNendo"
										value="#{pc_Xrf00201.propNyugkNendo.dateValue}"
										style="#{pc_Xrf00201.propNyugkNendo.style}"
										disabled="#{pc_Xrf00201.propNyugkNendo.disabled}" size="4"
										tabindex="1">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText>
											</TD>
										</TR>
										<TR>
											<TH class="v_b" width="200">
												<h:outputText
													styleClass="outputText" id="lblKamokCd"
													style="#{pc_Xrf00201.propKamokCd.labelStyle}"
													value="#{pc_Xrf00201.propKamokCd.labelName}">
												</h:outputText>
											</TH>
											<TD width="150">
												<h:inputText styleClass="inputText" id="htmlKamokCd"
										value="#{pc_Xrf00201.propKamokCd.stringValue}"
										maxlength="#{pc_Xrf00201.propKamokCd.maxLength}" size="10"
										style="#{pc_Xrf00201.propKamokCd.style}"
										onblur="return doKamokuAjax(this, event, 'form1:lblPreKamokName', 'form1:lblPreKamokTani');"
										disabled="#{pc_Xrf00201.propKamokCd.disabled}" tabindex="4">
										<hx:inputHelperAssist imeMode="disabled"
											errorClass="inputText_Error" />
									</h:inputText>
												<hx:commandExButton type="button" value=""
										styleClass="commandExButton_search" id="search"
										onclick="return openKamokuSubWindow('form1:htmlKamokCd');"
										disabled="#{pc_Xrf00201.propSearchButton.disabled}"
										tabindex="5">
									</hx:commandExButton>
												<TD width="540">
													<h:outputText styleClass="outputText" id="lblPreKamokName">
													</h:outputText>
												</TD>
												<TD width="100">
													<h:outputText styleClass="outputText" id="lblPreKamokTani">
													</h:outputText>
												</TD>
										
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
						
				</TBODY>
			</TABLE>


			<BR>
			<HR noshade class="hr">
			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" width="800px">
				<TBODY>
					<TR>
						<TD align="right">
							<h:outputText styleClass="outputText" id="htmlCount"
								value="#{pc_Xrf00201.propGakkiList.listCount}">
							</h:outputText>
							<h:outputText
								styleClass="outputText" id="lblCount" value="件">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>				

			<TABLE border="0" cellpadding="0" cellspacing="0"  width="800px">
				<TBODY>					
					<TR>
						<TD>
							<div id="listScroll" class="listScroll"
								onscroll="setScrollPosition('scroll',this);"
								style="height:168px;">
								<h:dataTable border="0"
									cellpadding="2" cellspacing="0" headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Xrf00201.propGakkiList.rowClasses}"
									styleClass="meisai_scroll" id="tableGakki"
									value="#{pc_Xrf00201.propGakkiList.list}" var="varlist">

									<h:column id="column1">
										<f:facet name="header">
											<h:outputText id="ListText01" styleClass="outputText" value="科目コード">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKmokuCd"
											value="#{varlist.listKamokCd}">
										</h:outputText>
										<f:attribute value="80" name="width" />
									</h:column>

									<h:column id="column2">
										<f:facet name="header">
											<h:outputText id="ListText02" styleClass="outputText" value="科目名称">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKamokuName"
											value="#{varlist.listKamokName.displayValue}"
											title="#{varlist.listKamokName.stringValue}">
										</h:outputText>
										<f:attribute value="225" name="width" />
									</h:column>

									<h:column id="column3">
										<f:facet name="header">
											<h:outputText id="ListText03" styleClass="outputText" value="分冊">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListBunSatu"
											value="#{varlist.listBunSatu}">
										</h:outputText>
										<f:attribute value="40" name="width" />
									</h:column>

									<h:column id="column4">
										<f:facet name="header">
											<h:outputText id="ListText04" styleClass="outputText" value="新旧刊">
											</h:outputText>
										</f:facet>
											<h:outputText styleClass="outputText" id="htmlListSinKyuKan"
												value="#{varlist.listSinKyuKan}">
											</h:outputText>
										<f:attribute value="50" name="width" />
									</h:column>

									<h:column id="column5">
										<f:facet name="header">
											<h:outputText id="ListText05" styleClass="outputText" value="教員コード">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKyoinCd"
											value="#{varlist.listKyoinCd.displayValue}"
											title="#{varlist.listKyoinCd.stringValue}">
										</h:outputText>
										<f:attribute value="80" name="width" />
									</h:column>

									<h:column id="column6">
										<f:facet name="header">
											<h:outputText id="ListText06" styleClass="outputText" value="教員名">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKyoinName"
											value="#{varlist.listKyoinName.displayValue}"
											title="#{varlist.listKyoinName.stringValue}">
										</h:outputText>
										<f:attribute value="200" name="width" />
									</h:column>

									<h:column id="column7">
										<f:facet name="header">
											<h:outputText id="ListText07" styleClass="outputText" value="課題">
											</h:outputText>
										</f:facet>
								<h:outputText styleClass="outputText" id="htmlListKadai"
									value="#{varlist.listKadai}"
									style="text-align: center; vertical-align: middle">
								</h:outputText>
								<f:attribute value="40" name="width" value="text-align: center" name="style" />
									</h:column>
									<h:column id="column8">
										<f:facet name="header">
											<h:outputText id="ListText08" styleClass="outputText" value="添削">
											</h:outputText>
										</f:facet>
								<h:outputText styleClass="outputText" id="htmlListTensaku"
									value="#{varlist.listTensaku}"
									style="text-align: center; vertical-align: middle">
								</h:outputText>
								<f:attribute value="40" name="width" value="text-align: center" name="style" />

									</h:column>

									<h:column id="column9">
										<f:facet name="header">
										</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="selectKamoku"
									action="#{pc_Xrf00201.doSelectKamokuAction}" tabindex="6">
								</hx:commandExButton>
								<f:attribute value="30" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>
								</h:dataTable>
							</div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit" value="CSV作成"
							styleClass="commandExButton_dat" id="csvout"
							action="#{pc_Xrf00201.doCsvoutAction}"
							disabled="#{pc_Xrf00201.propCsvout.disabled}"
							confirm="#{msg.SY_MSG_0020W}" tabindex="7">
						</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>


			<BR>
			<HR noshade class="hr">
			<BR>


			<TABLE border="0" cellpadding="0" cellspacing="0" width="800" class="table">
				<TBODY>
					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblKamokCd2"
								style="#{pc_Xrf00201.propKamokCd2.labelStyle}"
								value="#{pc_Xrf00201.propKamokCd2.labelName}">
							</h:outputText>
						</TH>

						<TD width="124">
							<h:inputText styleClass="inputText" id="htmlKamokCd2"
							value="#{pc_Xrf00201.propKamokCd2.stringValue}"
							maxlength="#{pc_Xrf00201.propKamokCd2.maxLength}" size="10"
							style="#{pc_Xrf00201.propKamokCd2.style}"
							onblur="return doKamokuAjax2(this, event, 'form1:lblPreKamokName2');"
							disabled="#{pc_Xrf00201.propKamokCd2.disabled}" tabindex="8">
							<hx:inputHelperAssist imeMode="disabled"
								errorClass="inputText_Error" />
						</h:inputText>
							<hx:commandExButton type="button" value=""
							styleClass="commandExButton_search" id="search2"
							onclick="return openKamokuSubWindow2('form1:htmlKamokCd2');"
							disabled="#{pc_Xrf00201.propSearchButton2.disabled}" tabindex="9">
						</hx:commandExButton>
						</TD>

						<TD colspan="4">
							<h:outputText styleClass="outputText" id="lblPreKamokName2">
							</h:outputText>
						</TD>
					</TR>

					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblBunsatu"
								value="#{pc_Xrf00201.propBunsatu.labelName}"
								style="#{pc_Xrf00201.propBunsatu.labelStyle}">
							</h:outputText>
						</TH>

						<TD colspan="5">
							<h:inputText styleClass="inputText" id="htmlBunsatu"
							value="#{pc_Xrf00201.propBunsatu.stringValue}"
							maxlength="#{pc_Xrf00201.propBunsatu.maxLength}"
							style="#{pc_Xrf00201.propBunsatu.style}"
							disabled="#{pc_Xrf00201.propBunsatu.disabled}" size="2"
							tabindex="10">
							<hx:inputHelperAssist imeMode="disabled"
								errorClass="inputText_Error" />
						</h:inputText>
						</TD>
					</TR>

					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblSinKyuKanKbn"
								value="#{pc_Xrf00201.propSinKyuKanKbn.labelName}"
								style="#{pc_Xrf00201.propSinKyuKanKbn.labelStyle}">
							</h:outputText>
						</TH>

						<TD colspan="5">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlSinKyuKanKbn"
							value="#{pc_Xrf00201.propSinKyuKanKbn.stringValue}"
							style="#{pc_Xrf00201.propSinKyuKanKbn.style};width:150px"
							disabled="#{pc_Xrf00201.propSinKyuKanKbn.disabled}" tabindex="11">
							<f:selectItems value="#{pc_Xrf00201.propSinKyuKanKbn.list}" />
						</h:selectOneMenu>
						</TD>
					</TR>

					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblKyoinCd"
								style="#{pc_Xrf00201.propKyoinCd.labelStyle}"
								value="#{pc_Xrf00201.propKyoinCd.labelName}">
							</h:outputText>
						</TH>

						<TD width="124">
							<h:inputText styleClass="inputText" id="htmlKyoinCd"
							value="#{pc_Xrf00201.propKyoinCd.stringValue}"
							maxlength="#{pc_Xrf00201.propKyoinCd.maxLength}" size="10"
							style="#{pc_Xrf00201.propKyoinCd.style}"
							onblur="return doKyoinAjax(this, event, 'form1:lblPreKyoinName');"
							disabled="#{pc_Xrf00201.propKyoinCd.disabled}" tabindex="12">
							<hx:inputHelperAssist imeMode="disabled"
								errorClass="inputText_Error" />
						</h:inputText>
							<hx:commandExButton type="button" value=""
							styleClass="commandExButton_search" id="search3"
							onclick="return openKamokuSubWindow3(this, event);"
							disabled="#{pc_Xrf00201.propSearchButton3.disabled}"
							tabindex="13">
						</hx:commandExButton>
						</TD>

						<TD colspan="4">
							<h:outputText styleClass="outputText" id="lblPreKyoinName">
							</h:outputText>
						</TD>
					</TR>

					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblKadaiFlag"
								style="#{pc_Xrf00201.propKadaiFlag.labelStyle}"
								value="課題フラグ">
							</h:outputText>
						</TH>
						<TD width="124">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlKadaiFlag"
								value="#{pc_Xrf00201.propKadaiFlag.checked}"
								disabled="#{pc_Xrf00201.propKadaiFlag.disabled}"
								style="#{pc_Xrf00201.propKadaiFlag.style}"
								onclick="return func_kadai_click(this, event);" tabindex="14">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" id="lblKadaiTanto" value="課題担当"
								style="#{pc_Xrf00201.propKadaiFlag.labelStyle}">
							</h:outputText>
						</TD>

						<TH class="v_b" width="124">
							<h:outputText
								styleClass="outputText" id="lblTegakiFlag"
								style="#{pc_Xrf00201.propTegakiFlag.labelStyle}"
								value="手書きフラグ">
							</h:outputText>
						</TH>
						<TD width="124">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlTegakiFlag"
								value="#{pc_Xrf00201.propTegakiFlag.checked}"
								disabled="#{pc_Xrf00201.propTegakiFlag.disabled}"
								style="#{pc_Xrf00201.propTegakiFlag.style}" tabindex="15">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" id="lblTegakiSitei" value="手書き指定"
								style="#{pc_Xrf00201.propTegakiFlag.labelStyle}">
							</h:outputText>
						</TD>

						<TH class="v_b" width="124">
							<h:outputText
								styleClass="outputText" id="lblKadaiIraibi"
								value="#{pc_Xrf00201.propKadaiIraibi.labelName}"
								style="#{pc_Xrf00201.propKadaiIraibi.labelStyle}">
							</h:outputText>
						</TH>

						<TD width="124">
							<h:inputText
								styleClass="inputText" id="htmlKadaiIraibi"
								value="#{pc_Xrf00201.propKadaiIraibi.dateValue}"
								disabled="#{pc_Xrf00201.propKadaiIraibi.disabled}"
								size="12" tabindex="16">
								<f:convertDateTime />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
					</TR>

					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblTensakuFlag"
								style="#{pc_Xrf00201.propTensakuFlag.labelStyle}"
								value="添削フラグ">
							</h:outputText>
						</TH>
						<TD colspan="5">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlTensakuFlag"
								value="#{pc_Xrf00201.propTensakuFlag.checked}"
								disabled="#{pc_Xrf00201.propTensakuFlag.disabled}"
								style="#{pc_Xrf00201.propTensakuFlag.style}" tabindex="17">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" id="lblTensakuTanto" value="添削担当"
								style="#{pc_Xrf00201.propTensakuFlag.labelStyle}">
							</h:outputText>
						</TD>
					</TR>

				</TBODY>
			</TABLE>

		
			<BR>
			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit" value="確定"
								styleClass="commandExButton_dat" id="register"
								action="#{pc_Xrf00201.doRegisterAction}"
								disabled="#{pc_Xrf00201.propRegister.disabled}"
								confirm="#{msg.SY_MSG_0002W}" tabindex="18">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
								id="delete" action="#{pc_Xrf00201.doDeleteAction}"
								disabled="#{pc_Xrf00201.propDelete.disabled}"
								confirm="#{msg.SY_MSG_0004W}" tabindex="19">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
								id="clear" action="#{pc_Xrf00201.doClearAction}"
								disabled="#{pc_Xrf00201.propClear.disabled}" 
								confirm="#{msg.SY_MSG_0014W}" tabindex="20">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrf00201.propGakkiList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
			<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrf00201.propBackPhase.integerValue}">
			</h:inputHidden>

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

