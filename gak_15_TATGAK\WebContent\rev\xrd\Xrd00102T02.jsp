<%-- 
	教育実習登録（レポート）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrd/Xrd00102T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function loadAction(event){
// 画面ロード時の学生名称再取得
  doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:htmlGakuseiName');
  doKyoinAjax(document.getElementById('form1:htmlRptTnskKyoinCd'), event, 'form1:htmlRptTnskKyoinName');
  scrollEvent();
}

function windowOnscroll(){
// スクロールポジション取得
    var scrollTop =
        document.documentElement.scrollTop;
        document.getElementById('form1:htmlScrollPosition').value = scrollTop;
}

function scrollEvent(){
// スクロールポジション指定
	var scrollHidden = document.getElementById('form1:htmlScrollPosition').value;
	window.scrollTo(0,scrollHidden);
}	

function doGakuseiAjax(thisObj, thisEvent) {
    // 学生名称を取得する
    var servlet = "rev/co/CobGakseiAJAX";
    var args = new Array();
    args['code1'] = thisObj.value;
    args['code2'] = "";
    args['code3'] = "";
    var target = "form1:htmlGakuseiName";
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, target, args);
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  windowOnscroll();
  if (confirm(messageCreate(id, args))) {
  	  onChangeData();
  	  return true;
  }

  return false;
}
// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlgT01 = document.getElementById("form1:htmlHidChangeDataFlgT01").value;
	if(changeDataFlgT01 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT02 = document.getElementById("form1:htmlHidChangeDataFlgT02").value;
	if(changeDataFlgT02 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT03 = document.getElementById("form1:htmlHidChangeDataFlgT03").value;
	if(changeDataFlgT03 == "1"){
	  return confirm(id);
	}
	return true;
}


// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlgT02").value = "1";
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
	var kengenCheckFlg = document.getElementById('form1:htmlHidKengenCheckFlg').value;
	if (kengenCheckFlg == "1"){
		//更新後の学籍が権限なしとなるが、処理続行
		document.getElementById("form1:htmlHidButtonKbnAuth").value = "1";
	}
	var action = document.getElementById("form1:htmlHidShoriKbn").value;
	indirectClick(action);
}

// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById("form1:htmlHidButtonKbnAuth").value = "0";
	document.getElementById("form1:htmlHidKengenCheckFlg").value = "0";
}

function openKynCdWindow() {
    // 教員検索画面（引数：なし）htmlRptTnskKyoinCd

    var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlRptTnskKyoinCd";
    openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
    return true;
}

function doKyoinAjax(thisObj, thisEvent) {
    // 教員名称を取得する
    var servlet = "rev/co/CobJinjAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    var target = 'form1:htmlRptTnskKyoinName';
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, target, args);
}

// 項目制御(活性非活性)
function setDesable(thisObj, thisEvent) {
	document.getElementById('form1:htmlDesable').click();
}


</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrd00102T02.onPageLoadBegin}">

<gakuen:itemStateCtrlDef managedbean="pc_Xrd00102T01" property="xrd00102" />
<gakuen:itemStateCtrl managedbean="pc_Xrd00102T02">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrd00102T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrd00102T02.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrd00102T02.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
			<hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');"
				action="#{pc_Xrd00102T02.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置--></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="950">
						<TABLE class="table" width="100%">
							<TBODY>
								<TR align="center" valign="middle">
								<!-- 実習年度 --> 
									<TH nowrap class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblJissyuNendo_head"
										style="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.labelStyle}"
										value="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.labelName}"></h:outputText></TH>
									<TD width="670"><h:inputText styleClass="inputText"
										id="htmlJissyuNendo"
										disabled="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.disabled}"
										value="#{pc_Xrd00102T01.xrd00102.propJissyuNendo.dateValue}"
										size="4">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>
									<TD rowspan="4" style="background-color: transparent; text-align: right; vertical-align: bottom"
										class="clear_border">
										<hx:commandExButton
											type="submit" styleClass="commandExButton" id="select"
											value="選択" disabled="#{pc_Xrd00102T01.xrd00102.propSelect.disabled}"
											action="#{pc_Xrd00102T01.xrd00102.doSelectAction}"
											onclick="return windowOnscroll();"></hx:commandExButton> <hx:commandExButton
											type="submit" value="解除" styleClass="commandExButton"
											id="unselectEnrollment"
											onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');"
											action="#{pc_Xrd00102T01.xrd00102.doUnselectAction}"
											disabled="#{pc_Xrd00102T01.xrd00102.propUnSelect.disabled}"></hx:commandExButton>
									</TD>
								</TR>
								<TR>
								<!-- 学籍番号 -->
                                  	<TH width="180" class="v_a"><h:outputText
                                    	styleClass="outputText" id="lblGakusekiCd"
                                        value="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.labelName}"
                                        style="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.labelStyle}"></h:outputText></TH>
                                    <TD width="670"><h:inputText styleClass="inputText"
                                        id="htmlGakusekiCd" size="18"
                                        value="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.stringValue}"
                                        maxlength="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.maxLength}"
                                        disabled="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.disabled}"
                                        style="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.style}"
                                        onblur="return doGakuseiAjax(this, event);"></h:inputText>
                                        <hx:commandExButton type="button" styleClass="commandExButton_search"
                                        	id="gakusekiSearch"
                                            onclick="return openGakusekiCdWindow(this, event);"
                                            disabled="#{pc_Xrd00102T01.xrd00102.propGaksekiCd.disabled}"></hx:commandExButton>
                                        <h:outputText styleClass="outputText" id="htmlGakuseiName"></h:outputText></TD>
                                </TR>
								<TR>
								<!-- 校種区分 --> 
									<TH nowrap class="v_b" width="180"><h:outputText
										styleClass="outputText" id="lblKosyuKbn"
										style="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.labelStyle}"
										value="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.labelName}"></h:outputText></TH>
									<TD width="670"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlKosyuKbnList"
										disabled="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.disabled}"
										value="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.value}">
										<f:selectItems value="#{pc_Xrd00102T01.xrd00102.propKosyuKbnList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
								<!-- 登録区分 --> 
									<TH nowrap class="v_c" width="180"><h:outputText
										styleClass="outputText" id="lblTorokKbn"
										style="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.labelStyle}"
										value="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.labelName}"></h:outputText></TH>
									<TD nowrap width="670"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlTorokKbnRadio"
										value="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.value}"
										disabled="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.disabled}">
										<f:selectItems value="#{pc_Xrd00102T01.xrd00102.propTorokKbnRadio.list}" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
 						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" align="left"
											style="border-bottom-style: none; ">
											<TBODY>
												<TR>
												<TD width="150px"><hx:commandExButton 
													type="submit" styleClass="tab_head_off" id="tabXrd00102T01" style="width:100%"
													value="基本情報" action="#{pc_Xrd00102T02.doTabXrd00102T01Action}"></hx:commandExButton></TD>
												<TD width="150px"><hx:commandExButton
													type="button" styleClass="tab_head_on" id="tabXrd00102T02" style="width:100%"
													value="事前レポート"
													disabled="#{pc_Xrd00102T01.xrd00102.propRptBtn.disabled}"></hx:commandExButton></TD>
												<TD width="150px"><hx:commandExButton
													type="submit" styleClass="tab_head_off" id="tabXrd00102T03" style="width:100%"
													value="日誌" action="#{pc_Xrd00102T02.doTabXrd00102T03Action}"
													disabled="#{pc_Xrd00102T01.xrd00102.propNissiBtn.disabled}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE class="tab_body" border="0" cellpadding="20" cellspacing="0" 
										width="100%" style="border-top-style: none; ">
										<TR>
											<TD align="center" width="100%">
											<div style="height: 600px"><BR>
											<TABLE class="table" width="822">
												<TBODY>
 													<TR>
													<!-- 科目コード --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblKamokCd"
															value="#{pc_Xrd00102T02.propKamokCd.labelName}"></h:outputText></TH>
														<TD colspan="3" width="638"><h:outputText
															styleClass="outputText" id="htmlKamokCd"
															value="#{pc_Xrd00102T02.propKamokCd.value}"
															style="width:211px;">
														</h:outputText></TD>
													</TR>
 													<TR>
													<!-- 科目名称 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblKamokName"
															value="#{pc_Xrd00102T02.propKamokName.labelName}"></h:outputText></TH>
														<TD><h:outputText
															styleClass="outputText" id="htmlKamokName"
															value="#{pc_Xrd00102T02.propKamokName.value}"
															style="width:211px;">
														</h:outputText></TD>
													</TR>
					                                <TR>
					                                <!-- レポート添削教員コード -->
					                                    <TH class="v_j" width="184"><h:outputText
					                                        styleClass="outputText" id="lblRptTnskKyoinCd"
					                                        value="#{pc_Xrd00102T02.propRptTnskKyoinCd.labelName}"
					                                        style="#{pc_Xrd00102T02.propRptTnskKyoinCd.labelStyle}"></h:outputText></TH>
					                                    <TD><h:inputText styleClass="inputText"
					                                        id="htmlRptTnskKyoinCd" value="#{pc_Xrd00102T02.propRptTnskKyoinCd.stringValue}"
					                                        onblur="return doKyoinAjax(this, event);"
					                                        style="#{pc_Xrd00102T02.propRptTnskKyoinCd.style}"
					                                        disabled="#{pc_Xrd00102T02.propRptTnskKyoinCd.disabled}"
					                                        onkeydown="onChangeData();"
					                                        maxlength="#{pc_Xrd00102T02.propRptTnskKyoinCd.maxLength}" size="20">
					                                    </h:inputText><hx:commandExButton type="button"
					                                        styleClass="commandExButton_search" id="kyoinSearch"
					                                        disabled="#{pc_Xrd00102T02.propRptTnskKyoinCd.disabled}"
					                                        onclick="return openKynCdWindow(this, event);">
					                                    </hx:commandExButton><h:outputText styleClass="outputText"
					                                        id="htmlRptTnskKyoinName"
					                                        value="#{pc_Xrd00102T02.propRptTnskKyoinName.stringValue}"
					                                        style="#{pc_Xrd00102T02.propRptTnskKyoinName.style}"></h:outputText></TD>
					                                </TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE class="table" width="822">
												<TBODY>
													<TR>
													<!-- 第1回レポート受付日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptDate1"
															value="#{pc_Xrd00102T02.propRptDate1.name}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptDate1"
															value="#{pc_Xrd00102T02.propRptDate1.dateValue}"
															onkeydown="onChangeData();"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
 													<TR>
													<!-- 第1回レポート合否 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblGohiKbn1"
															value="#{pc_Xrd00102T02.propGohiKbn1.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneRadio
															styleClass="selectOneRadio" id="htmlGohiKbn1"
															value="#{pc_Xrd00102T02.propGohiKbn1.value}"
															onkeydown="onChangeData();"
															onclick="return setDesable(this, event);"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T02.propGohiKbn1.list}" />
														</h:selectOneRadio></TD>
													<!-- 第1回レポート返却日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptRtnDate1"
															value="#{pc_Xrd00102T02.propRptRtnDate1.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlRptRtnDate1"
															value="#{pc_Xrd00102T02.propRptRtnDate1.dateValue}"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 第1回備考 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblRptNote1"
															value="#{pc_Xrd00102T02.propRptNote1.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptNote1"
															value="#{pc_Xrd00102T02.propRptNote1.value}"
															onkeydown="onChangeData();"
															maxlength="#{pc_Xrd00102T02.propRptNote1.maxLength}"
															style="width:600px;">
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE class="table" width="822">
												<TBODY>
													<TR>
													<!-- 第2回レポート受付日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptDate2"
															value="#{pc_Xrd00102T02.propRptDate2.name}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptDate2"
															value="#{pc_Xrd00102T02.propRptDate2.dateValue}"
															disabled="#{pc_Xrd00102T02.propRptDate2.disabled}"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
 													<TR>
													<!-- 第2回レポート合否 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblGohiKbn2"
															value="#{pc_Xrd00102T02.propGohiKbn2.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneRadio
															styleClass="selectOneRadio" id="htmlGohiKbn2"
															value="#{pc_Xrd00102T02.propGohiKbn2.value}"
															disabled="#{pc_Xrd00102T02.propRptDate2.disabled}"
															onclick="return setDesable(this, event);"
															onkeydown="onChangeData();"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T02.propGohiKbn2.list}" />
														</h:selectOneRadio></TD>
													<!-- 第2回レポート返却日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptRtnDate2"
															value="#{pc_Xrd00102T02.propRptRtnDate2.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlRptRtnDate2"
															value="#{pc_Xrd00102T02.propRptRtnDate2.dateValue}"
															disabled="#{pc_Xrd00102T02.propRptDate2.disabled}"
															onkeydown="onChangeData();"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 第2回備考 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblRptNote2"
															value="#{pc_Xrd00102T02.propRptNote2.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptNote2"
															value="#{pc_Xrd00102T02.propRptNote2.value}"
															disabled="#{pc_Xrd00102T02.propRptDate2.disabled}"
															maxlength="#{pc_Xrd00102T02.propRptNote2.maxLength}"
															onkeydown="onChangeData();"
															style="width:600px;">
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE class="table" width="822">
												<TBODY>
													<TR>
													<!-- 第3回レポート受付日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptDate3"
															value="#{pc_Xrd00102T02.propRptDate3.name}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptDate3"
															value="#{pc_Xrd00102T02.propRptDate3.dateValue}"
															disabled="#{pc_Xrd00102T02.propRptDate3.disabled}"
															onkeydown="onChangeData();"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
 													<TR>
													<!-- 第3回レポート合否 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblGohiKbn3"
															value="#{pc_Xrd00102T02.propGohiKbn3.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneRadio
															styleClass="selectOneRadio" id="htmlGohiKbn3"
															value="#{pc_Xrd00102T02.propGohiKbn3.value}"
															disabled="#{pc_Xrd00102T02.propRptDate3.disabled}"
															onkeydown="onChangeData();"
															onclick="return setDesable(this, event);"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T02.propGohiKbn3.list}" />
														</h:selectOneRadio></TD>
													<!-- 第3回レポート返却日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptRtnDate3"
															value="#{pc_Xrd00102T02.propRptRtnDate3.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlRptRtnDate3"
															value="#{pc_Xrd00102T02.propRptRtnDate3.dateValue}"
															onkeydown="onChangeData();"
															disabled="#{pc_Xrd00102T02.propRptDate3.disabled}"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 第3回備考 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblRptNote3"
															value="#{pc_Xrd00102T02.propRptNote3.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptNote3"
															value="#{pc_Xrd00102T02.propRptNote3.value}"
															disabled="#{pc_Xrd00102T02.propRptDate3.disabled}"
															onkeydown="onChangeData();"
															maxlength="#{pc_Xrd00102T02.propRptNote3.maxLength}"
															style="width:600px;">
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE class="table" width="822">
												<TBODY>
													<TR>
													<!-- 第4回レポート受付日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptDate4"
															value="#{pc_Xrd00102T02.propRptDate4.name}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptDate4"
															value="#{pc_Xrd00102T02.propRptDate4.dateValue}"
															disabled="#{pc_Xrd00102T02.propRptDate4.disabled}"
															onkeydown="onChangeData();"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
 													<TR>
													<!-- 第4回レポート合否 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblGohiKbn4"
															value="#{pc_Xrd00102T02.propGohiKbn4.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneRadio
															styleClass="selectOneRadio" id="htmlGohiKbn4"
															value="#{pc_Xrd00102T02.propGohiKbn4.value}"
															disabled="#{pc_Xrd00102T02.propRptDate4.disabled}"
															onkeydown="onChangeData();"
															onclick="return setDesable(this, event);"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T02.propGohiKbn4.list}" />
														</h:selectOneRadio></TD>
													<!-- 第4回レポート返却日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptRtnDate4"
															value="#{pc_Xrd00102T02.propRptRtnDate4.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlRptRtnDate4"
															value="#{pc_Xrd00102T02.propRptRtnDate4.dateValue}"
															disabled="#{pc_Xrd00102T02.propRptDate4.disabled}"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 第4回備考 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblRptNote4"
															value="#{pc_Xrd00102T02.propRptNote4.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptNote4"
															value="#{pc_Xrd00102T02.propRptNote4.value}"
															disabled="#{pc_Xrd00102T02.propRptDate4.disabled}"
															maxlength="#{pc_Xrd00102T02.propRptNote4.maxLength}"
															onkeydown="onChangeData();"
															style="width:600px;">
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE class="table" width="822">
												<TBODY>
													<TR>
													<!-- 第5回レポート受付日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptDate5"
															value="#{pc_Xrd00102T02.propRptDate5.name}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptDate5"
															value="#{pc_Xrd00102T02.propRptDate5.dateValue}"
															disabled="#{pc_Xrd00102T02.propRptDate5.disabled}"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
 													<TR>
													<!-- 第5回レポート合否 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblGohiKbn5"
															value="#{pc_Xrd00102T02.propGohiKbn5.labelName}"></h:outputText></TH>
														<TD align="left" width="227"><h:selectOneRadio
															styleClass="selectOneRadio" id="htmlGohiKbn5"
															value="#{pc_Xrd00102T02.propGohiKbn5.value}"
															disabled="#{pc_Xrd00102T02.propRptDate5.disabled}"
															onkeydown="onChangeData();"
															onclick="return setDesable(this, event);"
															style="width:211px;">
															<f:selectItems value="#{pc_Xrd00102T02.propGohiKbn5.list}" />
														</h:selectOneRadio></TD>
													<!-- 第5回レポート返却日 -->
														<TH nowrap class="v_i" width="184"><h:outputText
															styleClass="outputText" id="lblRptRtnDate5"
															value="#{pc_Xrd00102T02.propRptRtnDate5.name}"></h:outputText></TH>
														<TD align="left" width="227"><h:inputText
															styleClass="inputText" id="htmlRptRtnDate5"
															value="#{pc_Xrd00102T02.propRptRtnDate5.dateValue}"
															disabled="#{pc_Xrd00102T02.propRptDate5.disabled}"
															size="10"
															onkeydown="onChangeData();">
														<f:convertDateTime />
														<hx:inputHelperDatePicker />
														<hx:inputHelperAssist errorClass="inputText_Error"
															promptCharacter="_" />
														</h:inputText>
														</TD>
													</TR>
													<TR>
													<!-- 第5回備考 --> 
														<TH nowrap class="v_e" width="184"><h:outputText
															styleClass="outputText" id="lblRptNote5"
															value="#{pc_Xrd00102T02.propRptNote5.labelName}"></h:outputText></TH>
														<TD colspan="3"><h:inputText
															styleClass="inputText" id="htmlRptNote5"
															value="#{pc_Xrd00102T02.propRptNote5.value}"
															disabled="#{pc_Xrd00102T02.propRptDate5.disabled}"
															maxlength="#{pc_Xrd00102T02.propRptNote5.maxLength}"
															onkeydown="onChangeData();"
															style="width:600px;">
														</h:inputText></TD>
													</TR>
												</TBODY>
											</TABLE>
											<BR>
											<TABLE class="button_bar" cellspacing="1" cellpadding="1">
												<TBODY>
													<TR>
														<TD width="819" align="center">
															<hx:commandExButton
																type="submit" value="確定" styleClass="commandExButton_dat"
																id="kakutei"
																action="#{pc_Xrd00102T02.doKakuteiAction}"
																confirm="#{msg.SY_MSG_0001W}"
																onclick="return windowOnscroll();"></hx:commandExButton>
															<hx:commandExButton
																type="submit" value="クリア"
																styleClass="commandExButton_etc" id="clear"
																onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
																action="#{pc_Xrd00102T02.doClearAction}"></hx:commandExButton>
															<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
																id="delete"
																confirm="#{msg.SY_MSG_0004W}"
																action="#{pc_Xrd00102T02.doDeleteAction}"
																disabled="#{pc_Xrd00102T02.propDelete.disabled}"
																onclick="return windowOnscroll();"></hx:commandExButton>
															<!-- Hidden項目制御(活性・非活性)ボタン -->	
															<hx:commandExButton	
																style="display:none;" id="htmlDesable" 
																action="#{pc_Xrd00102T02.doDesable}"></hx:commandExButton>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
											</div>
											</TD>
										</TR>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE> 
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<h:inputHidden id="htmlHidChangeDataFlgT01" value="#{pc_Xrd00102T01.propHidChangeDataFlgT01.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlgT02" value="#{pc_Xrd00102T02.propHidChangeDataFlgT02.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlgT03" value="#{pc_Xrd00102T03.propHidChangeDataFlgT03.stringValue}" ></h:inputHidden>
			<h:inputHidden id="htmlScrollPosition" value="#{pc_Xrd00102T02.propScrollPosition.stringValue}"></h:inputHidden>
						</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
