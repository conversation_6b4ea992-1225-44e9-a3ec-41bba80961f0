<%-- 
	レポート添削教員割当の画面項目
	
	<AUTHOR>
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrf00601.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

	// バーコード入力：KeyDown処理
	function fncKeyEvt() {
		if (event.keyCode == 13) {
			var btnObj = document.getElementById('form1:readBarcode');
			//btnObj.click();
			indirectClick('readBarcode');
			//doKamokuAjax('form1:htmlKamokCd', event, 'form1:lblKamokName');
		}
	}	

	// 登録ボタンクリック：OK選択時の処理
	function confirmOk() {

		document.getElementById('form1:propExecutableRegister').value = 1;
				
		indirectClick('register');
	}

	// 登録ボタンクリック：キャンセル選択時の処理
	function confirmCancel() {

		return;
	}
	
	// 科目検索アイコン押下時処理
	function openKamokCdSearchWindow(targetObj) {
		
		var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp" +
			"?retFieldName=" + targetObj;
		openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
		return true;
	}

	// 科目名称を取得する
	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
		var servlet = "rev/km/KmzKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	//チェックボックスAll On
	function fncCheckAll(thisObj, thisEvent) {
		check('htmlRptTensakuList','htmlSelected');
	}

	//チェックボックスAll Off
	function fncCheckNone(thisObj, thisEvent) {
		uncheck('htmlRptTensakuList','htmlSelected');
	}

    //ボタンの活性化制御
    function fncButtonActive(thisEvent){
		var init = null;
					
		//確定ボタン
		init = document.getElementById('form1:htmlActiveControl').value;
		
		//検索条件項目
		document.getElementById('form1:htmlNendo').disabled = (init == 2);
		document.getElementById('form1:htmlKamokCd').disabled = (init == 2);
		document.getElementById('form1:searchKamokCd').disabled = (init == 2);
		document.getElementById('form1:htmlBunsatsu').disabled = (init == 2);
		document.getElementById('form1:htmlReportRecvDateFrom').disabled = (init == 2);
		document.getElementById('form1:htmlReportRecvDateTo').disabled = (init == 2);
//		document.getElementById('form1:htmlOutputStyle').disabled = (init == 2);
		
		//初期表示
		if(init == 0){
			document.getElementById('form1:readBarcode').disabled = false;		//バーコード読取
			document.getElementById('form1:searchKamokCd').disabled = false;	//科目検索
			document.getElementById('form1:search').disabled = false;			//選択
			document.getElementById('form1:clear').disabled = true;				//解除
			document.getElementById('form1:register').disabled = true;			//登録
			document.getElementById('form1:check').disabled = true;				//全選択
			document.getElementById('form1:uncheck').disabled = true;			//全解除
		}
		
		//バーコード読取ボタン押下
		if(init == 1){
			document.getElementById('form1:readBarcode').disabled = false;		//バーコード読取
			document.getElementById('form1:searchKamokCd').disabled = false;	//科目検索
			document.getElementById('form1:search').disabled = false;			//選択
			document.getElementById('form1:clear').disabled = true;				//解除
			document.getElementById('form1:register').disabled = true;			//登録
			document.getElementById('form1:check').disabled = true;				//全選択
			document.getElementById('form1:uncheck').disabled = true;			//全解除
		}
		
		//選択／登録ボタン押下
		if(init == 2){
			document.getElementById('form1:readBarcode').disabled = false;		//バーコード読取
			document.getElementById('form1:searchKamokCd').disabled = true;		//科目検索
			document.getElementById('form1:search').disabled = true;			//選択
			document.getElementById('form1:clear').disabled = false;			//解除
			document.getElementById('form1:register').disabled = false;			//登録
			document.getElementById('form1:check').disabled = false;			//全選択
			document.getElementById('form1:uncheck').disabled = false;			//全解除
		}
		
		//科目名称再表示
		var cdObj = document.getElementById('form1:htmlKamokCd');
		doKamokuAjax(cdObj, thisEvent, 'form1:lblKamokName');
	}

	

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY onload="fncButtonActive(event);">

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00601.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrf00601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00601.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 --> <!-- ↑ここに戻るボタンを配置 -->
			</DIV>

			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE width="860" border="0" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
						<TH width="100" class="v_a"><h:outputText styleClass="outputText"
							id="lblBarcode"
							value="バーコード"
							style="#{pc_Xrf00601.propBarcode.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText" id="htmlBarcode" size="25"
							onkeydown="fncKeyEvt();" style="#{pc_Xrf00601.propBarcode.style}"
							tabindex="5"
							maxlength="#{pc_Xrf00601.propBarcode.maxLength}"
							value="#{pc_Xrf00601.propBarcode.stringValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" imeMode="disabled"/>
						</h:inputText> <hx:commandExButton type="submit" value="読取"
							tabindex="6"
							styleClass="cmdBtn_dat_s" id="readBarcode"
							action="#{pc_Xrf00601.doReadBarcodeAction}">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="860" border="0" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
						<TH width="100" class="v_a"><h:outputText styleClass="outputText"
							id="lblNendo" value="#{pc_Xrf00601.propNendo.labelName}"
							style="#{pc_Xrf00601.propNendo.labelStyle}"></h:outputText></TH>
						<TD width="80"><h:inputText styleClass="inputText" id="htmlNendo" size="4"
							tabindex="7"
							style="#{pc_Xrf00601.propNendo.style}"
							value="#{pc_Xrf00601.propNendo.dateValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
						<TH width="120" class="v_a"><h:outputText styleClass="outputText"
							id="lblKamokCd" value="#{pc_Xrf00601.propKamokCd.labelName}"
							style="#{pc_Xrf00601.propKamokCd.labelStyle}"></h:outputText></TH>
						<TD width="430" nowrap><h:inputText styleClass="inputText" id="htmlKamokCd" size="10"
							onblur="return doKamokuAjax(this, event, 'form1:lblKamokName');"
							tabindex="8"
							style="#{pc_Xrf00601.propKamokCd.style}"
							maxlength="#{pc_Xrf00601.propKamokCd.maxLength}"
							value="#{pc_Xrf00601.propKamokCd.stringValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" imeMode="disabled" />
						</h:inputText> <hx:commandExButton type="button"
							styleClass="commandExButton_search" id="searchKamokCd"
							onclick="openKamokCdSearchWindow('form1:htmlKamokCd');"
							tabindex="9"
							disabled="#{pc_Xrf00601.propSearchKamokButton.disabled}"
							style="#{pc_Xrf00601.propSearchKamokButton.style}">
						</hx:commandExButton> <h:outputText styleClass="outputText"
							id="lblKamokName"
							value="#{pc_Xrf00601.propKamokName.stringValue}"
							style="#{pc_Xrf00601.propKamokName.labelStyle}"></h:outputText></TD>
						<TD rowspan="4" width="130" nowrap
							style="background-color: transparent;border-bottom-style: none; border-top-style: none; border-right-style: none; text-align: right">
							<hx:commandExButton type="submit" value="選択"
								styleClass="commandExButton" id="search"  tabindex="15"
								action="#{pc_Xrf00601.doSearchListAction}"
								disabled="#{pc_Xrf00601.propSearchButton.disabled}"
								style="#{pc_Xrf00601.propSearchButton.style}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="解除" tabindex="16"
								styleClass="commandExButton" id="clear" 
								action="#{pc_Xrf00601.doClearAction}"
								disabled="#{pc_Xrf00601.propClearButton.disabled}"
								style="#{pc_Xrf00601.propClearButton.style}">
							</hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TH width="100" class="v_a"><h:outputText styleClass="outputText"
							id="lblBunsatsu" value="#{pc_Xrf00601.propBunsatsu.labelName}"
							style="#{pc_Xrf00601.propBunsatsu.labelStyle}"></h:outputText></TH>
						<TD colspan="3"><h:inputText styleClass="inputText" id="htmlBunsatsu" size="2"
							tabindex="10"
							style="#{pc_Xrf00601.propBunsatsu.style}"
							maxlength="#{pc_Xrf00601.propBunsatsu.maxLength}"
							value="#{pc_Xrf00601.propBunsatsu.stringValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" imeMode="disabled" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH width="100" class="v_a"><h:outputText styleClass="outputText"
							id="lblReportRecvDate"
							value="#{pc_Xrf00601.propReportRecvDateFrom.labelName}"
							style="#{pc_Xrf00601.propReportRecvDateFrom.labelStyle}"></h:outputText></TH>
						<TD colspan="3"><h:inputText styleClass="inputText"
							id="htmlReportRecvDateFrom" size="12"
							tabindex="12"
							style="#{pc_Xrf00601.propReportRecvDateFrom.style}"
							value="#{pc_Xrf00601.propReportRecvDateFrom.dateValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" imeMode="disabled" />
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
						</h:inputText> <h:outputText styleClass="outputText"
							id="lblReportRecvDateCombine" value="　～　">
						</h:outputText><h:inputText styleClass="inputText"
							id="htmlReportRecvDateTo" size="12"
							tabindex="13"
							style="#{pc_Xrf00601.propReportRecvDateTo.style}"
							value="#{pc_Xrf00601.propReportRecvDateTo.dateValue}">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" imeMode="disabled" />
							<f:convertDateTime />
							<hx:inputHelperDatePicker />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH width="100" class="v_a"><h:outputText styleClass="outputText"
							id="lblOutputStyle" value="出力指定"></h:outputText></TH>
						<TD colspan="3"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlOutputStyle"
							tabindex="14"
							value="#{pc_Xrf00601.propOutputStyle.checked}"
							readonly="#{pc_Xrf00601.propOutputStyle.readonly}"
							disabled="#{pc_Xrf00601.propOutputStyle.disabled}"
							style="#{pc_Xrf00601.propOutputStyle.style}">
						</h:selectBooleanCheckbox> 割当済みを除く</TD>
					</TR>
				</TBODY>
			</TABLE>
 			<BR>
			<TABLE width="860" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<DIV align="right"><h:outputText styleClass="outputText" id="sum"
							value="#{pc_Xrf00601.propRptTensakuList.listCount}"></h:outputText>
						<h:outputText styleClass="outputText" id="lblKen" value="件"></h:outputText>
						</DIV>
						</TD>
					</TR>
					<TR>
						<TD>
						<div id="listScroll" class="listScroll"
								onscroll="setScrollPosition('scroll',this);"
								style="height:201px;">
							<h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							columnClasses="columnClass1" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrf00601.propRptTensakuList.rowClasses}"
							styleClass="meisai_scroll" id="htmlRptTensakuList"
							value="#{pc_Xrf00601.propRptTensakuList.list}" var="varlist">

							<h:column id="colum1">
								<f:facet name="header">
								</f:facet>
								<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
									id="htmlSelected" value="#{varlist.selected}" tabindex="17">
								</h:selectBooleanCheckbox>
								<f:attribute value="30" name="width" />
								<f:attribute value="center" name="align" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="colum2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学籍番号"
										id="gakusekiNo"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="lblRptTensakuList_gakusekiNo" value="#{varlist.gakusekiNo}">
								</h:outputText>
								<f:attribute value="100" name="width" />
							</h:column>

							<h:column id="colum3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="氏名" id="shimei"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="lblRptTensakuList_shimei"
									title="#{varlist.gakuseiShimei.stringValue}"
									value="#{varlist.gakuseiShimei.displayValue}">
								</h:outputText>
								<f:attribute value="150" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>

							<h:column id="colum4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="レポート提出日"
										id="teisyutsuDate"></h:outputText>
								</f:facet>
								<f:attribute value="120" name="width" />
								<h:outputText styleClass="outputText"
									id="lblRptTensakuList_teisyutsuDate"
									value="#{varlist.reportTeisyutsuDate}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="colum5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="枝番" id="edaban"></h:outputText>
								</f:facet>
								<f:attribute value="50" name="width" />
								<h:outputText styleClass="outputText"
									id="lblRptTensakuList_edaban" value="#{varlist.edaban}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="colum6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="添削依頼日"
										id="iraiDate"></h:outputText>
								</f:facet>
								<f:attribute value="100" name="width" />
								<h:outputText styleClass="outputText"
									id="lblRptTensakuList_iraiDate"
									value="#{varlist.tensakuIraiDate}">
								</h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="colum7">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="添削教員" id="kyoin"></h:outputText>
								</f:facet>
								<f:attribute value="150" name="width" />
								<h:outputText styleClass="outputText"
									id="lblRptTensakuList_kyoin"
									title="#{varlist.tensakuKyoinShimei.stringValue}"
									value="#{varlist.tensakuKyoinShimei.displayValue}">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
							</h:column>

							<h:column id="colum8">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="前回添削教員"
										id="lastKyoin"></h:outputText>
								</f:facet>
								<f:attribute value="150" name="width" />
								<h:outputText styleClass="outputText"
									id="lblRptTensakuList_lastKyoin"
									title="#{varlist.lastTensakuKyoinShimei.stringValue}"
									value="#{varlist.lastTensakuKyoinShimei.displayValue}">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
							</h:column>

						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
				<TR>
					<TD align="left"><hx:commandExButton type="button"
						styleClass="check" id="check"
						tabindex="20"
						disabled="#{pc_Xrf00601.propCheckRowsButton.disabled}"
						onclick="return fncCheckAll(this, event);">
					</hx:commandExButton> <hx:commandExButton type="button"
						styleClass="uncheck" id="uncheck"
						tabindex="21"
						disabled="#{pc_Xrf00601.propCheckRowsButton.disabled}"
						onclick="return fncCheckNone(this, event);">
					</hx:commandExButton></TD>
				</TR>
			</TABLE>

			<BR>

			<TABLE class="table" border="0" width="860">

				<TR>
					<TH nowrap class="v_a" width="150"><h:outputText
						styleClass="outputText" id="lblIraiDate"
						value="#{pc_Xrf00601.propIraiDate.labelName}"
 						style="#{pc_Xrf00601.propIraiDate.labelStyle}">
					</h:outputText></TH>
					<TD nowrap width="532"><h:inputText styleClass="inputText"
						id="htmlIraiDate" style="#{pc_Xrf00601.propIraiDate.style}"
						tabindex="22"
						disabled="#{pc_Xrf00601.propIraiDate.disabled}"
						value="#{pc_Xrf00601.propIraiDate.dateValue}" size="12">
						<f:convertDateTime />
						<hx:inputHelperAssist errorClass="inputText_Error"
							promptCharacter="_" imeMode="disabled" />
						<hx:inputHelperDatePicker />
					</h:inputText></TD>
				</TR>



				<TR>
					<TH nowrap class="v_b" width="150"><h:outputText
						styleClass="outputText" id="lblTensakuKyoin"
						value="#{pc_Xrf00601.propTensakuKyoin.labelName}"
						style="#{pc_Xrf00601.propTensakuKyoin.labelStyle}">
					</h:outputText></TH>
					<TD nowrap width="532"><h:selectOneMenu styleClass="selectOneMenu"
						id="htmlTensakuKyoin"
						tabindex="23"
						disabled="#{pc_Xrf00601.propTensakuKyoin.disabled}"
						style="#{pc_Xrf00601.propTensakuKyoin.style}"
						value="#{pc_Xrf00601.propTensakuKyoin.value}">
						<f:selectItems value="#{pc_Xrf00601.propTensakuKyoin.list}" />
					</h:selectOneMenu></TD>
				</TR>

			</TABLE>


			<TABLE class="button_bar" width="860" align="center" cellspacing="1"
				cellpadding="1">
				<TR>
					<TD align="center"><hx:commandExButton type="submit" value="登録"
						styleClass="commandExButton_dat" id="register"
						tabindex="24"
						disabled="#{pc_Xrf00601.propRegisterButton.disabled}"
						action="#{pc_Xrf00601.doRegisterAction}"></hx:commandExButton></TD>
				</TR>
			</TABLE>
			


			<!-- ↑ここにコンポーネントを配置 --></DIV>

			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrf00601.propRptTensakuList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrf00601.propExecutableRegister.integerValue}"
				id="propExecutableRegister"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrf00601.propActiveControl.integerValue}"
				id="htmlActiveControl">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

