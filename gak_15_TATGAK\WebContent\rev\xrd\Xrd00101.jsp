<%-- 
	教育実習登録（検索）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrd/Xrd00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function loadAction(event){
// 画面ロード時の学生名称再取得
  doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:htmlName');
  doKyoinAjax(document.getElementById('form1:htmlRptKynCd'), event, 'form1:htmlShimeiName');
  doKyoinAjax(document.getElementById('form1:htmlNissiKynCd'), event, 'form1:htmlNissiShimeiName');
  scrollEvent();
}

function windowOnscroll(){
// スクロールポジション取得
    var scrollTop =
        document.documentElement.scrollTop;
        document.getElementById('form1:htmlScrollPosition').value = scrollTop;
}

function scrollEvent(){
// スクロールポジション指定
	var scrollHidden = document.getElementById('form1:htmlScrollPosition').value;
	window.scrollTo(0,scrollHidden);
}	

function doGakuseiAjax(thisObj, thisEvent) {
    // 学生名称を取得する
    var servlet = "rev/co/CobGakseiAJAX";
    var args = new Array();
    args['code1'] = thisObj.value;
    args['code2'] = "";
    args['code3'] = "";
    var target = "form1:htmlName";
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, target, args);
}

function openGakusekiCdWindow(thisObj, thisEvent) {
    // 学生検索画面（引数：なし）
    var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakusekiCd";
    openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
    return true;
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}

// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
}

function openKynCdWindow(target) {
    // 教員検索画面（引数：id）
    var url;
	if(target == 'form1:htmlRptKynCd'){
	  url ="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlRptKynCd";
	}else{
	  url ="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlNissiKynCd";
	}	

    openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
    return true;
}

function doKyoinAjax(thisObj, thisEvent, target) {
    // 教員名称を取得する

    var servlet = "rev/co/CobJinjAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, target, args);

    
}

</SCRIPT>

</HEAD>
<f:view locale="#{SYSTEM_DATA.locale"}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrd00101.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Xrd00101">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrd00101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrd00101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrd00101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="新規登録" styleClass="commandExButton" id="insert"
				action="#{pc_Xrd00101.doInsertAction}">
			</hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->


			<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center">
						<!-- 検索条件 -->
						<TABLE class="table" width="885">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="200"><!-- 実習年度 --> <h:outputText
										styleClass="outputText" id="lblJissyuNendo"
										value="#{pc_Xrd00101.propJissyuNendo.labelName}"></h:outputText></TH>
									<TD width="211"><h:inputText styleClass="inputText"
										id="htmlJissyuNendo"
										value="#{pc_Xrd00101.propJissyuNendo.dateValue}"
										size="4">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TH nowrap class="v_b" width="200"><!-- 校種区分 --> <h:outputText
										styleClass="outputText" id="lblKosyuKbn"
										value="#{pc_Xrd00101.propKosyuKbnList.labelName}"></h:outputText></TH>
									<TD width="211"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlKosyuKbnList"
										value="#{pc_Xrd00101.propKosyuKbnList.value}"
										style="width:211px;">
										<f:selectItems value="#{pc_Xrd00101.propKosyuKbnList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH nowrap class="v_c" width="200"><!-- 登録区分 --> <h:outputText
										styleClass="outputText" id="lblTorokKbn"
										value="#{pc_Xrd00101.propTorokKbnCheck.labelName}"></h:outputText></TH>
									<TD nowrap width="685" colspan="3"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlTorokKbnCheck"
										value="#{pc_Xrd00101.propTorokKbnCheck.value}"
										disabled="#{pc_Xrd00101.propTorokKbnCheck.disabled}">
										<f:selectItems value="#{pc_Xrd00101.propTorokKbnCheck.list}" />
									</h:selectManyCheckbox></TD>
								</TR>
                                <TR>
                                  	<TH width="200" class="v_a"><!-- 学籍番号 --><h:outputText
                                    	styleClass="outputText" id="lblGakusekiCd"
                                        value="#{pc_Xrd00101.propGaksekiCd.labelName}"
                                        style="#{pc_Xrd00101.propGaksekiCd.labelStyle}"></h:outputText></TH>
                                    <TD width="685" colspan="3"><h:inputText styleClass="inputText"
                                        id="htmlGakusekiCd" size="18"
                                        value="#{pc_Xrd00101.propGaksekiCd.stringValue}"
                                        maxlength="#{pc_Xrd00101.propGaksekiCd.maxLength}"
                                        disabled="#{pc_Xrd00101.propGaksekiCd.disabled}"
                                        style="#{pc_Xrd00101.propGaksekiCd.style}"
                                        onblur="return doGakuseiAjax(this, event);"></h:inputText>
                                        <hx:commandExButton type="button" styleClass="commandExButton_search"
                                        	id="gakusekiSearch"
                                            onclick="return openGakusekiCdWindow(this, event);"
                                            disabled="#{pc_Xrd00101.propGaksekiCd.disabled}"></hx:commandExButton>
                                        <h:outputText styleClass="outputText" id="htmlName"></h:outputText></TD>
                                </TR>
								<TR>
									<TH nowrap class="v_e" width="200"><!-- 地域 --> <h:outputText
										styleClass="outputText" id="lblChiki"
										value="#{pc_Xrd00101.propChikiList.labelName}"></h:outputText></TH>
									<TD width="211"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlChikiList"
										value="#{pc_Xrd00101.propChikiList.value}"
										style="width:211px;">
										<f:selectItems value="#{pc_Xrd00101.propChikiList.list}" />
									</h:selectOneMenu></TD>
									<TH nowrap class="v_f" width="200"><!-- 詳細地区 --> <h:outputText
										styleClass="outputText" id="lblSyosaiChiku"
										value="#{pc_Xrd00101.propSyosaiChikuList.labelName}"></h:outputText></TH>
									<TD width="211"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlSyosaiChikuList"
										value="#{pc_Xrd00101.propSyosaiChikuList.value}"
										style="width:211px;">
										<f:selectItems value="#{pc_Xrd00101.propSyosaiChikuList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH nowrap class="v_g" width="200"><!-- 実習先都道府県 --> <h:outputText
										styleClass="outputText" id="lblJissyuSakiKen"
										value="#{pc_Xrd00101.propJissyuSakiKenList.labelName}"></h:outputText></TH>
									<TD width="685" colspan="3"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlJissyuSakiKenList"
										value="#{pc_Xrd00101.propJissyuSakiKenList.value}"
										style="width:211px;">
										<f:selectItems value="#{pc_Xrd00101.propJissyuSakiKenList.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH nowrap class="v_h" width="200"><!-- 実習先名称 --><h:outputText
										styleClass="outputText" id="lblJissyuSakiName"
										value="#{pc_Xrd00101.propJissyuSakiName.labelName}"></h:outputText></TH>
									<TD  width="211" style="border-right-style:none">
										<h:inputText
										styleClass="inputText" id="htmlJissyuSakiName"
										value="#{pc_Xrd00101.propJissyuSakiName.value}"
                                        maxlength="#{pc_Xrd00101.propJissyuSakiName.maxLength}"
										style="width:211px;" size="80" >
										</h:inputText>
									</TD>
									<TD width="211" colspan="2"  style="border-left-style:none;"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlIcchiJKRadioList"
										value="#{pc_Xrd00101.propIcchiJKRadioList.value}">
										<f:selectItems value="#{pc_Xrd00101.propIcchiJKRadioList.list}"/>
										</h:selectOneRadio>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_i" width="200"><!-- 実習期間 --> <h:outputText
										styleClass="outputText" id="lblJissyuKikan"
										value="#{pc_Xrd00101.proplblJissyuKikan.name}"></h:outputText></TH>
									<TD width="685" colspan="3"><h:inputText
										styleClass="inputText" id="htmlJissyuKikanFrom"
										value="#{pc_Xrd00101.propJissyuKikanFrom.dateValue}"
										size="10"
										onkeydown="onChangeData();">
									<f:convertDateTime />
									<hx:inputHelperDatePicker />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
									</h:inputText> ～ <h:inputText styleClass="inputText"
										id="htmlJissyuKikanTo"
										value="#{pc_Xrd00101.propJissyuKikanTo.dateValue}"
										size="10"
										onkeydown="onChangeData();">
									<f:convertDateTime />
									<hx:inputHelperDatePicker />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
									</h:inputText></TD>
								</TR>
                                <TR>
                                    <TH class="v_j" width="200"><!-- レポート教員コード --><h:outputText
                                        styleClass="outputText" id="lblRptKynCd"
                                        value="#{pc_Xrd00101.propRptKynCd.labelName}"
                                        style="#{pc_Xrd00101.propRptKynCd.labelStyle}"></h:outputText></TH>
                                    <TD width="685" colspan="3"><h:inputText styleClass="inputText"
                                        id="htmlRptKynCd" value="#{pc_Xrd00101.propRptKynCd.stringValue}"
                                        onblur="return doKyoinAjax(this, event, 'form1:htmlShimeiName');"
                                        style="#{pc_Xrd00101.propRptKynCd.style}"
                                        disabled="#{pc_Xrd00101.propRptKynCd.disabled}"
                                        maxlength="#{pc_Xrd00101.propRptKynCd.maxLength}" size="20">
                                    </h:inputText><hx:commandExButton type="button"
                                        styleClass="commandExButton_search" id="kyoinSearch"
                                        disabled="#{pc_Xrd00101.propRptKynCd.disabled}"
                                        onclick="return openKynCdWindow('form1:htmlRptKynCd');">
                                    </hx:commandExButton><h:outputText styleClass="outputText"
                                        id="htmlShimeiName"
                                        value="#{pc_Xrd00101.propRptShimeiName.stringValue}"
                                        style="#{pc_Xrd00101.propRptShimeiName.style}"></h:outputText></TD>
                                </TR>
							    <TR>
                                    <TH class="v_k" width="200"><!-- 日誌教員コード --><h:outputText
                                        styleClass="outputText" id="lblNissiKynCd"
                                        value="#{pc_Xrd00101.propNissiKynCd.labelName}"
                                        style="#{pc_Xrd00101.propNissiKynCd.labelStyle}"></h:outputText></TH>
                                    <TD width="685" colspan="3"><h:inputText styleClass="inputText"
                                        id="htmlNissiKynCd" value="#{pc_Xrd00101.propNissiKynCd.stringValue}"
                                        onblur="return doKyoinAjax(this, event,'form1:htmlNissiShimeiName');"
                                        style="#{pc_Xrd00101.propNissiKynCd.style}"
                                        disabled="#{pc_Xrd00101.propNissiKynCd.disabled}"
                                        maxlength="#{pc_Xrd00101.propNissiKynCd.maxLength}" size="20">
                                    </h:inputText><hx:commandExButton type="button"
                                        styleClass="commandExButton_search" id="nissiKyoinSearch"
                                        disabled="#{pc_Xrd00101.propNissiKynCd.disabled}"
                                        onclick="return openKynCdWindow('form1:htmlNissiKynCd');">
                                    </hx:commandExButton><h:outputText styleClass="outputText"
                                        id="htmlNissiShimeiName"
                                        value="#{pc_Xrd00101.propNissiShimeiName.stringValue}"
                                        style="#{pc_Xrd00101.propNissiShimeiName.style}"></h:outputText></TD>
                                </TR>
                                <TR>
                                	<TH class="v_l" width="200"><!-- 検索対象 --><h:outputText
                                		styleClass="outputText" id="lblOutputTaisyo"
                                		value="#{pc_Xrd00101.propOutputTaisyo.labelName}"
                                		style="#{pc_Xrd00101.propOutputTaisyo.labelStyle}">
                                		</h:outputText></TH>
                                	<TD width="211"><h:selectManyCheckbox 
                                		styleClass="selectManyCheckbox" id="htmlOutputTaisyo"
										value="#{pc_Xrd00101.propOutputTaisyo.value}"
										disabled="#{pc_Xrd00101.propOutputTaisyo.disabled}">
										<f:selectItems value="#{pc_Xrd00101.propOutputTaisyo.list}" />
									</h:selectManyCheckbox></TD>
                                	<TH class="v_m" width="200"><!-- 特定対象 --><h:outputText
                                		styleClass="outputText" id="lblTokuteiTaisyo"
                                		value="#{pc_Xrd00101.propTokuteiTaisyo.labelName}"
                                		style="#{pc_Xrd00101.propTokuteiTaisyo.labelStyle}">
                                		</h:outputText></TH>
                                	<TD width="211"><h:selectManyCheckbox 
                                		styleClass="selectManyCheckbox" id="htmlTokuteiTaisyo"
										value="#{pc_Xrd00101.propTokuteiTaisyo.value}"
										disabled="#{pc_Xrd00101.propTokuteiTaisyo.disabled}">
										<f:selectItems value="#{pc_Xrd00101.propTokuteiTaisyo.list}" />
									</h:selectManyCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						<!-- 優先順位 -->
						<BR>
						<TABLE border="0" class="table" width="885">
			              <TBODY>
			                <TR>
			                  <TH width="150" nowrap align="center"
			                    style="text-align:center;"><h:outputText
			                    styleClass="outputText" id="lblPriority" value="優先順位"></h:outputText></TH>
			                  <TH width="325" align="center" nowrap
			                    style="text-align:center;"><h:outputText
			                    styleClass="outputText" id="lblProject" value="項目"></h:outputText></TH>
			                  <TH width="175" nowrap align="center"
			                    style="text-align:center;"><h:outputText
			                    styleClass="outputText" id="lblOrder" value="ソート順"></h:outputText></TH>
			                </TR>
			                <TR>
			                  <TD width="150" nowrap align="center" style="text-align:center;"><h:outputText styleClass="outputText" id="lblPriorityOrder1" value="1"></h:outputText></TD>
			                  <TD width="325" nowrap><h:selectOneMenu
			                    styleClass="selectOneMenu"
			                    value="#{pc_Xrd00101.propPriorityProject1.stringValue}"
			                    disabled="#{pc_Xrd00101.propPriorityProject1.disabled}"
			                    id="htmlPriorityOrderMenu1" style="width:220px">
			                    <f:selectItems
			                      value="#{pc_Xrd00101.propPriorityProject1.list}" />
			                  </h:selectOneMenu></TD>
			                  <TD width="175" nowrap><h:selectOneRadio
			                    disabledClass="selectOneRadio_Disabled"
			                    styleClass="selectOneRadio" id="htmlPriorityOrder1"
			                    disabled="#{pc_Xrd00101.propPriorityOrder1.disabled}"
			                    value="#{pc_Xrd00101.propPriorityOrder1.stringValue}"
			                    style="#{pc_Xrd00101.propPriorityOrder1.style}">
			                    <f:selectItem itemValue="#{pc_Xrd00101.sortAsc}" itemLabel="昇順" />
			                    <f:selectItem itemValue="#{pc_Xrd00101.sortDesc}" itemLabel="降順" />
			                  </h:selectOneRadio></TD>
			                </TR>
			                <TR>
			                  <TD width="150" nowrap align="center" style="text-align:center;"><h:outputText styleClass="outputText" id="text6" value="2"></h:outputText></TD>
			                  <TD width="325" nowrap><h:selectOneMenu
			                    styleClass="selectOneMenu" id="htmlPriorityOrderMenu2"
			                    value="#{pc_Xrd00101.propPriorityProject2.stringValue}"
			                    disabled="#{pc_Xrd00101.propPriorityProject2.disabled}"
			                    style="width:220px">
			                    <f:selectItems
			                      value="#{pc_Xrd00101.propPriorityProject2.list}" />
			                  </h:selectOneMenu></TD>
			                  <TD width="175" nowrap><h:selectOneRadio
			                    disabledClass="selectOneRadio_Disabled"
			                    styleClass="selectOneRadio" id="htmlPriorityOrder2"
			                    disabled="#{pc_Xrd00101.propPriorityOrder2.disabled}"
			                    value="#{pc_Xrd00101.propPriorityOrder2.stringValue}"
			                    style="#{pc_Xrd00101.propPriorityOrder2.style}">
			                    <f:selectItem itemValue="#{pc_Xrd00101.sortAsc}" itemLabel="昇順" />
			                    <f:selectItem itemValue="#{pc_Xrd00101.sortDesc}" itemLabel="降順" />
			                  </h:selectOneRadio></TD>
			                </TR>
			              </TBODY>
			            </TABLE>
						<!-- ボタン -->
						<TABLE class="button_bar" width="885" align="center"
							cellspacing="0" cellpadding="0">
							<TR>
								<TD align="center"><hx:commandExButton type="submit" value="検索"
									styleClass="commandExButton_dat" id="search"
									action="#{pc_Xrd00101.doSearchAction}"
									onclick="return windowOnscroll();"></hx:commandExButton> <hx:commandExButton
									type="submit" value="クリア" styleClass="commandExButton_etc"
									id="clear" action="#{pc_Xrd00101.doClearAction}"
									onclick="return windowOnscroll();"></hx:commandExButton></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="885">
							<TR>
								<TD align="center">
								<TABLE width="870">
									<TR>
										<TD align="left" width="50%"></TD>
										<TD align="right" width="50%"><h:outputText
											styleClass="outputText" id="lblCount"
											value="#{pc_Xrd00101.propjissyuList.listCount}件"></h:outputText>
									</TR>
								</TABLE>
								</TD>
							</TR>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="885">
							<TR>
								<TD>
								<div class="listScroll" id="listScroll" style="height: 263px"
									onscroll="setScrollPosition('htmlHidScroll', this);">
								<TABLE width="875" class="meisai_scroll">
									<TR>
										<TH class="headerClass" align="center" rowspan="2" width="35"><h:outputText
											styleClass="outputText" value="登録区分" id="lblTorokKbn_head"></h:outputText></TH>
										<TH class="headerClass" align="center" rowspan="2" width="40"><h:outputText
											styleClass="outputText" value="実習年度" id="lblJissyuNendo_head"></h:outputText></TH>
										<TH class="headerClass" align="center" rowspan="2" width="35"><h:outputText
											styleClass="outputText" value="校種区分" id="lblKosyuKbnName_head"></h:outputText></TH>
										<TH class="headerClass" align="center" rowspan="2" width="40"><h:outputText
											styleClass="outputText" value="回数" id="lblJissyuKaisu_head"></h:outputText></TH>
										<TH class="headerClass" align="center" rowspan="2" width="80"><h:outputText
											id="lblGaksekiCd_head" styleClass="outputText" value="学籍番号"></h:outputText></TH>
										<TH class="headerClass" align="center" rowspan="2" width="100"><h:outputText
											styleClass="outputText" value="氏名" id="lblGakseiName_head"></h:outputText></TH>
										<TH class="headerClass" colspan="2" align="center" width="60"><h:outputText
											styleClass="outputText" value="実習先都道府県" id="lblJissyuKen_head"></h:outputText></TH>
										<TH class="headerClass" colspan="2" align="center" width="155"><h:outputText
											styleClass="outputText" value="実習先名称" id="lblJissyuName_head"></h:outputText></TH>
										<TH class="headerClass" colspan="2" align="center" width="140"><h:outputText
											styleClass="outputText" value="実習期間" id="lblJissyuKikan_head"></h:outputText></TH>
										<TH class="headerClass" colspan="2" align="center" width="100"><h:outputText
											styleClass="outputText" value="レポート添削教員名" id="lblRptKyin_head"></h:outputText></TH>
										<TH class="headerClass" colspan="2" align="center" width="50"><h:outputText
											styleClass="outputText" value="評価" id="lblHyoka_head"></h:outputText></TH>
										<TH class="headerClass" align="center" rowspan="2" width="35"><h:outputText
											styleClass="outputText" id="lblButton_select_head"></h:outputText></TH>
											
									</TR>
								</TABLE>
								<h:dataTable border="1" cellpadding="2" cellspacing="0"
									columnClasses="columnClass1" headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Xrd00101.propjissyuList.rowClasses}"
									styleClass="meisai_scroll" id="htmlJissyuList"
									value="#{pc_Xrd00101.propjissyuList.list}" 
									var="varlist">
									<h:column id="column1">
										<h:outputText styleClass="outputText" id="lblTorokKbn_list"
											title="#{varlist.torokKbnName.value}"
											value="#{varlist.torokKbnName.displayValue}"></h:outputText>
										<f:attribute value="36" name="width" />
									</h:column>
									<h:column id="column2">
										<f:attribute value="40" name="width" />
										<h:outputText styleClass="outputText"
											id="lblJissyuNendo_list"
											value="#{varlist.jissyuNendo}"></h:outputText>
									</h:column>
									<h:column id="column3">
										<f:attribute value="37" name="width" />
										<h:outputText styleClass="outputText"
											id="lblKosyuKbnName_list"
											title="#{varlist.kosyuKbnName.value}"
											value="#{varlist.kosyuKbnName.displayValue}"></h:outputText>
									</h:column>			
									<h:column id="column4">
										<h:outputText styleClass="outputText" id="lblJissyuKaisu_list"
											value="#{varlist.jissyuKaisu}"></h:outputText>
										<f:attribute value="43" name="width" />
									</h:column>
									<h:column id="column5">
										<h:outputText styleClass="outputText" id="lblGaksekiCd_list"
											value="#{varlist.gaksekiCd}"></h:outputText>
										<f:attribute value="83" name="width" />
									</h:column>
									<h:column id="column6">
										<h:outputText styleClass="outputText" id="lblGakseiName_list"
											title="#{varlist.gakuseiName.value}"
											value="#{varlist.gakuseiName.displayValue}"></h:outputText>
										<f:attribute value="104" name="width" />
									</h:column>
									<h:column id="column7">
										<h:outputText styleClass="outputText" id="lblJissyusakiKenName_list"
											title="#{varlist.jissyusakiKenName.value}"
											value="#{varlist.jissyusakiKenName.displayValue}"></h:outputText>
										<f:attribute value="62" name="width" />
									</h:column>
									<h:column id="column8">
										<h:outputText styleClass="outputText" id="lblJissyusakiName_list"
											title="#{varlist.jissyusakiName.value}"
											value="#{varlist.jissyusakiName.displayValue}"></h:outputText>
										<f:attribute value="160" name="width" />
									</h:column>
									<h:column id="column9">
										<h:outputText styleClass="outputText" id="lblJissyusakiKikan_list"
											value="#{varlist.jissyusakiKikan}">
										</h:outputText>
										<f:attribute value="148" name="width" />
									</h:column>
									<h:column id="column10">
										<h:outputText styleClass="outputText" id="lblRptTnskKyoinName_list"
											title="#{varlist.rptTnskKyoinName.value}"
											value="#{varlist.rptTnskKyoinName.displayValue}"></h:outputText>
										<f:attribute value="104" name="width" />
									</h:column>
									<h:column id="column11">
										<h:outputText styleClass="outputText" id="lblHyokaCd_list"
											value="#{varlist.hyokaCd}">
										</h:outputText>
										<f:attribute value="50" name="width" />
									</h:column>
									<h:column id="column12">
										<f:attribute value="30" name="width" />
										<hx:commandExButton type="submit" value="編集"
											styleClass="commandExButton" id="edit"
											action="#{pc_Xrd00101.doEditAction}"></hx:commandExButton>
									</h:column>
								</h:dataTable>
								</div>
								</TD>
							</TR>
							<TR>
								<TD></TD>
							</TR>
						</TABLE>
						<!-- CSV作成ボタン --><!-- 出力項目指定ボタン --> 
						<TABLE cellspacing="1" cellpadding="1" class="button_bar" width="810">
							<TR>
								<TD align="center">
									<hx:commandExButton	type="submit" styleClass="commandExButton_out"
										id="btnCsvOut" value="CSV作成"
										action="#{pc_Xrd00101.doCsvOutAction}"
										confirm="#{msg.SY_MSG_0020W}"
										disabled="#{pc_Xrd00101.propCsvoutBtn.disabled}"
										onclick="return windowOnscroll();"></hx:commandExButton>
									<hx:commandExButton	type="submit" styleClass="commandExButton_out"
										id="setoutput" value="出力項目指定" 
										action="#{pc_Xrd00101.doSetoutputAction}"
										disabled="#{pc_Xrd00101.propSetOutput.disabled}"
										onclick="return windowOnscroll();"></hx:commandExButton>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		</DIV>
		</DIV>
		</DIV>
		<!-- フッダーインクルード -->
		<jsp:include page="../inc/footer.jsp" />
		<h:inputHidden id="htmlHidScroll" value="#{pc_Xrd00101.propjissyuList.scrollPosition}"></h:inputHidden>
		<h:inputHidden id="htmlHidAction" value="#{pc_Xrd00101.propHidAction.stringValue}"></h:inputHidden>
		<h:inputHidden id="htmlScrollPosition" value="#{pc_Xrd00101.propScrollPosition.stringValue}"></h:inputHidden>
	
	</h:form>
	</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
	</f:view>

<SCRIPT LANGUAGE="JavaScript">
  window.attachEvent('onload', endload);

  function endload() {
    changeScrollPosition('htmlHidScroll', 'listScroll');
  }
</SCRIPT>

</HTML>

