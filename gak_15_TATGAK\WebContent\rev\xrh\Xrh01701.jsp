<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

	// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}
	
	// 学生氏名を取得する
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	//ページ初期化
	function reloadPage( thisEvent ){
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), thisEvent, 'form1:lblName');
	}
	
		//チェックボックスAll On
	function fncCheckAll(thisObj, thisEvent) {
		check('htmlList','htmlListCheck');
	}

	//チェックボックスAll Off
	function fncCheckNone(thisObj, thisEvent) {
		uncheck('htmlList','htmlListCheck');
	}
	


	// データ変更時
	function onChangeData() {
		document.getElementById("form1:htmlHidChangeDataFlg").value = 1;
	}



	function confirmOk() {
		document.getElementById('form1:htmlButtonKind').value = 1;
		indirectClick('release');
	}

	function confirmCancel() {
		document.getElementById('form1:htmlButtonKind').value = 9;
		indirectClick('release');
	}




</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="reloadPage(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton
					type="submit"
					value="閉じる"
					styleClass="commandExButton"
					id="closeDisp"
					action="#{pc_Xrh01701.doCloseDispAction}"
				>
				</hx:commandExButton>
				<h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01701.screenName}"></h:outputText>
			</div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" >
						<TBODY>
							<TR>
							<TD>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblNendo"
												value="#{pc_Xrh01701.propNendo.labelName}" 
												style="#{pc_Xrh01701.propNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="180">
											<h:inputText 
												id="htmlNendo" 
												styleClass="inputText" 
												readonly="#{pc_Xrh01701.propNendo.readonly}" 
												value="#{pc_Xrh01701.propNendo.dateValue}"
												disabled="#{pc_Xrh01701.propNendo.disabled}" 
												tabindex="1" size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
										<TH class="v_a" width="150">
											<h:outputText 
												styleClass="outputText"
												id="lblkamokSikenCnt"
												value="#{pc_Xrh01701.propKamokSikenCnt.labelName}"
												style="#{pc_Xrh01701.propKamokSikenCnt.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="200">
											<h:inputText 
												id="htmlKamokSikenCnt" 
												styleClass="inputText" 
												readonly="#{pc_Xrh01701.propKamokSikenCnt.readonly}" 
												value="#{pc_Xrh01701.propKamokSikenCnt.integerValue}"
												disabled="#{pc_Xrh01701.propKamokSikenCnt.disabled}" 
												tabindex="2" size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
								    			imeMode="inactive" promptCharacter="_" />
												<f:convertNumber type="number" pattern="#0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText>
											<h:outputText value="回" />
										</TD>
										<TD rowspan="5" width="100" align="right"
											style="background-color: transparent; text-align: right"
											class="clear_border">
											<hx:commandExButton type="submit"
												value="選択"
												styleClass="commandExButton" id="select"
												disabled="#{pc_Xrh01701.propSelect.disabled}"
												rendered="#{pc_Xrh01701.propSelect.rendered}"
												style="#{pc_Xrh01701.propSelect.style}"
												action="#{pc_Xrh01701.doSelectAction}"
												tabindex="5">
											</hx:commandExButton> 
											<hx:commandExButton type="submit"
												value="解除"
												styleClass="commandExButton" id="release"
												disabled="#{pc_Xrh01701.propRelease.disabled}"
												rendered="#{pc_Xrh01701.propRelease.rendered}"
												style="#{pc_Xrh01701.propRelease.style}"
												action="#{pc_Xrh01701.doReleaseAction}"
												tabindex="6">
											</hx:commandExButton>
										</TD>
									</TR>
									<TR>
						            	<TH nowrap class="v_a" width="150">
											<!--学籍番号 -->
						                	<h:outputText 
						                		styleClass="outputText" 
						                		id="lblGakusekiCd"
						                		value="#{pc_Xrh01701.propGakusekiCd.labelName}"
						                		style="#{pc_Xrh01701.propGakusekiCd.labelStyle}">
						                	</h:outputText>
						              	</TH>
						              	<TD colspan="3">
											<h:inputText id="htmlGakusekiCd" styleClass="inputText" 
												readonly="#{pc_Xrh01701.propGakusekiCd.readonly}" 
												disabled="#{pc_Xrh01701.propGakusekiCd.disabled}"
												maxlength="#{pc_Xrh01701.propGakusekiCd.maxLength}"
												size="10"
												tabindex="3"
												value="#{pc_Xrh01701.propGakusekiCd.stringValue}"
												onblur="return doGakuseiAjax(this, event, 'form1:lblName');">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="search"
												disabled="#{pc_Xrh01701.propSearch.disabled}"
												rendered="#{pc_Xrh01701.propSearch.rendered}"
												style="#{pc_Xrh01701.propSearch.style}"
												onclick="openSubWindow('form1:htmlGakusekiCd');" tabindex="4">
											</hx:commandExButton>
											<h:outputText
		              							styleClass="outputText"
		              							id="lblName"
		              							value="#{pc_Xrh01701.propName.stringValue}">
		              						</h:outputText>
		              					</TD>
		              				</TR>
								</TBODY>
								</TABLE>
								
								<TABLE border="0" style="margin-top:16px">
								<TBODY>
									<TR>
										<TD align="right">
											<h:outputText styleClass="outputText"
												id="lblListCnt"
												value="#{pc_Xrh01701.propList.listCount}"
												style="#{pc_Xrh01701.propListCnt.labelStyle}">
											</h:outputText>
											<h:outputText
												styleClass="outputText" id="lblCount" value="件">
											</h:outputText>
										</TD>
									</TR>
									<TR>
									<TD>
									<DIV id="listScroll" class="listScroll" style="height: 256px;">
										<h:dataTable 
											columnClasses="columnClass" 
											headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Xrh01701.propList.rowClasses}"
											styleClass="meisai_scroll" id="htmlList"
											value="#{pc_Xrh01701.propList.list}" var="varlist">
											
											<h:column id="column1">
												<f:facet name="header">
												</f:facet>
												<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlListCheck"
													disabled="#{pc_Xrh01701.propListCheck.disabled}"
													readonly="#{pc_Xrh01701.propListCheck.readonly}"
													rendered="#{pc_Xrh01701.propListCheck.rendered}"
													style="#{pc_Xrh01701.propListCheck.style}"
													value="#{varlist.listSel}">
												</h:selectBooleanCheckbox>
												<f:attribute value="32" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="試験日曜日"
														id="lblListSikenbiYobi">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListSikenbiYobi"
													style="#{pc_Xrh01701.propListSikenbiYobi.style}"
													value="#{varlist.listSikenbiYobiStr}">
												</h:outputText>
												<f:attribute value="80" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="試験日"
														id="lblListSikenDate">
													</h:outputText>
												</f:facet>
												<h:outputText 
													styleClass="outputText" 
													id="htmlListSikenDate"
													style="#{pc_Xrh01701.propListSikenDate.style}"
													value="#{varlist.listSikenBi}">
												<f:convertDateTime />
											</h:outputText>
												<f:attribute value="80" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="科目コード"
														id="lblListKamokCd">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListKamokCd"
													style="#{pc_Xrh01701.propListKamokCd.style}"
													value="#{varlist.listKamokuCd}">
												</h:outputText>
												<f:attribute value="80" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column5">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="科目名称"
														id="lblListKamokNm">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListKamokNm"
													value="#{varlist.listKamokuName.displayValue}"
													title="#{varlist.listKamokuName.stringValue}">
												</h:outputText>
												<f:attribute value="206" name="width" />
											</h:column>
											
											<h:column id="column6">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="失効理由"
														id="lblListSikkoriyu">
													</h:outputText>
												</f:facet>
											<h:outputText styleClass="outputText" id="htmlListSikkoriyu"
												value="#{varlist.listSikkoRiyuStr.displayValue}"
												title="#{varlist.listSikkoRiyuStr.stringValue}">
											</h:outputText>
											<f:attribute value="206" name="width" />
											<f:attribute value="text-align: left" name="style" />
											</h:column>
											
											<h:column id="column7">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="失効日"
														id="lblListSikkoDate">
													</h:outputText>
												</f:facet>
											<h:outputText styleClass="outputText" id="htmlListSikkoDate"
												value="#{varlist.listSikkoBi}">
												<f:convertDateTime />
											</h:outputText>
											<f:attribute value="92" name="width" />
											<f:attribute value="text-align: center" name="style" />
											</h:column>
										</h:dataTable>
									</DIV>
									</TD>
									</TR>
									<TR>
									<TD align="left">
										<hx:commandExButton type="button"
									 		styleClass="check" id="btnCheck"
									 		disabled="#{pc_Xrh01701.propAllCheck.disabled}"
											onclick="return fncCheckAll(this, event);" tabindex="7">
										</hx:commandExButton> 
										<hx:commandExButton
											type="button" styleClass="uncheck" id="btnUnCheck"
											disabled="#{pc_Xrh01701.propAllUnCheck.disabled}"
											onclick="return fncCheckNone(this, event);" tabindex="8">
										</hx:commandExButton>
									</TD>
									</TR>
								</TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="失効理由">
											</h:outputText>
										</TH>
										<TD width="640">
											<h:selectOneMenu 
												styleClass="selectOneMenu" 
												id="htmlSikkoCd"
												value="#{pc_Xrh01701.propSikkoriyu.stringValue}"
												style="#{pc_Xrh01701.propSikkoriyu.style};width:300px"
												disabled="#{pc_Xrh01701.propSikkoriyu.disabled}"
												onchange="onChangeData();"
												tabindex="9">
												<f:selectItems 
													value="#{pc_Xrh01701.propSikkoriyu.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="失効日">
											</h:outputText>
										</TH>
										<TD width="640">
											<h:inputText 
												id="htmlSikkoDate"
												styleClass="inputText" 
												size="12"
												value="#{pc_Xrh01701.propSikkoDate.dateValue}"
												disabled="#{pc_Xrh01701.propSikkoDate.disabled}"
												onblur="onChangeData();"
												tabindex="10">
												<f:convertDateTime />
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE width="700" border="0" cellpadding="0" cellspacing="0" 
									class="button_bar">
									<TBODY>
										<TR>
											<TD>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="register"
													value="確定"
													action="#{pc_Xrh01701.doRegisterAction}"
													disabled="#{pc_Xrh01701.propRegister.disabled}"
													rendered="#{pc_Xrh01701.propRegister.rendered}"
													style="#{pc_Xrh01701.propRegister.style}"
													confirm="#{msg.SY_MSG_0002W}"
													tabindex="11">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
							</TR>
						</TBODY>
						</TABLE>
						
				</DIV>
			</DIV>
			
		</DIV>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	
			<h:inputHidden id="htmlHidChangeDataFlg"
				value="#{pc_Xrh01701.propHidChangeDataFlg.integerValue}">
			</h:inputHidden>
	
			<h:inputHidden
				id="htmlButtonKind"
				value="#{pc_Xrh01701.propButtonKind.integerValue}">
			</h:inputHidden>
	
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
				