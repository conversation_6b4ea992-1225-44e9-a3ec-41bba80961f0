<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz03502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz03502.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
	// 確認メッセージでOKが押下された場合の処理
	function confirmOk() {
		document.getElementById('form1:executable').value = 1;
		indirectClick('exec');
	}

	// 確認メッセージでキャンセルが押下された場合の処理
	function confirmCancel() {
		// ボタン押下フラグをクリアする
		document.getElementById('form1:executable').value = 0;
	}
	
	function func_1(thisObj, thisEvent) {
		check('htmlMendanList', 'htmlListCheck');
	}

	function func_2(thisObj, thisEvent) {
		uncheck('htmlMendanList', 'htmlListCheck');
	}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz03502.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz03502.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz03502.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz03502.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
					<hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp" action="#{pc_Ssz03502.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			
			<TABLE width="100%" align="center">
				<TR>
					<TD>
						<TABLE width="583" style="margin-top:30px">
							<TBODY>
								<TR>
									<TD width="300">
									<TABLE border="0" class="table" cellspacing="0" cellpadding="0" width="100%">
										<TBODY>
											<TR>
												<TH class="v_b" width="120"><h:outputText
													styleClass="outputText" id="lblSearchNendo"
													value="#{pc_Ssz03502.propSearchNendo.labelName}"
													style="#{pc_Ssz03502.propSearchNendo.labelStyle}"></h:outputText></TH>
												<TD class="v_e" width="180"><h:inputText
													id="htmlSearchNendo"
													value="#{pc_Ssz03502.propSearchNendo.dateValue}"
													style="#{pc_Ssz03502.propSearchNendo.style}"
													styleClass="inputText" size="3">
													<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="inactive" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
													</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="283">
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%" style="margin-top:5px">
							<TBODY>
								<TR>
									<TD align="center" width="100%"><hx:commandExButton type="submit"
										value="検索" styleClass="commandExButton_dat" id="search"
										action="#{pc_Ssz03502.doSearchAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE width="583">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText
										styleClass="outputText" id="htmlCount"
										value="#{pc_Ssz03502.propMendanList.listCount}"></h:outputText><h:outputText
										styleClass="outputText" id="text8" value="件"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE width="560">
							<TR>
								<TD>
								<div id="listScroll" class="listScroll"
									style="height:230px;"
									onscroll="setScrollPosition('scroll',this);"><h:dataTable
									border="0" cellpadding="2" cellspacing="0"
									headerClass="headerClass" footerClass="footerClass"
									rowClasses="#{pc_Ssz03502.propMendanList.rowClasses}"
									styleClass="meisai_scroll" id="htmlMendanList" width="560"
									value="#{pc_Ssz03502.propMendanList.list}" var="varlist">
									<h:column id="column1">
										<f:facet name="header"></f:facet>
										<f:attribute value="30" name="width" />
										<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
											id="htmlListCheck" value="#{varlist.selected}"
											rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
									</h:column>
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="面談年度" id="text2"></h:outputText>
										</f:facet>
										<f:attribute value="72" name="width" />
										<h:outputText styleClass="outputText" id="text6"
											value="#{varlist.mendanNendo}"></h:outputText>
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText id="text1" styleClass="outputText" value="コード"></h:outputText>
										</f:facet>
										<f:attribute value="62" name="width" />
										<h:outputText styleClass="outputText" id="text5"
											value="#{varlist.mendanCd}"></h:outputText>
										<f:attribute value="text-align: left; vertical-align: middle"
											name="style" />
									</h:column>
									<h:column id="column4">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="内容" id="text3"></h:outputText>
										</f:facet>
										<f:attribute value="396" name="width" />
										<h:outputText styleClass="outputText" id="text7"
											value="#{varlist.mokuteout.displayValue}"
											title="#{varlist.mokuteout.value}"></h:outputText>
									</h:column>
								</h:dataTable></div>
								</TD>
							</TR>
							<TR>
								<TD align="left">
								<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
									<TBODY>
										<TR>
											<TD><hx:jspPanel id="jspPanel1">
												<hx:commandExButton type="submit" value="全選択"
													styleClass="check" id="checkAll"
													onclick="return func_1(this, event);" style="height:20px;"></hx:commandExButton>
		
												<hx:commandExButton type="submit" value="全解除"
													styleClass="uncheck" id="uncheckAll"
													onclick="return func_2(this, event);" style="height:20px;"></hx:commandExButton>
											</hx:jspPanel></TD>
										</TR>
									</TBODY>
								</TABLE>
								</TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE width="583">
							<TBODY>
								<TR>
									<TD width="300">
									<TABLE border="0" class="table" cellspacing="0" cellpadding="0"
									width="100%" style="margin-top:15px">
										<TBODY>
											<TR>
												<TH class="v_b" width="120"><h:outputText
													styleClass="outputText" id="lblMotoNendo"
													value="#{pc_Ssz03502.propMotoNendo.labelName}"
													style="#{pc_Ssz03502.propMotoNendo.labelStyle}"></h:outputText></TH>
												<TD class="v_e" width="180"><h:outputText
													id="htmlMotoNendo" styleClass="outputText"
													value="#{pc_Ssz03502.propMotoNendo.stringValue}"
													style="#{pc_Ssz03502.propMotoNendo.style}">
													</h:outputText></TD>
											</TR>
											<TR>
												<TH class="v_b" width="120"><h:outputText
													styleClass="outputText" id="lblSakiNendo"
													value="#{pc_Ssz03502.propSakiNendo.labelName}"
													style="#{pc_Ssz03502.propSakiNendo.labelStyle}"></h:outputText></TH>
												<TD class="v_e" width="180"><h:inputText
													id="htmlSakiNendo"
													value="#{pc_Ssz03502.propSakiNendo.dateValue}"
													style="#{pc_Ssz03502.propSakiNendo.style}"
													styleClass="inputText" size="3">
													<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="inactive" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
													</h:inputText></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" 
						width="100%" style="margin-top:30px">
							<TBODY>
								<TR>
									<TD align="center" width="100%"><hx:commandExButton type="submit"
										value="実行" styleClass="commandExButton_dat" id="exec"
										action="#{pc_Ssz03502.doExecAction}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz03502.propMendanList.scrollPosition}"
				id="scroll"></h:inputHidden>
			<h:inputHidden id="executable"
				value="#{pc_Ssz03502.propExecutable.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

