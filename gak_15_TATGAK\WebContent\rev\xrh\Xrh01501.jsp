<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh01501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01501.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">

				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
					<TBODY>
			           <TR>
			              <TH nowrap class="v_a" width="190">
			              		<!--年度 -->
			                	<h:outputText styleClass="outputText" id="lblNendo"
			                		value="#{pc_Xrh01501.propNendo.labelName}"
			                		style="#{pc_Xrh01501.propNendo.labelStyle}">
			                	</h:outputText>
			              </TH>
			              <TD>
			              		<h:inputText styleClass="inputText"
			                		id="htmlNendo" size="8"
			                		value="#{pc_Xrh01501.propNendo.dateValue}"
			                		style="#{pc_Xrh01501.propNendo.style}" tabindex="1">
			                		<hx:inputHelperAssist errorClass="inputText_Error"
						    		imeMode="inactive" promptCharacter="_" />
									<f:convertDateTime pattern="yyyy" />
			                	</h:inputText>
			              </TD>
			          </TR>
			          
			          <TR>
			          	 <TH class="v_a" width="190">
								<h:outputText styleClass="outputText" id="lblKamokSikenCnt"
									value="#{pc_Xrh01501.propKamokSikenCnt.labelName}" 
									style="#{pc_Xrh01501.propKamokSikenCnt.labelStyle}">
								</h:outputText>
						 </TH>
						 <TD width="500">
								<h:inputText id="htmlShikenCount"
									styleClass="inputText"
									style="#{pc_Xrh01501.propKamokSikenCnt.style}"
									value="#{pc_Xrh01501.propKamokSikenCnt.integerValue}"
									maxlength="#{pc_Xrh01501.propKamokSikenCnt.maxLength}"
									disabled="#{pc_Xrh01501.propKamokSikenCnt.disabled}" size="4"
									tabindex="2">
									<f:convertNumber type="number" pattern="#0" />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
								</h:inputText>
								回
					     </TD>
					   </TR>      	
					   
					 
					  
					  <TR>
						 <TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblSyoriKbn"
								value="#{pc_Xrh01501.propSyoriKbn.labelName}">
							</h:outputText>
						 </TH>
						 <TD>
							<h:selectBooleanCheckbox 
								styleClass="selectBooleanCheckbox"
                                id="htmlSyoriKbn" 
                                value="#{pc_Xrh01501.propSyoriKbn.checked}" tabindex="3">
                            </h:selectBooleanCheckbox>
                            チェックのみ(データの登録／更新は行いません)
						 </TD>	
					  </TR>
					  
					  <TR>
							<TH class="v_a">
								<h:outputText styleClass="outputText"
									value="チェックリスト出力指定">
								</h:outputText>
							</TH>
							<TD>
								<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlNorData" 
                                	value="#{pc_Xrh01501.propNorData.checked}" tabindex="4">
                            	</h:selectBooleanCheckbox>正常データ
                            	<BR>
								<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlErrData" 
                                	value="#{pc_Xrh01501.propErrData.checked}" tabindex="5">
                            	</h:selectBooleanCheckbox>エラーデータ
                            	<BR>
                            	<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlWarData" 
                                	value="#{pc_Xrh01501.propWarData.checked}" tabindex="6">
                            	</h:selectBooleanCheckbox>ワーニングデータ
							</TD>
					 </TR>
				  </TBODY>
				</TABLE>
				
				<BR>
				
				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
					
							<TD>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="exec"
									value="実行" tabindex="7"
									action="#{pc_Xrh01501.doExecAction}"
									confirm="#{msg.SY_MSG_0001W}"
									disabled="#{pc_Xrh01501.propExec.disabled}"
									style="#{pc_Xrh01501.propExec.style}">
								</hx:commandExButton>
		
							</TD>	
								
						</TR>
					</TBODY>
				</TABLE>
				
				</DIV>
			</DIV>
			
		</DIV>
		</h:form>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
