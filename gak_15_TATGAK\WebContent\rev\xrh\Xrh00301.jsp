<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">


<SCRIPT type="text/javascript">


function confirmOk() {
	var phase;
	phase = parseInt(document.getElementById('form1:htmlBackPhase').value);
	if (phase == 0) {
		document.getElementById('form1:htmlBackPhase').value = 1;
		indirectClick('register');
	}
}

function confirmCancel() {
	document.getElementById('form1:htmlBackPhase').value = 9;
	indirectClick('register');
}


</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00301.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここに戻るボタンを配置 -->
				<!-- ↑ここに戻るボタンを配置 -->
			</DIV>

			<!--↓content↓-->
			<DIV id="content">
				<DIV class="column" align="center">
				
				
					<TABLE border="0" cellpadding="5">
					<TBODY>
						<TR>
							<!-- ↓データテーブル部↓ -->
							<TD>
								<DIV id="listScroll" class="listScroll" style="height: 128px;"
									onscroll="setScrollPosition('scroll',this);">
									<h:dataTable
										columnClasses="columnClass" headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_Xrh00301.propTimeList.rowClasses}"
										styleClass="meisai_scroll" id="htmlTimeList"
										value="#{pc_Xrh00301.propTimeList.list}" var="varlist">
									
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="対象業務"
													id="lblListWorkColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListWork"
												value="#{varlist.listGymName}"
												style="#{pc_Xrh00301.propListWork.style}">
											</h:outputText>
											<f:attribute value="192" name="width" />
										</h:column>
										
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="時間割コード"
													id="lblListTimeCdColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListTimeCd"
												value="#{varlist.listPtnCd}"
												style="#{pc_Xrh00301.propListTimeCd.style}">
											</h:outputText>
											<f:attribute value="128" name="width" />
										</h:column>
										
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="時間割名称"
													id="lblListTimeNmColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListTimeNm"
												value="#{varlist.listPtnName.displayValue}"
												title="#{varlist.listPtnName.stringValue}"
												style="#{pc_Xrh00301.propListTimeNm.style}">
											</h:outputText>
											<f:attribute value="350" name="width" />
										</h:column>
										
										<h:column id="column4">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit"
												value="選択"
												styleClass="commandExButton" id="edit"
												action="#{pc_Xrh00301.doEditAction}">
											</hx:commandExButton>

											<f:attribute value="30" name="width" />
										</h:column>
									</h:dataTable>	
								</DIV>
							</TD>
						</TR>
					</TBODY>
					</TABLE>
					
					<BR>
					
					<TABLE border="0" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
						<TD align="left">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
							<TR>
								<TH class="v_a" width="150">
	                            	<h:outputText 
	                                	styleClass="outputText" id="lblWork"
	                                	style="#{pc_Xrh00301.propWork.labelStyle}"
	                                	value="#{pc_Xrh00301.propWork.labelName}">
	                            	</h:outputText>
	                            </TH>
	                            <TD>
	              					<h:selectOneMenu styleClass="selectOneMenu" id="htmlWork"
										value="#{pc_Xrh00301.propWork.stringValue}"
										style="#{pc_Xrh00301.propWork.style};width:256px"
										disabled="#{pc_Xrh00301.propWork.disabled}">
										<f:selectItems value="#{pc_Xrh00301.propWork.list}" />
									</h:selectOneMenu>
			              		</TD>					
							</TR>
							<TR>
	                        	<TH class="v_a" width="150">
	                             	<h:outputText 
	                                	styleClass="outputText" id="lblTimeCd"
	                                    style="#{pc_Xrh00301.propTimeCd.labelStyle}"
	                                    value="#{pc_Xrh00301.propTimeCd.labelName}">
	                                </h:outputText>
	                       		</TH>
	                            <TD width="200">
	                            	<h:inputText
	                                	id="htmlTimeCd" styleClass="inputText" 
	                                    value="#{pc_Xrh00301.propTimeCd.stringValue}"
	                                    readonly="#{pc_Xrh00301.propTimeCd.readonly}"
	                                    disabled="#{pc_Xrh00301.propTimeCd.disabled}"
										maxlength="#{pc_Xrh00301.propTimeCd.maxLength}"
	                                    style="#{pc_Xrh00301.propTimeCd.style}" size="3">
	                                </h:inputText>
	                            </TD>
							</TR>
							<TR>
	                        	<TH class="v_a" width="150">
	                             	<h:outputText 
	                                	styleClass="outputText" id="lblTimeNm"
	                                    style="#{pc_Xrh00301.propTimeNm.labelStyle}"
	                                    value="#{pc_Xrh00301.propTimeNm.labelName}">
	                                </h:outputText>
	                       		</TH>
	                            <TD width="370">
	                            	<h:inputText
	                                	id="htmlTimeNm" styleClass="inputText" 
	                                    size="50" 
	                                    value="#{pc_Xrh00301.propTimeNm.stringValue}"
	                                    readonly="#{pc_Xrh00301.propTimeNm.readonly}"
	                                    disabled="#{pc_Xrh00301.propTimeNm.disabled}"
										maxlength="#{pc_Xrh00301.propTimeNm.maxLength}" size="50"
	                                    style="#{pc_Xrh00301.propTimeNm.style}">
	                                </h:inputText>
	                            </TD>
							</TR>
							</TBODY>
							</TABLE>
							
							<BR>
							
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 1時限">
		                            	</h:outputText>
	                            	</TH>
	                            	<TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime1From"
	                                    	style="#{pc_Xrh00301.propTime1From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime1From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime1From" size="8"
						                	value="#{pc_Xrh00301.propTime1From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime1From.readonly}"
						                	style="#{pc_Xrh00301.propTime1From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime1To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime1To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime1To" size="8"
						                	value="#{pc_Xrh00301.propTime1To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime1To.readonly}"
						                	style="#{pc_Xrh00301.propTime1To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
			              			</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 2時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime2From"
	                                    	style="#{pc_Xrh00301.propTime2From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime2From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime2From" size="8"
						                	value="#{pc_Xrh00301.propTime2From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime2From.readonly}"
						                	style="#{pc_Xrh00301.propTime2From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime2To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime2To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime2To" size="8"
						                	value="#{pc_Xrh00301.propTime2To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime2To.readonly}"
						                	style="#{pc_Xrh00301.propTime2To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 3時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime3From"
	                                    	style="#{pc_Xrh00301.propTime3From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime3From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime3From" size="8"
						                	value="#{pc_Xrh00301.propTime3From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime3From.readonly}"
						                	style="#{pc_Xrh00301.propTime3From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime3To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime3To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime3To" size="8"
						                	value="#{pc_Xrh00301.propTime3To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime3To.readonly}"
						                	style="#{pc_Xrh00301.propTime3To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 4時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime4From"
	                                    	style="#{pc_Xrh00301.propTime4From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime4From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime4From" size="8"
						                	value="#{pc_Xrh00301.propTime4From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime4From.readonly}"
						                	style="#{pc_Xrh00301.propTime4From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime4To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime4To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime4To" size="8"
						                	value="#{pc_Xrh00301.propTime4To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime4To.readonly}"
						                	style="#{pc_Xrh00301.propTime4To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 5時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime5From"
	                                    	style="#{pc_Xrh00301.propTime5From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime5From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime5From" size="8"
						                	value="#{pc_Xrh00301.propTime5From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime5From.readonly}"
						                	style="#{pc_Xrh00301.propTime5From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime5To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime5To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime5To" size="8"
						                	value="#{pc_Xrh00301.propTime5To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime5To.readonly}"
						                	style="#{pc_Xrh00301.propTime5To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 6時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime6From"
	                                    	style="#{pc_Xrh00301.propTime6From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime6From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime6From" size="8"
						                	value="#{pc_Xrh00301.propTime6From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime6From.readonly}"
						                	style="#{pc_Xrh00301.propTime6From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime6To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime6To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime6To" size="8"
						                	value="#{pc_Xrh00301.propTime6To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime6To.readonly}"
						                	style="#{pc_Xrh00301.propTime6To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 7時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime7From"
	                                    	style="#{pc_Xrh00301.propTime7From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime7From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime7From" size="8"
						                	value="#{pc_Xrh00301.propTime7From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime7From.readonly}"
						                	style="#{pc_Xrh00301.propTime7From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime7To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime7To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime7To" size="8"
						                	value="#{pc_Xrh00301.propTime7To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime7To.readonly}"
						                	style="#{pc_Xrh00301.propTime7To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 8時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime8From"
	                                    	style="#{pc_Xrh00301.propTime8From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime8From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime8From" size="8"
						                	value="#{pc_Xrh00301.propTime8From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime8From.readonly}"
						                	style="#{pc_Xrh00301.propTime8From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime8To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime8To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime8To" size="8"
						                	value="#{pc_Xrh00301.propTime8To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime8To.readonly}"
						                	style="#{pc_Xrh00301.propTime8To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value=" 9時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime9From"
	                                    	style="#{pc_Xrh00301.propTime9From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime9From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime9From" size="8"
						                	value="#{pc_Xrh00301.propTime9From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime9From.readonly}"
						                	style="#{pc_Xrh00301.propTime9From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime9To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime9To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime9To" size="8"
						                	value="#{pc_Xrh00301.propTime9To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime9To.readonly}"
						                	style="#{pc_Xrh00301.propTime9To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
								<TR>
									<TH class="v_a" width="120">
		                            	<h:outputText 
		                                	styleClass="outputText" value="10時限">
		                            	</h:outputText>
		                            </TH>
		                            <TD width="400" nowrap>
	                            		<h:outputText 
	                                		styleClass="outputText" id="lblTime10From"
	                                    	style="#{pc_Xrh00301.propTime10From.labelStyle}"
	                                    	value="#{pc_Xrh00301.propTime10From.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime10From" size="8"
						                	value="#{pc_Xrh00301.propTime10From.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime10From.readonly}"
						                	style="#{pc_Xrh00301.propTime10From.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
	              						<h:outputText 
	                                		styleClass="outputText" id="lblTime10To"
	                                    	style="margin-left:64px"
	                                    	value="#{pc_Xrh00301.propTime10To.labelName}">
	                                	</h:outputText>
	                                	<h:inputText styleClass="inputText"
						                	id="htmlTime10To" size="8"
						                	value="#{pc_Xrh00301.propTime10To.dateValue}"
						                	readonly="#{pc_Xrh00301.propTime10To.readonly}"
						                	style="#{pc_Xrh00301.propTime10To.style}">
						                	<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
											<f:convertDateTime type="time" timeStyle="short" />
											<hx:inputHelperDatePicker delta="60" />
						                </h:inputText>
				              		</TD>
								</TR>
							</TBODY>
							</TABLE>	
						</TD>
						</TR>
					</TBODY>
					</TABLE>
					
					<BR>
					
					<TABLE  border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit" value="確定"
								styleClass="commandExButton_dat" id="register"
								action="#{pc_Xrh00301.doRegisterAction}"
								disabled="#{pc_Xrh00301.propRegister.disabled}"
								confirm="#{msg.SY_MSG_0002W}">
							</hx:commandExButton>
							<hx:commandExButton type="submit"
								styleClass="commandExButton_dat" id="sakujyo"
								value="削除"
								action="#{pc_Xrh00301.doDeleteAction}"
								confirm="#{msg.SY_MSG_0004W}"
								disabled="false">
							</hx:commandExButton>
						</TD>	
					</TR>
					</TBODY>
					</TABLE>
				</DIV>
			</DIV>
			
			</DIV>
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrh00301.propTimeList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
			<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrh00301.propBackPhase.integerValue}">
			</h:inputHidden>
		</h:form>

	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
