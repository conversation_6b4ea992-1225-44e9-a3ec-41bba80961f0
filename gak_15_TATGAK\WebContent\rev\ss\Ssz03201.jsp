<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz03201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz03201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz03201.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz03201.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz03201.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz03201.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 --><BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD align="right" width="519"><h:outputText styleClass="outputText"
							id="htmlCount" style="font-size: 8pt" value="#{pc_Ssz03201.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text4" value="件"
							style="font-size: 8pt; text-align: right"></h:outputText></TD>
						<TD width="25%"></TD>
					</TR>
					</TABLE>
					<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
					<TR>
						<TD width="20%"></TD>
						<TD width="55%">
						<DIV class="listScroll" style="height:296px;"
						id="listScroll" onscroll="setScrollPosition('scroll',this);">
						<h:dataTable border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz03201.propStudent.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz03201.propStudent.list}" var="varlist"
							width="500">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text1" styleClass="outputText" value="区分"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text6"
									value="#{varlist.sinroKbn}"></h:outputText>
							<f:attribute value="84" name="width" />
						</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text2"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text7"
									value="#{varlist.sinroName}"></h:outputText>
							<f:attribute value="442" name="width" />
						</h:column>
							<h:column id="column3">
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz03201.doSelectAction}"></hx:commandExButton>
								<f:facet name="header">
								</f:facet>
							<f:attribute value="26" name="width" />
						</h:column>
						</h:dataTable></DIV></TD>
						<TD width="25%"></TD>
					</TR>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%" height="40"></TD>
						<TD height="40" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
							<TBODY>
								<TR>
									<TH width="142" class="v_a"><h:outputText
										styleClass="outputText" id="ｌｂlsinrokbn"
										value="#{pc_Ssz03201.propSinroKbn.labelName}"
										style="#{pc_Ssz03201.propSinroKbn.labelStyle}"></h:outputText></TH>
									<TD width="362"><h:inputText styleClass="inputText"
										id="htmlsinrokbn"
										value="#{pc_Ssz03201.propSinroKbn.stringValue}"
										maxlength="#{pc_Ssz03201.propSinroKbn.maxLength}"
										style="#{pc_Ssz03201.propSinroKbn.style}" size="2">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="142" class="v_b"><h:outputText
										styleClass="outputText" id="ｌｂlsinroname"
										value="#{pc_Ssz03201.propSinroName.labelName}"
										style="#{pc_Ssz03201.propSinroName.labelStyle}"></h:outputText></TH>
									<TD width="362"><h:inputText styleClass="inputText"
										id="htmlsinroname" size="50"
										value="#{pc_Ssz03201.propSinroName.stringValue}"
										maxlength="#{pc_Ssz03201.propSinroName.maxLength}"
										style="#{pc_Ssz03201.propSinroName.style}">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD height="40" width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD valign="middle" align="center" width="100%"><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="register"
										action="#{pc_Ssz03201.doRegisterAction}" confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Ssz03201.doDeleteAction}" confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Ssz03201.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
			<BR>
			

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz03201.propStudent.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

