<%-- 
	物品情報一覧
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<f:view locale=#{SYSTEM_DATA.locale}>
	<HEAD>

	<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<META name="GENERATOR" content="IBM Software Development Platform">
	<META http-equiv="Content-Style-Type" content="text/css">

	<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
	<LINK rel="stylesheet" type="text/css"
		href="${pageContext.request.contextPath}/theme/stylesheet.css"
		title="Style">
	<LINK rel="stylesheet" type="text/css"
		href="../../theme/stylesheet.css" title="Style">
	<SCRIPT language="JavaScript"
		src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>

	<TITLE>xrc00201.jsp</TITLE>
	<SCRIPT type="text/javascript">

	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}

	</SCRIPT>

	</HEAD>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc00201.onPageLoadBegin}">
       <h:form styleClass="form" id="form1">   
				<!-- ヘッダーインクルード -->
				<jsp:include page ="../inc/header.jsp" />
				<!-- ヘッダーへのデータセット領域 -->
				<div style="display:none;">
					<hx:commandExButton type="submit" value="閉じる"
						styleClass="commandExButton" id="closeDisp"
						action="#{pc_Xrc00201.doCloseDispAction}">
					</hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrc00201.funcId}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrc00201.screenName}"></h:outputText>
				</div>
				<!--↓outer↓-->
				<DIV class="outer">
					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
					</FIELDSET>
					<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
						<!-- ↑ここに戻る／閉じるボタンを配置 -->
						　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
					</DIV>
					<!--↓content↓-->
					<DIV id="content">
						<DIV class="column" align="center">					
							<!-- ↓ここにコンポーネントを配置 -->
								<TABLE width="100%" border="0" align="center" class="button_bar" cellpadding="0" cellspacing="0">
									<TBODY>
										<TR>
											<TD>
												<hx:commandExButton type="submit" value="CSV作成"
													styleClass="commandExButton_out" id="CsvOut" action="#{pc_Xrc00201.doCsvoutAction}"
													confirm="#{msg.SY_MSG_0020W}">
												</hx:commandExButton>
												<hx:commandExButton type="submit"
													value="出力項目指定" 
													styleClass="commandExButton_out" id="setoutput"
													action="#{pc_Xrc00201.doSetoutputAction}" tabindex="6">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							<!-- ↑ここにコンポーネントを配置 -->	
						</DIV>
					</DIV>
				</DIV>									
				<!-- フッターインクルード -->
				<jsp:include page ="../inc/footer.jsp" />
			</h:form>       		
		</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

