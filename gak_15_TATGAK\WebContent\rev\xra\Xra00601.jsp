<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00601.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}

function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xra00601.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xra00601.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xra00601.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xra00601.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="400" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="140">
							<h:outputText styleClass="outputText"
            	                id="lblNyugakNendo"
								style="#{pc_Xra00601.propNyugakNendo.labelStyle}"
            	                value="#{pc_Xra00601.propNyugakNendo.labelName}">
							</h:outputText>
						</TH>
						<TD width="260">
							<h:inputText styleClass="inputText"
								id="htmlNyugakNendo"
								value="#{pc_Xra00601.propNyugakNendo.dateValue}"
								style="#{pc_Xra00601.propNyugakNendo.style}"
								size="10" tabindex="1">
								<f:convertDateTime pattern="yyyy" />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_"/>
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b">
							<h:outputText styleClass="outputText"
								id="lblNyugakGakkiNo"
								style="#{pc_Xra00601.propNyugakGakkiNo.labelStyle}"
								value="#{pc_Xra00601.propNyugakGakkiNo.labelName}">
							</h:outputText>
						</TH>
                        <TD>
							<h:inputText styleClass="inputText"
								id="htmlNyugakGakkiNo"
								value="#{pc_Xra00601.propNyugakGakkiNo.integerValue}"
								style="#{pc_Xra00601.propNyugakGakkiNo.style}"
								size="10" tabindex="2">
								<f:convertNumber type="number" pattern="#0" />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_"/>
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_a">
							<h:outputText styleClass="outputText"
								id="lblNyugakSbt"
								value="#{pc_Xra00601.propNyugakSbt.labelName}">
							</h:outputText>
						</TH>
						<TD>
							<h:selectOneMenu styleClass="selectOneMenu"
								id="htmlNyugakSbt" tabindex="3"
								value="#{pc_Xra00601.propNyugakSbt.value}">
								<f:selectItems value="#{pc_Xra00601.propNyugakSbt.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
					<TR>
                        <TH class="v_b">
                        	<h:outputText
								styleClass="outputText" id="lblNyugakNenji"
								value="#{pc_Xra00601.propNyugakNenji.labelName}">
							</h:outputText>
						</TH>
						<TD>
							<h:selectOneMenu styleClass="selectOneMenu"
								id="htmlNyugakNenji" tabindex="4"
								value="#{pc_Xra00601.propNyugakNenji.value}">
								<f:selectItems value="#{pc_Xra00601.propNyugakNenji.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="900" class="button_bar" border="0" cellpadding="0" cellspacing="0">
				<TR>
					<TD>
						<hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							style="#{pc_Xra00601.propRegister.style}"
							action="#{pc_Xra00601.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}" tabindex="5">
						</hx:commandExButton>

						<hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clear"
							style="#{pc_Xra00601.propClear.style}" 
							action="#{pc_Xra00601.doClearAction}" tabindex="6">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

