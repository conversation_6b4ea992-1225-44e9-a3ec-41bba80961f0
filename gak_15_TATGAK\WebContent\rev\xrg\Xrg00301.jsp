<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00301.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"></TD>
				</TR>
			</TABLE>
			<!-- ↑ここに戻るボタンを配置 --></DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="500" valign="top">
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText" id="lblNendo"
												value="#{pc_Xrg00301.propNendo.labelName}"
												style="#{pc_Xrg00301.propNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="350">
											<h:inputText styleClass="inputText"
												id="htmlNendo"
												value="#{pc_Xrg00301.propNendo.dateValue}"
												disabled="#{pc_Xrg00301.propNendo.disabled}"
												style="#{pc_Xrg00301.propNendo.style}" size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="inactive" promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_b" width="150">
											<h:outputText
												styleClass="outputText" id="lblBunrui"
												style="#{pc_Xrg00301.propBunrui.labelStyle}"
												value="#{pc_Xrg00301.propBunrui.labelName}">
											</h:outputText>
										</TH>
										<TD width="350">
											<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlBunrui"
												value="#{pc_Xrg00301.propBunrui.stringValue}">
												<f:selectItem itemValue="1" itemLabel="スクーリング"/>
												<f:selectItem itemValue="2" itemLabel="教育実習"/>
												<f:selectItem itemValue="3" itemLabel="介護等体験"/>
												<f:selectItem itemValue="4" itemLabel="テキスト"/>
											</h:selectOneRadio>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<BR>
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_c" width="150">
											<h:outputText styleClass="outputText" id="lblSyuturyokujun"
												value="#{pc_Xrg00301.propSyuturyokujun.labelName}">
											</h:outputText>
										</TH>
										<TD width="350">
											<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlSyuturyokujun"
												value="#{pc_Xrg00301.propSyuturyokujun.stringValue}">
												<f:selectItem itemValue="JUGY_CD" itemLabel="スクーリング種別、授業コード"/>
												<f:selectItem itemValue="KMK_CD" itemLabel="スクーリング種別、科目コード"/>
											</h:selectOneRadio>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="150">
											<h:outputText styleClass="outputText" 
												id="lblSyuturyokuCSV" 
												value="#{pc_Xrg00301.propSyuturyokuCSV.labelName}">
											</h:outputText>
										</TH>
										<TD width="350" style="padding-left: 2px">
											<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlSyuturyokuCSV" layout="pageDirection"
												value="#{pc_Xrg00301.propSyuturyokuCSV.stringValue}">
												<f:selectItem itemValue="NORMAL" itemLabel="通常（出力項目指定にて指定した内容）"/>
												<f:selectItem itemValue="NITTEI" itemLabel="種別・日程データＣＳＶ（一括登録用）"/>
												<f:selectItem itemValue="JUGY" itemLabel="授業データＣＳＶ（一括登録用）"/>
											</h:selectOneRadio>										
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<BR>
							<TABLE border="0" cellspacing="0" class="button_bar" width="100%">
								<TBODY>
									<TR>
										<TD width="" nowrap>
											<hx:commandExButton
												type="submit" value="CSV作成" styleClass="commandExButton_out"
												id="csvout" confirm="#{msg.SY_MSG_0020W}"
												action="#{pc_Xrg00301.doCsvoutAction}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="出力項目指定"
												styleClass="commandExButton_out" id="setOutput"
												tabindex="14"
												action="#{pc_Xrg00301.doSetOutputAction}"
												disabled="#{pc_Xrg00301.propSetOutput.disabled}"
												rendered="#{pc_Xrg00301.propSetOutput.rendered}"
												style="#{pc_Xrg00301.propSetOutput.style}">
											</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			
		</h:form></div>
		<!-- フッターインクルード -->
		<jsp:include page ="../inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
	
</f:view>

</HTML>
