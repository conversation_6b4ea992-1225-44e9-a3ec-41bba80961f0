<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00201.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>

<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xrx00201.jsp</TITLE>

<SCRIPT type="text/javascript">
	function init(){
		changeScrollPosition('htmlScroll','listScroll');
	}
		
    function func_check_on(thisObj, thisEvent, flg) {
        if(flg == 0){
            check('htmlNameTypeList','htmlListSelected');
        }else if(flg == 1){
            check('htmlNameTypeList','htmlListSelected');
        }
    }

    function func_check_off(thisObj, thisEvent, flg) {
        if(flg == 0){
            uncheck('htmlNameTypeList','htmlListSelected');
        }else if(flg == 1){
            uncheck('htmlNameTypeList','htmlListSelected');
        }
    }

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="init();">
 <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00201.onPageLoadBegin}">
  <h:form styleClass="form" id="form1">
   
   <!-- ヘッダーインクルード -->
   <jsp:include page ="../inc/header.jsp" />

   <!--↓OUTER↓-->
   <DIV class="outer">
    
    <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
     <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	 	styleClass="outputText" escape="false"></h:outputText>
	</FIELDSET>
				
    <DIV id="content">
	 <DIV class="column">
	 
      <TABLE width="80%" cellspacing="0" cellpadding="0" border="0">
       <TBODY>
        <TR>
        
         <TD width="40%">
          <TABLE width="100%" class="table" cellspacing="0" cellpadding="0" border="0">
           <TBODY>
            <TR>
             <TH class="v_a" width="35%">
              <h:outputText styleClass="outputText" id="lblWorkType"
			      value="#{pc_Xrx00201.propWorkTypeCombo.labelName}"
			      style="#{pc_Xrx00201.propWorkTypeCombo.labelStyle}"></h:outputText>
			 </TH>
			 <TD width="65%">
              <h:selectOneMenu styleClass="selectOneMenu" id="htmlWorkTypeCombo"
                  value="#{pc_Xrx00201.propWorkTypeCombo.value}"
				  disabled="#{pc_Xrx00201.propWorkTypeCombo.disabled}"
                  style="width:98%">
               <f:selectItems value="#{pc_Xrx00201.propWorkTypeCombo.list}" />
              </h:selectOneMenu>
             </TD>
            </TR>
           </TBODY>
          </TABLE>
		 </TD>
		 
         <TD align="left">
          <SPAN style="margin-left:20px">
           <hx:commandExButton type="submit" value="選択"
               styleClass="commandExButton" id="htmlSelect"
               action="#{pc_Xrx00201.doSelectAction}"
               disabled="#{pc_Xrx00201.propSelect.disabled}"></hx:commandExButton>
          </SPAN>
          <SPAN style="margin-left:5px">
           <hx:commandExButton type="submit" value="解除"
               styleClass="commandExButton" id="htmlCancel"
               action="#{pc_Xrx00201.doUnSelectAction}"
               disabled="#{pc_Xrx00201.propCancel.disabled}"></hx:commandExButton>
          </SPAN>
         </TD>

        </TR>
	    
	    <TR>
	    
         <TD align="right" colspan="2">
          <h:outputText styleClass="outputText"
              id="htmlTotal" value="#{pc_Xrx00201.propNameTypeList.listCount}"></h:outputText>
          <h:outputText styleClass="outputText"
              id="lblDataCount" value="件"></h:outputText>
         </TD>

        </TR>
        
        <TR>
        
         <TD colspan="2">
          <DIV style="height: 338px;" id="listScroll" onscroll="setScrollPosition('htmlScroll',this);" class="listScroll">
           <h:dataTable border="0" cellpadding="2" cellspacing="0"
               columnClasses="columnClass1" headerClass="headerClass"
               styleClass="meisai_scroll" footerClass="footerClass"
               rowClasses="#{pc_Xrx00201.propNameTypeList.rowClasses}"
               id="htmlNameTypeList" value="#{pc_Xrx00201.propNameTypeList.list}"
               first="#{pc_Xrx00201.propNameTypeList.first}"
               rows="#{pc_Xrx00201.propNameTypeList.rows}"
               var="varlist">
          
            <h:column id="column1">
             <h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
                 id="htmlListSelected" value="#{varlist.selected}"
                 rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
             <f:facet name="header"></f:facet>
             <f:attribute value="25" name="width" />
            </h:column>
          
            <h:column id="column2">
             <h:outputText styleClass="outputText" id="htmlWorkType"
                 title="#{varlist.workTypeOut.value}"
                 value="#{varlist.workTypeOut.displayValue}"></h:outputText>
             <f:facet name="header">
              <h:outputText styleClass="outputText" value="#{pc_Xrx00201.propWorkTypeCombo.labelName}"></h:outputText>
             </f:facet>
             <f:attribute value="90" name="width" />
            </h:column>
          
            <h:column id="column3">
             <h:outputText styleClass="outputText" id="htmlNameTypeCode"
                 title="#{varlist.nameTypeCodeOut.value}"
                 value="#{varlist.nameTypeCodeOut.displayValue}"></h:outputText>
             <f:facet name="header">
              <h:outputText
                  id="lblNameTypeCode"
                  styleClass="outputText"
                  value="#{pc_Xrx00201.propNameTypeCode.labelName}"
                  style="#{pc_Xrx00201.propNameTypeCode.labelStyle}"></h:outputText>
             </f:facet>
			 <f:attribute value="110" name="width" />
			</h:column>
          
            <h:column id="column4">
             <h:outputText styleClass="outputText" id="htmlTypeName"
                 title="#{varlist.typeNameOut.value}"
                 value="#{varlist.typeNameOut.displayValue}">
             </h:outputText>
             <f:facet name="header">
              <h:outputText
                  id="lblTypeName"
                  styleClass="outputText"
                  value="#{pc_Xrx00201.propTypeName.labelName}"
                  style="#{pc_Xrx00201.propTypeName.labelStyle}"></h:outputText>
             </f:facet>
             <f:attribute value="420" name="width" />
            </h:column>
          
            <h:column id="column5">
             <hx:commandExButton type="submit" value="選択"
                 styleClass="commandExButton" id="htmlSelectButton"
                 rendered="#{varlist.rendered}"
                 style="width: 40px"
                 action="#{pc_Xrx00201.doSelectItemAction}">
             </hx:commandExButton>
             <f:facet name="header"></f:facet>
             <f:attribute value="40" name="width" />
            </h:column>
          
            <h:column id="column6">
             <hx:commandExButton type="submit" value="編集"
                 styleClass="commandExButton" id="htmlEditButton"
                 rendered="#{varlist.rendered}"
                 style="width: 40px"
                 action="#{pc_Xrx00201.doEditItemAction}">
             </hx:commandExButton>
             <f:facet name="header"></f:facet>
             <f:attribute value="40" name="width" />
            </h:column>
          
           </h:dataTable>
          </DIV>
         </TD>
        
        </TR>
        
        <TR>
        
         <TD align="left">
          <hx:jspPanel id="jspPanel1">
           <INPUT type="button" name="check" value="on"
               onclick="return func_check_on(this, event, 0);" class="check">
           <INPUT type="button" name="uncheck" value="off"
               onclick="return func_check_off(this, event, 0);" class="uncheck">
          </hx:jspPanel>
         </TD>
         
         <TD  align="right">
          <hx:commandExButton
              type="submit" value="CSV作成" styleClass="commandExButton_out"
              id="htmlCsvOut" action="#{pc_Xrx00201.doCsvOutAction}"
              disabled="#{pc_Xrx00201.propCsvOut.disabled}"
              confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton>
         </TD>
         
        </TR>

       </TBODY>
      </TABLE>
      
      <HR noshade class="hr">
      
      <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="80%">
       <TBODY>
        <TR>
         <TH class="v_a">
          <h:outputText
              styleClass="outputText" id="lblSelectNameTypeCode"
              value="#{pc_Xrx00201.propSelectNameTypeCode.labelName}"
              style="#{pc_Xrx00201.propSelectNameTypeCode.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputText styleClass="inputText"
              id="htmlSelectNameTypeCode"
              value="#{pc_Xrx00201.propSelectNameTypeCode.stringValue}"
              style="#{pc_Xrx00201.propSelectNameTypeCode.style}"
              maxlength="#{pc_Xrx00201.propSelectNameTypeCode.maxLength}"
              disabled="#{pc_Xrx00201.propSelectNameTypeCode.disabled}"
              size="10">
           <hx:inputHelperAssist errorClass="inputText_Error" />
          </h:inputText></TD>
        </TR>
        <TR>
         <TH class="v_b">
          <h:outputText
              styleClass="outputText" id="lblSelectTypeName"
              value="#{pc_Xrx00201.propSelectTypeName.labelName}"
              style="#{pc_Xrx00201.propSelectTypeName.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputText styleClass="inputText"
              id="htmlSelectTypeName"
              value="#{pc_Xrx00201.propSelectTypeName.stringValue}"
              style="#{pc_Xrx00201.propSelectTypeName.style}"
              maxlength="#{pc_Xrx00201.propSelectTypeName.maxLength}"
              disabled="#{pc_Xrx00201.propSelectTypeName.disabled}"
              size="70">
           <hx:inputHelperAssist errorClass="inputText_Error" />
          </h:inputText>
         </TD>
        </TR>
        <TR>
         <TH class="v_c">
          <h:outputText
              styleClass="outputText" id="lblSelectTypeDescription"
              value="#{pc_Xrx00201.propSelectTypeDescription.labelName}"
              style="#{pc_Xrx00201.propSelectTypeDescription.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputTextarea styleClass="inputTextarea"
              id="htmlSelectTypeDescription"
              value="#{pc_Xrx00201.propSelectTypeDescription.stringValue}"
              style="#{pc_Xrx00201.propSelectTypeDescription.style}"
              disabled="#{pc_Xrx00201.propSelectTypeDescription.disabled}"
              cols="76" rows="3">
          </h:inputTextarea>
         </TD>
        </TR>
       </TBODY>
      </TABLE>
      
      <TABLE border="0" class="button_bar" width="80%">
       <TBODY>
        <TR>
         <TD>
          <SPAN>
          <hx:commandExButton type="submit"
              value="確定" styleClass="commandExButton_dat" id="htmlDetermine"
              confirm="#{msg.SY_MSG_0001W}"
              action="#{pc_Xrx00201.doDetermineAction}"
              disabled="#{pc_Xrx00201.propDetermine.disabled}">
          </hx:commandExButton>
          <SPAN style="margin-left: 20px">
          <hx:commandExButton type="submit" value="クリア" 
              styleClass="commandExButton_etc" id="htmlClear" 
              action="#{pc_Xrx00201.doClearAction}"
              disabled="#{pc_Xrx00201.propClear.disabled}">
          </hx:commandExButton>
          </SPAN>
          </SPAN>
         </TD>
        </TR>
       </TBODY>
      </TABLE>
      
     </DIV>
    </DIV>
   </DIV>
   <!--↑OUTER↑-->
   
   <h:inputHidden id="htmlScroll" value="#{pc_Xrx00201.propNameTypeList.scrollPosition}"></h:inputHidden>
			
   <!-- フッダーインクルード -->
   <jsp:include page ="../inc/footer.jsp" />

  </h:form>
 </hx:scriptCollector>
</BODY>

<jsp:include page ="../inc/common.jsp" />

</f:view>
</HTML>
