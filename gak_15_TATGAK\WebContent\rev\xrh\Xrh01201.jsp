
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
	
<%@ page import="com.jast.gakuen.framework.util.*"%>
<%@ page import="com.jast.gakuen.rev.xrh.Xrh01201"%>
<% Xrh01201 pc = (Xrh01201)UtilSystem.getManagedBean(Xrh01201.class); %>

<SCRIPT type="text/javascript">
	
	//	試験地検索
	function openKaisaitiSearchWindow(thisObj, thisEvent) {
		var url="${pageContext.request.contextPath}/faces/rev/xrh/pXrh0101.jsp?retFieldName=form1:htmlSikentiCd";
		openModalWindow(url, "pXrh0101", "<%= com.jast.gakuen.rev.xrh.PXrh0101.getWindowOpenOption() %>");
		return true;
	}
	
	//	試験地名称表示（Ajax）
	function doSikentiAjax(thisObj, thisEvent) {
		var servlet = "rev/xrh/XrhMeiSikentiNmAJAX";
		var target = "form1:lblSikentiName";
		var args = new Array();
		args['code'] = thisObj.value;
	
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
	
	//	ページ初期化
	function reloadPage( thisEvent, classDisAble, webDisAble ){
		doSikentiAjax(document.getElementById('form1:htmlSikentiCd'), thisEvent);
		
		if(webDisAble == "true"){
			
			document.getElementById('form1:htmlWebFlg').disabled = true;
		}else{
		
			document.getElementById('form1:htmlWebFlg').disabled = false;
		}
		
		if(classDisAble == "true"){
			
			document.getElementById('form1:htmlClasswariKetas').disabled = true;
		}else{
		
			document.getElementById('form1:htmlClasswariKetas').disabled = false;
		}
		
	}
	
	//	選択可否切替
	function setWebSaitenDisabled( thisEvent, thisObj ) {
		
		//	出力CSV形式を取得
		var chkValue = "0";
		//	* onload時はnull
		if (thisObj != null) {
			chkValue = thisObj.value;
			if (typeof chkValue === "undefined") {
				return;
			}
		}
		
		changeCondition(chkValue);
	}
	
	function changeCondition(val){
			//	使用可否を設定htmlClasswariKetas
		if (val == "0"){
			//教室割桁数
			document.getElementById('form1:htmlClasswariKetas').value = "";
			document.getElementById('form1:htmlClasswariKetas').disabled = false;
			document.getElementById('form1:htmlClassDisable').value = "false";
			//WEB採点
			document.getElementById('form1:htmlWebFlg').disabled = false;
			document.getElementById('form1:htmlWebDisable').value = "false";
			document.getElementsByName('form1:htmlWebFlg')[1].checked = true;
			//許可
			document.getElementById('form1:htmlNoMuko').checked = true;
			//不許可
			document.getElementById('form1:htmlMuko').checked = false;
			//出席
			document.getElementById('form1:htmlNoKesseki').checked = true;
			//欠席
			document.getElementById('form1:htmlKesseki').checked = true;
			//評価あり
			document.getElementById('form1:htmlHyoka').checked = true;
			//評価なし
			document.getElementById('form1:htmlNoHyoka').checked = true;
		} else {
			//教室割桁数
			document.getElementById('form1:htmlClasswariKetas').value = "";
			document.getElementById('form1:htmlClasswariKetas').disabled = true;
			document.getElementById('form1:htmlClassDisable').value = "true";
			//WEB採点
			document.getElementById('form1:htmlWebFlg').disabled = true;
			document.getElementById('form1:htmlWebDisable').value = "true";
			document.getElementsByName('form1:htmlWebFlg')[1].checked = true;
			//許可
			document.getElementById('form1:htmlNoMuko').checked = true;
			//不許可
			document.getElementById('form1:htmlMuko').checked = false;
			//出席
			document.getElementById('form1:htmlNoKesseki').checked = true;
			//欠席
			document.getElementById('form1:htmlKesseki').checked = false;
			//評価あり
			document.getElementById('form1:htmlHyoka').checked = true;
			//評価なし
			document.getElementById('form1:htmlNoHyoka').checked = true;
		}
	
	
	}
 	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<% 
		String classDisAble = pc.getPropClassDisable().getStringValue();
		String webDisAble = pc.getPropWebDisable().getStringValue();
		out.print("<BODY onload=\"reloadPage(event,'" + classDisAble + "','" + webDisAble + "');\">");
	%>
	
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				tabindex="60" value="閉じる" styleClass="commandExButton"
				id="closeDisp" action="#{pc_Xrh01201.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01201.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 --> <!-- ↑ここにボタンを配置 -->
			</DIV>

			<DIV id="content">
			<DIV class="column" align="center">

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblNendo" value="#{pc_Xrh01201.propNendo.labelName}"
							style="#{pc_Xrh01201.propNendo.labelStyle}">
						</h:outputText></TH>
						<TD width="180"><h:inputText id="htmlNendo" styleClass="inputText"
							tabindex="1" style="#{pc_Xrh01201.propNendo.style}"
							value="#{pc_Xrh01201.propNendo.dateValue}"
							disabled="#{pc_Xrh01201.propNendo.disabled}" size="4">
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
					</TR>

					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							style="#{pc_Xrh01201.propKamokSikenCnt.style}"
							id="lblKamokSikenCnt"
							value="#{pc_Xrh01201.propKamokSikenCnt.labelName}">
						</h:outputText></TH>
						<TD width="520"><h:inputText id="htmlKamokSikenCnt"
							styleClass="inputText" tabindex="2"
							value="#{pc_Xrh01201.propKamokSikenCnt.integerValue}"
							disabled="#{pc_Xrh01201.propKamokSikenCnt.disabled}" size="4">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertNumber type="number" pattern="#0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText> <h:outputText value="回" /></TD>
					</TR>

					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							style="#{pc_Xrh01201.propSikentiCd.style}" id="lblKamokSikenti"
							value="#{pc_Xrh01201.propSikentiCd.labelName}">
						</h:outputText></TH>
						<TD width="200"><h:inputText id="htmlSikentiCd"
							styleClass="inputText" tabindex="3"
							style="#{pc_Xrh01201.propSikentiCd.style}"
							value="#{pc_Xrh01201.propSikentiCd.stringValue}"
							maxlength="#{pc_Xrh01201.propSikentiCd.maxLength}"
							disabled="#{pc_Xrh01201.propSikentiCd.disabled}" size="4"
							onblur="return doSikentiAjax(this, event);">
						</h:inputText> <hx:commandExButton type="button"
							styleClass="commandExButton_search" id="search" tabindex="4"
							disabled="#{pc_Xrh01201.propSearch.disabled}"
							style="#{pc_Xrh01201.propSearch.style}"
							onclick="return openKaisaitiSearchWindow(this, event);">
						</hx:commandExButton> <h:outputText styleClass="outputText"
							id="lblSikentiName"
							value="#{pc_Xrh01201.propSikentiName.labelName}"
							style="#{pc_Xrh01201.propSikentiName.labelStyle}">
						</h:outputText></TD>
					</TR>

					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblOutputCsvStyle" value="出力ＣＳＶ形式">
						</h:outputText></TH>
						<TD><h:selectOneRadio disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlOutputCsvStyle" tabindex="5"
							layout="pageDirection"
							onclick="return setWebSaitenDisabled(event, this);"
							value="#{pc_Xrh01201.propOutputCsvStyle.stringValue}"
							style="#{pc_Xrh01201.propOutputCsvStyle.style}">
							<f:selectItem itemValue="0" itemLabel="教室割データ" />
							<f:selectItem itemValue="1" itemLabel="抜き取りデータ" />
							<f:selectItem itemValue="2" itemLabel="答案返却チェックリスト用データ" />
						</h:selectOneRadio>
						
					
						
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblClasswariKetas" value="教室割桁数">
						</h:outputText></TH>
						<TD width="180"><h:inputText id="htmlClasswariKetas"
							styleClass="inputText" tabindex="6"
							
							value="#{pc_Xrh01201.propClasswariKetas.integerValue}"
							 size="4">
							<f:convertNumber type="number" pattern="#" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblWebFlg" value="WEB採点">
						</h:outputText></TH>
						<TD width="180"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled" tabindex="7"
							styleClass="selectOneRadio" id="htmlWebFlg" 
							value="#{pc_Xrh01201.propWebFlg.stringValue}"
							style="#{pc_Xrh01201.propWebFlg.style}">
							<f:selectItem itemValue="2" itemLabel="全て" />
							<f:selectItem itemValue="1" itemLabel="あり" />
							<f:selectItem itemValue="0" itemLabel="なし" />
						</h:selectOneRadio></TD>
					</TR>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblMuko" value="許可状態">
						</h:outputText></TH>
						<TD colspan="3"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlNoMuko" tabindex="8"
							value="#{pc_Xrh01201.propNoMuko.checked}">
						</h:selectBooleanCheckbox>許可 <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlMuko" tabindex="9"
							value="#{pc_Xrh01201.propMuko.checked}">
						</h:selectBooleanCheckbox>不許可</TD>
					</TR>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblKesseki" value="出欠状況">
						</h:outputText></TH>
						<TD colspan="3"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlNoKesseki"
							tabindex="10" value="#{pc_Xrh01201.propNoKesseki.checked}">
						</h:selectBooleanCheckbox>出席 <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlKesseki" tabindex="11"
							value="#{pc_Xrh01201.propKesseki.checked}">
						</h:selectBooleanCheckbox>欠席</TD>
					</TR>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblHyoka" value="評価状態">
						</h:outputText></TH>
						<TD colspan="3"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlHyoka" tabindex="12"
							value="#{pc_Xrh01201.propHyoka.checked}">
						</h:selectBooleanCheckbox>評価あり <h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlNoHyoka" tabindex="13"
							value="#{pc_Xrh01201.propNoHyoka.checked}">
						</h:selectBooleanCheckbox>評価なし</TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE width="700" border="0" cellpadding="0" cellspacing="0"
				class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="csvOut" value="ＣＳＶ作成"
							action="#{pc_Xrh01201.doCsvOutAction}" tabindex="14"
							disabled="#{pc_Xrh01201.propCsvOut.disabled}"
							rendered="#{pc_Xrh01201.propCsvOut.rendered}"
							style="#{pc_Xrh01201.propCsvOut.style}">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
			</DIV>
			</DIV>
			<!-- フッターインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			
			<h:inputHidden
				id="htmlClassDisable"
				value="#{pc_Xrh01201.propClassDisable.stringValue}">
			</h:inputHidden>
			
			<h:inputHidden
				id="htmlWebDisable"
				value="#{pc_Xrh01201.propWebDisable.stringValue}">
			</h:inputHidden>
			
			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
