<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Xrg00501.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--	title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<SCRIPT type="text/javascript">
// 入力項目指定画面を開く
function openPCos0401() {
	setTarget("xxxx");
	openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
	return true;
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrg00501.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00501.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">
	
	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
	<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText>
	</FIELDSET>

	<DIV class="head_button_area" >　
	<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	
	<!--↓content↓-->
	<DIV id="content">
		<DIV class="column" align="center">
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD nowrap width="80%">
									<CENTER>
									<TABLE class="table" width="650" border="0" cellpadding="0"
										cellspacing="0" style="">
										<TBODY>
											<TR>
												<TH class="v_a" width="150">
													<h:outputText styleClass="outputText" id="lblNendo"
														value="#{pc_Xrg00501.propNendo.labelName}"
														style="#{pc_Xrg00501.propNendo.labelStyle}">
													</h:outputText>

												</TH>
												<TD nowrap width="500">
													<h:inputText id="htmlNendo" styleClass="inputText"
														readonly="#{pc_Xrg00501.propNendo.readonly}"
														style="#{pc_Xrg00501.propNendo.style}"
														value="#{pc_Xrg00501.propNendo.dateValue}"
														disabled="#{pc_Xrg00501.propNendo.disabled}" size="4"
														tabindex="1">
														<hx:inputHelperAssist errorClass="inputText_Error"
								    						imeMode="inactive" promptCharacter="_" />
														<f:convertDateTime pattern="yyyy" />
													</h:inputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_a" width="150">
													<h:outputText styleClass="outputText" id="lblGakuseiMibun"
														value="#{pc_Xrg00501.propGakuseiMibun.labelName}"
														style="#{pc_Xrg00501.propGakuseiMibun.labelStyle}">
													</h:outputText>
												</TH>
												<TD nowrap width="500">
													<h:selectOneMenu styleClass="selectOneMenu"
														id="htmlGakuseiMibun" value="#{pc_Xrg00501.propGakuseiMibun.value}"
														readonly="#{pc_Xrg00501.propGakuseiMibun.readonly}"
														style="#{pc_Xrg00501.propGakuseiMibun.style}"
														tabindex="2">
														<f:selectItems value="#{pc_Xrg00501.propGakuseiMibun.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
											<TR>
												<TH class="v_d" width="150">
													<h:outputText
														styleClass="outputText" id="lblEntlyKbn" value="データ登録区分指定">
													</h:outputText>
												</TH>
												<TD nowrap width="500">
													<h:selectOneRadio
														disabledClass="selectOneRadio_Disabled"
														styleClass="selectOneRadio" id="htmlEntlyKbn"
														layout="pageDirection" tabindex="3" 
														value="#{pc_Xrg00501.propEntlyKbn.stringValue}"
														readonly="#{pc_Xrg00501.propEntlyKbn.readonly}"
														disabled="#{pc_Xrg00501.propEntlyKbn.disabled}"
														style="#{pc_Xrg00501.propEntlyKbn.style}">
														<f:selectItem itemValue="2"
															itemLabel="データを登録または更新します。同一データが存在すれば上書き更新します。" />
														<f:selectItem itemValue="4"
															itemLabel="指定された申込関連データを削除します。" />
													</h:selectOneRadio>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="150">
													<h:outputText
														styleClass="outputText" id="lblSyoriKbn" value="処理区分指定">
													</h:outputText>
												</TH>
												<TD nowrap width="500">
													<h:selectBooleanCheckbox
														styleClass="selectBooleanCheckbox" id="htmlSyoriKbn" tabindex="3" 
														value="#{pc_Xrg00501.propSyoriKbn.checked}"
														readonly="#{pc_Xrg00501.propSyoriKbn.readonly}"
														disabled="#{pc_Xrg00501.propSyoriKbn.disabled}"
														style="#{pc_Xrg00501.propSyoriKbn.style}">
													</h:selectBooleanCheckbox>
													チェックのみ（データの登録/更新は行いません）
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="150">
													<h:outputText
														styleClass="outputText" id="lblCheckList" value="チェックリスト出力指定">
													</h:outputText>
												</TH>
												<TD nowrap width="500">
													<TABLE border="0" cellpadding="0" cellspacing="0">
														<TR>
															<TD class="clear_border">
																<h:selectBooleanCheckbox
																	styleClass="selectBooleanCheckbox" 
																	id="htmlChkListNormal" tabindex="4" 
																	value="#{pc_Xrg00501.propChkListNormal.checked}"
																	readonly="#{pc_Xrg00501.propChkListNormal.readonly}"
																	disabled="#{pc_Xrg00501.propChkListNormal.disabled}"
																	style="#{pc_Xrg00501.propChkListNormal.style}">
																</h:selectBooleanCheckbox>
																<h:outputText styleClass="outputText"
																	id="lblChkListNormal" value="正常データ">
																</h:outputText>
															</TD>
														</TR>
														<TR>
															<TD class="clear_border">
																<h:selectBooleanCheckbox
																	styleClass="selectBooleanCheckbox" 
																	id="htmlChkListError" tabindex="5"
																	value="#{pc_Xrg00501.propChkListError.checked}"
																	readonly="#{pc_Xrg00501.propChkListError.readonly}"
																	disabled="#{pc_Xrg00501.propChkListError.disabled}"
																	style="#{pc_Xrg00501.propChkListError.style}">
																</h:selectBooleanCheckbox>
																<h:outputText styleClass="outputText"
																	id="lblChkListError" value="エラーデータ">
																</h:outputText>
															</TD>
														</TR>
													</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</CENTER>
									</TD>
								</TR>
								<TR>
									<TD nowrap width="80%"></TD>
								</TR>
							</TBODY>
						</TABLE>
						<CENTER>
							<TABLE border="0" cellpadding="5" width="650">
								<TBODY>
									<TR>
										<TD align="left">
										</TD>
									</TR>
							
								</TBODY>
							</TABLE>
						</CENTER>
						<BR>
						<CENTER>
							<TABLE class="button_bar" width="650">
								<TBODY>
									<TR>
										<TD width="650">
											<hx:commandExButton type="submit"
												value="実　行" styleClass="commandExButton_dat" id="exec"
												action="#{pc_Xrg00501.doExecAction}"
												confirm="#{msg.SY_MSG_0001W}" tabindex="6"
												disabled="#{pc_Xrg00501.propExec.disabled}"
												style="#{pc_Xrg00501.propExec.style}">
											</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</CENTER>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page ="../inc/footer.jsp" />
	</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
