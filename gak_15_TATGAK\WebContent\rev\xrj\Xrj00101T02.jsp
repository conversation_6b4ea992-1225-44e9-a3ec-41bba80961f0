<%@page language="java" contentType="text/html; charset=UTF-8"	pageEncoding="UTF-8"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrj/Xrj00101T02.java" --%><%-- /jsf:pagecode --%>
<HTML>
<HEAD>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrj00101T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<SCRIPT language="javascript"> 
	function confirmOk() {	
		document.getElementById('form1:removeAllButtonCount').value = "1";
		indirectClick('removeAllGakusei');
	}
	function confirmCancel() {
		// alert('実行を中断しました。');	
	}

	function openSubWindow5(thisObj, thisEvent) {
		var mode = document.getElementById('form1:htmlStudentKbn');
		if(mode.value == "0"){
			// 学生検索画面（引数：なし）
			var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGaksekiCd";
			openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
//			setTarget("pCob0101");
			return true;
		}else{
			// 出学生検索画面（引数：なし）
			var url="${pageContext.request.contextPath}/faces/rev/xrj/pXrj0101.jsp?retFieldName=form1:htmlGaksekiCd";
			openModalWindow(url, "pXrj0101", "<%=com.jast.gakuen.rev.xrj.PXrj0101.getWindowOpenOption() %>");
//			setTarget("pXrj0101");
			return true;		
		}
	}

	function doGakuseiAjax(thisObj, thisEvent) {
		var mode = document.getElementById('form1:htmlStudentKbn');
		if(mode.value == "0"){		
		
			// 学生名称を取得する

			var servlet = "rev/co/CobGakseiAJAX";
			var args = new Array();
			args['code1'] = thisObj.value;
			args['code2'] = "";
			args['code3'] = "";
			var target = "form1:lblGakuseName";
			var ajaxUtil = new AjaxUtil();
//		ajaxUtil.getCodeName(servlet, target, args);
    		ajaxUtil.getPluralValueSetMethod(servlet, null, args,'callBackMethodKd');
    	}else{
			var servlet = "rev/xrj/XrjGakseiAJAX";
			var args = new Array();
			args['code1'] = thisObj.value;
			args['code2'] = "";
			args['code3'] = "";
			var target = "form1:lblGakuseName";
			var ajaxUtil = new AjaxUtil();
//			ajaxUtil.getCodeName(servlet, target, args);
    		ajaxUtil.getPluralValueSetMethod(servlet, null, args,'callBackMethodKd');    	
    	}
	}

	function callBackMethodKd(value){
	    document.getElementById('form1:lblGakuseName').innerText = value['name'];
	    document.getElementById('form1:lblGakuseName').title = value['name'];
	}
	
	function loadAction(event) {
		doGakuseiAjax(document.getElementById("form1:htmlGaksekiCd"), event);
	}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrj00101T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrj00101T02.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrj00101T02.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrj00101T02.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				styleClass="outputText" id="htmlMessage"
				value="#{requestScope.DISPLAY_INFO.displayMessage}" escape="false"></h:outputText>
			</FIELDSET>
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　</DIV>
			<!--↓content↓-->
			<DIV id="content"><!-- ↓ここにコンポーネントを配置 -->
			<DIV class="column">
			<TABLE border="0" cellpadding="0" width="650">
				<TBODY>
					<TR>
						<TD width="650">

						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="180">
										<h:outputText styleClass="outputText" id="lblStudentKbn"
										value="#{pc_Xrj00101T01.propStudentKbn.labelName}"
										style="#{pc_Xrj00101T01.propStudentKbn.labelStyle}"></h:outputText></TH>
									<TD width="469">
										<h:selectOneMenu styleClass="selectOneMenu" id="htmlStudentKbn"
										value="#{pc_Xrj00101T01.propStudentKbn.stringValue}"
										disabled="#{pc_Xrj00101T01.propSelect.disabled}">
											<f:selectItems value="#{pc_Xrj00101T01.propStudentKbn.list}" />
										</h:selectOneMenu><hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select"
										action="#{pc_Xrj00101T01.doSelectAction}"
										disabled="#{pc_Xrj00101T01.propSelect.disabled}">
										</hx:commandExButton><hx:commandExButton type="submit"
										value="解除" styleClass="commandExButton" id="unselect"
										action="#{pc_Xrj00101T01.doUnselectAction}"
										disabled="#{!pc_Xrj00101T01.propSelect.disabled}">
										</hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblRegistRangeNumber"
										value="#{pc_Xrj00101T01.propRegistRangeNumber.labelName}"
										style="#{pc_Xrj00101T01.propRegistRangeNumber.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:outputText 
										styleClass="outputText" id="htmlRegistRangeNumberFirst" value="小数点第"></h:outputText>
										<h:inputText styleClass="inputText" id="txtRegistRangeNumber"
										value="#{pc_Xrj00101T01.propRegistRangeNumber.integerValue}"
										style="#{pc_Xrj00101T01.propRegistRangeNumber.style}"
										size="3"
										maxlength="#{pc_Xrj00101T01.propRegistRangeNumber.maxLength}">
											<f:convertNumber type="number" pattern="0" />
											<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText><h:outputText styleClass="outputText"
										id="htmlRegistRangeNumberLast" value="位までを計算します"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_b" width="180"><h:outputText
										styleClass="outputText" id="lblMarumeKbn"
										value="#{pc_Xrj00101T01.propMarumeKbn.labelName}"
										style="#{pc_Xrj00101T01.propMarumeKbn.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="rdbMarumeKbn"
										value="#{pc_Xrj00101T01.propMarumeKbn.stringValue}"
										style="#{pc_Xrj00101T01.propMarumeKbn.style}">
										<f:selectItems value="#{pc_Xrj00101T01.propMarumeKbn.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_a" width="180">
										<h:outputText styleClass="outputText" id="lblGpaKbn"
										value="#{pc_Xrj00101T01.propGpaKbn.labelName}"
										style="#{pc_Xrj00101T01.propGpaKbn.labelStyle}"></h:outputText></TH>
									<TD width="469">
										<h:selectManyCheckbox id="chkGpaKbn" layout="pageDirection" styleClass="selectManyCheckbox"
										value="#{pc_Xrj00101T01.propGpaKbn.stringValue}">
											<f:selectItems value="#{pc_Xrj00101T01.propGpaKbn.list}" />
										</h:selectManyCheckbox>
									</TD>
								</TR>
							</TBODY>
						</TABLE>

						<BR>
						</TD>
					</TR>
					<c:if test="${pc_Xrj00101T01.propSelect.disabled}">
					<TR>
						<TD align="left"><br>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="140">
							<TBODY>
								<TR>
									<c:if test="${pc_Xrj00101T02.propBatch.rendered}">
									<TD width="70" nowrap align="center" valign="middle"
										class="tab_head_off"><hx:commandExButton type="submit"
										value="一括指定" styleClass="tab_head_off"
										action="#{pc_Xrj00101T02.doSwitchAction}"></hx:commandExButton></TD>
									</c:if>
									<TD width="70" nowrap align="center" valign="middle"><hx:commandExButton
										type="button" value="学生指定" styleClass="tab_head_on">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="650" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
							height="330px" class="tab_body">
							<TBODY>
								<TR>
									<TD width="100%" valign="top">
									<TABLE border="0" cellpadding="0" cellspacing="0"
										style="margin-top:10px">
										<TBODY>
											<TR>
												<TD width="500"></TD>
											</TR>
										</TBODY>
									</TABLE>
									<TABLE width="100%" class="table">
										<TBODY>
											<TR>
												<TH class="v_a" width="180">
													<h:outputText styleClass="outputText" id="lblCSVFile"
													value="#{pc_Xrj00101T02.propCSVFile.labelName}"></h:outputText><BR>
													<h:outputText styleClass="outputText" id="lblPreFile"
														value="#{pc_Xrj00101T02.propPreFile.labelName}"></h:outputText></TH>
												<TD colspan="2">
													<hx:fileupload styleClass="fileupload"
														id="htmlCSVFile"
														value="#{pc_Xrj00101T02.propCSVFile.value}"
														style="#{pc_Xrj00101T02.propCSVFile.style};width:410px">
														<hx:fileProp name="fileName"
															value="#{pc_Xrj00101T02.propCSVFile.fileName}" />
														<hx:fileProp name="contentType"
															value="#{pc_Xrj00101T02.propCSVFile.contentType}" />
													</hx:fileupload><hx:commandExButton type="submit"
													value="取込" styleClass="commandExButton"
													action="#{pc_Xrj00101T02.doLoadCsvAction}"></hx:commandExButton><BR>&nbsp;
													<h:outputText styleClass="outputText" id="htmlPreFile"
														value="#{pc_Xrj00101T02.propPreFile.stringValue}"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_b" width="180"><h:outputText
													styleClass="outputText" id="lblGaksekiCd"
													value="#{pc_Xrj00101T02.propGaksekiCd.labelName}"
													style="#{pc_Xrj00101T02.propGaksekiCd.labelStyle}"></h:outputText></TH>
												<TD width="230"><h:inputText styleClass="inputText"
													id="htmlGaksekiCd"
													value="#{pc_Xrj00101T02.propGaksekiCd.stringValue}"
													maxlength="#{pc_Xrj00101T02.propGaksekiCd.maxLength}"
													onblur="return doGakuseiAjax(this, event);"
													style="#{pc_Xrj00101T02.propGaksekiCd.style}" size="10"></h:inputText><hx:commandExButton
													type="button" value="検" styleClass="commandExButton_search"
													id="doSearchAction"
													action="#{pc_Xrj00101T02.doSearchAction}"
													onclick="return openSubWindow5(this, event);"></hx:commandExButton>
												<hx:commandExButton type="submit" value="追加"
													styleClass="commandExButton" id="addSingleGakusei"
													action="#{pc_Xrj00101T02.doAddStudentAction}"></hx:commandExButton></TD>
												<TD width="238">
												<DIV
													style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis;width:200px"><h:outputText
													styleClass="outputText" id="lblGakuseName"
													value="#{pc_Xrj00101T02.propGakuseName.displayValue}"
													title="#{pc_Xrj00101T02.propGakuseName.value}"></h:outputText></DIV>
												</TD>
											</TR>
										</TBODY>
									</TABLE>


									<TABLE border="0" width="100%">
										<TBODY>
											<TR>
												<TD align="center">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													width="500">
													<TBODY>
														<TR>
															<TD width="410" align="left"><h:outputText
																styleClass="outputText" id="lblTaisyouGakuse"
																value="#{pc_Xrj00101T02.propTaisyouGakusei.labelName}"
																style="#{pc_Xrj00101T02.propTaisyouGakusei.labelStyle}"></h:outputText></TD>
															<TD width="90"></TD>
														</TR>
														<TR>
															<TD width="410"><h:selectManyListbox styleClass="selectManyListbox"
																style="width:100%" id="htmlTaisyouGakusei" size="10"
																value="#{pc_Xrj00101T02.propTaisyouGakusei.stringValue}">
																<f:selectItems
																	value="#{pc_Xrj00101T02.propTaisyouGakusei.list}" />
															</h:selectManyListbox></TD>
															<TD align="center" width="90" valign="top"><hx:commandExButton
																type="submit" value="除外" styleClass="commandExButton"
																id="removeGakusei"
																action="#{pc_Xrj00101T02.doDelStudentAction}"
																style="width: 60px"></hx:commandExButton><BR>
															<h:outputText styleClass="outputText" id="text6"
																value="(複数選択可)"></h:outputText> <BR>
															<hx:commandExButton type="submit" value="全て除外"
																styleClass="commandExButton" id="removeAllGakusei"
																action="#{pc_Xrj00101T02.doDelAllAction}"
																style="width: 60px">
															</hx:commandExButton></TD>

														</TR>
														<TR>
															<TD width="410" align="right"><h:outputFormat
																styleClass="outputFormat" id="format1"
																value="合計件数：{0}件　正常件数：{1}件　エラー件数：{2}件">
																<f:param name="maxCount"
																	value="#{pc_Xrj00101T02.propTaisyouGakusei.listCount}"></f:param>
																<f:param name="nomalCount"
																	value="#{pc_Xrj00101T02.propTaisyouGakusei.listCount - pc_Xrj00101T02.propErrorCount.integerValue}"></f:param>
																<f:param name="errCount"
																	value="#{pc_Xrj00101T02.propErrorCount.integerValue}"></f:param>
															</h:outputFormat></TD>
															<TD align="center" width="90"></TD>
														</TR>
													</TBODY>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									<BR>
									<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
										class="button_bar">
										<TBODY>
											<TR>
												<TD align="center" height="8"><hx:commandExButton
													type="submit" value="実行" styleClass="commandExButton_dat"
													id="doGakuseiSiteiHantei"
													action="#{pc_Xrj00101T02.doExecAction}"
													confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>

						</TD>
					</TR>
					</c:if>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑--></DIV>

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrj00101T02.propRemoveAllButton.integerValue}"
				id="removeAllButtonCount">
				<f:convertNumber type="number" />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
