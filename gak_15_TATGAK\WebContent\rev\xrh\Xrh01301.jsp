<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	
 	
 	// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}
	
	// 学生氏名を取得する
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	function doKamokuAjax(thisObj, thisEvent, targetLabel, targetLabel2) {
	// 科目名称,単位数を取得する
		var servlet = "rev/xrf/XrfKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;

	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);


		// 単位数
		if ( targetLabel2 != "" ) {
		    var args2 = new Array();
		    args2['code'] = thisObj.value;
		    args2['tanisu'] = "GET";
		    args2['addString'] = " 単位";

			ajaxUtil.getCodeName(servlet, targetLabel2, args2);
		}
	}
	
	function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}
	
	function openKaisaitiSearchWindow(thisObj, thisEvent) {
		var url="${pageContext.request.contextPath}/faces/rev/xrh/pXrh0101.jsp?retFieldName=form1:htmlSikentiCd";
		openModalWindow(url, "pXrh0101", "<%= com.jast.gakuen.rev.xrh.PXrh0101.getWindowOpenOption() %>");
		return true;
	}
	
	
	function doSikentiAjax(thisObj, thisEvent) {
		var servlet = "rev/xrh/XrhMeiSikentiNmAJAX";
		var target = "form1:lblSikentiNm";
		var args = new Array();
		args['code'] = thisObj.value;
	
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
	
	function confirmOk(){
 		if(document.getElementById("form1:htmlConfirmFlg").value == 1){
 			indirectClick("search");
 		}
 	}
 	
 	function confirmCancel(){
 	
 		document.getElementById("form1:htmlConfirmFlg").value = 0;
 		
 	}
 	
 	//ページ初期化
	function reloadPage( thisEvent ){
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), thisEvent, 'form1:lblName');
		
		doSikentiAjax(document.getElementById('form1:htmlSikentiCd'),thisEvent);
		
		doKamokuAjax(document.getElementById('form1:htmlKamokCd'), thisEvent,
			'form1:lblKamokNm', '');
			
		document.getElementById("form1:register").focus();
	}
 	
 	
 	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="reloadPage(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh01301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01301.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<hx:commandExButton
					type="submit" value="結果登録"
					styleClass="commandExButton" id="register"
					disabled="#{pc_Xrh01301.propRegister.disabled}"
					tabindex="1"
					style="width:80px"
					action="Xrh01302T01">
				</hx:commandExButton>
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" >
						<TBODY>
							<TR>
							<TD>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblNendo"
												value="#{pc_Xrh01301.propNendo.labelName}" 
												style="#{pc_Xrh01301.propNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="200">
											<h:inputText id="htmlNendo" styleClass="inputText" 
												readonly="#{pc_Xrh01301.propNendo.readonly}" 
												style="#{pc_Xrh01301.propNendo.style}" 
												value="#{pc_Xrh01301.propNendo.dateValue}"
												disabled="#{pc_Xrh01301.propNendo.disabled}" 
												size="4" tabindex="2">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblKaisu"
												value="#{pc_Xrh01301.propKamokSikenCnt.labelName}"
												style="#{pc_Xrh01301.propKamokSikenCnt.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="200">
											<h:inputText id="htmlKamokSikenCnt" styleClass="inputText" 
												readonly="#{pc_Xrh01301.propKamokSikenCnt.readonly}" 
												value="#{pc_Xrh01301.propKamokSikenCnt.integerValue}"
												disabled="#{pc_Xrh01301.propKamokSikenCnt.disabled}" 
													size="2" tabindex="3">
												<hx:inputHelperAssist errorClass="inputText_Error"
								    			imeMode="inactive" promptCharacter="_" />
												<f:convertNumber type="number" pattern="#0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText>
											<h:outputText styleClass="outputText"
												value="回">
											</h:outputText>
										</TD>
									</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblSikenbiYobi"
												value="#{pc_Xrh01301.propSikenbiYobi.labelName}" 
												style="#{pc_Xrh01301.propSikenbiYobi.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:selectOneMenu 
												styleClass="selectOneMenu" 
												id="htmlSikenbiYobi"
												value="#{pc_Xrh01301.propSikenbiYobi.stringValue}"
												style="#{pc_Xrh01301.propSikenbiYobi.style};width:160px"
												disabled="#{pc_Xrh01301.propSikenbiYobi.disabled}" 
												tabindex="4">
												<f:selectItems 
													value="#{pc_Xrh01301.propSikenbiYobi.list}" />
											</h:selectOneMenu>
										</TD>
									 </TR>
									 
									 
									<TR>
						            	<TH nowrap class="v_a" width="150">
											<!--学籍番号 -->
						                	<h:outputText 
						                		styleClass="outputText" 
						                		id="lblGakusekiCd"
						                		value="#{pc_Xrh01301.propGakusekiCd.labelName}"
						                		style="#{pc_Xrh01301.propGakusekiCd.labelStyle}">
						                	</h:outputText>
						              	</TH>
						              	<TD colspan="3">
											<h:inputText id="htmlGakusekiCd" styleClass="inputText" 
												readonly="#{pc_Xrh01301.propGakusekiCd.readonly}" 
												style="#{pc_Xrh01301.propGakusekiCd.style}"
												disabled="#{pc_Xrh01301.propGakusekiCd.disabled}"
												maxlength="#{pc_Xrh01301.propGakusekiCd.maxLength}"
												size="10"
												tabindex="5"
												value="#{pc_Xrh01301.propGakusekiCd.stringValue}"
												onblur="return doGakuseiAjax(this, event, 'form1:lblName');">
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="disabled"/>
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="searchGakuseki"
												disabled="#{pc_Xrh01301.propSearchGakuseki.disabled}"
												rendered="#{pc_Xrh01301.propSearchGakuseki.rendered}"
												style="#{pc_Xrh01301.propSearchGakuseki.style}"
												onclick="openSubWindow('form1:htmlGakusekiCd');" 
												tabindex="6">
											</hx:commandExButton>
											<h:outputText
		              							styleClass="outputText"
		              							id="lblName"
		              							value="#{pc_Xrh01301.propName.stringValue}">
		              						</h:outputText>
		              					</TD>
		              				</TR>
									
	
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblKamokCd"
												value="#{pc_Xrh01301.propKamokCd.labelName}" 
												style="#{pc_Xrh01301.propKamokCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlKamokCd" styleClass="inputText" 
				                        		size="10"
				                      			maxlength="#{pc_Xrh01301.propKamokCd.maxLength}"
				                        		onblur="return doKamokuAjax(this, event, 'form1:lblKamokNm', '');"
												value="#{pc_Xrh01301.propKamokCd.stringValue}" 
												tabindex="7">
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="disabled"/>
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="searchKamok"
												onclick="return openKamokuSearchWindow(this, event);"
												disabled="#{pc_Xrh01301.propSearchKamok.disabled}" 
												tabindex="8">
											</hx:commandExButton>	
				      						<h:outputText
				      							styleClass="outputText" id="lblKamokNm"
				  								value="#{pc_Xrh01301.propKamokNm.stringValue}">
				  							</h:outputText>				
										</TD>
									</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblSikentiCd"
												value="#{pc_Xrh01301.propSikentiCd.labelName}" 
												style="#{pc_Xrh01301.propSikentiCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlSikentiCd" styleClass="inputText" 
				                        		size="5"
				                      			maxlength="#{pc_Xrh01301.propSikentiCd.maxLength}"
				                        		onblur="return doSikentiAjax(this, event);"
												value="#{pc_Xrh01301.propSikentiCd.stringValue}" 
												tabindex="9">
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="disabled"/>
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" 
												id="searchSikenti"
												onclick="return openKaisaitiSearchWindow(this, event);"
												disabled="#{pc_Xrh01301.propSearchSikenti.disabled}" 
												tabindex="10">
											</hx:commandExButton>	
				      						<h:outputText
				      							styleClass="outputText" id="lblsikentiNm"
				  								value="#{pc_Xrh01301.propSikentiNm.stringValue}">
				  							</h:outputText>
										</TD>	
									</TR>	
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblSeiriNo"
												value="#{pc_Xrh01301.propSeiriNoFrom.labelName}" 
												style="#{pc_Xrh01301.propSeiriNoFrom.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlSeiriNoFrom" styleClass="inputText" 
												readonly="#{pc_Xrh01301.propSeiriNoFrom.readonly}" 
												value="#{pc_Xrh01301.propSeiriNoFrom.stringValue}"
												disabled="#{pc_Xrh01301.propSeiriNoFrom.disabled}" size="8" 
												maxlength="#{pc_Xrh01301.propSeiriNoFrom.maxLength}" 
												tabindex="11">
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="disabled"/>
											</h:inputText>
											<h:outputText styleClass="outputText"
												value="～">
											</h:outputText>
											<h:inputText id="htmlSeiriNoTo" styleClass="inputText" 
												readonly="#{pc_Xrh01301.propSeiriNoTo.readonly}" 
												value="#{pc_Xrh01301.propSeiriNoTo.stringValue}"
												disabled="#{pc_Xrh01301.propSeiriNoTo.disabled}" size="8" 
												maxlength="#{pc_Xrh01301.propSeiriNoTo.maxLength}" 
												tabindex="12">
												<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="disabled"/>
											</h:inputText>
										</TD>	
									</TR>
								</TBODY>
								</TABLE>

								<BR>
								
								<TABLE width="700" border="0" cellpadding="0" cellspacing="0" 
									class="button_bar">
									<TBODY>
										<TR>
											<TD>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="search"
													value="検索"
													action="#{pc_Xrh01301.doSearchAction}"
													disabled="#{pc_Xrh01301.propSearch.disabled}"
													rendered="#{pc_Xrh01301.propSearch.rendered}"
													style="#{pc_Xrh01301.propSearch.style}" 
													tabindex="13">
												</hx:commandExButton>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="clear"
													value="クリア"
													action="#{pc_Xrh01301.doClearAction}"
													disabled="#{pc_Xrh01301.propClear.disabled}"
													rendered="#{pc_Xrh01301.propClear.rendered}"
													style="#{pc_Xrh01301.propClear.style}" 
													tabindex="14">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE border="0">
								<TBODY>
									<TR>
										<TD align="right">
											<h:outputText styleClass="outputText"
												id="lblListCnt"
												value="#{pc_Xrh01301.propListCnt.stringValue}"
												style="#{pc_Xrh01301.propListCnt.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
									<TD>
									<DIV id="listScroll" class="listScroll" style="height: 256px;">
										<h:dataTable 
											columnClasses="columnClass" 
											headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Xrh01301.propList.rowClasses}"
											styleClass="meisai_scroll" id="htmlList"
											value="#{pc_Xrh01301.propList.list}" var="varlist">
											
											<h:column id="column1">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="許可整理番号"
														id="lblListSeiriNo">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListSeiriNo"
													value="#{varlist.seiriNo}">
												</h:outputText>
												<f:attribute value="96" name="width" />
												<f:attribute value="text-align: right" name="style" />
											</h:column>
											
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="学籍番号"
														id="lblListGakusekiCd">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListGakusekiCd"
													value="#{varlist.gakusekiCd}">
												</h:outputText>
												<f:attribute value="66" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="学生氏名"
														id="lblListName">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListName"
													value="#{varlist.name.displayValue}"
													title="#{varlist.name.stringValue}">
												</h:outputText>
												<f:attribute value="120" name="width" />
												<f:attribute value="text-align: left" name="style" />
											</h:column>
											
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="出欠"
														id="lblListSyukketu">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListSyuketu"
													value="#{varlist.syuketu}">
												</h:outputText>
												<f:attribute value="36" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column5">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="科目名"
														id="lblListKamokNm">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListKamokNm"
													value="#{varlist.kamokNm.displayValue}"
													title="#{varlist.kamokNm.stringValue}">
												</h:outputText>
												<f:attribute value="160" name="width" />
											</h:column>
											
											<h:column id="column6">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="新旧刊"
														id="lblListSinkyuKbn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListSinkyuKbn"
													value="#{varlist.sinkyu}">
												</h:outputText>
												<f:attribute value="66" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column7">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="試験地コード"
														id="lblListSikentiCd">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListSikentiCd"
													value="#{varlist.sikentiCd}">
												</h:outputText>
												<f:attribute value="80" name="width" />
											 </h:column>
											
											 <h:column id="column8">
												<f:facet name="header">
													<h:outputText styleClass="outputText" 
														value="試験地名称"
														id="lblListSikentiNm">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" 
													id="htmlListSikentiNm"
													value="#{varlist.sikentiNm.displayValue}"
													title="#{varlist.sikentiNm.stringValue}">
												</h:outputText>
												<f:attribute value="110" name="width" />
											 </h:column>
											
										</h:dataTable>
									</DIV>
									</TD>
									</TR>
								</TBODY>
								</TABLE>
							</TD>
							</TR>
						</TBODY>
						</TABLE>
				</DIV>
			</DIV>
			
		</DIV>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		<h:inputHidden
				value="#{pc_Xrh01301.propConfirmFlg.integerValue}"
				id="htmlConfirmFlg">
		</h:inputHidden>
		
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
				
