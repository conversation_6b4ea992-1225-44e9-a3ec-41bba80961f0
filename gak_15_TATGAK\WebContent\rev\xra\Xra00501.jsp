<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
// 確認ダイアログで「ＯＫ」の場合
function confirmOk() {
	addListWarnOK("htmlExecutableBtnAdd", "btnAdd1");
}
// 確認ダイアログで「キャンセル」の場合
function confirmCancel() {	
	addListWarnCancel("htmlExecutableBtnAdd");
}
function loadAction(event){
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xra00501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xra00501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xra00501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xra00501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="400" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="140">
							<!-- 入学年度 -->
							<h:outputText styleClass="outputText"
								id="lblNyugakNendo"
								style="#{pc_Xra00501.propNyugakNendo.labelStyle}"
								value="#{pc_Xra00501.propNyugakNendo.labelName}">
							</h:outputText>
						</TH>
						<TD width="260">
							<h:inputText styleClass="inputText"
								id="htmlNyugakNendo"
								value="#{pc_Xra00501.propNyugakNendo.dateValue}"
								style="#{pc_Xra00501.propNyugakNendo.style}"
								size="10" tabindex="1">
								<f:convertDateTime pattern="yyyy" />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_"/>
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b">
							<!-- 入学学期No -->
							<h:outputText styleClass="outputText"
								id="lblNyugakGakkiNo"
								style="#{pc_Xra00501.propNyugakGakkiNo.labelStyle}"
								value="#{pc_Xra00501.propNyugakGakkiNo.labelName}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputText styleClass="inputText"
								id="htmlNyugakGakkiNo"
								value="#{pc_Xra00501.propNyugakGakkiNo.integerValue}"
								style="#{pc_Xra00501.propNyugakGakkiNo.style}"
								size="10" tabindex="2">
								<f:convertNumber type="number" pattern="#0" />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_"/>
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_a">
							<!-- 就学種別 -->
							<h:outputText styleClass="outputText"
								id="lblSyugakSbt"
								value="#{pc_Xra00501.propSyugakSbt.labelName}">
							</h:outputText>
						</TH>
						<TD>
							<h:selectOneMenu styleClass="selectOneMenu"
								id="htmlSyugakSbt" tabindex="3"
								value="#{pc_Xra00501.propSyugakSbt.value}">
								<f:selectItems value="#{pc_Xra00501.propSyugakSbt.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
					<TR>
						<!-- 選考結果日 -->
						<TH class="v_g" nowrap>
							<h:outputText styleClass="outputText"
								id="lblSenkokekkaDate"
								value="#{pc_Xra00501.propSenkokekkaDateFrom.labelName}">
							</h:outputText>
						</TH>
						<TD>
							<!-- 選考結果日(From) -->
							<h:inputText styleClass="inputText"
							 id="htmlSenkokekkaDateFrom" size="10"
							 value="#{pc_Xra00501.propSenkokekkaDateFrom.dateValue}"
							 disabled="#{pc_Xra00501.propSenkokekkaDateFrom.disabled}"
							 style="#{pc_Xra00501.propSenkokekkaDateFrom.style}"
							 tabindex="4">
								<f:convertDateTime />
								<hx:inputHelperAssist errorClass="inputText_Error"
								 promptCharacter="_" />
								<hx:inputHelperDatePicker />
							</h:inputText>
							　～　
							<!-- 選考結果日(To) -->
							<h:inputText styleClass="inputText"
							 id="htmlSenkokekkaDateTo" size="10"
							 value="#{pc_Xra00501.propSenkokekkaDateTo.dateValue}"
							 disabled="#{pc_Xra00501.propSenkokekkaDateTo.disabled}"
							 style="#{pc_Xra00501.propSenkokekkaDateTo.style}"
							 tabindex="5">
								<f:convertDateTime />
								<hx:inputHelperAssist errorClass="inputText_Error"
								 promptCharacter="_" />
								<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
				<TBODY>
					<!-- CSV出力 -->
					<TR align="right">
						<TD align="center">
							<hx:commandExButton type="submit" value="CSV作成"
							 styleClass="commandExButton_out" id="CsvOut" action="#{pc_Xra00501.doBtnCsvOutAction}"
							 confirm="#{msg.SY_MSG_0020W}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

