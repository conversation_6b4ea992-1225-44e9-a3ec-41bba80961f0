<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
var kyusyokuJoKyoKbnName0 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName1');
var kyusyokuJoKyoKbnName1 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName2');
var kyusyokuJoKyoKbnName2 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName3');
var kyusyokuJoKyoKbnName3 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName4');
var kyusyokuJoKyoKbnName4 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName5');
var kyusyokuJoKyoKbnName5 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName6');
var kyusyokuJoKyoKbnName6 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName7');
var kyusyokuJoKyoKbnName7 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName8');
var kyusyokuJoKyoKbnName8 = document.getElementById('form1:htmlKyusyokuJoKyoKbnName9');


//求職状況区分０標準名称
kyusyokuJoKyoKbnName0.value = '学内選考対象';
//求職状況区分１標準名称
kyusyokuJoKyoKbnName1.value = '学内選考により合格';
//求職状況区分２標準名称
kyusyokuJoKyoKbnName2.value = '学内選考により落選';
//求職状況区分３標準名称
kyusyokuJoKyoKbnName3.value = '採用試験応募済';
//求職状況区分４標準名称
kyusyokuJoKyoKbnName4.value = '内定済';
//求職状況区分５標準名称
kyusyokuJoKyoKbnName5.value = '不採用';
//求職状況区分６標準名称
kyusyokuJoKyoKbnName6.value = '内定辞退';
//求職状況区分７標準名称
kyusyokuJoKyoKbnName7.value = '内定取消';
//求職状況区分８標準名称
kyusyokuJoKyoKbnName8.value = '就職先として決定';

indirectClick('register');
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz01401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz01401.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz01401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz01401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="22%"></TD>
						<TD width="53%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
							<TBODY>
								<TR>
									<TH width="41"><h:outputText styleClass="outputText" id="text1" value="区分"></h:outputText></TH>
									<TH width="152"><h:outputText styleClass="outputText"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName1.labelStyle}"
										id="text16" value="活動区分名称(全10)"></h:outputText></TH>
									<TH width="297"><h:outputText styleClass="outputText"
										id="text17" value="活動区分を表す意味"></h:outputText></TH>
								</TR>
								<TR>
									<TH width="41" class="v_a"><h:outputText
										styleClass="outputText" id="text2" value="０："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName1" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName1.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName1.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName1.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text18" value="推薦で学内選考の対象となっている状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_b"><h:outputText
										styleClass="outputText" id="text3" value="１："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName2" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName2.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName2.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName2.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text19" value="学校推薦により応募した状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_c"><h:outputText
										styleClass="outputText" id="text4" value="２："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName3" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName3.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName3.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName3.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text20" value="学内選考により落選した状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_d"><h:outputText
										styleClass="outputText" id="text5" value="３："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName4" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName4.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName4.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName4.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text21" value="求人に対して応募した状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_e"><h:outputText
										styleClass="outputText" id="text6" value="４："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName5" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName5.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName5.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName5.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text22" value="企業から内定をもらった状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_f"><h:outputText
										styleClass="outputText" id="text7" value="５："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName6" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName6.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName6.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName6.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text23" value="不採用となった状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_g"><h:outputText
										styleClass="outputText" id="text8" value="６："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName7" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName7.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName7.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName7.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text24" value="企業からの内定を辞退した状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_a"><h:outputText
										styleClass="outputText" id="text9" value="７："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName8" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName8.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName8.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName8.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text25" value="内定の取消を受けた状態。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="41" class="v_b"><h:outputText
										styleClass="outputText" id="text10" value="８："></h:outputText></TH>
									<TD width="152"><h:inputText styleClass="inputText"
										id="htmlKyusyokuJoKyoKbnName9" size="20"
										value="#{pc_Ssz01401.propKyusyokuJoKyoKbnName9.stringValue}"
										maxlength="#{pc_Ssz01401.propKyusyokuJoKyoKbnName9.maxLength}"
										style="#{pc_Ssz01401.propKyusyokuJoKyoKbnName9.style}"></h:inputText></TD>
									<TD width="297"><h:outputText styleClass="outputText"
										id="text26" value="該当企業を学生が就職先として決定。"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz01401.doRegisterAction}"
							confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="標準名称" styleClass="commandExButton_etc"
							id="edit" onclick="return func_1(this, event);"
							confirm="#{msg.SY_MSG_0005W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

