<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00402T04.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	//	科目検索画面表示
	 function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}

 	//	画面ロード時の処理
	function formLoad(thisEvent) {
		//	科目名称再表示
		doKamokuAjax(document.getElementById('form1:htmlKamokuCd'),
			thisEvent, 'form1:lblKamokuName');
		//	教員名再表示
		//		* 作問
		getJinjiName(document.getElementById('form1:htmlSakumonCd'),
			thisEvent, 'form1:lblSakumonName');
		//		* 採点
		getJinjiName(document.getElementById('form1:htmlSaitenCd'),
			thisEvent, 'form1:htmlSaitenName');
	}
 
	// 科目名称を取得する（Ajax）
	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
		//var servlet = "rev/km/KmzKmkAJAX";
		var servlet = "rev/km/KmKamokuDetailAJAX";
	    var args = new Array();
	    //args['code'] = thisObj.value;
	    args['kamokuCd'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		//ajaxUtil.getCodeName(servlet, targetLabel, args);
		ajaxUtil.getPluralValueSetMethod(servlet, targetLabel, args, "setKamokuHName");
	}
	
	// 科目名称を取得する（Ajax:CallBack関数）
	function setKamokuHName(value){
		//var lblName = value['kamokNameHyojun'];
		//var nameObj = document.getElementById('form1:lblKamokuName');
		//nameObj.value = lblName;
		document.getElementById('form1:lblKamokuName').innerHTML = value['kamokNameHyojun'];
	}

	//	教員検索画面表示
	function func_1(thisObj, thisEvent, fieldName) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=" + fieldName;
		 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	}
	
	// 教員名称を取得する（Ajax）
	function getJinjiName(thisObj, thisEvent, target ) {
  		var servlet = "rev/co/CobJinjAJAX";
  		//var target = "form1:lblSyokuinName";
  		getCodeName(servlet, target, thisObj.value);
 	}
 	
	// 戻るボタン押下時処理
	function onClickReturnDisp(id) {
		var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
		if(changeDataFlg == "1"){
			return confirm(id);
		}
		
		return true;
	}
	
	// データ変更時
	function onChangeData() {
		document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
	}
	
	// ポップアップメッセージを表示
	function doPopupMsg(id, msg) {
		var args = new Array();
		args[0] = msg;
		if (confirm(messageCreate(id, args))) {
			onChangeData();
			return true;
		}
	  
		return false;
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="formLoad(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00402T04.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00402T04.xrh00402.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00402T04.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00402T04.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"><hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="53"
						action="#{pc_Xrh00402T04.xrh00402.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
					</hx:commandExButton></TD>
				</TR>
			</TABLE>
			<!-- ↑ここにボタンを配置 --></DIV>

			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="800" valign="top"><!-- ↓タブ間共有テーブル↓ -->
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="190"><!--年度 --> <h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Xrh00402T04.xrh00402.propNendo.labelName}"
										style="#{pc_Xrh00402T04.xrh00402.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNendo" size="4"
										value="#{pc_Xrh00402T04.xrh00402.propNendo.dateValue}"
										readonly="#{pc_Xrh00402T04.xrh00402.propNendo.readonly}"
										disabled="#{pc_Xrh00402T04.xrh00402.propNendo.disabled}"
										style="#{pc_Xrh00402T04.xrh00402.propNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TD width="100" align="right" rowspan="2"
										style="background-color: transparent; text-align: right"
										class="clear_border"><hx:commandExButton type="submit"
										value="#{pc_Xrh00402T04.xrh00402.propSelect.caption}"
										styleClass="commandExButton" id="select"
										disabled="#{pc_Xrh00402T04.xrh00402.propSelect.disabled}"
										rendered="#{pc_Xrh00402T04.xrh00402.propSelect.rendered}"
										style="#{pc_Xrh00402T04.xrh00402.propSelect.style}"
										action="#{pc_Xrh00402T04.xrh00402.doSelectAction}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="#{pc_Xrh00402T04.xrh00402.propUnSelect.caption}"
										styleClass="commandExButton" id="unselect"
										disabled="#{pc_Xrh00402T04.xrh00402.propUnSelect.disabled}"
										rendered="#{pc_Xrh00402T04.xrh00402.propUnSelect.rendered}"
										style="#{pc_Xrh00402T04.xrh00402.propUnSelect.style}"
										action="#{pc_Xrh00402T04.xrh00402.doUnSelectAction}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenKaisu"
										style="#{pc_Xrh00402T04.xrh00402.propSikenKaisu.labelStyle}"
										value="#{pc_Xrh00402T04.xrh00402.propSikenKaisu.labelName}">
									</h:outputText></TH>
									<TD width="200"><h:inputText id="htmlSikenKaisu"
										styleClass="inputText" size="4"
										value="#{pc_Xrh00402T04.xrh00402.propSikenKaisu.integerValue}"
										readonly="#{pc_Xrh00402T04.xrh00402.propSikenKaisu.readonly}"
										disabled="#{pc_Xrh00402T04.xrh00402.propSikenKaisu.disabled}"
										style="#{pc_Xrh00402T04.xrh00402.propSikenKaisu.style}">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenbiYobi"
										style="#{pc_Xrh00402T04.xrh00402.propSikenbiYoubi.labelStyle}"
										value="#{pc_Xrh00402T04.xrh00402.propSikenbiYoubi.labelName}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSikenbiYoubi"
										value="#{pc_Xrh00402T04.xrh00402.propSikenbiYoubi.stringValue}"
										style="#{pc_Xrh00402T04.xrh00402.propSikenbiYoubi.style};width:128px"
										disabled="#{pc_Xrh00402T04.xrh00402.propSikenbiYoubi.disabled}">
										<f:selectItems
											value="#{pc_Xrh00402T04.xrh00402.propSikenbiYoubi.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						<!-- ↑タブ間共有テーブル↑ --> <BR>
						<TABLE border="0" cellpadding="20" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="800" align="left">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="日程・時間割" id="moveNiteiJikanTab"
													disabled="#{pc_Xrh00402T04.xrh00402.propMoveNiteiJikanTab.disabled}"
													styleClass="tab_head_off" action="Xrh00402T01">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="場所情報" styleClass="tab_head_off" id="moveLocateTab"
													disabled="#{pc_Xrh00402T04.xrh00402.propMoveLocateTab.disabled}"
													action="Xrh00402T02">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="試験監督" styleClass="tab_head_off"
													disabled="#{pc_Xrh00402T04.xrh00402.propMoveSikenKantokuTab.disabled}"
													id="moveSikenKantokuTab" action="Xrh00402T03">
												</hx:commandExButton></TD>
												<TD class="tab_head_on"><hx:commandExButton type="button"
													disabled="#{pc_Xrh00402T04.xrh00402.propMoveSakumonTab.disabled}"
													value="作問情報" styleClass="tab_head_on" id="moveSakumonTab">
												</hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD valign="top">
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="100%">
										<TBODY>
											<TR>
												<TD>
												<CENTER><BR>
												<TABLE border="0" cellpadding="5">
													<TBODY>
														<TR>
															<TD>
															<DIV id="listScroll" class="listScroll"
																style="height: 256px;" onscroll="setScrollPosition('scroll',this);"><h:dataTable
																columnClasses="columnClass" headerClass="headerClass"
																footerClass="footerClass"
																rowClasses="#{pc_Xrh00402T04.propSakumonList.rowClasses}"
																styleClass="meisai_scroll" id="htmlSakumonList"
																value="#{pc_Xrh00402T04.propSakumonList.list}"
																var="varlist">

																<h:column id="column1">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="科目コード"
																			id="lblListKamokuCdColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.kamokuCd.displayValue}"
																		title="#{varlist.kamokuCd.stringValue}"
																		id="htmlListKamokuCd">
																	</h:outputText>
																	<f:attribute value="70" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>


																<h:column id="column2">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="科目名"
																			id="lblListKamokuNameColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.kamokuName.displayValue}"
																		title="#{varlist.kamokuName.stringValue}"
																		id="htmlListKamokuName">
																	</h:outputText>
																	<f:attribute value="240" name="width" />
																</h:column>


																<h:column id="column3">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="作問教員名"
																			id="lblListSakumonNameColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.sakumonKyoinName.displayValue}"
																		title="#{varlist.sakumonKyoinName.stringValue}"
																		id="htmlListSakumonName">
																	</h:outputText>
																	<f:attribute value="130" name="width" />
																</h:column>

																<h:column id="column4">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="採点教員名"
																			id="lblListSaitenNameColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.saitenKyoinName.displayValue}"
																		title="#{varlist.saitenKyoinName.stringValue}"
																		id="htmlListSaitenName">
																	</h:outputText>
																	<f:attribute value="130" name="width" />
																</h:column>

																<h:column id="column5">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="WEB"
																			id="lblListWebColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.webFlgDisp.stringValue}"
																		id="htmlListWeb">
																	</h:outputText>
																	<f:attribute value="40" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>

																<h:column id="column6">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="新旧刊"
																			id="lblListSinkyuColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.sinkyuFlgDisp.stringValue}"
																		id="htmlListSinkyu">
																	</h:outputText>
																	<f:attribute value="50" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>

																<h:column id="column7">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="作問返却日"
																			id="lblListHenkyakubiColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.sakumonHenkyakuDate.dateValue}"
																		id="htmlListHenkyakubi">
																	</h:outputText>
																	<f:attribute value="70" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>

																<h:column id="column8">
																	<f:facet name="header">
																	</f:facet>
																	<hx:commandExButton type="submit"
																		value="#{varlist.btnEdit.caption}"
																		action="#{pc_Xrh00402T04.doEditAction}"
																		disabled="false" styleClass="commandExButton"
																		id="edit">
																	</hx:commandExButton>
																	<f:attribute value="30" name="width" />
																	<f:attribute value="true" name="nowrap" />
																	<f:attribute value="center" name="align" />
																	<f:attribute value="middle" name="valign" />
																</h:column>
															</h:dataTable></DIV>
															</TD>
														</TR>
													</TBODY>
												</TABLE>

												<BR>
												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="table">
													<TBODY>
														<TR>
															<TH nowrap class="v_a" width="132"><!--科目コード --> <h:outputText
																styleClass="outputText" id="lblKmaokuCd"
																value="#{pc_Xrh00402T04.propKamokuCd.labelName}"
																style="#{pc_Xrh00402T04.propKamokuCd.labelStyle}">
															</h:outputText></TH>
															<TD width="500"><h:inputText styleClass="inputText"
																id="htmlKamokuCd" size="10" onchange="onChangeData();"
																value="#{pc_Xrh00402T04.propKamokuCd.stringValue}"
																disabled="#{pc_Xrh00402T04.propKamokuCd.disabled}"
																maxlength="#{pc_Xrh00402T04.propKamokuCd.maxLength}"
																style="#{pc_Xrh00402T04.propKamokuCd.style}"
																onblur="return doKamokuAjax(this, event, 'form1:lblKamokuName');">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText> <hx:commandExButton type="button"
																styleClass="commandExButton_search" id="searchKamoku"
																disabled="#{pc_Xrh00402T04.propSearchKamoku.disabled}"
																onclick="return openKamokuSearchWindow(this,event)">
															</hx:commandExButton> <h:outputText
																styleClass="outputText" id="lblKamokuName"
																value="#{pc_Xrh00402T04.propKamokuName.labelName}"
																style="#{pc_Xrh00402T04.propKamokuName.labelStyle}">
															</h:outputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a"><!--作問教員 --> <h:outputText
																styleClass="outputText" id="lblSakumonCode"
																value="#{pc_Xrh00402T04.propSakumonCd.labelName}"
																style="#{pc_Xrh00402T04.propSakumonCd.labelStyle}">
															</h:outputText></TH>
															<TD width="500"><h:inputText styleClass="inputText"
																id="htmlSakumonCd" size="10" onchange="onChangeData();"
																value="#{pc_Xrh00402T04.propSakumonCd.stringValue}"
																disabled="#{pc_Xrh00402T04.propSakumonCd.disabled}"
																maxlength="#{pc_Xrh00402T04.propSakumonCd.maxLength}"
																style="#{pc_Xrh00402T04.propSakumonCd.style}"
																onblur="return getJinjiName(this, event,'form1:lblSakumonName');">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText> <hx:commandExButton type="button"
																styleClass="commandExButton_search" id="searchSakumon"
																disabled="#{pc_Xrh00402T04.propSearchSakumon.disabled}"
																onclick="return func_1(this, event, 'form1:htmlSakumonCd');">
															</hx:commandExButton> <h:outputText
																styleClass="outputText" id="lblSakumonName"
																value="#{pc_Xrh00402T04.propSakumonName.stringValue}"
																style="#{pc_Xrh00402T04.propSakumonName.labelStyle}">
															</h:outputText></TD>
														</TR>

														<TR>
															<TH nowrap class="v_a"><!--採点教員 --> <h:outputText
																styleClass="outputText" id="lblSaitenCd"
																value="#{pc_Xrh00402T04.propSaitenCd.labelName}"
																style="#{pc_Xrh00402T04.propSaitenCd.labelStyle}">
															</h:outputText></TH>
															<TD width="500"><h:inputText styleClass="inputText"
																id="htmlSaitenCd" size="10" onchange="onChangeData();"
																value="#{pc_Xrh00402T04.propSaitenCd.stringValue}"
																disabled="#{pc_Xrh00402T04.propSaitenCd.disabled}"
																maxlength="#{pc_Xrh00402T04.propSaitenCd.maxLength}"
																style="#{pc_Xrh00402T04.propSaitenCd.style}"
																onblur="return getJinjiName(this, event,'form1:htmlSaitenName');">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText> <hx:commandExButton type="button"
																styleClass="commandExButton_search" id="searchSaiten"
																disabled="#{pc_Xrh00402T04.propSearchSaiten.disabled}"
																onclick="return func_1(this, event, 'form1:htmlSaitenCd');">
															</hx:commandExButton> <h:inputText
																styleClass="likeOutput" id="htmlSaitenName"
																value="#{pc_Xrh00402T04.propSaitenName.stringValue}"
																style="width:270px;" readonly="true">
															</h:inputText> <h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="checkWeb"
																onchange="onChangeData();"
																value="#{pc_Xrh00402T04.propCheckWeb.checked}"
																disabled="#{pc_Xrh00402T04.propCheckWeb.disabled}">
															</h:selectBooleanCheckbox> <h:outputText
																styleClass="outputText" id="lblWeb"
																value="#{pc_Xrh00402T04.propCheckWeb.labelName}">
															</h:outputText></TD>
														</TR>
														<TR>
															<TH class="v_a"><h:outputText
																styleClass="outputText" id="lblSinkyuKubun"
																value="#{pc_Xrh00402T04.propRadioSinkyu.labelName}"
																style="#{pc_Xrh00402T04.propRadioSinkyu.labelStyle}">
															</h:outputText></TH>
															<TD width="500"><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlSinkyu"
																onchange="onChangeData();"
																value="#{pc_Xrh00402T04.propRadioSinkyu.stringValue}"
																disabled="#{pc_Xrh00402T04.propRadioSinkyu.disabled}"
																style="#{pc_Xrh00402T04.propRadioSinkyu.style}">
																<f:selectItem itemValue="0" itemLabel="新刊" />
																<f:selectItem itemValue="1" itemLabel="旧刊" />
															</h:selectOneRadio></TD>
														</TR>
													</TBODY>
												</TABLE>

												<BR>

												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="button_bar">
													<TBODY>
														<TR>
															<TD><hx:commandExButton type="submit"
																styleClass="commandExButton_dat" id="register"
																onclick="return confirm('#{msg.SY_MSG_0002W}');"
																value="確定" action="#{pc_Xrh00402T04.doRegisterAction}"
																disabled="#{pc_Xrh00402T04.propRegister.disabled}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																styleClass="commandExButton_dat" id="delete" value="削除"
																onclick="return confirm('#{msg.SY_MSG_0004W}');"
																action="#{pc_Xrh00402T04.doDeleteAction}"
																disabled="#{pc_Xrh00402T04.propDelete.disabled}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																styleClass="commandExButton_dat" id="clear" value="クリア"
																onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '表示内容');"
																action="#{pc_Xrh00402T04.doClearAction}"
																disabled="#{pc_Xrh00402T04.propClear.disabled}">
															</hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>



												</CENTER>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<h:inputHidden id="htmlHidChangeDataFlg"
				value="#{pc_Xrh00402T04.propHidChangeDataFlg.stringValue}"></h:inputHidden>
   <h:inputHidden value="#{pc_Xrh00402T04.propSakumonList.scrollPosition}"
    id="scroll"></h:inputHidden>
			</DIV>

			</DIV>

		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
	changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
