/*****default style in case user didn't set the style class*********/

/*Use for tree normal node text label style*/

.labelNormalStyle {
	FONT-SIZE: 11px; COLOR: #000000; FONT-FAMILY: Tahoma, Verdana, Geneva, Arial, Helvetica, sans-serif; 
}

/*Use for tree node image style*/
.nodeImageStyle {
	BORDER-RIGHT: 0px; BORDER-TOP: 0px; BORDER-LEFT: 0px; WIDTH: 19px; BORDER-BOTTOM: 0px; HEIGHT: 16px
}

/*Use for highlighted node text label style*/
.labelHighlightStyle{
	FONT-SIZE: 11px; COLOR: #000000; FONT-FAMILY: Tahoma, Verdana, Geneva, Arial, Helvetica, sans-serif; font-weight: bold;
}
/*Use for node text label style when mouse over the label*/
.labelMouseoverStyle{
	FONT-SIZE: 11px; COLOR: #000000; FONT-FAMILY: Tahoma, Verdana, Geneva, Arial, Helvetica, sans-serif; font-style: italic;

}
/*So that the text will have no underline*/
.linkTextStyle{
	TEXT-DECORATION: none
}

/*Use for getting all the system image icons*/
.classForTreeSysIcons {
	/*Sample style, please uncomment the following line and modify the icons path if you want use your own customized system icons*/
	/*list-style-image:url("|plustop=jsl/tree/icons/plustop.gif|plus=jsl/tree/icons/plus.gif|plusbottom=jsl/tree/icons/plusbottom.gif|minustop=jsl/tree/icons/minustop.gif|minus=jsl/tree/icons/minus.gif|minusbottom=jsl/tree/icons/minusbottom.gif|jointtop=jsl/tree/icons/jointop.gif|joint=jsl/tree/icons/join.gif|jointbottom=jsl/tree/icons/joinbottom.gif|empty=jsl/tree/icons/empty.gif|line=jsl/tree/icons/line.gif|nodeopen=jsl/tree/icons/open_folder.gif|nodeclose=jsl/tree/icons/folder.gif|leaf=jsl/tree/icons/page.gif|baseopen=jsl/tree/icons/root.gif|");*/
}


/********STYLE FOR TREE *******/
.tree{
}

/*Use for tree normal node text label style*/

.tree_labelNormalStyle {
	FONT-SIZE: 11px; COLOR: #000000; FONT-FAMILY: Tahoma, Verdana, Geneva, Arial, Helvetica, sans-serif; 
}

/*Use for tree node image style*/
.tree_nodeImageStyle {
	BORDER-RIGHT: 0px; BORDER-TOP: 0px; BORDER-LEFT: 0px; WIDTH: 19px; BORDER-BOTTOM: 0px; HEIGHT: 16px
}

/*Use for highlighted node text label style*/
.tree_labelHighlightStyle{
	FONT-SIZE: 11px; COLOR: #000000; FONT-FAMILY: Tahoma, Verdana, Geneva, Arial, Helvetica, sans-serif; font-weight: bold;
}
/*Use for node text label style when mouse over the label*/
.tree_labelMouseoverStyle{
	FONT-SIZE: 11px; COLOR: #000000; FONT-FAMILY: Tahoma, Verdana, Geneva, Arial, Helvetica, sans-serif; font-style: italic;

}
/*So that the text will have no underline*/
.tree_linkTextStyle{
	TEXT-DECORATION: none
}


/*Use for getting all the system image icons*/
.tree_classForTreeSysIcons {
	/*Sample style, please uncomment the following line and modify the icons path if you want use your own customized system icons*/
	/*list-style-image:url("|plustop=jsl/tree/icons/plustop.gif|plus=jsl/tree/icons/plus.gif|plusbottom=jsl/tree/icons/plusbottom.gif|minustop=jsl/tree/icons/minustop.gif|minus=jsl/tree/icons/minus.gif|minusbottom=jsl/tree/icons/minusbottom.gif|jointtop=jsl/tree/icons/jointop.gif|joint=jsl/tree/icons/join.gif|jointbottom=jsl/tree/icons/joinbottom.gif|empty=jsl/tree/icons/empty.gif|line=jsl/tree/icons/line.gif|nodeopen=jsl/tree/icons/open_folder.gif|nodeclose=jsl/tree/icons/folder.gif|leaf=jsl/tree/icons/page.gif|baseopen=jsl/tree/icons/root.gif|");*/
}

