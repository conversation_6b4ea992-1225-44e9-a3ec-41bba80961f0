<%-- 
	物品登録
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function loadAction(event) {
// 画面ロード時の物品コード、物品名称の再取得
	doBpnAjax(document.getElementById('form1:htmlBuppinCd'), event, 'form1:lblPreBuppinName');
}

function openBpnuSubWindow(field1) {
// 物品検索画面
    var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0101.jsp"
		+ "?retFieldName=" + field1;
	openModalWindow(url, "pXrc0101", "<%=com.jast.gakuen.rev.xrc.PXrc0101.getWindowOpenOption() %>");
	return false;
}
function doBpnAjax(thisObj, thisEvent, targetLabel) {
// 物品名称を取得する
	var servlet = "rev/xrc/XrcBpnAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc00101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc00101.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc00101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc00101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellspacing="0" cellpadding="0" width="750">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%" border="0" cellspacing="0" cellpadding="0" class="table" >
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblBuppinCd"
										style="#{pc_Xrc00101.propBuppinCd.labelStyle}"
										value="#{pc_Xrc00101.propBuppinCd.labelName}"></h:outputText></TH>
									<TD width="600"><h:inputText styleClass="inputText"
										id="htmlBuppinCd"
										value="#{pc_Xrc00101.propBuppinCd.stringValue}"
										style="#{pc_Xrc00101.propBuppinCd.style}"
										maxlength="#{pc_Xrc00101.propBuppinCd.maxLength}" size="10"
										onblur="return doBpnAjax(this, event, 'form1:lblPreBuppinName');"
										disabled="#{pc_Xrc00101.propBuppinCd.disabled}">
										</h:inputText><hx:commandExButton
										type="button" value="" styleClass="commandExButton_search"
										id="search"
										onclick="return openBpnuSubWindow('form1:htmlBuppinCd');"
										disabled="#{pc_Xrc00101.propSearchButton.disabled}"></hx:commandExButton>
										<hx:commandExButton type="submit" styleClass="commandExButton"
									     id="select" value="選択" action="#{pc_Xrc00101.doSelectAction}"
									     disabled="#{pc_Xrc00101.propSelectButton.disabled}"></hx:commandExButton>
								        <f:attribute value="30" name="width" />
								        <f:attribute value="text-align: center; vertical-align: middle"
									    name="style" /><hx:commandExButton type="submit" value="解除"
										styleClass="commandExButton" id="unselect"
										action="#{pc_Xrc00101.doUnSelectAction}"
										disabled="#{pc_Xrc00101.propUnSelectButton.disabled}">
									</hx:commandExButton>
										<h:outputText styleClass="outputText" id="lblPreBuppinName"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblBuppinName"
										value="#{pc_Xrc00101.propBuppinName.labelName}"
										style="#{pc_Xrc00101.propBuppinName.labelStyle}"></h:outputText></TH>
									<TD width="600"><h:inputText styleClass="inputText"
										id="htmlBuppinName"
										value="#{pc_Xrc00101.propBuppinName.stringValue}"
										style="#{pc_Xrc00101.propBuppinName.style}"
										maxlength="#{pc_Xrc00101.propBuppinName.maxLength}" size="60"
										disabled="#{pc_Xrc00101.propBuppinName.disabled}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_c"><h:outputText
										styleClass="outputText" id="lblBuppinNameKana"
										value="#{pc_Xrc00101.propBuppinNameKana.labelName}"
										style="#{pc_Xrc00101.propBuppinNameKana.labelStyle}"></h:outputText></TH>
									<TD width="600"><h:inputText styleClass="inputText"
										id="htmlBuppinNameKana"
										value="#{pc_Xrc00101.propBuppinNameKana.stringValue}"
										style="#{pc_Xrc00101.propBuppinNameKana.style}"
										maxlength="#{pc_Xrc00101.propBuppinNameKana.maxLength}"
										size="60" disabled="#{pc_Xrc00101.propBuppinNameKana.disabled}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_d"><h:outputText
										styleClass="outputText" id="lblBuppinKbn"
										value="#{pc_Xrc00101.propBuppinKbn.labelName}"
										style="#{pc_Xrc00101.propBuppinKbn.labelStyle}"></h:outputText></TH>
									<TD width="600"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlBuppinKbn"
										value="#{pc_Xrc00101.propBuppinKbn.value}"
										style="#{pc_Xrc00101.propBuppinKbn.style};width:200px"
										disabled="#{pc_Xrc00101.propBuppinKbn.disabled}">
										<f:selectItems value="#{pc_Xrc00101.propBuppinKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150" class="v_g"><h:outputText
										styleClass="outputText" id="lblHaccyuTen"
										value="#{pc_Xrc00101.propHaccyuTen.labelName}"
										style="#{pc_Xrc00101.propHaccyuTen.labelStyle}"></h:outputText></TH>
									<TD width="600"><h:inputText styleClass="inputText"
										id="htmlHaccyuTen"
										value="#{pc_Xrc00101.propHaccyuTen.integerValue}"
										style="#{pc_Xrc00101.propHaccyuTen.style}"
										maxlength="#{pc_Xrc00101.propHaccyuTen.maxLength}"
										size="4" 
										disabled="#{pc_Xrc00101.propHaccyuTen.disabled}">
										<f:convertNumber type="number" pattern="######0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_g"><h:outputText
										styleClass="outputText" id="lblHaihonTeisiTen"
										value="#{pc_Xrc00101.propHaihonTeisiTen.labelName}"
										style="#{pc_Xrc00101.propHaihonTeisiTen.labelStyle}"></h:outputText></TH>
									<TD width="600"><h:inputText styleClass="inputText"
										id="htmlHaihonTeisiTen"
										value="#{pc_Xrc00101.propHaihonTeisiTen.integerValue}"
										style="#{pc_Xrc00101.propHaihonTeisiTen.style}"
										maxlength="#{pc_Xrc00101.propHaihonTeisiTen.maxLength}"
										size="4" 
										disabled="#{pc_Xrc00101.propHaihonTeisiTen.disabled}">
										<f:convertNumber type="number" pattern="######0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_g"><h:outputText
										styleClass="outputText" id="lblHaihonEd"
										value="#{pc_Xrc00101.propHaihonEd.labelName}"
										style="#{pc_Xrc00101.propHaihonEd.labelStyle}"></h:outputText></TH>
									<TD width="600"><h:inputText styleClass="inputText"
										id="htmlHaihonEd"
										value="#{pc_Xrc00101.propHaihonEd.integerValue}"
										style="#{pc_Xrc00101.propHaihonEd.style}"
										maxlength="#{pc_Xrc00101.propHaihonEd.maxLength}"
										size="2" 
										disabled="#{pc_Xrc00101.propHaihonEd.disabled}">
										<f:convertNumber type="number" pattern="##0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_h"><h:outputText
										styleClass="outputText" id="lblHaisiFlg"
										value="#{pc_Xrc00101.propHaisiFlg.labelName}"
										style="#{pc_Xrc00101.propHaisiFlg.labelStyle}"></h:outputText></TH>
									<TD width="600"><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="htmlHaisiFlg"
										value="#{pc_Xrc00101.propHaisiFlg.checked}"
										style="#{pc_Xrc00101.propHaisiFlg.style}"
										disabled="#{pc_Xrc00101.propHaisiFlg.disabled}"></h:selectBooleanCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><CENTER><TABLE class="button_bar" width="750">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="明細登録"
							styleClass="commandExButton_dat" id="exec"
							action="#{pc_Xrc00101.doExecAction}"
							disabled="#{pc_Xrc00101.propExecButton.disabled}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE></CENTER><BR>
			<HR class="hr" noshade>
			<BR>
			<TABLE width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Xrc00101.doRegisterAction}"
							disabled="#{pc_Xrc00101.propRegisterButton.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Xrc00101.doDeleteAction1}"
							disabled="#{pc_Xrc00101.propDeleteButton.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrc00101.doClearAction}"
							disabled="#{pc_Xrc00101.propClearButton.disabled}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />			
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

