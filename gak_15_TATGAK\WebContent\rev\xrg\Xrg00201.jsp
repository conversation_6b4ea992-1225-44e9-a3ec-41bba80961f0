<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"	>	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
}

// 授業コード検索アイコン押下時処理
function openJugyoCdSearchWindow(thisObj, thisEvent) {
	
	var nendo = document.getElementById('form1:htmlTargetNendo').value;
	var schSbtCd = document.getElementById('form1:htmlSearchSchoolingSbtCd').value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlSearchInputJugyoCd&nendo="+nendo+"&schSbtCd="+schSbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return true;
}

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します

//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

check('htmlJugyoList','htmlSelected');

}
function func_2(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します

//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

uncheck('htmlJugyoList','htmlSelected');
}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
				<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00201.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00201.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">
				<!-- ↓ここに戻るボタンを配置 -->
				<TABLE>
					<TR>
						<TD></TD>
					</TR>
				</TABLE>
				<!-- ↑ここに戻るボタンを配置 -->
			</DIV>

			<!--↓content↓-->
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="716">
						<TBODY>
							<TR>
								<TH class="v_b">
									<h:outputText styleClass="outputText"
										id="lblTargetNendo"
										value="#{pc_Xrg00201.propTargetNendo.labelName}" 
										style="#{pc_Xrg00201.propTargetNendo.labelStyle}">
									</h:outputText>
								</TH>
								<TD width="450">
									<h:inputText styleClass="inputText"
										id="htmlTargetNendo" tabindex="1"
										value="#{pc_Xrg00201.propTargetNendo.dateValue}"
										disabled="#{pc_Xrg00201.propTargetNendo.disabled}"
										style="#{pc_Xrg00201.propTargetNendo.style}" size="10">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText>
								</TD>
								<TD rowspan="3" width="126" style="background-color: transparent;border-bottom-style: none; border-top-style: none; border-right-style: none; text-align: right">
									<hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select" tabindex="2"
										action="#{pc_Xrg00201.doSelectAction}"
										disabled="#{pc_Xrg00201.propSelect.disabled}"
										style="#{pc_Xrg00201.propSelect.style}">
									</hx:commandExButton>
									<hx:commandExButton type="submit" value="解除" tabindex="3"
										styleClass="commandExButton" id="release" 
										action="#{pc_Xrg00201.doReleaseAction}"
										disabled="#{pc_Xrg00201.propRelease.disabled}"
										style="#{pc_Xrg00201.propRelease.style}">
									</hx:commandExButton>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					<HR width="100%" class="hr" noshade>
					<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar">
						<TBODY>
							<TR>
								<TD width="700">
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="entry" tabindex="4" value="新規登録"
										action="#{pc_Xrg00201.doEntryAction}" 
										disabled="#{pc_Xrg00201.propEntry.disabled}"
										style="#{pc_Xrg00201.propEntry.style}">
									</hx:commandExButton>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					<BR>
					<TABLE border="0" cellpadding="0" cellspacing="0" width="716"
						class="table">
						<TBODY>
							<TR>
								<TH class="v_a" width="140">
									<h:outputText styleClass="outputText" 
										id="lblSearchSchoolingSbtCd" 
										value="#{pc_Xrg00201.propSearchSchoolingSbtCd.labelName}" 
										style="#{pc_Xrg00201.propSearchSchoolingSbtCd.labelStyle}">
									</h:outputText>
								</TH>
								<TD width="300" colspan="2">
									<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSearchSchoolingSbtCd"
										value="#{pc_Xrg00201.propSearchSchoolingSbtCd.value}"
										tabindex="5" style="width: 400px"
										disabled="#{pc_Xrg00201.propSearchSchoolingSbtCd.disabled}">
										<f:selectItems
											value="#{pc_Xrg00201.propSearchSchoolingSbtCd.list}" />
									</h:selectOneMenu>
								</TD>
								<TD rowspan="6" width="126" style="background-color: transparent;border-bottom-style: none; border-top-style: none; border-right-style: none; text-align: right">
									<hx:commandExButton type="submit" value="検索"
										styleClass="commandExButton" id="search"	tabindex="14"
										action="#{pc_Xrg00201.doSearchAction}"
										style="#{pc_Xrg00201.propSearch.style}"
										disabled="#{pc_Xrg00201.propSearch.disabled}">
									</hx:commandExButton> 
									<hx:commandExButton type="submit" value="解除"
										styleClass="commandExButton" id="releaseSearchResult" tabindex="15"
										action="#{pc_Xrg00201.doReleaseSearchResultAction}"
										disabled="#{pc_Xrg00201.propReleaseSearchResult.disabled}"
										style="#{pc_Xrg00201.propReleaseSearchResult.style}">
									</hx:commandExButton>
								</TD>
							</TR>
							<TR>
								<TH class="v_b">
									<h:outputText styleClass="outputText"
										id="lblSearchInputKi"
										value="#{pc_Xrg00201.propSearchInputKi.labelName}"
										style="#{pc_Xrg00201.propSearchInputKi.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSearchInputKi"
										value="#{pc_Xrg00201.propSearchInputKi.value}"
										tabindex="6" style="width: 160px"
										disabled="#{pc_Xrg00201.propSearchInputKi.disabled}">
										<f:selectItems
											value="#{pc_Xrg00201.propSearchInputKi.list}" />
									</h:selectOneMenu>
								</TD>
							</TR>
							<TR>
								<TH class="v_b">
									<h:outputText styleClass="outputText"
										id="lblSearchInputJigen"
										value="#{pc_Xrg00201.propSearchInputJigen.labelName}"
										style="#{pc_Xrg00201.propSearchInputJigen.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:inputText styleClass="inputText"
										id="htmlSearchInputJigen" tabindex="7"
										value="#{pc_Xrg00201.propSearchInputJigen.integerValue}"
										readonly="#{pc_Xrg00201.propSearchInputJigen.readonly}"
										style="#{pc_Xrg00201.propSearchInputJigen.style}"
										disabled="#{pc_Xrg00201.propSearchInputJigen.disabled}"
										maxlength="#{pc_Xrg00201.propSearchInputJigen.max}"
										size="5">
										<f:convertNumber type="number" pattern="#0"/>
										<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
									</h:inputText>
								</TD>
							</TR>
							<TR>
								<TH class="v_c">
									<h:outputText styleClass="outputText"
										id="lblSearchInputJugyoCd"
										value="#{pc_Xrg00201.propSearchInputJugyoCd.labelName}"
										style="#{pc_Xrg00201.propSearchInputJugyoCd.labelStyle}">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText styleClass="inputText"
										id="htmlSearchInputJugyoCd" tabindex="8"
										value="#{pc_Xrg00201.propSearchInputJugyoCd.stringValue}"
										readonly="#{pc_Xrg00201.propSearchInputJugyoCd.readonly}"
										style="#{pc_Xrg00201.propSearchInputJugyoCd.style}"
										disabled="#{pc_Xrg00201.propSearchInputJugyoCd.disabled}"
										maxlength="#{pc_Xrg00201.propSearchInputJugyoCd.max}"
										onblur=""
										size="20">
									</h:inputText>
									<hx:commandExButton type="button"
										styleClass="commandExButton_search" id="searchJugyoCd"
										onclick="return openJugyoCdSearchWindow(this, event);"
										tabindex="9"
										disabled="#{pc_Xrg00201.propSearchJugyoCd.disabled}"
										style="#{pc_Xrg00201.propSearchJugyoCd.style}">
									</hx:commandExButton>
								</TD>
								<TD width="150">
									<h:outputText styleClass="outputText" id="lblSearchCondition1"
									value="（前方一致）">
									</h:outputText>
								</TD>
							</TR>
							<TR>
								<TH class="v_d">
									<h:outputText styleClass="outputText" 
										id="lblSearchInputKamokuName" 
										value="#{pc_Xrg00201.propSearchInputKamokuName.labelName}" 
										style="#{pc_Xrg00201.propSearchInputKamokuName.labelStyle}">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText styleClass="inputText" 
										id="htmlSearchInputKamokuName"	tabindex="10"
										value="#{pc_Xrg00201.propSearchInputKamokuName.stringValue}"
										readonly="#{pc_Xrg00201.propSearchInputKamokuName.readonly}"
										style="#{pc_Xrg00201.propSearchInputKamokuName.style}"
										disabled="#{pc_Xrg00201.propSearchInputKamokuName.disabled}" 
										maxlength="#{pc_Xrg00201.propSearchInputKamokuName.maxLength}" 
										size="40">
									</h:inputText>
								</TD>
								<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSearchCondition2" tabindex="11"
										value="#{pc_Xrg00201.propSearchCondition2.value}" 
										style="#{pc_Xrg00201.propSearchCondition2.style}"
										disabled="#{pc_Xrg00201.propSearchCondition2.disabled}"
										readonly="#{pc_Xrg00201.propSearchCondition2.readonly}">
										<f:selectItems value="#{pc_Xrg00201.propSearchCondition2.list}" />
									</h:selectOneRadio>
								</TD>
							</TR>
							<TR>
								<TH class="v_d">
									<h:outputText styleClass="outputText" 
										id="lblSearchInputJugyoName" 
										value="#{pc_Xrg00201.propSearchInputJugyoName.labelName}" 
										style="#{pc_Xrg00201.propSearchInputJugyoName.labelStyle}">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText styleClass="inputText" 
										id="htmlSearchInputJugyoName" tabindex="12"
										value="#{pc_Xrg00201.propSearchInputJugyoName.stringValue}"
										readonly="#{pc_Xrg00201.propSearchInputJugyoName.readonly}"
										style="#{pc_Xrg00201.propSearchInputJugyoName.style}"
										disabled="#{pc_Xrg00201.propSearchInputJugyoName.disabled}" 
										maxlength="#{pc_Xrg00201.propSearchInputJugyoName.maxLength}" 
										size="40">
									</h:inputText>
								</TD>
								<TD>
									<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSearchCondition3" tabindex="13"
										value="#{pc_Xrg00201.propSearchCondition3.value}" 
										style="#{pc_Xrg00201.propSearchCondition3.style}"
										disabled="#{pc_Xrg00201.propSearchCondition3.disabled}"
										readonly="#{pc_Xrg00201.propSearchCondition3.readonly}">
										<f:selectItems value="#{pc_Xrg00201.propSearchCondition3.list}" />
									</h:selectOneRadio>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					<HR width="100%" class="hr" noshade>
					<!-- ↓データテーブル部↓ -->
					<TABLE border="0" cellpadding="5">
						<TBODY>
							<TR>
								<TD align="right">
									<h:outputText styleClass="outputText"
										id="htmlJugyoListCount"
										value="#{pc_Xrg00201.propJugyoListCount.stringValue}"
										style="#{pc_Xrg00201.propJugyoListCount.style}">
									</h:outputText>
								</TD>
							</TR>
							<TR>
								<TD>
									<DIV style="height:220px" class="listScroll" 
									id="listScroll" onscroll="setScrollPosition('scroll',this);">
									<h:dataTable border="0" cellpadding="0" cellspacing="0"
										columnClasses="columnClass"
										headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_Xrg00201.propJugyoList.rowClasses}"
										styleClass="meisai_scroll" id="htmlJugyoList"
										width="845" value="#{pc_Xrg00201.propJugyoList.list}"
										var="varlist">
										<!-- サンプル:Kmc01101 -->
										<h:column id="column01">
											<f:facet name="header">
											</f:facet>
											<h:selectBooleanCheckbox
												styleClass="selectBooleanCheckbox" id="htmlSelected" tabindex="16"
												value="#{varlist.selected}">
											</h:selectBooleanCheckbox>
											<f:attribute value="35" name="width" />
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
										<h:column id="column02">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="授業コード"
													id="lblJugyoCd">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlJugyoCd" value="#{varlist.jugyoCd}" 
												>
											</h:outputText>
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="75" name="width" />
										</h:column>
										<h:column id="column03">
											<f:facet name="header">	
												<h:outputText styleClass="outputText" id="lblKiColumn"
													value="開催期">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlKiColumn" styleClass="outputText" 
												value="#{varlist.propKiColumn.displayValue}" 
												title="#{varlist.propKiColumn.stringValue}"
												>
											</h:outputText>
											<f:attribute value="true" name="nowrap" />
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="60" name="width" />
										</h:column>
										<h:column id="column04">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblJigenColumn" value="時限">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlJigenColumn" styleClass="outputText" 
												value="#{varlist.propJigenColumn.displayValue}" 
												title="#{varlist.propJigenColumn.stringValue}"
												>
											</h:outputText>
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="50" name="width" />
										</h:column>
										<h:column id="column05">
											<f:facet name="header">	
												<h:outputText styleClass="outputText" id="lblJugyoKaisuColumn"
													value="授業回数">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlJugyoKaisuColumn" styleClass="outputText" 
												value="#{varlist.jugyoKaisu}" 
												>
											</h:outputText>
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="70" name="width" />
										</h:column>
										<h:column id="column06">
											<f:facet name="header">
												<h:outputText styleClass="outputText" styleClass="outputText" 
													id="lblKomasuColumn" value="コマ数">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlKomasuColumn" styleClass="outputText"
												value="#{varlist.komasu}" 
												>
											</h:outputText>
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="60" name="width" />
										</h:column>
										<h:column id="column07">
											<f:facet name="header">	
												<h:outputText styleClass="outputText" id="lblShussekirtColumn"
													value="出席率">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlShussekirtColumn" styleClass="outputText" 
												value="#{varlist.shussekirt}" 
												>
											</h:outputText>
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="60" name="width" />
										</h:column>
										<h:column id="column08">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblClassFrwkFlgColumn" value="クラス振分">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlClassFrwkFlgColumn" styleClass="outputText" 
												value="#{varlist.propClassFrwkFlgColumn.displayValue}" 
												title="#{varlist.propClassFrwkFlgColumn.stringValue}"
												>
											</h:outputText>
											<f:attribute value="center" name="align" />
											<f:attribute value="text-align: center" name="style" />
											<f:attribute value="80" name="width" />
										</h:column>
										<h:column id="column09">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblCalcPtnColumn" value="諸費計算パターン">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlCalcPtnColumn" styleClass="outputText" 
												value="#{varlist.propCalcPtnColumn.displayValue}" 
												title="#{varlist.propCalcPtnColumn.stringValue}"
												>
											</h:outputText>
											<f:attribute value="160" name="width" />
										</h:column>
										<h:column id="column10">
											<f:facet name="header">	
												<h:outputText styleClass="outputText" id="lblJugyoName"
													value="授業名称">
												</h:outputText>
											</f:facet>
											<h:outputText id="htmlJugyoNameColumn" styleClass="outputText" 
												value="#{varlist.propJugyoNameColumn.displayValue}" 
												title="#{varlist.propJugyoNameColumn.stringValue}"
												>
											</h:outputText>
											<f:attribute value="200" name="width" />
										</h:column>
										<h:column id="column11">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="編集"
												styleClass="commandExButton" tabindex="17"
												action="#{pc_Xrg00201.doEditAction}"
												disabled="#{pc_Xrg00201.propEdit.disabled}"
												style="#{pc_Xrg00201.propEdit.style}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="コピー"
												styleClass="commandExButton" tabindex="18"
												action="#{pc_Xrg00201.doCopyAction}"
												disabled="#{pc_Xrg00201.propCopy.disabled}"
												style="#{pc_Xrg00201.propCopy.style}">
											</hx:commandExButton>
											<f:attribute value="true" name="nowrap" />
											<f:attribute value="75" name="width" />
										</h:column>
										</h:dataTable>
									</DIV>
								</TD>
							</TR>
							<TR>
								<TD align="left">
									<hx:commandExButton type="button" styleClass="check" tabindex="19"
										id="check"onclick="return func_1(this, event);">
									</hx:commandExButton>
									<hx:commandExButton type="button" styleClass="uncheck" tabindex="20"
										id="uncheck" onclick="return func_2(this, event);">
									</hx:commandExButton>
								</TD>
							</TR>
							<TR>
								<TD align="center">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="800"
										class="button_bar">
										<TBODY>
											<TR>
												<TD>
													<hx:commandExButton type="submit" value="削除"
														styleClass="commandExButton_dat" id="delete"
														action="#{pc_Xrg00201.doDeleteAction}" tabindex="21"
														disabled="#{pc_Xrg00201.propDelete.disabled}"
														style="#{pc_Xrg00201.propDelete.style}"
														confirm="#{msg.SY_MSG_0004W}">
													</hx:commandExButton>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					<!-- ↑データテーブル部↑ -->
				</DIV>
			</DIV>
			<h:inputHidden
				value="#{pc_Xrg00201.propExecutableSearch.stringValue}"
				id="htmlExecutableSearch">
			</h:inputHidden>
		</DIV>
				<h:inputHidden value="#{pc_Xrg00201.propJugyoList.scrollPosition}"
					id="scroll"></h:inputHidden>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>