<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@page import="com.jast.gakuen.rev.xrl.Xrl00701"; %>
<%@page import="com.jast.gakuen.framework.util.UtilSystem";%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrl00701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	return true;
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00701.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrl00701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="700" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
					   <TH width="80" nowrap class="v_a">
					   		<h:outputText
								styleClass="outputText" id="lblTeateNendo"
								value="#{pc_Xrl00701.propTeateNendo.labelName}"
								style="#{pc_Xrl00701.propTeateNendo.labelStyle}">
							</h:outputText></TH>
						<TD nowrap style="border: medium none;" width="200px">
							<h:inputText styleClass="inputText"
								id="txtTeateNendo"
								value="#{pc_Xrl00701.propTeateNendo.dateValue}"
								maxlength="#{pc_Xrl00701.propTeateNendo.maxLength}"
								disabled="#{pc_Xrl00701.outputTargetKbn != 1}"
								style="#{pc_Xrl00701.propTeateNendo.style}" size="6"
								>
							 <hx:inputHelperAssist errorClass="inputText_Error"
								    imeMode="inactive" promptCharacter="_" />
							 <f:convertDateTime pattern="yyyy" />
	                  		</h:inputText>					
						</TD>
						<TD style="background-color: transparent; text-align: left" class="clear_border">&nbsp;</TD>
					</TR>
					<TR>
			            <TH class="v_b" width="80"><h:outputText styleClass="outputText"
							id="lblOutputTarget" value="#{pc_Xrl00701.propOutputTarget.labelName}"
							style="#{pc_Xrl00701.propOutputTarget.labelStyle}">
							</h:outputText></TH>
						<TD><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlOutputTarget"
							value="#{pc_Xrl00701.propOutputTarget.value}"
							layout="lineDirection"
							onclick=""
							disabled="#{pc_Xrl00701.outputTargetKbn != 1}">
							<f:selectItems value="#{pc_Xrl00701.propOutputTarget.list}" />
						</h:selectOneRadio>
						</TD>
						<TD style="background-color: transparent; text-align: left" class="clear_border">
						<hx:commandExButton type="submit"
							style="margin-left:10px;"
							value="選択" styleClass="commandExButton" id="selectXrlTatt"
							disabled="#{pc_Xrl00701.outputTargetKbn != 1}"
							action="#{pc_Xrl00701.doSelectXrlTattAction}">
						</hx:commandExButton>
						<hx:commandExButton type="submit"
							value="解除" styleClass="commandExButton" id="releaseXrlTatt"
							disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
							action="#{pc_Xrl00701.doReleaseXrlTattAction}">
						</hx:commandExButton>
						</TD>
					</TR>

				</TBODY>
			</TABLE>
			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" width="700" class="tab_body">
			<TBODY>
				<TR height="100">
				<TD valign="top"  align="center">
					<BR>
				<% 
				Xrl00701 pc = (Xrl00701)UtilSystem.getManagedBean(Xrl00701.class);		
				int outputTarget= pc.getOutputTargetKbn();
				if(outputTarget == 2){
             	%>
					<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="table">
						<TBODY>				
								<TR>
								<TH class="v_c" width="120"><h:outputText styleClass="outputText"
									id="lblNendo" value="#{pc_Xrl00701.propSikyuTaisyoTuk.labelName}"
									></h:outputText></TH>
								<TD align="left"><h:selectOneMenu styleClass="selectOneMenu"
									id="htmlNendo" value="#{pc_Xrl00701.propSikyuTaisyoTuk.value}"
									disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
									style="width:150px">
									<f:selectItems value="#{pc_Xrl00701.propSikyuTaisyoTuk.list}" />
								</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_d" width="120"><h:outputText styleClass="outputText"
										id="lblGakkiNo" value="#{pc_Xrl00701.propTeateGyomuName.labelName}"
										></h:outputText></TH>
									<TD align="left"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakkiNo" value="#{pc_Xrl00701.propTeateGyomuName.value}"
										disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
										style="#{pc_Xrl00701.propTeateGyomuName.style};width:450px">
										<f:selectItems value="#{pc_Xrl00701.propTeateGyomuName.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_e" width="120"><h:outputText styleClass="outputText"
										id="lblTeateSakuseiDate"
										value="#{pc_Xrl00701.propTeateSakuseiDate.labelName}"></h:outputText></TH>
									<TD align="left"><h:inputText styleClass="inputText"
										id="htmlGakkiStartDate"
										value="#{pc_Xrl00701.propTeateSakuseiDate.dateValue}"
										disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
										style="#{pc_Xrl00701.propTeateSakuseiDate.style}" size="10">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_f" width="120"><h:outputText styleClass="outputText"
										id="lblSiharaiDate"
										value="#{pc_Xrl00701.propSiharaiDate.labelName}"
										></h:outputText></TH>
									<TD align="left"><h:inputText styleClass="inputText"
										id="htmlGakkiEndDate"
										value="#{pc_Xrl00701.propSiharaiDate.dateValue}"
										disabled="#{pc_Xrl00701.propSiharaiDate.disabled}"
										style="#{pc_Xrl00701.propSiharaiDate.style}" size="10">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
					</TABLE>
					<% }%>
				<TD/>
				<TR/>
				<TR height="100">
				<TD valign="top"  align="center">
					<BR>
					<% 
					if(outputTarget == 2){
             		%>
					<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="table">
						<TBODY>
				
								<TR>
									<TH class="v_g" width="130"><h:outputText styleClass="outputText"
										id="lblReprotTitle"
										value="#{pc_Xrl00701.propReprotTitle.labelName}"
										style="#{pc_Xrl00701.propReprotTitle.labelStyle}"></h:outputText></TH>
									<TD width="470"><h:inputText styleClass="inputText" id="htmlReprotTitle"
										style="#{pc_Xrl00701.propReprotTitle.style}"
										maxlength="#{pc_Xrl00701.propReprotTitle.maxLength}"
										disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
										value="#{pc_Xrl00701.propReprotTitle.stringValue}" size="60"></h:inputText></TD>
								</TR>
							</TBODY>
					</TABLE>
					<% }%>
				<TD/>
				<TR/>
				<TR>
					<% 
					if(outputTarget == 2){
             		%>
					<TD align="center" valign="middle"><hx:commandExButton
						disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
						type="submit" value="PDF作成" styleClass="commandExButton_out"
						id="pdfout" action="#{pc_Xrl00701.doPdfoutAction}"
						confirm="#{msg.SY_MSG_0019W}"
						style="margin-right:6px"></hx:commandExButton><hx:commandExButton
						disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
						type="submit" value="CSV作成" styleClass="commandExButton_out"
						id="csvout" action="#{pc_Xrl00701.doCsvoutAction}"
						confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton
						disabled="#{pc_Xrl00701.outputTargetKbn != 2}"
						type="submit" value="出力項目指定" styleClass="commandExButton_out"
						id="setoutput" action="#{pc_Xrl00701.doSetoutputAction}"
						onclick="return func_1(this, event);"
						style="margin-left:6px"></hx:commandExButton></TD>
					<% }%>

				</TR>
				<TR  height="150">
					<TD>&nbsp;</TD>
				</TR>	
				</TBODY>
			</TABLE>
			
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

