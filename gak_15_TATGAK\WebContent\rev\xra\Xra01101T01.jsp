<%-- 
	学籍情報登録（入試）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra01101T01.java" --%>
<%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>

<%@page import="com.jast.gakuen.rev.xra.Xra01101T01"%>
<%@page import="java.util.ArrayList"%>
<%@page import="com.jast.gakuen.framework.util.UtilSystem"%>


<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob00101T53.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

// 画面ロード時の学生名称・出身校その他出身校名称の再取得
function loadAction(event){
	doGakuseiAjax(document.getElementById('form1:htmlGaksekiNo'), event, 'form1:htmlGakuseiNm');
	
		
//@@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 START 
  	// 学生検索リストから遷移時、選択ボタン実行
  	var exeSelectflg = document.getElementById("form1:htmlHidExeSelectFlg").value;
	document.getElementById("form1:htmlHidExeSelectFlg").value = "0";
	if (exeSelectflg == "1") {
		document.getElementById("form1:select").click();
	}
//@@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 End 

}

//@@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 START 
	// 学生リストのクリック処理
	function onClickGakuseiList(id) {
	  var kbn = document.getElementById("form1:htmlHidChangeDataFlg").value;
	  if (kbn == "1") {
	  	//document.getElementById("form1:htmlHidChangeDataFlg").value = "0";
	    return doPopupMsg(id);
	  }
	  return true;
	}
	// データチェンジ時
	function onCangeData() {
	  document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
	}
//@@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 End 

// 学生検索画面（引数：①学籍番号）
function openSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
		+ "?retFieldName=" + field1;
		
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption()%>");
	return false;
}

// 学生氏名を取得する
function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// 出身校検索画面
function openSyushinSubWindow(thisObj, thisEven) {
	openModalWindow("", "PCoa0101", "<%=com.jast.gakuen.rev.co.PCoa0101.getWindowOpenOption()%>");
		setTarget("PCoa0101");
		return true;
	}

	function doSyushinAjax(thisObj, thisEvent, targetLabel) {
		// 出身校名称を取得する
		var servlet = "rev/co/CoaSsnAJAX";
		var args = new Array();
		args['code1'] = thisObj.value;

		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

	// 異動登録遷移時のクリック処理
	function onClickIdoToroku(id) {
		var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
		if (kbn == "0") {
			var changeDataFlg = document
					.getElementById("form1:htmlHidChangeDataFlg").value;
			if (changeDataFlg == "1") {
				return doPopupMsg(id, "画面に変更があった場合、更新内容は反映されませんが、");
			} else {
				return true;
			}
		}
		return true;
	}

	// 更新時のクリック処理
	function onClickUpdate(id) {
		var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
		if (kbn == "0") {
			return confirm(id);
		}
		return true;
	}

	// 削除時のクリック処理
	function onClickDelete(id) {
		var kbn = document.getElementById("form1:htmlHidButtonKbn").value;
		if (kbn == "0") {
			return confirm(id);
		}
		return true;
	}

	// ポップアップメッセージを表示
	function doPopupMsg(id, msg) {
		var args = new Array();
		args[0] = msg;
		return confirm(messageCreate(id, args));
	}

	// confirmメッセージ後「はい」を選択した時の処理
	function confirmOk() {
		document.getElementById("form1:htmlHidButtonKbn").value = "1";
		if (document.getElementById("form1:htmlHidKengenNashiFlg").value == "1") {
			document.getElementById("form1:htmlHidButtonKbnAuth").value = "1";
		}
		// 資格削除チェックの実行フラグ
		// 資格削除チェックが実行されている場合
		if (document.getElementById("form1:htmlHidButtonKbnSikakFlg").value == "1") {
			// 再度アクション実行時は資格チェックではなく資格削除処理を実行させるため区分に「1」を格納する
			document.getElementById("form1:htmlHidButtonKbnSikak").value = "1";
		}
		var action = document.getElementById("form1:htmlHidAction").value;
		indirectClick(action);
	}
	// confirmメッセージ後「キャンセル」を選択した時の処理
	function confirmCancel() {
		document.getElementById("form1:htmlHidButtonKbn").value = "0";
		document.getElementById("form1:htmlHidButtonKbnAuth").value = "0";
		document.getElementById("form1:htmlHidKengenNashiFlg").value = "0";
		document.getElementById("form1:htmlHidButtonKbnSikak").value = "0";
		document.getElementById("form1:htmlHidButtonKbnSikakFlg").value = "0";
	}
</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<f:loadBundle basename="properties.messageCO" var="msgCO" />

	<BODY onLoad="loadAction(event)">
		<hx:scriptCollector id="scriptCollector1"
			preRender="#{pc_Xra01101T01.onPageLoadBegin}">
			<gakuen:itemStateCtrl managedbean="pc_Xra01101T01">
				<h:form styleClass="form" id="form1">

					<!-- ヘッダーインクルード -->
					<jsp:include page="../inc/header.jsp" />

					<!-- ヘッダーへのデータセット領域 -->
					<div style="display: none;">
						<hx:commandExButton type="submit" value="閉じる"
							styleClass="commandExButton" id="closeDisp"
							action="#{pc_Cob00101T01.doCloseDispAction}"></hx:commandExButton>
						<h:outputText styleClass="outputText" id="htmlFuncId"
							value="#{pc_Xra01101T01.funcId}"></h:outputText>
						<h:outputText styleClass="outputText" id="htmlLoginId"
							value="#{SYSTEM_DATA.loginID}"></h:outputText>
						<h:outputText styleClass="outputText" id="htmlScrnName"
							value="#{pc_Xra01101T01.screenName}"></h:outputText>
					</div>

					<!--↓outer↓-->
					<DIV class="outer">

						<FIELDSET class="fieldset_err">
							<LEGEND>エラーメッセージ</LEGEND>
							<h:outputText id="message"
								value="#{requestScope.DISPLAY_INFO.displayMessage}"
								styleClass="outputText" escape="false">
							</h:outputText>
						</FIELDSET>

						<!--↓content↓-->
						<DIV class="head_button_area">
							<!-- ↓ここに戻る／閉じるボタンを配置 -->
							<hx:commandExButton type="submit" value="プロファイル"
								styleClass="commandExButton" id="referProfile"
								disabled="#{pc_Cob00101T01.cob00101.propDetail.disabled}"
								action="#{pc_Xra01101T01.doReferProfileAction}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="自由設定"
								styleClass="commandExButton" id="freeSettei"
								disabled="#{pc_Cob00101T01.cob00101.propDetail.disabled}"
								action="#{pc_Xra01101T01.doFreeAction}"></hx:commandExButton>
							<hx:commandExButton type="submit" value="異動登録"
								styleClass="commandExButton" id="idoToroku"
								disabled="#{pc_Cob00101T01.cob00101.propDetail.disabled}"
								onclick="return onClickIdoToroku('#{msgCO.CO_MSG_0008W}');"
								action="#{pc_Xra01101T01.doIdoAction}"></hx:commandExButton>
							<!-- ↑ここに戻る／閉じるボタンを配置 -->
							<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
						</DIV>
						<DIV id="content">
							<DIV class="column" align="center">
								<!-- ↓ここにコンポーネントを配置 -->

								<TABLE border="0" cellpadding="5">
									<TBODY>
										<TR>
											<TD width="870">
												<TABLE class="table" width="100%">
													<TBODY>
														<TR align="center" valign="middle">
															<TH nowrap class="v_a" width="190">
																<!--学籍番号 --> <h:outputText styleClass="outputText"
																	id="lblGaksekiNo"
																	value="#{pc_Cob00101T01.cob00101.propGakusekiNo.labelName}"
																	style="#{pc_Cob00101T01.cob00101.propGakusekiNo.labelStyle}"></h:outputText>
															</TH>
															<TD width="450" colspan="3"><h:inputText
																	styleClass="inputText" id="htmlGaksekiNo" size="18"
																	maxlength="#{pc_Cob00101T01.cob00101.propGakusekiNo.maxLength}"
																	disabled="#{pc_Cob00101T01.cob00101.propGakusekiNo.disabled}"
																	value="#{pc_Cob00101T01.cob00101.propGakusekiNo.stringValue}"
																	readonly="#{pc_Cob00101T01.cob00101.propGakusekiNo.readonly}"
																	style="#{pc_Cob00101T01.cob00101.propGakusekiNo.style}"
																	onblur="return doGakuseiAjax(this, event, 'form1:htmlGakuseiNm');"></h:inputText>
																<hx:commandExButton type="button" value="検"
																	styleClass="commandExButton_search" id="btnGakusekiF"
																	disabled="#{pc_Cob00101T01.cob00101.propGakusekiNo.disabled}"
																	onclick="openSubWindow('form1:htmlGaksekiNo');">
																</hx:commandExButton> <h:inputText styleClass="likeOutput" id="htmlGakuseiNm"
																	size="40" tabindex="-1" readonly="true"
																	value="#{pc_Cob00101T01.cob00101.propName.stringValue}"></h:inputText>
															</TD>
															<TD rowspan="3"
																style="background-color: transparent; text-align: right; vertical-align: bottom"
																class="clear_border"><hx:commandExButton
																	type="submit" value="選　択" styleClass="commandExButton"
																	id="select"
																	disabled="#{pc_Cob00101T01.cob00101.propSelect.disabled}"
																	action="#{pc_Cob00101T01.doSelectAction}"></hx:commandExButton>
																<hx:commandExButton type="submit" value="解　除"
																	styleClass="commandExButton" id="unselect"
																	disabled="#{pc_Cob00101T01.cob00101.propUnSelect.disabled}"
																	action="#{pc_Cob00101T01.doUnselectAction}"></hx:commandExButton>
															</TD>
															<!-- @@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 START -->
															<TD rowspan="3"
																style="background-color: transparent; text-align: right"
																class="clear_border">

																<table style="padding: 0px; border: none;">
																	<tr>
																		<td colspan="4"><hx:commandExButton type="submit"
																				value=" リスト検索 " styleClass="commandExButton"
																				id="search"
																				onclick="return onClickGakuseiList('#{msg.SY_MSG_0014W}');"
																				action="#{pc_Cob00101T01.doGakuseiListSearchAction}"></hx:commandExButton>
																		</td>
																	</tr>
																	<TR>
																		<TD align="right"><hx:commandExButton
																				type="submit" value="|<"
											                                    styleClass="commandExButton" id="top" style="width: 100%"
																				onclick="return onClickGakuseiList('#{msg.SY_MSG_0014W}');"
																				action="#{pc_Cob00101T01.doGakuseiListTopAction}"
																				disabled="#{pc_Cob00101T01.cob00101.propTop.disabled}"></hx:commandExButton>
																		</TD>
																		<TD align="right"><hx:commandExButton
																				type="submit" value="<"
											                                    styleClass="commandExButton" id="pre" style="width: 100%"
																				onclick="return onClickGakuseiList('#{msg.SY_MSG_0014W}');"
																				action="#{pc_Cob00101T01.doGakuseiListPreAction}"
																				disabled="#{pc_Cob00101T01.cob00101.propPre.disabled}"></hx:commandExButton>
																		</TD>
																		<TD align="right"><hx:commandExButton
																				type="submit" value=">" styleClass="commandExButton"
																				id="next" style="width: 100%"
																				onclick="return onClickGakuseiList('#{msg.SY_MSG_0014W}');"
																				action="#{pc_Cob00101T01.doGakuseiListNextAction}"
																				disabled="#{pc_Cob00101T01.cob00101.propNext.disabled}"></hx:commandExButton>
																		</TD>
																		<TD align="right"><hx:commandExButton
																				type="submit" value=">|"
																				styleClass="commandExButton" id="last"
																				style="width: 100%"
																				onclick="return onClickGakuseiList('#{msg.SY_MSG_0014W}');"
																				action="#{pc_Cob00101T01.doGakuseiListLastAction}"
																				disabled="#{pc_Cob00101T01.cob00101.propLast.disabled}"></hx:commandExButton>
																		</TD>
																	</TR>
																	<TR>
																		<td colspan="4" style="background-color: transparent;">
																			<div align="right">
																				<h:outputText styleClass="outputText"
																					id="htmlGakuListCount"
																					value="#{pc_Cob00101T01.cob00101.propGakuListCount.stringValue}"></h:outputText>
																			</div>
																		</td>
																	</TR>
																</table>
															</TD>

															<!-- @@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 END -->
														</TR>
														<TR>
															<TH nowrap class="v_b">
																<!-- 学籍状況 --> <h:outputText styleClass="outputText"
																	id="lblGakJokyo"
																	value="#{pc_Cob00101T01.cob00101.propGakJokyo.labelName}"
																	style="#{pc_Cob00101T01.cob00101.propGakJokyo.labelStyle}"></h:outputText>
															</TH>
															<TD width="225" colspan="3"><h:outputText
																	styleClass="outputText" id="htmlGakJokyoOut"
																	value="#{pc_Cob00101T01.cob00101.propGakJokyo.stringValue}"></h:outputText>
															</TD>
															<!-- 旧学籍番号 -->
															<h:inputHidden id="lblKyuGaksekiCd"
																value="#{pc_Cob00101T01.cob00101.propKyuGakusekiCd.stringValue}"></h:inputHidden>
														</TR>
														<TR>
															<TH nowrap class="v_b">
																<!-- 所属基準日 --> <h:outputText styleClass="outputText"
																	id="lblKijunDate"
																	value="#{pc_Cob00101T01.cob00101.propKijunDate.labelName}"
																	style="#{pc_Cob00101T01.cob00101.propKijunDate.labelStyle}"></h:outputText>
															</TH>
															<TD width="225" colspan="3"><h:inputText
																	styleClass="inputText" id="htmlKijunDate" size="10"
																	readonly="#{pc_Cob00101T01.cob00101.propKijunDate.readonly}"
																	onblur="onCangeData();"
																	disabled="#{pc_Cob00101T01.cob00101.propKijunDate.disabled}"
																	value="#{pc_Cob00101T01.cob00101.propKijunDate.dateValue}"
																	style="#{pc_Cob00101T01.cob00101.propKijunDate.style}">
																	<f:convertDateTime />
																	<hx:inputHelperAssist errorClass="inputText_Error"
																		promptCharacter="_" />
																	<hx:inputHelperDatePicker />
																</h:inputText></TD>
														</TR>
														<!-- @@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 START -->
														<TR
															style="padding: 0px; border-left: none; border-right: none; border-bottom: none; background-color: transparent;">
															<TD
																style="padding: 0px; border-left: none; border-right: none; border-bottom: none; background-color: transparent;">
																&nbsp;</TD>
														</TR>
														<!-- @@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 END -->
													</TBODY>
												</TABLE> <BR>
												<TABLE border="0" cellpadding="0" cellspacing="0"
													width="100%">
													<TBODY>
														<TR>
															<TD>
																<TABLE border="0" cellpadding="0" cellspacing="0"
																	width="100%" style="border-bottom-style: none;">
																	<TBODY>
																		<TR>
																			<TD class="tab_head_off" width="72px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T01" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameKihon.stringValue}"
																					action="#{pc_Xra01101T01.doTabCob00101T01Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="72px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T02" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameShozoku.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T02Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="73px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T03" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameSnkCls.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T03Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="73px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T04" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameAddr.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T04Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="73px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T05" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameKAddr.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T05Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="73px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T06" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameHsy.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T06Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="73px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T07" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameAtsk.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T07Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="73px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T08" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameRyugak.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T08Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="72px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T09" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameKyoin.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T09Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="72px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T10" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameNyushi.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T10Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="72px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T11" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameHantei.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T11Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="72px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T12" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameSonota.stringValue}"
																					action="#{pc_Cob00101T01.doTabCob00101T12Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_off" width="72px"><hx:commandExButton
																					type="submit" styleClass="tab_head_off"
																					id="tabCob00101T52" style="width:100%"
																					value="#{pc_Cob00101T01.cob00101.propTabNameTushinGakseki.stringValue}"
																					action="#{pc_Xra01101T01.doTabCob00101T52Action}"></hx:commandExButton>
																			</TD>
																			<TD class="tab_head_on" width="*"><hx:commandExButton
																					type="button" styleClass="tab_head_on"
																					id="tabCob00101T53"
																					value="#{pc_Cob00101T01.cob00101.propTabNameTushinNyusotu.stringValue}"
																					style="border-left-style: none;width: 100%"></hx:commandExButton>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</TD>
														</TR>
														<TR>
															<TD>
																<TABLE class="tab_body" border="0" cellpadding="20"
																	cellspacing="0" width="100%"
																	style="border-top-style: none;">
																	<TBODY>
																		<TR>
																			<TD width="100%">
																				<div style="height: 380px">
																					<BR>
																					<TABLE class="table" width="810">
																						<TBODY>
																							<TR>
																								<TH nowrap class="v_b" width="180">
																									<!-- 受付番号 --> <h:outputText
																										styleClass="outputText"
																										id="lblUketukeNumTitle"
																										value="#{pc_Xra01101T01.propUketukeNum.labelName}"
																										style="#{pc_Xra01101T01.propUketukeNum.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:outputText styleClass="outputText"
																										id="lblUketukeNum"
																										value="#{pc_Xra01101T01.propUketukeNum.stringValue}"
																										style="#{pc_Xra01101T01.propUketukeNum.labelStyle}">
																									</h:outputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																					<BR>
																					<TABLE class="table" width="810">
																						<TBODY>
																							<TR>
																								<TH nowrap class="v_b" width="180">
																									<!-- 登録日 --> <h:outputText
																										styleClass="outputText"
																										id="lblTorokuDateTitle"
																										value="#{pc_Xra01101T01.propTorokuDate.labelName}"
																										style="#{pc_Xra01101T01.propTorokuDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="225"><h:outputText
																										styleClass="outputText" id="lblTorokuDate"
																										value="#{pc_Xra01101T01.propTorokuDate.stringValue}"
																										style="#{pc_Xra01101T01.propTorokuDate.labelStyle}">
																									</h:outputText>
																								</TD>
																								<TH nowrap class="v_b" width="180">
																									<!-- 最終更新日 --> <h:outputText
																										styleClass="outputText"
																										id="lblSaisyuKosinDateTitle"
																										value="#{pc_Xra01101T01.propSaisyuKosinDate.labelName}"
																										style="#{pc_Xra01101T01.propSaisyuKosinDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="225"><h:outputText
																										styleClass="outputText"
																										id="lblSaisyuKosinDate"
																										value="#{pc_Xra01101T01.propSaisyuKosinDate.stringValue}"
																										style="#{pc_Xra01101T01.propSaisyuKosinDate.labelStyle}">
																									</h:outputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH nowrap class="v_b">
																									<!-- 入学許可 --> <h:outputText
																										styleClass="outputText"
																										id="lblNyugakuKyokaTitle"
																										value="#{pc_Xra01101T01.propNyugakuKyoka.labelName}"
																										style="#{pc_Xra01101T01.propNyugakuKyoka.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:outputText styleClass="outputText"
																										id="lblNyugakuKyoka"
																										value="#{pc_Xra01101T01.propNyugakuKyoka.stringValue}"
																										style="#{pc_Xra01101T01.propNyugakuKyoka.labelStyle}">
																									</h:outputText>
																								</TD>
																								<TH nowrap class="v_b">
																									<!-- 選考結果日 --> <h:outputText
																										styleClass="outputText"
																										id="lblSenkoKekkaDateTitle"
																										value="#{pc_Xra01101T01.propSenkoKekkaDate.labelName}"
																										style="#{pc_Xra01101T01.propSenkoKekkaDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:outputText styleClass="outputText"
																										id="lblSenkoKekkaDate"
																										value="#{pc_Xra01101T01.propSenkoKekkaDate.stringValue}"
																										style="#{pc_Xra01101T01.propSenkoKekkaDate.labelStyle}">
																									</h:outputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH nowrap class="v_b">
																									<!-- 合格証発行日 --> <h:outputText
																										styleClass="outputText"
																										id="lblGokakuSyoHakkoDateTitle"
																										value="#{pc_Xra01101T01.propGokakuSyoHakkoDate.labelName}"
																										style="#{pc_Xra01101T01.propGokakuSyoHakkoDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:outputText styleClass="outputText"
																										id="lblGokakuSyoHakkoDate"
																										value="#{pc_Xra01101T01.propGokakuSyoHakkoDate.stringValue}"
																										style="#{pc_Xra01101T01.propGokakuSyoHakkoDate.labelStyle}">
																									</h:outputText>
																								</TD>
																								<TH nowrap class="v_b">
																									<!-- 学生証発行日 --> <h:outputText
																										styleClass="outputText"
																										id="lblGakuseiSyoHakkoDate"
																										value="#{pc_Xra01101T01.propGakuseiSyoHakkoDate.labelName}"
																										style="#{pc_Xra01101T01.propGakuseiSyoHakkoDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:inputText styleClass="inputText"
																										id="htmlGakuseiSyoHakkoDate" size="10"
																										onblur="onCangeData();"
																										disabled="#{pc_Xra01101T01.propGakuseiSyoHakkoDate.disabled}"
																										value="#{pc_Xra01101T01.propGakuseiSyoHakkoDate.dateValue}"
																										style="#{pc_Xra01101T01.propGakuseiSyoHakkoDate.style}"
																										readonly="#{pc_Xra01101T01.propGakuseiSyoHakkoDate.readonly}">
																										<f:convertDateTime />
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											promptCharacter="_" />
																										<hx:inputHelperDatePicker />
																									</h:inputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH nowrap class="v_b">
																									<!-- 学生証シール発行年度 --> <h:outputText
																										styleClass="outputText"
																										id="lblGakuseisyoSealHakkoNen"
																										value="#{pc_Xra01101T01.propGakuseisyoSealHakkoNen.labelName}"
																										style="#{pc_Xra01101T01.propGakuseisyoSealHakkoNen.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:inputText styleClass="inputText"
																										id="htmlGakuseisyoSealHakkoNen" size="25"
																										onchange="onCangeData();"
																										maxlength="#{pc_Xra01101T01.propGakuseisyoSealHakkoNen.maxLength}"
																										disabled="#{pc_Xra01101T01.propGakuseisyoSealHakkoNen.disabled}"
																										value="#{pc_Xra01101T01.propGakuseisyoSealHakkoNen.dateValue}"
																										style="#{pc_Xra01101T01.propGakuseisyoSealHakkoNen.style}"
																										readonly="#{pc_Xra01101T01.propGakuseisyoSealHakkoNen.readonly}">
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											imeMode="inactive" promptCharacter="_" />
																										<f:convertDateTime pattern="yyyy" />
																									</h:inputText>
																								</TD>
																								<TH nowrap class="v_b">
																									<!-- 学生証シール発行日 --> <h:outputText
																										styleClass="outputText"
																										id="lblGakuseiSyoSealHakkoDate"
																										value="#{pc_Xra01101T01.propGakuseiSyoSealHakkoDate.labelName}"
																										style="#{pc_Xra01101T01.propGakuseiSyoSealHakkoDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:inputText styleClass="inputText"
																										id="htmlGakuseiSyoSealHakkoDate" size="10"
																										onblur="onCangeData();"
																										disabled="#{pc_Xra01101T01.propGakuseiSyoSealHakkoDate.disabled}"
																										value="#{pc_Xra01101T01.propGakuseiSyoSealHakkoDate.dateValue}"
																										style="#{pc_Xra01101T01.propGakuseiSyoSealHakkoDate.style}"
																										readonly="#{pc_Xra01101T01.propGakuseiSyoSealHakkoDate.readonly}">
																										<f:convertDateTime />
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											promptCharacter="_" />
																										<hx:inputHelperDatePicker />
																									</h:inputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH nowrap class="v_b">
																									<!-- 玉通発送年月号 --> <h:outputText
																										styleClass="outputText"
																										id="lblTamatuHassoNengetuGo"
																										value="#{pc_Xra01101T01.propTamatuHassoNengetuGo.labelName}"
																										style="#{pc_Xra01101T01.propTamatuHassoNengetuGo.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:inputText styleClass="inputText"
																										id="htmlTamatuHassoNengetuGo" size="10"
																										onchange="onCangeData();"
																										disabled="#{pc_Xra01101T01.propTamatuHassoNengetuGo.disabled}"
																										value="#{pc_Xra01101T01.propTamatuHassoNengetuGo.dateValue}"
																										style="#{pc_Xra01101T01.propTamatuHassoNengetuGo.style}"
																										readonly="#{pc_Xra01101T01.propTamatuHassoNengetuGo.readonly}">
																										<f:convertDateTime pattern="yyyy/MM" />
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											promptCharacter="_" />
																									</h:inputText>
																								</TD>
																								<TH nowrap class="v_b">
																									<!-- 第１回配本日 --> <h:outputText
																										styleClass="outputText"
																										id="lblHaihonDateTitle"
																										value="#{pc_Xra01101T01.propHaihonDate.labelName}"
																										style="#{pc_Xra01101T01.propHaihonDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:outputText styleClass="outputText"
																										id="lblHaihonDate"
																										value="#{pc_Xra01101T01.propHaihonDate.stringValue}"
																										style="#{pc_Xra01101T01.propHaihonDate.labelStyle}">
																									</h:outputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																					<BR>
																					<TABLE class="table" width="810">
																						<TBODY>
																							<TR>
																								<TH nowrap class="v_b" width="180">
																									<!-- 卒業論文受付日 --> <h:outputText
																										styleClass="outputText"
																										id="lblSotuRonUketukeDate"
																										value="#{pc_Xra01101T01.propSotuRonUketukeDate.labelName}"
																										style="#{pc_Xra01101T01.propSotuRonUketukeDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:inputText styleClass="inputText"
																										id="htmlSotuRonUketukeDate" size="10"
																										onblur="onCangeData();"
																										disabled="#{pc_Xra01101T01.propSotuRonUketukeDate.disabled}"
																										value="#{pc_Xra01101T01.propSotuRonUketukeDate.dateValue}"
																										style="#{pc_Xra01101T01.propSotuRonUketukeDate.style}">
																										<f:convertDateTime />
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											promptCharacter="_" />
																										<hx:inputHelperDatePicker />
																									</h:inputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																					<BR>
																					<TABLE class="table" width="810">
																						<TBODY>
																							<TR>
																								<TH nowrap class="v_b" width="180">
																									<!-- 論文審査結果登録日 --> <h:outputText
																										styleClass="outputText"
																										id="lblRonbunSinsaKekkaTorokuDate"
																										value="#{pc_Xra01101T01.propRonbunSinsaKekkaTorokuDate.labelName}"
																										style="#{pc_Xra01101T01.propRonbunSinsaKekkaTorokuDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD width="225"><h:inputText
																										styleClass="inputText"
																										id="htmlRonbunSinsaKekkaTorokuDate" size="10"
																										onblur="onCangeData();"
																										disabled="#{pc_Xra01101T01.propRonbunSinsaKekkaTorokuDate.disabled}"
																										value="#{pc_Xra01101T01.propRonbunSinsaKekkaTorokuDate.dateValue}"
																										style="#{pc_Xra01101T01.propRonbunSinsaKekkaTorokuDate.style}">
																										<f:convertDateTime />
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											promptCharacter="_" />
																										<hx:inputHelperDatePicker />
																									</h:inputText>
																								</TD>
																								<TH nowrap class="v_b" width="180">
																									<!-- 論文審査結果 --> <h:outputText
																										styleClass="outputText"
																										id="lblRonbunSinsaKekka"
																										value="#{pc_Xra01101T01.propRonbunSinsaKekka.labelName}"
																										style="#{pc_Xra01101T01.propRonbunSinsaKekka.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:selectOneMenu
																										styleClass="selectOneMenu"
																										id="htmlRonbunSinsaKekka"
																										onchange="onCangeData();"
																										disabled="#{pc_Xra01101T01.propRonbunSinsaKekka.disabled}"
																										value="#{pc_Xra01101T01.propRonbunSinsaKekka.value}"
																										style="width:150px;">
																										<f:selectItems
																											value="#{pc_Xra01101T01.propRonbunSinsaKekka.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																							<TR>
																								<TH nowrap class="v_b">
																									<!-- 面接試問結果登録日 --> <h:outputText
																										styleClass="outputText"
																										id="lblMensetuSimonKekkaTorokuDate"
																										value="#{pc_Xra01101T01.propMensetuSimonKekkaTorokuDate.labelName}"
																										style="#{pc_Xra01101T01.propMensetuSimonKekkaTorokuDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:inputText styleClass="inputText"
																										id="htmlMensetuSimonKekkaTorokuDate" size="10"
																										onblur="onCangeData();"
																										disabled="#{pc_Xra01101T01.propMensetuSimonKekkaTorokuDate.disabled}"
																										value="#{pc_Xra01101T01.propMensetuSimonKekkaTorokuDate.dateValue}"
																										style="#{pc_Xra01101T01.propMensetuSimonKekkaTorokuDate.style}">
																										<f:convertDateTime />
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											promptCharacter="_" />
																										<hx:inputHelperDatePicker />
																									</h:inputText>
																								</TD>
																								<TH nowrap class="v_b">
																									<!-- 面接試問結果 --> <h:outputText
																										styleClass="outputText"
																										id="lblMensetuSimonKekka"
																										value="#{pc_Xra01101T01.propMensetuSimonKekka.labelName}"
																										style="#{pc_Xra01101T01.propMensetuSimonKekka.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:selectOneMenu
																										styleClass="selectOneMenu"
																										id="htmlMensetuSimonKekka"
																										onchange="onCangeData();"
																										disabled="#{pc_Xra01101T01.propMensetuSimonKekka.disabled}"
																										value="#{pc_Xra01101T01.propMensetuSimonKekka.value}"
																										style="width:150px;">
																										<f:selectItems
																											value="#{pc_Xra01101T01.propMensetuSimonKekka.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																							<TR>
																								<TH nowrap class="v_b">
																									<!-- 卒業申請登録日 --> <h:outputText
																										styleClass="outputText"
																										id="lblSotugyoSinseiTorokuDate"
																										value="#{pc_Xra01101T01.propSotugyoSinseiTorokuDate.labelName}"
																										style="#{pc_Xra01101T01.propSotugyoSinseiTorokuDate.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:inputText styleClass="inputText"
																										id="htmlSotugyoSinseiTorokuDate" size="10"
																										onblur="onCangeData();"
																										disabled="#{pc_Xra01101T01.propSotugyoSinseiTorokuDate.disabled}"
																										value="#{pc_Xra01101T01.propSotugyoSinseiTorokuDate.dateValue}"
																										style="#{pc_Xra01101T01.propSotugyoSinseiTorokuDate.style}">
																										<f:convertDateTime />
																										<hx:inputHelperAssist
																											errorClass="inputText_Error"
																											promptCharacter="_" />
																										<hx:inputHelperDatePicker />
																									</h:inputText>
																								</TD>
																								<TH nowrap class="v_b">
																									<!-- 卒業申請状況 --> <h:outputText
																										styleClass="outputText"
																										id="lblSotugyoSinseiJokyo"
																										value="#{pc_Xra01101T01.propSotugyoSinseiJokyo.labelName}"
																										style="#{pc_Xra01101T01.propSotugyoSinseiJokyo.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD><h:selectOneMenu
																										styleClass="selectOneMenu"
																										id="htmlSotugyoSinseiJokyo"
																										onchange="onCangeData();"
																										disabled="#{pc_Xra01101T01.propSotugyoSinseiJokyo.disabled}"
																										value="#{pc_Xra01101T01.propSotugyoSinseiJokyo.value}"
																										style="width:150px;">
																										<f:selectItems
																											value="#{pc_Xra01101T01.propSotugyoSinseiJokyo.list}" />
																									</h:selectOneMenu>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																					<TABLE cellspacing="1" cellpadding="1"
																						class="button_bar" width="810">
																						<TBODY>
																							<TR align="right">
																								<TD align="center"><hx:commandExButton
																										type="submit" value="クリア"
																										styleClass="commandExButton_etc" id="clear"
																										disabled="#{pc_Cob00101T01.cob00101.propUpdate.disabled}"
																										onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
																										action="#{pc_Xra01101T01.doClearAction}"></hx:commandExButton>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																					<BR>
																				</div>
																			</TD>
																		</TR>
																	</TBODY>
																</TABLE>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
												<TABLE cellspacing="1" cellpadding="1" class="button_bar"
													width="100%">
													<TBODY>
														<TR align="right">
															<TD align="center"><hx:commandExButton type="submit"
																	value="確定" styleClass="commandExButton_dat" id="update"
																	disabled="#{pc_Cob00101T01.cob00101.propUpdate.disabled}"
																	onclick="return onClickUpdate('#{msg.SY_MSG_0001W}');"
																	action="#{pc_Xra01101T01.doUpdateAction}"></hx:commandExButton>
																<hx:commandExButton type="submit" value="削除"
																	styleClass="commandExButton_dat" id="delete"
																	disabled="#{pc_Cob00101T01.cob00101.propDelete.disabled}"
																	onclick="return onClickDelete('#{msg.SY_MSG_0004W}');"
																	action="#{pc_Xra01101T01.doDeleteAction}"></hx:commandExButton>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
								<h:inputHidden id="htmlHidButtonKbn"
									value="#{pc_Cob00101T01.cob00101.propHidButtonKbn.integerValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidAction"
									value="#{pc_Cob00101T01.cob00101.propHidAction.stringValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidInsUpdKbn"
									value="#{pc_Cob00101T01.cob00101.propHidInsUpdKbn.stringValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidKanriNo"
									value="#{pc_Cob00101T01.cob00101.propHidKanriNo.longValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidErrMessage"
									value="#{pc_Cob00101T01.cob00101.propHidErrMessage.value}"></h:inputHidden>
								<!-- @@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 START -->
								<h:inputHidden id="htmlHidExeSelectFlg"
									value="#{pc_Cob00101T01.cob00101.propHidExeSelectFlg.stringValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidChangeDataFlg"
									value="#{pc_Cob00101T01.cob00101.changeDataFlg}"></h:inputHidden>
								<!-- @@@@@ (GAKEX) リスト検索 FW)nagano 2008/2/2 END -->
								<h:inputHidden id="htmlHidKengenNashiFlg"
									value="#{pc_Cob00101T01.cob00101.propHidKengenNashiFlg.integerValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidButtonKbnAuth"
									value="#{pc_Cob00101T01.cob00101.propHidButtonKbnAuth.integerValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidButtonKbnSikak"
									value="#{pc_Cob00101T01.cob00101.propHidButtonKbnSikak.integerValue}"></h:inputHidden>
								<h:inputHidden id="htmlHidButtonKbnSikakFlg"
									value="#{pc_Cob00101T01.cob00101.propHidButtonKbnSikakFlg.integerValue}"></h:inputHidden>

								<!-- ↑ここにコンポーネントを配置 -->
							</DIV>
						</DIV>
						<!--↑content↑-->
					</DIV>
					<!--↑outer↑-->
					<!-- フッダーインクルード -->
					<jsp:include page="../inc/footer.jsp" />

				</h:form>
			</gakuen:itemStateCtrl>
		</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
