<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}

function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xra00101.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xra00101.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xra00101.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xra00101.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
            <TABLE width="900">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140"><h:outputText styleClass="outputText"
	                                id="lblNyugakNendo"
									style="#{pc_Xra00101.propNyugakNendo.labelStyle}"
	                                value="#{pc_Xra00101.propNyugakNendo.labelName}"></h:outputText></TH>
	                            <TD width="260"><h:inputText styleClass="inputText"
	                                id="htmlNyugakNendo"
									disabled="#{pc_Xra00101.propNyugakNendo.disabled}"
	                                value="#{pc_Xra00101.propNyugakNendo.dateValue}"
	                                style="#{pc_Xra00101.propNyugakNendo.style}"
	                                size="10" tabindex="1">
									<f:convertDateTime pattern="yyyy" />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_"/>
	                            </h:inputText></TD>
	                           	<TH class="v_b" width="140"><h:outputText styleClass="outputText"
	                                id="lblNyugakGakkiNo"
									style="#{pc_Xra00101.propNyugakGakkiNo.labelStyle}"
	                                value="#{pc_Xra00101.propNyugakGakkiNo.labelName}"></h:outputText></TH>
	                            <TD width="260"><h:inputText styleClass="inputText"
	                                id="htmlNyugakGakkiNo"
									disabled="#{pc_Xra00101.propNyugakGakkiNo.disabled}"
	                                value="#{pc_Xra00101.propNyugakGakkiNo.integerValue}"
	                                style="#{pc_Xra00101.propNyugakGakkiNo.style}"
	                                size="10" tabindex="2">
	                                <f:convertNumber type="number" pattern="#0" />
	                                <hx:inputHelperAssist errorClass="inputText_Error"
	                                    promptCharacter="_"/>
	                            </h:inputText></TD>
	                        </TR>
	                    </TABLE>
                    </TD>
                    <TD align="left"><hx:commandExButton type="submit"
						value="選択" styleClass="commandExButton" id="search"
						action="#{pc_Xra00101.doSearchAction}"
						disabled="#{pc_Xra00101.propSearch.disabled}" tabindex="3">
					</hx:commandExButton><hx:commandExButton type="submit"
						value="解除" styleClass="commandExButton" id="cancel"
						action="#{pc_Xra00101.doCancelAction}"
						disabled="#{pc_Xra00101.propCancel.disabled}" tabindex="4"></hx:commandExButton></TD>
                </TR>
            </TABLE>
            <TABLE width="900">
                <tr>
                    <td width="810" align="left">
	                    <table class="table">
	                        <tr>
	                            <TH class="v_c" width="140" nowrap><h:outputText
									styleClass="outputText" id="lblLastUkeNo"
									value="最終受付番号"></h:outputText></TH>
	                            <TD width="660" nowrap><h:outputText
									styleClass="outputText" id="htmlLastUkeNo"
									value="#{pc_Xra00101.propLastUkeNo.stringValue}"></h:outputText></td>
	                        </tr>
	                    </table>
                    </td>
                    <td align="left"></td>
                </tr>
            </TABLE>
            <HR class="hr" noshade>
			<TABLE width="900" border="0" cellpadding="0">
				<TR>
					<TD align="right" nowrap class="outputText">
						<h:outputText styleClass="outputText"
							id="htmlFubanJokenListCount"
							value="#{pc_Xra00101.propFubanJokenListCount.value}">
						</h:outputText>
					</TD>
				</TR>
				<tr>
					<td>
						<DIV style="height:180px" class="listScroll"
							onscroll="setScrollPosition('scroll', this);">
							<h:dataTable border="0" cellpadding="0" cellspacing="0"
								columnClasses="columnClass1" headerClass="headerClass"
								footerClass="footerClass"
								rowClasses="#{pc_Xra00101.propFubanJokenList.rowClasses}"
								styleClass="meisai_scroll" id="htmlFubanJokenList"
								value="#{pc_Xra00101.propFubanJokenList.list}" var="varlist">
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="所属学科組織"
											id="lblListSzkGakkaCd">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListSzkGakkaCd"
										value="#{varlist.szkGakkaName}">
									</h:outputText>
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="150" name="width" />
								</h:column>
								<h:column id="column2">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="就学種別"
											id="lblListSyugakSbt">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListSyugakSbt"
										value="#{varlist.syugakSbtName}">
									</h:outputText>
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="120" name="width" />
								</h:column>
								<h:column id="column3">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="入学年次"
											id="lblListNyugakNenji">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListNyugakNenji"
										value="#{varlist.nyugakNenjiName}">
									</h:outputText>
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="60" name="width" />
								</h:column>
								<h:column id="column4">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="身分区分"
											id="lblListMibunKbn">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListMibunKbn"
										value="#{varlist.mibunKbnName}">
									</h:outputText>
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="250" name="width" />
								</h:column>
								<h:column id="column5">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="連番開始番号"
											id="lblListRenbanStaNo">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListRenbanStaNo"
										value="#{varlist.renbanStaNo}">
										<f:convertNumber />
									</h:outputText>
									<f:attribute value="text-align:right" name="style" />
									<f:attribute value="60" name="width" />
								</h:column>
								<h:column id="column6">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="連番桁数"
											id="lblListRenbanKeta">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListRenbanKeta"
										value="#{varlist.renbanKeta}">
										<f:convertNumber />
									</h:outputText>
									<f:attribute value="text-align:right" name="style" />
									<f:attribute value="60" name="width" />
								</h:column>
								<h:column id="column7">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="固定文字"
											id="lblListKoteiMoji">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListKoteiMoji"
										value="#{varlist.koteiMoji}">
									</h:outputText>
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="60" name="width" />
								</h:column>
								<h:column id="column8">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="連番最終番号"
											id="lblListRenbanLastNo">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListRenbanLastNo"
										value="#{varlist.renbanLastNo}">
										<f:convertNumber />
									</h:outputText>
									<f:attribute value="text-align:right" name="style" />
									<f:attribute value="80" name="width" />
								</h:column>
								<h:column id="column9">
									<f:facet name="header">
									</f:facet>
									<hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="edit"
										onblur="return func_1(this, event);"
										action="#{pc_Xra00101.doEditAction}" tabindex="5">
									</hx:commandExButton>
									<f:attribute value="40" name="width" />
								</h:column>
							</h:dataTable>
							<BR>
						</DIV>
					</td>
				</tr>
			</TABLE>
            <HR class="hr" noshade>
            <TABLE width="900" class="table">
                <TBODY>
                    <TR>
                        <TH class="v_c" width="170"><h:outputText
							styleClass="outputText" id="lblSzkGakkaCd"
							value="所属学科組織"></h:outputText></TH>
                        <TD colspan="3" width="730"><h:selectOneMenu styleClass="selectOneMenu"
                        	id="htmlSzkGakkaCd" tabindex="6"
							disabled="#{pc_Xra00101.propSzkGakkaCd.disabled}"
							value="#{pc_Xra00101.propSzkGakkaCd.value}">
							<f:selectItems value="#{pc_Xra00101.propSzkGakkaCd.list}" />
							</h:selectOneMenu>
						</TD>
                    </TR>
                    <TR>
                        <TH class="v_a"><h:outputText
							styleClass="outputText" id="lblSyugakSbt"
							style="#{pc_Xra00101.propSyugakSbt.labelStyle}"
							value="就学種別"></h:outputText></TH>
                        <TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSyugakSbt" tabindex="7"
							disabled="#{pc_Xra00101.propSyugakSbt.disabled}"
							value="#{pc_Xra00101.propSyugakSbt.value}">
							<f:selectItems value="#{pc_Xra00101.propSyugakSbt.list}" />
						</h:selectOneMenu></TD>
                        <TH class="v_b"><h:outputText
							styleClass="outputText" id="lblNyugakNenji"
							style="#{pc_Xra00101.propNyugakNenji.labelStyle}"
							value="入学年次"></h:outputText></TH>
                        <TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlNyugakNenji" tabindex="8"
							disabled="#{pc_Xra00101.propNyugakNenji.disabled}"
							value="#{pc_Xra00101.propNyugakNenji.value}">
							<f:selectItems value="#{pc_Xra00101.propNyugakNenji.list}" />
							</h:selectOneMenu>
						</TD>
                    </TR>
                    <TR>
                        <TH class="v_d"><h:outputText
							styleClass="outputText" id="lblMibunKbn"
							style="#{pc_Xra00101.propMibunKbn.labelStyle}"
							value="身分区分"></h:outputText></TH>
                        <TD colspan="3"><h:selectOneMenu styleClass="selectOneMenu"
                        	id="htmlMibunKbn" tabindex="9"
							disabled="#{pc_Xra00101.propMibunKbn.disabled}"
							value="#{pc_Xra00101.propMibunKbn.value}">
							<f:selectItems value="#{pc_Xra00101.propMibunKbn.list}" />
							</h:selectOneMenu>
						</TD>
                    </TR>
                    <TR>
                        <TH class="v_g" width="170" nowrap><h:outputText
							styleClass="outputText" id="lblRenbanStaNo"
							style="#{pc_Xra00101.propRenbanStaNo.labelStyle}"
							value="#{pc_Xra00101.propRenbanStaNo.labelName}"></h:outputText></TH>
                        <TD width="250"><h:inputText styleClass="inputText"
                        	id="htmlRenbanStaNo"
							disabled="#{pc_Xra00101.propRenbanStaNo.disabled}"
							value="#{pc_Xra00101.propRenbanStaNo.integerValue}"
							style="#{pc_Xra00101.propRenbanStaNo.style}" size="4"
							 tabindex="10">
							<f:convertNumber type="number" pattern="###0"/>
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
						</h:inputText></TD>
                        <TH class="v_a" width="170"><h:outputText
							styleClass="outputText" id="lblRenbanKeta"
							style="#{pc_Xra00101.propRenbanKeta.labelStyle}"
							value="#{pc_Xra00101.propRenbanKeta.labelName}"></h:outputText></TH>
                        <TD><h:inputText styleClass="inputText"
							id="htmlRenbanKeta"
							disabled="#{pc_Xra00101.propRenbanKeta.disabled}"
							value="#{pc_Xra00101.propRenbanKeta.integerValue}"
							style="#{pc_Xra00101.propRenbanKeta.style}" size="2"
							tabindex="11">
							<f:convertNumber type="number" pattern="#0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>
						</TD>
                    </TR>
                    <TR>
                        <TH class="v_b"><h:outputText
							styleClass="outputText" id="lblKoteiMoji"
							style="#{pc_Xra00101.propKoteiMoji.labelStyle}"
							value="#{pc_Xra00101.propKoteiMoji.labelName}"></h:outputText></TH>
                        <TD><h:inputText styleClass="inputText"
							id="htmlKoteiMoji"
							maxlength="#{pc_Xra00101.propKoteiMoji.maxLength}"
							disabled="#{pc_Xra00101.propKoteiMoji.disabled}"
							value="#{pc_Xra00101.propKoteiMoji.stringValue}"
							style="#{pc_Xra00101.propKoteiMoji.style}" size="6"
							tabindex="12"></h:inputText></TD>
                        <TH class="v_c"><h:outputText
							styleClass="outputText" id="lblRenbanLastNo"
							style="#{pc_Xra00101.propRenbanLastNo.labelStyle}"
							value="#{pc_Xra00101.propRenbanLastNo.labelName}"></h:outputText></TH>
                        <TD><h:inputText styleClass="inputText"
							id="htmlRenbanLastNo"
							disabled="#{pc_Xra00101.propRenbanLastNo.disabled}"
							value="#{pc_Xra00101.propRenbanLastNo.integerValue}"
							style="#{pc_Xra00101.propRenbanLastNo.style}" size="8"
							tabindex="13">
							<f:convertNumber type="number" pattern="#######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>
						</TD>
                    </TR>
                </TBODY>
            </TABLE>
            <TABLE width="900" class="button_bar" border="0" cellpadding="0"
                cellspacing="0">
                <TR>
                    <TD >
                    	<hx:commandExButton type="submit" value="確定"
						styleClass="commandExButton_dat" id="register"
						disabled="#{pc_Xra00101.propRegister.disabled}"
						style="#{pc_Xra00101.propRegister.style}"
						action="#{pc_Xra00101.doRegisterAction}" confirm="#{msg.SY_MSG_0001W}" tabindex="14"></hx:commandExButton>

                        <hx:commandExButton type="submit" value="削除" 
                        styleClass="commandExButton_dat" id="delete" 
                        disabled="#{pc_Xra00101.propDelete.disabled}"
                        style="#{pc_Xra00101.propDelete.style}" 
                        action="#{pc_Xra00101.doDeleteAction}" confirm="#{msg.SY_MSG_0004W}" tabindex="15"></hx:commandExButton>

                        <hx:commandExButton type="submit" value="クリア"
                        styleClass="commandExButton_etc" id="clear"
                        disabled="#{pc_Xra00101.propClear.disabled}"
                        style="#{pc_Xra00101.propClear.style}" 
                        action="#{pc_Xra00101.doClearAction}" tabindex="16"></hx:commandExButton>
                    </TD>
                </TR>
            </TABLE>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

