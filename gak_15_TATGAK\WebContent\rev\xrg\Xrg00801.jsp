<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード	'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード	'event' の代わりに 'thisEvent' を使用します
var url="${pageContext.request.contextPath}/faces/rev/xrg/pCob0301.jsp?retFieldName=form1:htmlSchoolingCd&kyoShokuin=3";
 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
}

// 授業コード検索アイコン押下時処理
function openJugyoCdSearchWindow(thisObj, thisEvent) {
	
    var nendo = document.getElementById("form1:htmlNendo").value;
    var sbtCd = document.getElementById("form1:htmlSchoolingSbtCd").value;
	var url="${pageContext.request.contextPath}/faces/rev/xrg/pXrg0101.jsp?retFieldName=form1:htmlTargetJugyoCd&nendo="+nendo+"&schSbtCd="+sbtCd;
	openModalWindow(url, "pXrg0101", "<%= com.jast.gakuen.rev.xrg.PXrg0101.getWindowOpenOption() %>");
	return true;
}


// スクーリング授業名称を取得する
function doJugyoAjax(thisObj,thisEvent){
    // 授業名称を取得する htmlJugyoName
    var servlet = "rev/xrg/XrgJugyAJAX";
    var args = new Array();
    args['nendo'] = document.getElementById("form1:htmlNendo").value;
    args['sbtCd'] = document.getElementById("form1:htmlSchoolingSbtCd").value;
    args['jugyoCd'] = thisObj.value;
    var target = "form1:htmlJugyoName";
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet,target,args);
}

function confirmCancel(){
	try{
		document.getElementById("form1:htmlConfilm").value = "0";
	} catch (e) {
	}
}
function confirmOk(){
	try{
		document.getElementById("form1:htmlConfilm").value = "1";
		indirectClick(document.getElementById("form1:htmlAction").value);
	} catch (e) {
	}
}

var schSbtCd = "";
function getSchoolingSbtCb() {
	// スクーリング種別コンボボックス取得AJAX
	var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
	var args = new Array();
	args['nendo'] = document.getElementById('form1:htmlNendo').value;
	args['textdelFlg'] = "true";
	args['propName'] = 'propSchoolingSbtCd';
	var target = "";
	
	comb = document.getElementById('form1:htmlSchoolingSbtCd');
	schSbtCd = comb.options[comb.selectedIndex].value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
}

function callBackMethod(value){
	var comb = document.getElementById('form1:htmlSchoolingSbtCd');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00801.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00801.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5" width="660" class="table">
				<TBODY>
					<TR>
						<TH class="v_b" width="150">
							<h:outputText styleClass="outputText"
								id="lblNendo"
								value="#{pc_Xrg00801.propNendo.labelName}" 
								style="#{pc_Xrg00801.propNendo.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="400">
							<h:inputText styleClass="inputText"
								id="htmlNendo" tabindex="1"
								value="#{pc_Xrg00801.propNendo.dateValue}"
								disabled="#{pc_Xrg00801.propNendo.disabled}"
								onblur="getSchoolingSbtCb();"
								style="#{pc_Xrg00801.propNendo.style}" size="10">
								<hx:inputHelperAssist errorClass="inputText_Error"
									imeMode="inactive" promptCharacter="_" />
								<f:convertDateTime pattern="yyyy" />
							</h:inputText>
						</TD>
						<TD width="110" rowspan="3"
							style="background-color: transparent; text-align: center"
							class="clear_border">
							<hx:commandExButton type="submit"
								value="選択" styleClass="commandExButton" id="selectJugyo"
								action="#{pc_Xrg00801.doSelectAction}"
								tabindex="4"
								disabled="#{pc_Xrg00801.propSelect.disabled}"
								style="#{pc_Xrg00801.propSelect.style}"
								rendered="#{pc_Xrg00801.propSelect.rendered}">
							</hx:commandExButton>
							<hx:commandExButton type="submit"
								value="解除" styleClass="commandExButton" id="releaseJugyo"
								action="#{pc_Xrg00801.doReleaseAction}" tabindex="5"
								style="#{pc_Xrg00801.propRelease.style}"
								disabled="#{pc_Xrg00801.propRelease.disabled}"
								rendered="#{pc_Xrg00801.propRelease.rendered}">
							</hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="150">
							<h:outputText styleClass="outputText" id="lblSchoolingSbtCd" 
								value="#{pc_Xrg00801.propSchoolingSbtCd.labelName}" 
								style="#{pc_Xrg00801.propSchoolingSbtCd.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="400">
							<h:selectOneMenu styleClass="selectOneMenu"
								id="htmlSchoolingSbtCd"
								value="#{pc_Xrg00801.propSchoolingSbtCd.value}"
								tabindex="2" style="width: 390px"
								readonly="#{pc_Xrg00801.propSchoolingSbtCd.readonly}"
								disabled="#{pc_Xrg00801.propSchoolingSbtCd.disabled}">
								<f:selectItems
									value="#{pc_Xrg00801.propSchoolingSbtCd.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
					<TR>
						<TH class="v_a" width="150">
							<h:outputText styleClass="outputText" id="lblTargetFrwk"
								value="#{pc_Xrg00801.propTargetFrwk.labelName}"
								style="#{pc_Xrg00801.propTargetFrwk.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="400">
							<h:selectOneRadio
								tabindex="3"
								disabledClass="selectOneRadio_Disabled"
								styleClass="selectOneRadio" id="htmlTargetFrwk"
								value="#{pc_Xrg00801.propTargetFrwk.stringValue}"
								disabled="#{pc_Xrg00801.propTargetFrwk.disabled}"
								style="#{pc_Xrg00801.propTargetFrwk.style}">
								<f:selectItems
									value="#{pc_Xrg00801.propTargetFrwk.list}" />
							</h:selectOneRadio>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade width="750">
			<BR>
			<TABLE border="0" cellpadding="5" width="660" class="table">
				<TBODY>
					<TR>
						<TH class="v_c" width="150"><h:outputText styleClass="outputText"
							id="lblTargetJugyoCd"
							value="#{pc_Xrg00801.propTargetJugyoCd.labelName}"
							style="#{pc_Xrg00801.propTargetJugyoCd.labelStyle}">
						</h:outputText></TH>
						<TD width="400"><h:inputText styleClass="inputText"
							id="htmlTargetJugyoCd" onblur="return doJugyoAjax(this, event);"
							tabindex="6"
							value="#{pc_Xrg00801.propTargetJugyoCd.stringValue}"
							readonly="#{pc_Xrg00801.propTargetJugyoCd.readonly}"
							style="#{pc_Xrg00801.propTargetJugyoCd.style}"
							onblur="return doJugyoAjax(this, event);"
							disabled="#{pc_Xrg00801.propTargetJugyoCd.disabled}"
							maxlength="#{pc_Xrg00801.propTargetJugyoCd.max}" size="10">
						</h:inputText> <hx:commandExButton type="button"
							styleClass="commandExButton_search" id="searchJugyoCd"
							onclick="return openJugyoCdSearchWindow(this, event);"
							tabindex="7"
							disabled="#{pc_Xrg00801.propSearchJugyo.disabled}"
							style="#{pc_Xrg00801.propSearchJugyo.style}">
						</hx:commandExButton> <hx:commandExButton type="submit" value="追加"
							styleClass="commandExButton" id="addJugyo"
							action="#{pc_Xrg00801.doAddAction}" tabindex="8"
							disabled="#{pc_Xrg00801.propAdd.disabled}"
							style="#{pc_Xrg00801.propAdd.style}"
							rendered="#{pc_Xrg00801.propAdd.rendered}">
						</hx:commandExButton>
						<h:outputText styleClass="outputText"
							id="htmlJugyoName"
							value="#{pc_Xrg00801.propTargetJugyoName.displayValue}"
							style="#{pc_Xrg00801.propTargetJugyoName.style}">
						</h:outputText></TD>
						<TD width="110"
							style="background-color: transparent;"
							class="clear_border">
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="5" width="660">
				<TBODY>
					<TR>
						<TD align="left" width="550">
							<h:selectManyListbox
								styleClass="selectManyListbox"
								id="htmlTargetJugyoList" size="15" tabindex="9"
								value="#{pc_Xrg00801.propTargetJugyoList.value}"
								readonly="#{pc_Xrg00801.propTargetJugyoList.readonly}"
								disabled="#{pc_Xrg00801.propTargetJugyoList.disabled}"
								style="width:100%">
								<f:selectItems value="#{pc_Xrg00801.propTargetJugyoList.list}" />
							</h:selectManyListbox>										
						</TD>
						<TD align="center" width="110" valign="top">
							<hx:commandExButton type="submit"
								value="　除外　" styleClass="commandExButton" id="remove"
								action="#{pc_Xrg00801.doRemoveAction}" tabindex="10"
								style="#{pc_Xrg00801.propRemove.style}"
								disabled="#{pc_Xrg00801.propRemove.disabled}"
								rendered="#{pc_Xrg00801.propRemove.rendered}">
							</hx:commandExButton>
							<BR/>
							(複数選択可)
							<BR/>
							<hx:commandExButton type="submit"
								value="全て除外" styleClass="commandExButton" id="removeAll"
								action="#{pc_Xrg00801.dopRemoveAllAction}" tabindex="11"
								style="#{pc_Xrg00801.propRemoveAll.style}"
								disabled="#{pc_Xrg00801.propRemoveAll.disabled}"
								rendered="#{pc_Xrg00801.propRemoveAll.rendered}">
							</hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TD width="660" align="center" rowspan="2">
							<hx:commandExButton type="submit"
								value="実　行" styleClass="commandExButton_dat" id="exec"
								action="#{pc_Xrg00801.doExecAction}"
								confirm="#{msg.SY_MSG_0001W}" tabindex="12"
								disabled="#{pc_Xrg00801.propExec.disabled}"
								style="#{pc_Xrg00801.propExec.style}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑-->
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrg00801.propConfilm.stringValue}"
				id="htmlConfilm">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrg00801.propAction.stringValue}"
				id="htmlAction">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

