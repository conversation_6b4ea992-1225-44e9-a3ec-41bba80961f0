<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00201.java" --%><%-- /jsf:pagecode --%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@ taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK href="../../theme/stylesheet.css" rel="stylesheet" type="text/css">
<TITLE>Xrl00201.jsp</TITLE>
<script language="JavaScript">

	function openKamokuSubWindow() {
		// 科目検索画面
		openPCog0301("<%=com.jast.gakuen.rev.co.PCog0301.getWindowOpenOption() %>");
		return true;
	}
	
	//勘定科目コード検索画面呼び出し
	//@param windowSize		ウィンドウサイズ
	function openPCog0301(windowSize) {

		openModalWindow("", "PCog0301", windowSize);
		setTarget("PCog0301");
		return true;
	}
	
	//勘定科目情報取得Ajax実装関数
	//@param thisObj	キー用オブジェクト(勘定科目コード)
	//@param nendoObj	キー用オブジェクト(年度)
	//@param targetObj	勘定科目名称項目id (戻り値表示項目)
	function funcAjaxKeiriKamoku(thisObj, nendoObj, targetObj) {

		var args = new Array();
		args['code1'] = nendoObj;
		args['code2'] = thisObj;
		args['code3'] = '60';

		var servlet = "rev/co/CogKeiriKamokAJAX";
		var target = targetObj;
		
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
	
	function doKamokuAjax() {
		// 科目名称を取得する
		//学費業務年度
		var ghNendo = document.getElementById('form1:txtTeateNendo').value;
		//勘定科目コード
		var kmkCd = document.getElementById('form1:txtteateKmkCd').value;
		//勘定科目名称項目id
		var kmkNameId = "form1:lblPreKamokName";
		//勘定科目名称取得Ajax呼び出し
		funcAjaxKeiriKamoku(kmkCd, ghNendo, kmkNameId);
	}
    
    //ボタンの活性化制御
    function fncButtonActive(){
		doKamokuAjax();
	
    	//hdnConfirmFlag値が復元
    	document.getElementById('form1:hdnConfirmFlag').value = '0';
    			
		//フォーカスの設定
		setFocus();

		//スクロール位置保持
		changeScrollPosition('scroll', 'listScroll');
	}
	
	//フォーカスの設定
	function setFocus(){
		
		var id = null;

		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;

		//選択ボタン押下時
		if (document.getElementById('form1:htmlMenjList:0:select') != null) {
			if ((id != null) && (id != "")) {
				document.getElementById(id).focus();
			} else {
				document.getElementById("form1:htmlMenjList:0:select").focus();				
			}
		}
	}

	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		document.getElementById('form1:hdnConfirmFlag').value = '1';
		indirectClick('delete');
	}

	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		return;
	}

	</script>


</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncButtonActive();">

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00201.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrl00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00201.screenName}"></h:outputText></div>


			<!--↓OUTER↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　</DIV>

			<!--↓CONTENT↓-->
			<DIV id="content">
			<DIV class="column">
			<TABLE border="0" cellpadding="0" cellspacing="0" width="700px" style="margin:0 auto;">
				<TBODY>
					<TR>
						<TD style="padding-left:50px;padding-right:50px;">
							<!-- ↓ここに選択（年度）／解除（年度）ボタンを配置 -->
							<TABLE  width="100%" border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
									   <TH width="120px" nowrap class="v_b">
									   		<h:outputText
												styleClass="outputText" id="lblTeateNendo"
												value="#{pc_Xrl00201.propTeateNendo.labelName}"
												style="#{pc_Xrl00201.propTeateNendo.labelStyle}">
											</h:outputText></TH>
										<TD nowrap style="border: medium none;">
											<h:inputText styleClass="inputText"
												id="txtTeateNendo"
												value="#{pc_Xrl00201.propTeateNendo.dateValue}"
												maxlength="#{pc_Xrl00201.propTeateNendo.maxLength}"
												disabled="#{pc_Xrl00201.propTeateNendo.disabled}"
												style="#{pc_Xrl00201.propTeateNendo.style}" size="6"
												>
											<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
											<f:convertDateTime pattern="yyyy" />
					                  		</h:inputText>
										
											<hx:commandExButton type="submit"
												style="margin-left:20px;"
												value="選択" styleClass="commandExButton" id="selectXrlTatt"
												action="#{pc_Xrl00201.doSelectXrlTattAction}"
												disabled="#{pc_Xrl00201.propSelectXrlTatt.disabled}">
											</hx:commandExButton>
											<hx:commandExButton type="submit"
												value="解除" styleClass="commandExButton" id="releaseXrlTatt"
												action="#{pc_Xrl00201.doReleaseXrlTattAction}"
												disabled="#{pc_Xrl00201.propReleaseXrlTatt.disabled}">
											</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<!-- ↑ここに選択（年度）／解除（年度）ボタンを配置 -->
						</TD>
					</TR>
					<TR>
						<TD  style="padding-left:50px;padding-right:50px;">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText styleClass="outputText"
										id="text5" value="#{pc_Xrl00201.propTeateGyomuList.listCount}">
									</h:outputText> <h:outputText styleClass="outputText"
										id="text6" value="件">
									</h:outputText></TD>
								</TR>
								<TR>
									<TD>
									<DIV style="height: 200px; width=100%;" id="listScroll"
										onscroll="setScrollPosition('scroll',this);"
										class="listScroll"><h:dataTable styleClass="meisai_scroll"
										id="htmlTeateGyomuList"
										value="#{pc_Xrl00201.propTeateGyomuList.list}" var="varlist"
										footerClass="footerClass"
										rows="#{pc_Xrl00201.propTeateGyomuList.rows}" width="800px"
										rowClasses="#{pc_Xrl00201.propTeateGyomuList.rowClasses}"
										headerClass="headerClass">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="lblTeateCd"
													value="#{pc_Xrl00201.propTeate.name}"
													styleClass="outputText"></h:outputText>
											</f:facet>
											<h:outputText id="lblTeateCdValue" value="#{varlist.teateCd}"
												styleClass="outputText"></h:outputText>
											<f:attribute value="80px" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText id="lblTeateNm"
													value="#{pc_Xrl00201.propTeateNm.name}"
													styleClass="outputText"></h:outputText>
											</f:facet>
											<h:outputText id="lblTeateNmValue" value="#{varlist.teateNm}"
												styleClass="outputText"></h:outputText>
											<f:attribute value="170px" name="width" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText value="#{pc_Xrl00201.propTeateRyohiKbn.name}"
													styleClass="outputText" id="lblTeateRyohiKbn"></h:outputText>
											</f:facet>
											<f:attribute value="text-align:center;" name="style" />
											<h:outputText id="lblTeateRyohiKbnValue"
												value='#{varlist.teateRyohiKbn}' styleClass="outputText"></h:outputText>
											<f:attribute value="100px" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText id="lblTeateKbn"
													value="#{pc_Xrl00201.propTeateKbn.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<f:attribute value="text-align:center;" name="style" />
											<h:outputText id="lblTeateKbnValue"
												value="#{varlist.teateKbn}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="100px" name="width" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText id="lblTeateTani"
													value="#{pc_Xrl00201.propTeateTani.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<f:attribute value="text-align:center;" name="style" />
											<h:outputText id="lblTeateTaniValue"
												value="#{varlist.teateTani}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="80px" name="width" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
												<h:outputText id="lblTeateTanka"
													value="#{pc_Xrl00201.propTeateTanka.name}（円）"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<f:attribute value="100px;" name="width" />
											<h:outputText id="lblTeateTankaValue"
												value="#{varlist.teateTanka}" styleClass="outputText">
												<f:convertNumber pattern="#,###,##0" />
											</h:outputText>
											<f:attribute value="text-align:right;" name="style" />
										</h:column>
										<h:column id="column7">
											<f:facet name="header">
												<h:outputText id="lblTeateKmkCd"
													value="#{pc_Xrl00201.propTeateKmkCd.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<h:outputText id="lblTeateKmkCdValue"
												value="#{varlist.teateKmkCd}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="120px" name="width" />
										</h:column>
										<h:column id="column8">
											<f:facet name="header">
												<h:outputText id="lblTeateKmkNm"
													value="#{pc_Xrl00201.propTeateKmkName.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<h:outputText id="lblTeateKmkNmVlaue"
												value="#{varlist.teateKmkNm}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="180px" name="width" />
										</h:column>
										<h:column id="column9">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="40px" name="width" />
											<hx:commandExButton type="submit" value="選択"
												styleClass="cmdBtn_dat_s" id="select"
												action="#{pc_Xrl00201.doSelectAction}">
											</hx:commandExButton>
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="40">
							<hr/>
						</TD>
					</TR>
					<TR>
						<TD  style="padding-left:50px;padding-right:50px;">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="100%">
							<TBODY>
								<TR>
									<TH width="150px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateMeisai"
										value="#{pc_Xrl00201.propTeateNm.name}"
										style="#{pc_Xrl00201.propTeateNm.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:selectOneMenu
										styleClass="selectOneMenu" id="cmbTeate"
										style="width:320px"
										disabled="#{pc_Xrl00201.propTeateNm.disabled}"
										value="#{pc_Xrl00201.propTeateNm.stringValue}">
										<f:selectItems value="#{pc_Xrl00201.propTeateNm.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateRyohiKbnMeisai"
										value="#{pc_Xrl00201.propTeateRyohiKbn.name}"
										style="#{pc_Xrl00201.propTeateRyohiKbn.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:selectOneMenu
										styleClass="selectOneMenu" id="cmbTeateRyohiKbn"
										style="width:180px"
										disabled="#{pc_Xrl00201.propTeateRyohiKbn.disabled}"
										value="#{pc_Xrl00201.propTeateRyohiKbn.stringValue}">
										<f:selectItems value="#{pc_Xrl00201.propTeateRyohiKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateKbnMeisai"
										value="#{pc_Xrl00201.propTeateKbn.name}"
										style="#{pc_Xrl00201.propTeateKbn.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:selectOneMenu
										styleClass="selectOneMenu" id="cmbTeateKbn"
										style="width:180px"
										disabled="#{pc_Xrl00201.propTeateKbn.disabled}"
										value="#{pc_Xrl00201.propTeateKbn.stringValue}">
										<f:selectItems value="#{pc_Xrl00201.propTeateKbn.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateTaniMeisai"
										value="#{pc_Xrl00201.propTeateTani.name}"
										style="#{pc_Xrl00201.propTeateTani.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:selectOneMenu
										styleClass="selectOneMenu" id="cmbTeateTani"
										disabled="#{pc_Xrl00201.propTeateTani.disabled}"
										style="width:180px"
										value="#{pc_Xrl00201.propTeateTani.stringValue}">
										<f:selectItems value="#{pc_Xrl00201.propTeateTani.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateTankaMeisai"
										value="#{pc_Xrl00201.propTeateTanka.labelName}"
										style="#{pc_Xrl00201.propTeateTanka.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="*"><h:inputText styleClass="inputText"
										id="txtTeateTanka"
										value="#{pc_Xrl00201.propTeateTanka.integerValue}"
										style="#{pc_Xrl00201.propTeateTanka.style}" size="8"
										maxlength="#{pc_Xrl00201.propTeateTanka.maxLength}"
										style="text-align:right"
										disabled="#{pc_Xrl00201.propTeateTanka.disabled}">
										<f:convertNumber type="number" pattern="###,###,##0" />
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
									</h:inputText>
									<h:outputText styleClass="outputText"
										id="text7" value="円"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH width="150px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblteateKmkCdMeisai"
										value="#{pc_Xrl00201.propTeateKmkCd.labelName}"
										style="#{pc_Xrl00201.propTeateKmkCd.labelStyle}">
									</h:outputText>
									</TH>
									<TD nowrap width="*"><h:inputText styleClass="inputText"
											id="txtteateKmkCd"
											value="#{pc_Xrl00201.propTeateKmkCd.stringValue}"
											style="#{pc_Xrl00201.propTeateKmkCd.style}" size="14"
											disabled="#{pc_Xrl00201.propTeateKmkCd.disabled}"
											maxlength="#{pc_Xrl00201.propTeateKmkCd.maxLength}"
											onblur="return doKamokuAjax();"
											disabled="#{pc_Xrl00201.propTeateKmkCd.disabled}">
										</h:inputText>
										<hx:commandExButton
											type="submit" value="" styleClass="commandExButton_search"
											id="search"
											onclick="return openKamokuSubWindow();"
											disabled="#{pc_Xrl00201.propTeateKmkCd.disabled}"
											action="#{pc_Xrl00201.doSearchKamokAction}"
											>
										</hx:commandExButton>
										<h:outputText styleClass="outputText" id="lblPreKamokName"
										value="#{pc_Xrl00201.propTeateKmkName.stringValue}"
										></h:outputText>
									</TD>
									
								</TR>
								<TR>
									<!-- メモ -->
									<TH nowrap class="v_a" width="150px">
										<h:outputText styleClass="outputText" id="lblTeateMemoMeisai"
										value="#{pc_Xrl00201.propTeateMemo.labelName}"
										style="#{pc_Xrl00201.propTeateMemo.labelStyle}"></h:outputText><BR>
										<hx:graphicImageEx styleClass="graphicImageEx" id="imageEx2"
										value="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
									<TD colspan="3"><h:inputTextarea styleClass="inputTextarea"
										id="htmlTeateMemo" cols="75" rows="3"
										disabled="#{pc_Xrl00201.propTeateMemo.disabled}"
										value="#{pc_Xrl00201.propTeateMemo.stringValue}"
										style="#{pc_Xrl00201.propTeateMemo.style}; height: 56px;"></h:inputTextarea>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="20">
							<hr/>
						</TD>
					</TR>
					<TR>
						<TD width="100%">
						<TABLE width="100%" border="0" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="regist"
										confirm="#{msg.SY_MSG_0001W}"
										action="#{pc_Xrl00201.doRegistAction}"
										disabled="#{pc_Xrl00201.propRegist.disabled}">
									</hx:commandExButton>&nbsp; <hx:commandExButton type="submit"
										value="削除" styleClass="commandExButton_dat" id="delete"
										confirm="#{msg.SY_MSG_0004W}"
										action="#{pc_Xrl00201.doDeleteAction}"
										disabled="#{pc_Xrl00201.propDelete.disabled}">
									</hx:commandExButton>&nbsp; <hx:commandExButton type="submit"
										value="クリア" styleClass="commandExButton_etc" id="clear"
										action="#{pc_Xrl00201.doClearAction}"
										disabled="#{pc_Xrl00201.propClear.disabled}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrl00201.propTeateGyomuList.scrollPosition}" id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrl00201.propScrollPos.stringValue}"
				id="htmlScrollPos"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrl00201.propConfirmFlag.integerValue}"
				id="hdnConfirmFlag">
				<f:convertNumber />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>

	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
