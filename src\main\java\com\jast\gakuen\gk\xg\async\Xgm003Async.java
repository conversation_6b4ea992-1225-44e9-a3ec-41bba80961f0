/*
 * Xgm003Async.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.async;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;

import javax.inject.Inject;

import com.jast.gakuen.core.common.SessionInfo;
import com.jast.gakuen.core.common.SystemInfo;
import com.jast.gakuen.core.common.UserInfo;
import com.jast.gakuen.core.common.annotation.RxAsyncErrorCheck;
import com.jast.gakuen.core.common.annotation.RxAsyncExecute;
import com.jast.gakuen.core.common.annotation.RxAsyncInit;
import com.jast.gakuen.core.common.annotation.RxAsyncNumberCount;
import com.jast.gakuen.core.gk.async.GkBaseCollectiveOutputAsync;
import com.jast.gakuen.core.common.constant.FileTypeConst;
import com.jast.gakuen.core.common.constant.Order;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.database.DbSession;
import com.jast.gakuen.core.common.dto.FileDTO;
import com.jast.gakuen.core.common.dto.OrderItemDTO;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.RxLogger;
import com.jast.gakuen.core.common.util.Transporter;
import com.jast.gakuen.core.common.util.UtilDate;
import com.jast.gakuen.core.common.util.UtilStr;
import com.jast.gakuen.core.common.async.ICollectiveWriter;
import com.jast.gakuen.core.gk.util.UtilFormWriter;
import com.jast.gakuen.gk.xg.dto.Xgm003CollectiveOutputDTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003DTO02;
import com.jast.gakuen.gk.xg.dto.Xgm003ConditionDTO02;
import com.jast.gakuen.gk.xg.service.IXgm003Service;
import com.jast.gakuen.common.database.pk.dao.PkbGakHatbnDAO;
import com.jast.gakuen.common.database.pk.dao.PkbGkfrDAO;
import com.jast.gakuen.common.database.pk.dao.PkbSgksDAO;
import com.jast.gakuen.common.database.pk.dao.PkbSotSgksDAO;
import com.jast.gakuen.common.database.pk.dao.PkbStfrDAO;
import com.jast.gakuen.common.database.pk.entity.PkbGakHatbnAR;
import com.jast.gakuen.common.database.pk.entity.PkbGkfrAR;
import com.jast.gakuen.common.database.pk.entity.PkbSgksAR;
import com.jast.gakuen.common.database.pk.entity.PkbSotSgksAR;
import com.jast.gakuen.common.database.pk.entity.PkbStfrAR;
import com.jast.gakuen.gk.database.gh.dao.GhbGakDAO;
import com.jast.gakuen.gk.database.gh.entity.GhbGakAR;
import com.jast.gakuen.common.database.gh.dao.GhbGkfrDAO;
import com.jast.gakuen.common.database.gh.dao.GhbStfrDAO;
import com.jast.gakuen.common.database.gh.dao.GhePaywBunDAO;
import com.jast.gakuen.common.database.gh.entity.GhbGkfrAR;
import com.jast.gakuen.common.database.gh.entity.GhbStfrAR;
import com.jast.gakuen.common.database.gh.entity.GhePaywBunAR;
import com.jast.gakuen.core.common.exception.GakuenException;
import com.jast.gakuen.gk.gh.business.GhzGakSearchLogic;
import com.jast.gakuen.gk.gh.constant.Ghd008OrderConst;
import com.jast.gakuen.gk.gh.dto.Ghd008CollectiveOutputDTO01;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO07;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO08;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO11;
import com.jast.gakuen.gk.gh.dto.Ghf008DTO09;
import com.jast.gakuen.gk.gh.dto.GhzGakSearchConditionDTO01;
import com.jast.gakuen.gk.gh.dto.GhzGakSearchResultDTO01;
import com.jast.gakuen.gk.pk.business.PkbGakAuthLogic;
import com.jast.gakuen.gk.pk.dto.PkbGakAuthConditionDTO01;

/**
 * 学生納付金通知書出力（CSV+PDF一括ダウンロード）非同期処理クラス
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
public class Xgm003Async extends GkBaseCollectiveOutputAsync {

	/**
	 * プロダクトコード
	 */
	public static final String PRD_CD = "Xg";

	/**
	 * 非同期処理ID
	 */
	public static final String ASYNC_EXEC_ID = "Xgm003Async";

	/**
	 * 処理名：CSV+PDF一括ダウンロード
	 */
	public static final String EXEC_REPORT = "EXEC_REPORT";

	/**
	 * ログ出力
	 */
	protected RxLogger LOGGER;

	/**
	 * ログインユーザ情報
	 */
	protected UserInfo userInfo;

	/**
	 * 学生納付金通知書サービス
	 */
	@Inject
	private IXgm003Service xgm003Service;

	/**
	 * Transporter
	 */
	private final Transporter transporter = new Transporter();

	/**
	 * 対象志願者リスト
	 */
	protected final List<Ghf008DTO09> targetList = new ArrayList<>();

	/**
	 * コンストラクタ
	 *
	 * @throws Exception 例外
	 */
	public Xgm003Async() throws Exception {
		super();
		LOGGER = new RxLogger(Xgm003Async.class);
	}

	/**
	 * 初期処理
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	@RxAsyncInit(EXEC_REPORT)
	public void initOutPutReport(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// ログインユーザ情報を取得
		SessionInfo sessionInfo = SessionInfo.getSessionInfo();
		this.userInfo = sessionInfo.getLoginUser();
	}

	/**
	 * エラーチェック
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return エラーチェック結果
	 * @throws Exception 例外
	 */
	@RxAsyncErrorCheck(EXEC_REPORT)
	public List<Message> checkOutputData(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		List<Message> messageList = new ArrayList<>();

		if (condition.getPayList().isEmpty()) {
			// E_SY_00058={0}は存在しません。
			messageList.add(new Message("SY", Message.TypeCode.E, 58, "納付金"));
		}

		return messageList;
	}

	/**
	 * 処理件数取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 処理件数
	 * @throws Exception 例外
	 */
	@RxAsyncNumberCount(EXEC_REPORT)
	public int getCount(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// 処理件数は固定で2（CSV + PDF）
		return 2;
	}

	/**
	 * 業務処理：CSV+PDF一括ダウンロード
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	@RxAsyncExecute(EXEC_REPORT)
	public void executePackageOutput(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {

		LOGGER.info("CSV+PDF一括ダウンロード処理開始");

		// 圧縮対象ファイルのリスト
		List<FileDTO> zipFileDtoList = new ArrayList<>();

		try {
			// 1. CSV ファイル生成
			FileDTO csvFileDto = generateCsvFile(dbs, condition);
			if (csvFileDto != null) {
				zipFileDtoList.add(csvFileDto);
				addNormalCount(1);
				addCount(1);
			}

			// 2. PDF ファイル生成
			FileDTO pdfFileDto = generatePdfFile(dbs, condition);
			if (pdfFileDto != null) {
				zipFileDtoList.add(pdfFileDto);
				addNormalCount(1);
				addCount(1);
			}

			// 3. ZIP ファイル生成
			if (!zipFileDtoList.isEmpty()) {
				FileDTO zipFileDTO = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_ZIP01", FileTypeConst.ZIP);
				// ZIPファイルを生成し、圧縮対象ファイルを登録する
				compressOutputFile(zipFileDTO, zipFileDtoList, true);
			}

		} catch (Exception e) {
			LOGGER.error(e);
			addErrorCount(1);
			throw e;
		}

		LOGGER.info("CSV+PDF一括ダウンロード処理終了");
	}

	/**
	 * CSV ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return CSV ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generateCsvFile(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		LOGGER.info("CSV ファイル生成開始");

		// CSV ファイルのFileDTOを取得
		FileDTO csvFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_CSV01", FileTypeConst.CSV);

		// 出力ファイル用の一括出力用ユーティリティを取得する
		try (ICollectiveWriter csvWriter = getCollectiveWriter(csvFileDto)) {

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// データ行を出力
			for (Xgm003DTO02 data : dataList) {
				writeCsvDataLine(dbs, csvWriter, data, condition);
				addNormalCount(1);
				addCount(1);
			}

			LOGGER.info("CSV ファイル生成完了：" + dataList.size() + "件");
		}

		LOGGER.info("CSV ファイル生成終了");
		return csvFileDto;
	}

	/**
	 * PDF ファイル生成
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return PDF ファイルDTO
	 * @throws Exception 例外
	 */
	private FileDTO generatePdfFile(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		LOGGER.info("PDF ファイル生成開始");

		// PDF ファイルのFileDTOを取得
		FileDTO pdfFileDto = getOutputFileDTO(dbs, condition.getPrdCd(), "XGM003_PDF01", FileTypeConst.PDF);

		// SVF を使用してPDF生成
		UtilFormWriter pdfWriter = new UtilFormWriter();

		try {
			// PDF ファイルを開く
			pdfWriter.open(UtilFormWriter.DocFileType.PDF, pdfFileDto.getAbsolutePath());

			// SVF テンプレート（Xgm003RPT01.xml）を使用してPDF生成
			pdfWriter.setForm(com.jast.gakuen.core.common.constant.code.PrdKbn.XG, "Xgm003RPT01");

			// 納付金通知書データを取得
			List<Xgm003DTO02> dataList = getNotificationData(dbs, condition);

			// 各納付金データに対してPDFページを生成
			for (Xgm003DTO02 data : dataList) {
				// 基本情報を設定
				pdfWriter.print("年度", String.valueOf(data.getNendo()));
				pdfWriter.print("納付金コード", data.getPayCd());
				pdfWriter.print("納付金名称", data.getPayName());
				pdfWriter.print("分納区分", data.getBunnoKbnName());
				pdfWriter.print("分割番号", String.valueOf(data.getBunkatsuNo()));

				// 納入期限を設定
				if (data.getPayLimitDate() != null) {
					pdfWriter.print("納入期限", data.getPayLimitDate().toString());
				}

				// 発行日を設定
				if (condition.getHattyuDate() != null) {
					pdfWriter.print("発行日", condition.getHattyuDate().toString());
				}

				// 通信欄を設定
				if (condition.getTsuukyak() != null) {
					pdfWriter.print("通信欄", condition.getTsuukyak());
				}

				// 次のページに移動（最後のデータでない場合）
				pdfWriter.next();

				addNormalCount(1);
				addCount(1);
			}

			LOGGER.info("PDF ファイル生成完了：" + dataList.size() + "件");

		} finally {
			pdfWriter.close();
		}

		LOGGER.info("PDF ファイル生成終了");
		return pdfFileDto;
	}

	/**
	 * 納付金通知書データを取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期処理DTO
	 * @return 納付金通知書データリスト
	 * @throws Exception 例外
	 */
	private List<Xgm003DTO02> getNotificationData(final DbSession dbs, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		List<Xgm003DTO02> resultList = new ArrayList<>();

		// 選択された納付金リストが存在する場合
		if (condition.getPayList() != null && !condition.getPayList().isEmpty()) {
			// 選択された納付金リストが存在する場合は、そのまま使用
			// 学生情報の補完は行わない（既に必要な情報が設定済みと仮定）
			resultList.addAll(condition.getPayList());
			return resultList;
		}

		// 選択された納付金リストが存在しない場合は、学生検索共通処理で学生情報を取得
		// 学籍番号リストが空の場合は処理対象なし
		if (condition.getGaksekiCdList() == null || condition.getGaksekiCdList().isEmpty()) {
			LOGGER.warn("対象学籍番号リストが空のため、処理対象データなし");
			return resultList; // 空のリストを返す
		}

		// Ghd008Asyncと同様の方式で学生情報を取得
		for (String gaksekiCd : condition.getGaksekiCdList()) {

			// 対象学生
			List<Ghd008DTO07> gakseiList = this.getGakseiList(dbs, condition);

			if (gakseiList != null && !gakseiList.isEmpty()) {
				for (GhzGakSearchResultDTO01 gakInfo : gakseiList) {
					// 学生情報から納付金データを作成
					Xgm003DTO02 payData = createPayDataFromGakInfo(gakInfo, condition);
					if (payData != null) {
						resultList.add(payData);
					}
				}
			} else {
				LOGGER.warn("学生情報が見つかりません: 学籍番号=" + gaksekiCd);
			}
		}

		// データが存在しない場合はエラー
		if (resultList.isEmpty()) {
			throw new GakuenException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 199, "納付金"));
		}

		return resultList;
	}

	/**
	 * CSV データ行を出力
	 *
	 * @param dbs DBセッション
	 * @param csvWriter CSV ライター
	 * @param data 納付金データ
	 * @param condition 非同期処理DTO
	 * @throws Exception 例外
	 */
	private void writeCsvDataLine(final DbSession dbs, final ICollectiveWriter csvWriter, final Xgm003DTO02 data, final Xgm003CollectiveOutputDTO02 condition) throws Exception {
		// 21フィールド形式でCSV出力
		List<String> csvLine = new ArrayList<>();

		// 01: データ区分（固定値：2）
		csvLine.add("2");

		// 02: 処理区分（固定値：0）
		csvLine.add("0");

		// 03: 顧客番号（委託人コード）
		String furikomiIraiCd = getFurikomiIraiCd(dbs, data, condition);
		csvLine.add(furikomiIraiCd != null ? furikomiIraiCd : "");

		// 04: 確認番号
		csvLine.add(generateKakuninNo(data));

		// 05: コンビニネットコード
		csvLine.add(generateKonbiniNetCd(data));

		// 06: 請求金額
		csvLine.add("0");

		// 07: 元金
		csvLine.add("0");

		// 08: 延滞金額
		csvLine.add("0");

		// 09: 消費税
		csvLine.add("0");

		// 10: 請求内容カナ
		csvLine.add(data.getPayName() != null ? data.getPayName() : "");

		// 11: 請求内容漢字
		csvLine.add(data.getPayName() != null ? data.getPayName() : "");

		// 12: 氏名カナ
		csvLine.add("");

		// 13: 氏名漢字
		csvLine.add("");

		// 14-20: 空フィールド（7個）
		for (int i = 0; i < 7; i++) {
			csvLine.add("");
		}

		// 21: 請求情報有効期限
		csvLine.add(data.getPayLimitDate() != null ? data.getPayLimitDate().toString() : "");

		// CSV行を出力
		csvWriter.writeLineData(csvLine, false);
	}

	/**
	 * 学生情報から納付金データを作成（Ghd008Asyncの方式を参考）
	 *
	 * @param gakInfo 学生情報
	 * @param condition 非同期処理DTO
	 * @return 納付金データ
	 */
	private Xgm003DTO02 createPayDataFromGakInfo(final GhzGakSearchResultDTO01 gakInfo, final Xgm003CollectiveOutputDTO02 condition) {

		// 基本的な納付金データを作成
		Xgm003DTO02 payData = new Xgm003DTO02();

		// 条件から基本情報を設定
		if (condition.getWariateNendo() != null) {
			payData.setNendo(condition.getWariateNendo());
		}

		// 納付金コードやパターンコードは条件から取得するか、デフォルト値を設定
		// 実際の業務要件に応じて適切な値を設定する必要がある
		if (condition.getNoufuKinShubetsu() != null && !condition.getNoufuKinShubetsu().isEmpty()) {
			payData.setPayCd(condition.getNoufuKinShubetsu().get(0)); // 最初の納付金種別を使用
		}

		// パターンコードは業務ロジックに応じて設定
		payData.setPatternCd("01"); // デフォルト値
		payData.setBunnoKbnCd(1); // デフォルト値
		payData.setBunkatsuNo(1); // デフォルト値

		LOGGER.info("学生情報から納付金データを作成: 学籍番号=" + gakInfo.getGaksekiCd() +
				   ", 管理番号=" + gakInfo.getKanriNo() +
				   ", ProductKbn=" + gakInfo.getProductKbn() +
				   ", GakSotKbn=" + gakInfo.getGakSotKbn());

		return payData;
	}

	/**
	 * 確認番号を生成
	 *
	 * @param payData 納付金データ
	 * @return 確認番号
	 */
	private String generateKakuninNo(final Xgm003DTO02 payData) {
		try {
			// 暫定実装として、固定値+納付金コードを使用
			String baseNo = "1" + payData.getPayCd();

			// 年度と納付金コードから連番を生成
			String sequenceNo = String.format("%04d",
				Math.abs((payData.getNendo() + payData.getPayCd().hashCode() +
				payData.getBunnoKbnCd() + payData.getBunkatsuNo()) % 10000));

			String kakuninNo = baseNo + sequenceNo;

			// 最大桁数制限
			if (kakuninNo.length() > 20) {
				kakuninNo = kakuninNo.substring(0, 20);
			}

			return kakuninNo;
		} catch (Exception e) {
			LOGGER.warn("確認番号生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

	/**
	 * コンビニネットコードを生成
	 *
	 * @param payData 納付金データ
	 * @return コンビニネットコード
	 */
	private String generateKonbiniNetCd(final Xgm003DTO02 payData) {
		try {
			// バーコードを生成
			StringBuilder bcd = new StringBuilder();

			// 企業コード（暫定値）
			bcd.append("91234");

			// 委託コード（暫定値）
			bcd.append("567");

			// 顧客番号（確認番号の一部を使用）
			String kakuninNo = generateKakuninNo(payData);
			if (kakuninNo.length() >= 8) {
				bcd.append(kakuninNo.substring(0, 8));
			} else {
				bcd.append(String.format("%-8s", kakuninNo).replace(' ', '0'));
			}

			// 期限（YYYYMMDD形式）
			if (payData.getPayLimitDate() != null) {
				String limitDate = payData.getPayLimitDate().toString().replace("-", "");
				bcd.append(limitDate);
			} else {
				bcd.append("00000000");
			}

			// 金額（暫定値）
			bcd.append(String.format("%08d", 0));

			return bcd.toString();
		} catch (Exception e) {
			LOGGER.warn("コンビニネットコード生成エラー: " + e.getMessage());
			return ""; // エラー時は空文字を返す
		}
	}

	/**
	 * 振込依頼人コードを取得
	 *
	 * @param dbs DBセッション
	 * @param payData 納付金データ
	 * @param condition 非同期処理DTO
	 * @return 振込依頼人コード
	 */
	private String getFurikomiIraiCd(final DbSession dbs, final Xgm003DTO02 payData, final Xgm003CollectiveOutputDTO02 condition) {
		
		return "";
	}

	/**
	 * 学籍番号から管理番号を取得
	 *
	 * @param dbs DBセッション
	 * @param gaksekiCd 学籍番号
	 * @return 管理番号
	 */
	private Long getKanriNoByGaksekiCd(final DbSession dbs, final String gaksekiCd) {
		try {
			// 学籍番号発番管理から管理番号を取得
			PkbGakHatbnDAO pkbGakHatbnDAO = dbs.getDao(PkbGakHatbnDAO.class);
			PkbGakHatbnAR pkbGakHatbnAR = pkbGakHatbnDAO.findByPrimaryKey(gaksekiCd);

			if (pkbGakHatbnAR != null) {
				return pkbGakHatbnAR.getKanriNo();
			}

			// 発番TBLに存在しない場合、学費学籍から取得
			GhbGakDAO ghbGakDAO = dbs.getDao(GhbGakDAO.class);
			List<GhbGakAR> ghbGakARList = ghbGakDAO.findByGaksekiCd(gaksekiCd);
			if (!ghbGakARList.isEmpty()) {
				return ghbGakARList.get(0).getKanriNo();
			}

			return null;
		} catch (Exception e) {
			LOGGER.warn("管理番号取得エラー: " + e.getMessage());
			return null;
		}
	}

	/**
	 * 対象学生リスト取得
	 *
	 * @param dbs DBセッション
	 * @param condition 非同期パラメータDTO
	 * @return 対象学生リスト
	 * @throws Exception 例外
	 */
	protected List<Ghd008DTO07> getGakseiList(final DbSession dbs,
			final Xgm003CollectiveOutputDTO02 condition)
			throws Exception {

		List<Ghd008DTO07> rtnList = new ArrayList<>();

		GhzGakSearchLogic ghzCheckLogic = new GhzGakSearchLogic(dbs, this.userInfo.getUserId());

		PkbSgksDAO pkbSgksDAO = dbs.getDao(PkbSgksDAO.class);
		PkbSotSgksDAO pkbSotSgksDAO = dbs.getDao(PkbSotSgksDAO.class);

		if (condition.getTubNo() == 1) {
			// パラメータ.タブNOが[1：納付金指定]の場合
			// パラメータ.学生一括指定条件に一致する学生を取得する。
			GhzGakSearchConditionDTO01 paramDTO = new GhzGakSearchConditionDTO01();
			paramDTO.setShoriKbnList(this.getShoriKbnList(condition));
			paramDTO.setKijunDate(SystemInfo.getSystemDate());
			paramDTO.setGbushoCd(condition.getGbushoCd());
			paramDTO.setSgksCd(condition.getSgksCd());
			paramDTO.setGhSgksCd(condition.getGhSgksCd());
			paramDTO.setGaknen(condition.getGaknen());
			paramDTO.setSemester(condition.getSemester());
			paramDTO.setNyugakNendo(condition.getNyugakNendo());
			paramDTO.setNyugakNendo(condition.getNyugakNendo());
			paramDTO.setNyugakGakkiNo(condition.getNyugakGakkiNo());
			paramDTO.setNyugakNendoDeemed(condition.getNyugakNendoDeemed());
			paramDTO.setNyugakGakkiNoDeemed(condition.getNyugakGakkiNoDeemed());
			paramDTO.setCgksCd(condition.getCgksCd());
			paramDTO.setNyugakSbtCd(condition.getNyugakSbtCd());
			paramDTO.setShugakSbtCd(condition.getShugakSbtCd());
			paramDTO.setClassSbtCd(condition.getClassSbtCd());
			paramDTO.setClassCd(condition.getClassCd());
			paramDTO.setMajorCourseSbtCd(condition.getMajorCourseSbtCd());
			paramDTO.setMajorCourseCd(condition.getMajorCourseCd());
			paramDTO.setShshnkCd(condition.getShshnkCd());
			if (condition.getRyugakseiFlg() == null) {
				paramDTO.setRyugakseiFlg(null);
			} else if (condition.getRyugakseiFlg()) {
				paramDTO.setRyugakseiFlg(1);
			} else if (!condition.getRyugakseiFlg()) {
				paramDTO.setRyugakseiFlg(0);
			}
			paramDTO.setRyugakseiKbn(condition.getRyugakseiKbn());

			if (condition.getShogaisyaFlg() == null) {
				paramDTO.setShogaisyaFlg(null);
			} else if (condition.getShogaisyaFlg()) {
				paramDTO.setShogaisyaFlg(1);
			} else if (!condition.getShogaisyaFlg()) {
				paramDTO.setShogaisyaFlg(0);
			}
			if (condition.getShakaijinFlg() == null) {
				paramDTO.setShakaijinFlg(null);
			} else if (condition.getShakaijinFlg()) {
				paramDTO.setShakaijinFlg(1);
			} else if (!condition.getShakaijinFlg()) {
				paramDTO.setShakaijinFlg(0);
			}

			paramDTO.setIdoKikanFrom(condition.getIdoKikanFrom());
			paramDTO.setIdoKikanTo(condition.getIdoKikanTo());
			paramDTO.setIdoShutgakApplyKekkaKbnList(condition.getIdoShutgakApplyKekkaKbnList());
			paramDTO.setIdoShutgakSbtKbnList(condition.getIdoShutgakSbtKbnList());
			paramDTO.setIdoInfoReportFlag(2);

			PkbGakAuthLogic gakAuth = new PkbGakAuthLogic(dbs, SystemInfo.getSystemDate(), this.userInfo.getUserId());

			List<GhzGakSearchResultDTO01> resultDTOList = ghzCheckLogic.gakSotSerach(paramDTO);
			for (GhzGakSearchResultDTO01 resultDTO : resultDTOList) {
				// 共通直轄学生のうち、参照権限がない学生については、処理対象外とする。
				if ("PK".equals(resultDTO.getProductKbn())) {

					if ("GAK".equals(resultDTO.getGakSotKbn())) {
						PkbGakAuthConditionDTO01 gakAuthDTO = new PkbGakAuthConditionDTO01();
						gakAuthDTO.setKanriNo(resultDTO.getKanriNo());
						if (!gakAuth.hasGakAuth(gakAuthDTO)) {
							continue;
						}
					} else {
						PkbGakAuthConditionDTO01 sotAuthDTO = new PkbGakAuthConditionDTO01();
						sotAuthDTO.setKanriNo(resultDTO.getKanriNo());
						if (!gakAuth.hasSotAuth(sotAuthDTO)) {
							continue;
						}
					}
				}

				// パラメータ.自由設定項目リスト（学生/卒業生/学費学生/学費卒業生）の条件に一致しない学生は、処理対象外とする。
				if (!isMatchFreeVal(dbs, condition, resultDTO)) {
					continue;
				}

				Ghd008DTO07 rtnDTO = new Ghd008DTO07();
				transporter.move(resultDTO, rtnDTO);

				// 所属学科組織並び順NO
				if ("PK".equals(resultDTO.getProductKbn())) {
					if ("GAK".equals(resultDTO.getGakSotKbn())) {
						PkbSgksAR pkbSgksAR = pkbSgksDAO.findByPrimaryKey(resultDTO.getSgksCd());
						if (pkbSgksAR != null) {
							rtnDTO.setSortSgksSortNo(pkbSgksAR.getSortNo());
						}
					} else {
						PkbSotSgksAR pkbSotSgksAR = pkbSotSgksDAO.findByPrimaryKey(
								resultDTO.getNyugakNendo(),
								resultDTO.getNyugakGakkiNo(),
								resultDTO.getSgksCd());
						if (pkbSotSgksAR != null) {
							rtnDTO.setSortSgksSortNo(pkbSotSgksAR.getSortNo());
						}
					}
				}

				// // 郵便番号
				// Ghd008DTO08 addrInfo = this.getAddrInfo(dbs,
				// 		resultDTO.getProductKbn(),
				// 		resultDTO.getGakSotKbn(),
				// 		resultDTO.getKanriNo(),
				// 		condition.getAddAtesaki(),
				// 		condition.getAddHoshoninSbt());
				// rtnDTO.setSortPostCd(addrInfo.getAtskPostCd());

				// 学生区分
				if ("PK".equals(resultDTO.getProductKbn())) {
					if ("GAK".equals(resultDTO.getGakSotKbn())) {
						rtnDTO.setSortGakKbn(0);
					} else {
						rtnDTO.setSortGakKbn(2);
					}
				} else {
					if ("GAK".equals(resultDTO.getGakSotKbn())) {
						rtnDTO.setSortGakKbn(1);
					} else {
						rtnDTO.setSortGakKbn(3);
					}
				}

				rtnList.add(rtnDTO);
			}

			Comparator<Ghd008DTO07> comparator = this.getComparator(condition.getOrderItems());
			if (comparator != null) {
				// ソート（指定した並び順）
				rtnList.sort(comparator);
			}

		} else {
			// パラメータ.タブNOが[3：納付金・学生指定]、[4：学生指定]の場合
			// パラメータ.出力学生一覧の学籍番号毎に学生情報を取得する。
			for (String gaksekiCd : condition.getGaksekiCdList()) {

				GhzGakSearchConditionDTO01 paramDTO = new GhzGakSearchConditionDTO01();
				paramDTO.setShoriKbnList(new ArrayList<>(Arrays.asList("1", "2", "3", "4", "5")));
				paramDTO.setIdoInfoReportFlag(2);
				paramDTO.setKijunDate(SystemInfo.getSystemDate());
				paramDTO.setGaksekiCd(gaksekiCd);

				List<GhzGakSearchResultDTO01> resultDTOList = ghzCheckLogic.gakSotSerach(paramDTO);
				for (GhzGakSearchResultDTO01 resultDTO : resultDTOList) {
					Ghd008DTO07 rtnDTO = new Ghd008DTO07();
					transporter.move(resultDTO, rtnDTO);
					rtnList.add(rtnDTO);
				}
			}
		}
		return rtnList;
	}

	/**
	 * [学生(卒業生)検索共通処理] 学生(卒業生)検索.処理区分リスト取得
	 *
	 * @param condition 非同期パラメータDTO
	 * @return 処理区分リスト
	 */
	protected List<String> getShoriKbnList(final Xgm003CollectiveOutputDTO02 condition) {

		List<String> distinguish1 = Arrays.asList("1", "3", "4");
		List<String> distinguish2 = Arrays.asList("1");
		List<String> distinguish3 = Arrays.asList("3", "4");

		if (condition.isCommonManageStu()) {
			if (condition.isGhbManageStu()) {
				return distinguish1;
			} else {
				return distinguish2;
			}
		} else {
			if (condition.isGhbManageStu()) {
				return distinguish3;
			} else {
				return distinguish1;
			}
		}
	}

	/**
	 * 並び順項目DTOのリストを基にコンパレーターを生成する
	 *
	 * @param orderItems 並び順項目DTOのリスト
	 * @return コンパレーター
	 */
	protected Comparator<Ghd008DTO07> getComparator(final List<OrderItemDTO> orderItems) {
		// リターンするコンパレーター変数
		Comparator<Ghd008DTO07> rtnComparator = null;
		// 並び順項目を順番にコンパレーターを生成する
		int orderNo = 0;
		for (OrderItemDTO orderItem : orderItems) {
			orderNo++;
			// 並び順項目指定がない場合
			if (UtilStr.isEmpty(orderItem.getValue())) {
				// For文を中断する
				break;
			}
			// 該当項目の並び順のコンパレーターを生成して保存する変数
			Comparator<Ghd008DTO07> itemComparator = null;
			// 該当項目のValueを取得
			Ghd008OrderConst order = Ghd008OrderConst.of(orderItem.getValue());

			// 取得したValueによりコンパレーターを作成
			switch (order) {
			// 学年
			case gaknen:
				if (orderItem.getOrder() == Order.DESC) {
					itemComparator = Comparator.comparing(Ghd008DTO07::getGaknen, Comparator.nullsLast(Comparator.naturalOrder())).reversed();
				} else {
					itemComparator = Comparator.comparing(Ghd008DTO07::getGaknen, Comparator.nullsLast(Comparator.naturalOrder()));
				}
				break;
			// セメスタ
			case semester:
				if (orderItem.getOrder() == Order.DESC) {
					itemComparator = Comparator.comparing(Ghd008DTO07::getSemester, Comparator.nullsLast(Comparator.naturalOrder())).reversed();
				} else {
					itemComparator = Comparator.comparing(Ghd008DTO07::getSemester, Comparator.nullsLast(Comparator.naturalOrder()));
				}
				break;
			// 所属学科組織
			case sgks:
				if (orderItem.getOrder() == Order.DESC) {
					itemComparator = Comparator.comparing(Ghd008DTO07::getSortSgksSortNo).reversed()
							.thenComparing(Ghd008DTO07::getSgksCd, Comparator.nullsLast(Comparator.naturalOrder())).reversed();
				} else {
					itemComparator = Comparator.comparing(Ghd008DTO07::getSortSgksSortNo)
							.thenComparing(Ghd008DTO07::getSgksCd, Comparator.nullsLast(Comparator.naturalOrder()));
				}

				break;
			// 郵便番号
			case postCd:
				if (orderItem.getOrder() == Order.DESC) {
					itemComparator = Comparator.comparing(Ghd008DTO07::getSortPostCd, Comparator.nullsLast(Comparator.naturalOrder())).reversed();
				} else {
					itemComparator = Comparator.comparing(Ghd008DTO07::getSortPostCd, Comparator.nullsLast(Comparator.naturalOrder()));
				}
				break;
			default:
				break;
			}

			if (orderNo == 1) {
				if (Ghd008OrderConst.postCd.equals(order)) {
					// 郵便番号が第一キーの場合は、郵便番号に続いて共通在学→学費在学→共通卒業→学費卒業
					itemComparator = itemComparator.thenComparing(Ghd008DTO07::getSortGakKbn);
				} else {
					// 郵便番号が第一キー以外の場合は、第一キーの前に共通在学→学費在学→共通卒業→学費卒業
					itemComparator = Comparator.comparing(Ghd008DTO07::getSortGakKbn).thenComparing(itemComparator);
				}
			}

			if (rtnComparator != null) {
				// 該当項目の並び順のコンパレーターをリターンするコンパレーターに追加
				rtnComparator = rtnComparator.thenComparing(itemComparator);
			} else {
				// 該当項目の並び順のコンパレーターをリターンするコンパレーターに設定
				rtnComparator = itemComparator;
			}
		}

		// 学籍番号を追加
		if (rtnComparator != null) {
			rtnComparator = rtnComparator.thenComparing(Ghd008DTO07::getGaksekiCd);
		} else {
			rtnComparator = Comparator.comparing(Ghd008DTO07::getGaksekiCd);
		}

		// コンパレーターをリターンする
		return rtnComparator;
	}

	/**
	 * 学生自由設定に一致するかどうかのチェック
	 *
	 * @param dbs Dbセッション
	 * @param condition 指定条件
	 * @param gakseiDTO 学生DTO
	 * @return true/false
	 * @throws Exception 例外
	 */
	protected boolean isMatchFreeVal(final DbSession dbs,
			final Xgm003CollectiveOutputDTO02 condition,
			final GhzGakSearchResultDTO01 gakseiDTO) throws Exception {

		boolean reFlg = false;
		if ("PK".equals(gakseiDTO.getProductKbn()) && "GAK".equals(gakseiDTO.getGakSotKbn())) {
			// 【共通在学生】■■■■■■

			// ①共通学生自由設定項目リストのチェック
			reFlg = chkPkGkfr(dbs, condition.getPkGkfrList(), gakseiDTO.getKanriNo());
			if (!reFlg) {
				return false;
			}

			// ③学費学生自由設定項目リストのチェック
			return chkGhGkfr(dbs, condition.getGhGkfrList(), gakseiDTO.getKanriNo());

		} else if ("PK".equals(gakseiDTO.getProductKbn()) && "SOT".equals(gakseiDTO.getGakSotKbn())) {
			// 【共通卒業生】■■■■■■

			// ②共通卒業生自由設定項目リストのチェック
			reFlg = chkPkStfr(dbs, condition.getPkStfrList(), gakseiDTO.getKanriNo());
			if (!reFlg) {
				return false;
			}
			// ③学費学生自由設定項目リスト
			reFlg = chkGhGkfr(dbs, condition.getGhGkfrList(), gakseiDTO.getKanriNo());
			if (!reFlg) {
				return false;
			}
			// ④学費卒業生自由設定項目リスト
			return chkGhStfr(dbs, condition.getGhStfrList(), gakseiDTO.getKanriNo());

		} else if ("GH".equals(gakseiDTO.getProductKbn()) && "GAK".equals(gakseiDTO.getGakSotKbn())) {
			// 【学費在学生】■■■■■■

			// ③学費学生自由設定項目リスト
			return chkGhGkfr(dbs, condition.getGhGkfrList(), gakseiDTO.getKanriNo());

		} else if ("GH".equals(gakseiDTO.getProductKbn()) && "SOT".equals(gakseiDTO.getGakSotKbn())) {
			// 【学費卒業生】■■■■■■

			// ④学費卒業生自由設定項目リスト
			return chkGhStfr(dbs, condition.getGhStfrList(), gakseiDTO.getKanriNo());
		}

		return reFlg;
	}

	/**
	 * 「共通学生自由設定項目」チェック
	 *
	 * @param dbs Dbセッション
	 * @param frList Ghd008DTO11
	 * @param kanriNo 管理番号
	 * @return true：チェックOK
	 * @throws Exception 例外
	 */
	protected boolean chkPkGkfr(final DbSession dbs, final List<Ghd008DTO11> frList, final long kanriNo) throws Exception {
		if (frList.isEmpty()) {
			// 条件なし：一致する
			return true;
		}
		Map<String, Boolean> frMatchMap = new HashMap<>();

		// 自由設定対象コード毎にグループ化
		Map<String, List<Ghd008DTO11>> frMap = this.getFreeValConditionMap(frList);

		// 自由設定対象コード毎に判定（同一データNOで自由設定対象が登録されているか）
		for (String freeTgtCd : frMap.keySet()) {
			frMatchMap.put(freeTgtCd, false);

			// 自由設定対象に該当する条件を取得する
			List<Ghd008DTO11> frTsyList = frMap.get(freeTgtCd);

			// 1件目の項目NOに対して、条件に一致するデータNOを取得する
			Ghd008DTO11 firstDto = frTsyList.get(0);
			PkbGkfrDAO frDAO = dbs.getDao(PkbGkfrDAO.class);
			List<PkbGkfrAR> firstFrARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
					kanriNo,
					firstDto.getFreeTgtCd(),
					null,
					firstDto.getFreeKomokNo(),
					firstDto.getFreeVal());
			if (firstFrARList.isEmpty()) {
				// 一致する自由設定項目が0件：一致しない
				return false;
			}

			// 一致したデータNOごとに、他の項目NOの条件が一致するかを確認する
			for (PkbGkfrAR firstFrAR : firstFrARList) {
				boolean match = true;
				int dataNo = firstFrAR.getFreeDataNo();

				// 2件目の項目NO以降の判定
				for (int i = 1; i < frTsyList.size(); i++) {
					Ghd008DTO11 dto = frTsyList.get(i);
					List<PkbGkfrAR> frARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
							kanriNo,
							dto.getFreeTgtCd(),
							dataNo,
							dto.getFreeKomokNo(),
							dto.getFreeVal());
					if (frARList.isEmpty()) {
						// 存在しない場合は、不一致
						// 次の項目NOを確認することなくループを抜ける⇒次のデータNOへ
						match = false;
						break;
					}
				}
				if (match) {
					// 一致した場合は、該当の自由設定対象コードは、OK
					// 次のデータNOを確認することなくループを抜ける⇒次の自由設定対象コードへ
					frMatchMap.put(freeTgtCd, match);
					break;
				}
			}
		}
		// 条件が指定されている自由設定対象コードのうち、１つでも不一致であれば、不一致を返す
		for (Map.Entry<String, Boolean> entry : frMatchMap.entrySet()) {
			Boolean match = entry.getValue();
			if (!match) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 「共通卒業生自由設定項目」チェック
	 *
	 * @param dbs Dbセッション
	 * @param frList Ghd008DTO11
	 * @param kanriNo 管理番号
	 * @return true：チェックOK
	 * @throws Exception 例外
	 */
	protected boolean chkPkStfr(final DbSession dbs, final List<Ghd008DTO11> frList, final long kanriNo) throws Exception {
		if (frList.isEmpty()) {
			// 条件なし：一致する
			return true;
		}
		Map<String, Boolean> frMatchMap = new HashMap<>();

		// 自由設定対象コード毎にグループ化
		Map<String, List<Ghd008DTO11>> frMap = this.getFreeValConditionMap(frList);

		// 自由設定対象コード毎に判定（同一データNOで自由設定対象が登録されているか）
		for (String freeTgtCd : frMap.keySet()) {
			frMatchMap.put(freeTgtCd, false);

			// 自由設定対象に該当する条件を取得する
			List<Ghd008DTO11> frTsyList = frMap.get(freeTgtCd);

			// 1件目の項目NOに対して、条件に一致するデータNOを取得する
			Ghd008DTO11 firstDto = frTsyList.get(0);
			PkbStfrDAO frDAO = dbs.getDao(PkbStfrDAO.class);
			List<PkbStfrAR> firstFrARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
					kanriNo,
					firstDto.getFreeTgtCd(),
					null,
					firstDto.getFreeKomokNo(),
					firstDto.getFreeVal());

			if (firstFrARList.isEmpty()) {
				// 一致する自由設定項目が0件：一致しない
				return false;
			}

			// 一致したデータNOごとに、他の項目NOの条件が一致するかを確認する
			for (PkbStfrAR firstFrAR : firstFrARList) {
				boolean match = true;
				int dataNo = firstFrAR.getFreeDataNo();

				// 2件目の項目NO以降の判定
				for (int i = 1; i < frTsyList.size(); i++) {

					Ghd008DTO11 dto = frTsyList.get(i);
					List<PkbStfrAR> frARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
							kanriNo,
							dto.getFreeTgtCd(),
							dataNo,
							dto.getFreeKomokNo(),
							dto.getFreeVal());
					if (frARList.isEmpty()) {
						// 存在しない場合は、不一致
						// 次の項目NOを確認することなくループを抜ける⇒次のデータNOへ
						match = false;
						break;
					}
				}
				if (match) {
					// 一致した場合は、該当の自由設定対象コードは、OK
					// 次のデータNOを確認することなくループを抜ける⇒次の自由設定対象コードへ
					frMatchMap.put(freeTgtCd, match);
					break;
				}
			}
		}
		// 条件が指定されている自由設定対象コードのうち、１つでも不一致であれば、不一致を返す
		for (Map.Entry<String, Boolean> entry : frMatchMap.entrySet()) {
			Boolean match = entry.getValue();
			if (!match) {
				return false;
			}
		}
		return true;
	}


	/**
	 * 「学費学生自由設定項目」チェック
	 *
	 * @param dbs Dbセッション
	 * @param frList Ghd008DTO11
	 * @param kanriNo 管理番号
	 * @return true：チェックOK
	 * @throws Exception 例外
	 */
	protected boolean chkGhGkfr(final DbSession dbs, final List<Ghd008DTO11> frList, final long kanriNo) throws Exception {
		if (frList.isEmpty()) {
			// 条件なし：一致する
			return true;
		}
		Map<String, Boolean> frMatchMap = new HashMap<>();

		// 自由設定対象コード毎にグループ化
		Map<String, List<Ghd008DTO11>> frMap = this.getFreeValConditionMap(frList);

		// 自由設定対象コード毎に判定（同一データNOで自由設定対象が登録されているか）
		for (String freeTgtCd : frMap.keySet()) {
			frMatchMap.put(freeTgtCd, false);

			// 自由設定対象に該当する条件を取得する
			List<Ghd008DTO11> frTsyList = frMap.get(freeTgtCd);

			// 1件目の項目NOに対して、条件に一致するデータNOを取得する
			Ghd008DTO11 firstDto = frTsyList.get(0);
			GhbGkfrDAO frDAO = dbs.getDao(GhbGkfrDAO.class);
			List<GhbGkfrAR> firstFrARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
					kanriNo,
					firstDto.getFreeTgtCd(),
					null,
					firstDto.getFreeKomokNo(),
					firstDto.getFreeVal());

			if (firstFrARList.isEmpty()) {
				// 一致する自由設定項目が0件：一致しない
				return false;
			}

			// 一致したデータNOごとに、他の項目NOの条件が一致するかを確認する
			for (GhbGkfrAR firstFrAR : firstFrARList) {
				boolean match = true;
				int dataNo = firstFrAR.getFreeDataNo();

				// 2件目の項目NO以降の判定
				for (int i = 1; i < frTsyList.size(); i++) {

					Ghd008DTO11 dto = frTsyList.get(i);
					List<GhbGkfrAR> frARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
							kanriNo,
							dto.getFreeTgtCd(),
							dataNo,
							dto.getFreeKomokNo(),
							dto.getFreeVal());
					if (frARList.isEmpty()) {
						// 存在しない場合は、不一致
						// 次の項目NOを確認することなくループを抜ける⇒次のデータNOへ
						match = false;
						break;
					}
				}

				if (match) {
					// 一致した場合は、該当の自由設定対象コードは、OK
					// 次のデータNOを確認することなくループを抜ける⇒次の自由設定対象コードへ
					frMatchMap.put(freeTgtCd, match);
					break;
				}
			}
		}

		// 条件が指定されている自由設定対象コードのうち、１つでも不一致であれば、不一致を返す
		for (Map.Entry<String, Boolean> entry : frMatchMap.entrySet()) {
			Boolean match = entry.getValue();
			if (!match) {
				return false;
			}
		}
		return true;
	}


	/**
	 * 「学費卒業生自由設定項目」チェック
	 *
	 * @param dbs Dbセッション
	 * @param frList Ghd008DTO11
	 * @param kanriNo 管理番号
	 * @return true：チェックOK
	 * @throws Exception 例外
	 */
	protected boolean chkGhStfr(final DbSession dbs, final List<Ghd008DTO11> frList, final long kanriNo) throws Exception {
		if (frList.isEmpty()) {
			// 条件なし：一致する
			return true;
		}
		Map<String, Boolean> frMatchMap = new HashMap<>();

		// 自由設定対象コード毎にグループ化
		Map<String, List<Ghd008DTO11>> frMap = this.getFreeValConditionMap(frList);

		// 自由設定対象コード毎に判定（同一データNOで自由設定対象が登録されているか）
		for (String freeTgtCd : frMap.keySet()) {
			frMatchMap.put(freeTgtCd, false);

			// 自由設定対象に該当する条件を取得する
			List<Ghd008DTO11> frTsyList = frMap.get(freeTgtCd);

			// 1件目の項目NOに対して、条件に一致するデータNOを取得する
			Ghd008DTO11 firstDto = frTsyList.get(0);
			GhbStfrDAO frDAO = dbs.getDao(GhbStfrDAO.class);
			List<GhbStfrAR> firstFrARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
					kanriNo,
					firstDto.getFreeTgtCd(),
					null,
					firstDto.getFreeKomokNo(),
					firstDto.getFreeVal());

			if (firstFrARList.isEmpty()) {
				// 一致する自由設定項目が0件：一致しない
				return false;
			}

			// 一致したデータNOごとに、他の項目NOの条件が一致するかを確認する
			for (GhbStfrAR firstFrAR : firstFrARList) {
				boolean match = true;
				int dataNo = firstFrAR.getFreeDataNo();

				// 2件目の項目NO以降の判定
				for (int i = 1; i < frTsyList.size(); i++) {

					Ghd008DTO11 dto = frTsyList.get(i);
					List<GhbStfrAR> frARList = frDAO.findByKanriNoFreeTgtCdFreeDataNoFreeKomokNoFreeVal(
							kanriNo,
							dto.getFreeTgtCd(),
							dataNo,
							dto.getFreeKomokNo(),
							dto.getFreeVal());
					if (frARList.isEmpty()) {
						// 存在しない場合は、不一致
						// 次の項目NOを確認することなくループを抜ける⇒次のデータNOへ
						match = false;
						break;
					}
				}

				if (match) {
					// 一致した場合は、該当の自由設定対象コードは、OK
					// 次のデータNOを確認することなくループを抜ける⇒次の自由設定対象コードへ
					frMatchMap.put(freeTgtCd, match);
					break;
				}
			}
		}

		// 条件が指定されている自由設定対象コードのうち、１つでも不一致であれば、不一致を返す
		for (Map.Entry<String, Boolean> entry : frMatchMap.entrySet()) {
			Boolean match = entry.getValue();
			if (!match) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 自由設定項目条件を自由設定対象コード毎にグループ化する
	 *
	 * @param frList 自由設定項目条件
	 * @return 自由設定対象コード毎にグループ分けしたMAP
	 */
	protected Map getFreeValConditionMap(final List<Ghd008DTO11> frList) {

		Map<String, List<Ghd008DTO11>> rtnMap = new HashMap<>();
		for (Ghd008DTO11 dto : frList) {
			if (!rtnMap.containsKey(dto.getFreeTgtCd())) {
				rtnMap.put(dto.getFreeTgtCd(), new ArrayList<>());
			}

			List<Ghd008DTO11> list = rtnMap.get(dto.getFreeTgtCd());
			list.add(dto);
		}

		return rtnMap;
	}

}
