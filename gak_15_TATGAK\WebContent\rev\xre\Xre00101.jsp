<%-- 
	介護等体験登録（検索）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xre/Xre00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function loadAction(event){
　// 画面ロード時の学生・教員名称再取得
  doGakuseiAjax(document.getElementById('form1:htmlGaksekiCd'), event, 'form1:htmlName');
  doKyoinAjax(document.getElementById('form1:htmlNissiKyoinCd'), event, 'form1:htmlKyoinName');
  scrollEvent();
}

function windowOnscroll(){
// スクロールポジション取得
    var scrollTop =
        document.documentElement.scrollTop;
        document.getElementById('form1:htmlScrollPosition').value = scrollTop;
}

function scrollEvent(){
// スクロールポジション指定
	var scrollHidden = document.getElementById('form1:htmlScrollPosition').value;
	window.scrollTo(0,scrollHidden);
}	


function openSubWindow(thisObj, thisEvent) {
	// 学生検索子画面（引数：なし）
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGaksekiCd";
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return true;
}


// 学生名称を取得する
function doGakuseiAjax(thisObj, thisEven){
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;
    args['code2'] = "";
    args['code3'] = "";
    var target = "form1:htmlName";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// 教員検索画面（引数：なし）
function openKyoinSubWindow() {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlNissiKyoinCd";
	openModalWindow(url, "PCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return true;
}

// 教員名称を取得する
function doKyoinAjax(thisObj, thisEvent) {
	var servlet = "rev/co/CobJinjAJAX";
	var args = new Array();
	args['code'] = thisObj.value;
	var target = "form1:htmlKyoinName"
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
  document.getElementById("form1:htmlHidButtonKbn").value = "1";
  var action = document.getElementById("form1:htmlHidAction").value;
  indirectClick(action);
}

// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
}

</SCRIPT>

</HEAD>
<f:view locale="#{SYSTEM_DATA.locale"}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xre00101.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Xre00101">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton 
					type="submit"
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp"
					action="#{pc_Xre00101.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText
					styleClass="outputText" 
					id="htmlFuncId"
					value="#{pc_Xre00101.funcId}">
				</h:outputText> <h:outputText
					styleClass="outputText" 
					id="htmlLoginId"
					value="#{SYSTEM_DATA.loginID}">
				</h:outputText>
				<h:outputText
					styleClass="outputText" 
					id="htmlScrnName"
					value="#{pc_Xre00101.screenName}">
				</h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err">
			<LEGEND>エラーメッセージ</LEGEND>
				<h:outputText
					id="message" 
					value="#{requestScope.DISPLAY_INFO.displayMessage}"
					styleClass="outputText" 
					escape="false">
				</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
				<hx:commandExButton
					type="submit" 
					value="新規登録" 
					styleClass="commandExButton" 
					id="insert"
					action="#{pc_Xre00101.doInsertAction}">
				</hx:commandExButton>
				<!-- ↑ここに戻る／閉じるボタンを配置 -->
				<!-- ← レイアウトの問題の為に、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->


			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE class="table" width="900">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="180"><!-- 体験年度 -->
										<h:outputText
											styleClass="outputText"
											id="lblTiknNendo"
											value="#{pc_Xre00101.propTiknNendo.labelName}">
										</h:outputText>
									</TH>
									<TD width="720" colspan="4">
										<h:inputText
											styleClass="inputText"
											id="htmlTiknNendo"
											style="width:60px;"
											value="#{pc_Xre00101.propTiknNendo.dateValue}"
											size="4">
										<hx:inputHelperAssist
											errorClass="inputText_Error"
											imeMode="inactive" 
											promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText> 
									</TD>
								</TR>	
								<TR>
									<TH nowrap class="v_e" width="180"><!-- 申請時期 -->
										<h:outputText
											styleClass="outputText"
											id="lblSinseiJiki"
											value="#{pc_Xre00101.proplblSinseiJiki.labelName}">
										</h:outputText>
									</TH>
									<TD width="720" colspan="4">
										<h:selectOneMenu
											styleClass="selectOneMenu"
											id="htmlSinseiJiki"
											value="#{pc_Xre00101.propSinseiJikiList.value}"
											style="width:180px;">
												<f:selectItems value="#{pc_Xre00101.propSinseiJikiList.list}" />
										</h:selectOneMenu>
									</TD>								
								</TR>
								<TR>
									<TH nowrap class="v_a" width="180"><!-- 学籍番号 -->
										<h:outputText 
											styleClass="outputText" 
											id="lblGaksekiCd" 
											value="#{pc_Xre00101.propGaksekiCd.labelName}"
											style="#{pc_Xre00101.propGaksekiCd.labelStyle}">
										</h:outputText>
									</TH>		
									<TD width="720" colspan="4">
										<h:inputText
											styleClass="inputText"
											id="htmlGaksekiCd" 
											disabled="#{pc_Xre00101.propGaksekiCd.disabled}"
											value="#{pc_Xre00101.propGaksekiCd.stringValue}"
											readonly="#{pc_Xre00101.propGaksekiCd.readonly}"
											maxlength="#{pc_Xre00101.propGaksekiCd.maxLength}"
											onblur="return doGakuseiAjax(this, event);">
										</h:inputText>
										<hx:commandExButton 
 											type="button" 
											value="検"
											styleClass="commandExButton_search" 
											id="btnGakusekiF"
											disabled="#{pc_Xre00101.propGaksekiCd.disabled}"
											onclick="openSubWindow(this, event);">
										</hx:commandExButton>
										<h:inputText 
											styleClass="likeOutput"
											id="htmlName" 
											readonly="true"
											value="#{pc_Xre00101.propName.stringValue}">
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_b" width="180"><!-- 体験先種別 -->
										<h:outputText
											styleClass="outputText" 
											id="lblTiknskSbt"
											value="#{pc_Xre00101.proplblTiknskSbt.labelName}">
										</h:outputText>
									</TH>
									<TD width="240">
										<h:selectOneMenu
											styleClass="selectOneMenu" 
											id="htmlTiknskSbt"
											value="#{pc_Xre00101.propTiknskSbtList.value}"
											style="width:180px;">
												<f:selectItems value="#{pc_Xre00101.propTiknskSbtList.list}" />
										</h:selectOneMenu>
									</TD>
									<TH nowrap class="v_c" width="160"><!-- 施設種別 --> 
										<h:outputText
											styleClass="outputText"
											id="lblSisetuSbt"
											value="#{pc_Xre00101.propSisetuSbt.labelName}">
										</h:outputText>
									</TH>
									<TD style="border-right-style:none">
										<h:inputText 
											styleClass="inputText"
											id="htmlSisetuSbt"
											value="#{pc_Xre00101.propSisetuSbt.value}"
											maxlength="#{pc_Xre00101.propSisetuSbt.maxLength}"
											style="width:150px;">
										</h:inputText>
									</TD>
									<TD width="234" style="border-left-style:none">
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" 
											id="htmlSisetuSbtFindType"
											value="#{pc_Xre00101.propSisetuSbtFindType.value}">
												<f:selectItem itemValue="0" itemLabel="前方一致" />
												<f:selectItem itemValue="1" itemLabel="部分一致" />
										</h:selectOneRadio>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_d" width="180"><!-- 体験先都道府県 --> 
										<h:outputText
											styleClass="outputText"
											id="lblTiknskKen"
											value="#{pc_Xre00101.proplblTiknskKen.labelName}">
										</h:outputText>
									</TH>
									<TD width="720" colspan="4">
										<h:selectOneMenu
											styleClass="selectOneMenu" 
											id="htmlTiknskKen"
											value="#{pc_Xre00101.propTiknskKenList.value}"
											style="width:180px;">
												<f:selectItems value="#{pc_Xre00101.propTiknskKenList.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_e" width="180"><!-- 体験先名称 --> 
										<h:outputText
											styleClass="outputText" 
											id="lblTiknskName"
											value="#{pc_Xre00101.propTiknskName.labelName}">
										</h:outputText>
									</TH>
									<TD style="border-right-style:none">
										<h:inputText 
											styleClass="inputText"
											id="htmlTiknskName"
											value="#{pc_Xre00101.propTiknskName.value}"
											maxlength="#{pc_Xre00101.propTiknskName.maxLength}"
											size="4"
											style="width:240px;">
										</h:inputText>
									</TD>
									<TD colspan="3"  style="border-left-style:none">
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" 
											id="htmlTiknskNameFindType"
											value="#{pc_Xre00101.propTiknskNameFindType.value}">
												<f:selectItem itemValue="0" itemLabel="前方一致" />
												<f:selectItem itemValue="1" itemLabel="部分一致" />
										</h:selectOneRadio>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_f" width="180"><!-- 体験期間 --> 
										<h:outputText
											styleClass="outputText" 
											id="lblTiknKikan"
											value="#{pc_Xre00101.proplblTiknKikan.labelName}">
										</h:outputText>
									</TH>
									<TD nowrap width="720" colspan="4">
										<h:inputText
											styleClass="inputText" 
											id="htmlTiknKikanFrom"
											value="#{pc_Xre00101.propTiknKikanFrom.dateValue}"
											size="10">
												<f:convertDateTime />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
									    </h:inputText> ～ 
									    <h:inputText 
										    styleClass="inputText"
											id="htmlTiknKikanTo"
											value="#{pc_Xre00101.propTiknKikanTo.dateValue}"
											size="10">
												<f:convertDateTime />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_g" width="180"><!-- 日誌添削教員コード --> 
									<h:outputText
										styleClass="outputText" 
										id="lblNissiKyoinCd"
										value="#{pc_Xre00101.propNissiKyoinCd.labelName}">
										</h:outputText>
										</TH>
									<TD nowrap width="720" colspan="4">
									<h:inputText 
										styleClass="inputText"
										id="htmlNissiKyoinCd" 
										size="20"
										maxlength="#{pc_Xre00101.propNissiKyoinCd.maxLength}"
										value="#{pc_Xre00101.propNissiKyoinCd.value}"
										onblur="return doKyoinAjax(this, event);">
										<hx:inputHelperAssist imeMode="inactive" errorClass="inputText_Error" />
									</h:inputText> 
										<hx:commandExButton 
											type="button" 
											value="検"
											styleClass="commandExButton_search" 
											id="btnRga"
											onclick="openKyoinSubWindow('form1:htmlNissiKyoinCd');">
										</hx:commandExButton>
										<h:inputText 
											styleClass="likeOutput" 
											id="htmlKyoinName"
											readonly="true" 
											value="#{pc_Xre00101.propKyoinName.stringValue}">
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_g" width="180"><!-- 体験状態 -->
										<h:outputText
											styleClass="outputText"
											id="lblTiknJotai"
											value="#{pc_Xre00101.proplblTiknJotai.labelName}">
										</h:outputText>
									</TH>
									<TD nowrap width="720" colspan="4">
										<h:selectManyCheckbox
						                    disabledClass="selectManyCheckbox_Disabled"
						                    styleClass="selectManyCheckbox" 
						                    id="htmlTiknJotaiChkbox"
					    	                value="#{pc_Xre00101.propTiknJotaiChkbox.value}">
					    	                	<f:selectItems value="#{pc_Xre00101.propTiknJotaiChkbox.list}"/>
					                    </h:selectManyCheckbox>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_g" width="180"><!-- 出力対象 -->
										<h:outputText
											styleClass="outputText"
											id="lblOutTaisyo"
											value="#{pc_Xre00101.proplblOutTaisyo.labelName}">
										</h:outputText>
									</TH>
									<TD nowrap width="240">
										<h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" 
											id="htmlOutTaisyoChkbox"
 											value="#{pc_Xre00101.propOutTaisyoChkbox.checked}">
										</h:selectBooleanCheckbox>
										<h:outputText
											styleClass="outputtext"
											id="lblOutTaisyoChkbox"
											value="学費未納者(検索日付で学費チェックを行う)">
										</h:outputText>
									</TD>
									<TH nowrap class="v_g" width="160"><!-- 特定対象 -->
										<h:outputText
											styleClass="outputText"
											id="lblTokuteiTaisyo"
											value="#{pc_Xre00101.proplblTokuteiTaisyo.labelName}">
										</h:outputText>
									</TH>
									<TD nowrap colspan="2">
										<h:selectManyCheckbox
						                    styleClass="selectManyCheckbox" 
						                    id="htmlTokuteiTaisyoChkbox"
					    	                value="#{pc_Xre00101.propTokuteiTaisyoChkbox.value}"
					        	            disabled="#{pc_Xre00101.propTokuteiTaisyoChkbox.disabled}"
					            	        style="#{pc_Xre00101.propTokuteiTaisyoChkbox.style}">
							                    <f:selectItems value="#{pc_Xre00101.propTokuteiTaisyoChkbox.list}" />
					                    </h:selectManyCheckbox>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<br>
						
						<TABLE width="900" border="0" class="table">
							<TBODY>
								<TR>
									<TH width="255" class="headerClass" style="text-align: center">
										<h:outputText
											styleClass="outputText" 
											value="優先順位" 
											id="lblYusenRank_head">
										</h:outputText>
									</TH>
									<TH width="390" class="headerClass" style="text-align: center">
										<h:outputText
											styleClass="outputText" 
											value="項目" 
											id="lblKomoku_head">
										</h:outputText>
									</TH>
									<TH width="255" class="headerClass" style="text-align: center">
										<h:outputText
											styleClass="outputText" 
											value="ソート順" 
											id="lblSort_head">
										</h:outputText>
									</TH>
								</TR>	
								<TR><!-- 優先順位１ -->
									<TD style="text-align:center">
										<h:outputText
											value="１"
											styleClass="outputText">
										</h:outputText>
									</TD>	
									<TD>
										<h:selectOneMenu
											id="htmlComboOrder1"
											style="width:300px; "
											value="#{pc_Xre00101.propComboOrder1.value}">
												<f:selectItems value="#{pc_Xre00101.propComboOrder1.list}" />
										</h:selectOneMenu>
									</TD>
									<TD>
										<h:selectOneRadio
											id="htmlRadioOrderType1"
											styleClass="selectOneRadio"
											value="#{pc_Xre00101.propRadioOrderType1.value}">
												<f:selectItem itemValue="0" itemLabel="昇順" />
												<f:selectItem itemValue="1" itemLabel="降順" />
										</h:selectOneRadio>
									</TD>
								</TR>	
								<TR><!-- 優先順位２ -->
									<TD style="text-align:center">
										<h:outputText
											value="２"
											styleClass="outputText">
										</h:outputText>
									</TD>	
									<TD>
										<h:selectOneMenu
											style="width:300px;"
											styleClass="selectOneMenu"
											value="#{pc_Xre00101.propComboOrder2.value}">
												<f:selectItems value="#{pc_Xre00101.propComboOrder2.list}" />									
										</h:selectOneMenu>
									</TD>
									<TD>
										<h:selectOneRadio
											id="htmlRadioOrderType2"
											styleClass="selectOneRadio"
											value="#{pc_Xre00101.propRadioOrderType2.value}">
												<f:selectItem itemValue="0" itemLabel="昇順" />
												<f:selectItem itemValue="1" itemLabel="降順" />
										</h:selectOneRadio>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						
						
						<TABLE class="button_bar" width="900" align="center" cellspacing="1" cellpadding="1">
							<TR>
								<TD align="center">
									<hx:commandExButton 
										type="submit" 
										value="検索"
										styleClass="commandExButton_dat" 
										id="search"
										action="#{pc_Xre00101.doSearchAction}"
										onclick="return windowOnscroll();">
									</hx:commandExButton> 
									<hx:commandExButton
										type="submit" 
										value="クリア" 
										styleClass="commandExButton_etc"
										id="clear" 
										action="#{pc_Xre00101.doClearAction}"
										onclick="return windowOnscroll();">
									</hx:commandExButton>
								</TD>
							</TR>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900">
							<TR>
								<TD align="center">
									<TABLE width="900">
										<TR>
											<TD align="left" width="50%">
											</TD>
											<TD align="right" width="50%">
												<h:outputText
													styleClass="outputText" 
													id="lblCount"
													value="#{pc_Xre00101.propTiknList.listCount}件">
												</h:outputText>
											</TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
						</TABLE>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="900">
							<TR>
								<TD>
									<div 
										class="listScroll" 
										id="listScroll" 
										style="height: 263px"
										onscroll="setScrollPosition('htmlHidScroll', this);">
											<TABLE width="880" class="meisai_scroll">
												<TR>
													<TH class="headerClass" width="40">
														<h:outputText
															styleClass="outputText" 
															value="体験年度" 
															id="lblSotNendo_head">
														</h:outputText>
													</TH>
													<TH class="headerClass" width="40">
														<h:outputText
															styleClass="outputText" 
															value="申請時期" 
															id="lblSotGakki_head">
														</h:outputText>
													</TH>
													<TH class="headerClass" width="70">
														<h:outputText
															 styleClass="outputText" 
															 value="学籍番号" 
															 id="gakusekiCd">
														</h:outputText>
													</TH>
													<TH class="headerClass" width="85">
														<h:outputText
															styleClass="outputText" 
															value="氏名" 
															id="lblGakseiName_head">
														</h:outputText>
													</TH>
													<TH class="headerClass" width="100">
														<h:outputText
															styleClass="outputText" 
															value="体験先種別" 
															id="lblSzks_head">
														</h:outputText>
													</TH>
													<TH class="headerClass" width="65">
														<h:outputText
															styleClass="outputText" 
															value="体験先都道府県" 
															id="lblIdoSbt_head">
														</h:outputText>
													</TH>
													<TH class="headerClass" width="135">
														<h:outputText
															styleClass="outputText"
															value="体験先名称" 
															id="lblIdoSbt2_head">
														</h:outputText>
													</TH>	
													<TH class="headerClass" width="148">
														<h:outputText
															styleClass="outputText" 
															value="体験期間" 
															id="lblIdoSbt3_head">
														</h:outputText>
													</TH>	
													<TH class="headerClass" width="85">
														<h:outputText
															styleClass="outputText" 
															value="日誌添削教員名" 
															id="lblIdoSbt4_head">
														</h:outputText>
													</TH>										
													<TH class="headerClass" width="65">
														<h:outputText
															styleClass="outputText" 
															value="体験状態" 
															id="lblIdoSbt5_head">
														</h:outputText>
													</TH>											
													<TH class="headerClass" rowspan="2" width="36">
														<h:outputText
															styleClass="outputText" 
															id="lblButton_select_head">
														</h:outputText>
													</TH>
												</TR>
											</TABLE>
												<h:dataTable 
													border="1" 
													cellpadding="2" 
													cellspacing="0"
													columnClasses="columnClass1" 
													headerClass="headerClass"
													footerClass="footerClass"
													rowClasses="#{pc_Xre00101.propTiknList.rowClasses}"
													styleClass="meisai_scroll"
													id="htmlTiknList"
													value="#{pc_Xre00101.propTiknList.list}"
													var="varlist">
														<h:column id="column1"><!-- 体験年度 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblGakunen_list"
																value="#{varlist.tiknNendo}">
															</h:outputText>
																<f:attribute value="40" name="width" />
														</h:column>
														<h:column id="column2"><!-- 申請時期 -->
															<h:outputText 
																styleClass="outputText"
																id="lblSotunendogakki_list"
																value="#{varlist.sinseiJiki}">
															</h:outputText>
																<f:attribute value="40" name="width" />
														</h:column>
														<h:column id="column3"><!-- 学籍番号 -->
															<h:outputText
																styleClass="outputText" 
																id="lblGaksekiCd_list" 
																value="#{varlist.gaksekiCd}">
															</h:outputText>
																<f:attribute value="70" name="width" />
														</h:column>
														<h:column id="column4"><!-- 氏名 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblGakseiName_list"
																title="#{varlist.gakuseiName.value}"
																value="#{varlist.gakuseiName.displayValue}">
															</h:outputText>
																<f:attribute value="85" name="width" />
														</h:column>
														<h:column id="column5"><!-- 体験先種別 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblSzksCd_list"
																title="#{varlist.tiknskSbt.value}"
																value="#{varlist.tiknskSbt.displayValue}">
															</h:outputText>
																<f:attribute value="100" name="width" />
														</h:column>
														<h:column id="column6"><!-- 体験先都道府県 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblSzksName_list"
																value="#{varlist.tiknskKen}">
															</h:outputText>
																<f:attribute value="65" name="width" />
														</h:column>
														<h:column id="column7"><!-- 体験先名称 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblIdoSbtCd_list"
																title="#{varlist.tiknskName.value}"
																value="#{varlist.tiknskName.displayValue}">
															</h:outputText>
																<f:attribute value="139" name="width" />
														</h:column>
														<h:column id="column8"><!-- 体験期間 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblIdoSbtName2_list"
																value="#{varlist.tiknKikan}">
															</h:outputText>
																<f:attribute value="148" name="width" />
														</h:column>
														<h:column id="column9"><!-- 日誌添削教員氏名 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblIdoSbtName3_list"
																title="#{varlist.nissiKyoinName.value}"
																value="#{varlist.nissiKyoinName.displayValue}">
															</h:outputText>
																<f:attribute value="85" name="width" />
														</h:column>
														<h:column id="column10"><!-- 体験状態 -->
															<h:outputText 
																styleClass="outputText" 
																id="lblIdoSbtName4_list"
																title="#{varlist.tiknJotai.value}"
																value="#{varlist.tiknJotai.displayValue}">
															</h:outputText>
																<f:attribute value="65" name="width" />
														</h:column>																		
														<h:column id="column11"><!-- 編集ボタン -->
															<hx:commandExButton 
																type="submit" 
																value="編集"
																styleClass="commandExButton" 
																id="edit"
																action="#{pc_Xre00101.doEditAction}">
															</hx:commandExButton>
																<f:attribute value="36" name="width" />
														</h:column>
												</h:dataTable>
										</div>
									</TD>
								</TR>
								<TR>
									<TD></TD>
								</TR>
							</TABLE>
							<TABLE cellspacing="1" cellpadding="1" class="button_bar" width="810">
								<TR>
								<!-- CSV作成ボタン --><!-- 出力項目指定ボタン --> 
									<TD align="center">
										<hx:commandExButton	
											type="submit" 
											styleClass="commandExButton_out"
											id="btnCsvOut" 
											value="CSV作成"
											action="#{pc_Xre00101.doCsvOutAction}"
											confirm="#{msg.SY_MSG_0020W}"
											disabled="#{pc_Xre00101.propCsvoutBtn.disabled}"
											rendered="#{pc_Xre00101.propCsvoutBtn.rendered}"
											onclick="return windowOnscroll();">
										</hx:commandExButton>
										<hx:commandExButton	
											type="submit" 
											styleClass="commandExButton_out"
											id="setoutput" 
											value="出力項目指定" 
											action="#{pc_Xre00101.doSetoutputAction}"
											disabled="#{pc_Xre00101.propSetOutput.disabled}"
											rendered="#{pc_Xre00101.propSetOutput.rendered}"
											onclick="return windowOnscroll();">
										</hx:commandExButton>
									</TD>
								</TR>
							</TABLE>

						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			</DIV>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --> 

			</DIV>
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
				<h:inputHidden 
					id="htmlHidScroll" 
					value="#{pc_Xre00101.propTiknList.scrollPosition}">
				</h:inputHidden>
				<h:inputHidden 
					id="htmlHidButtonKbn" 
					value="#{pc_Xre00101.propHidButtonKbn.integerValue}">
						<f:convertNumber />
				</h:inputHidden>
				<h:inputHidden 
					id="htmlHidAction"
					value="#{pc_Xre00101.propHidAction.stringValue}">
				</h:inputHidden>
				<h:inputHidden 
					id="htmlScrollPosition" 
					value="#{pc_Xre00101.propScrollPosition.stringValue}">
				</h:inputHidden>
			</h:form>
		</gakuen:itemStateCtrl>
	</hx:scriptCollector>
</BODY>
<jsp:include page="../inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);

	function endload() {
    	changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>

</HTML>

