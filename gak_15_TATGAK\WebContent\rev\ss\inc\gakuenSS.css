@charset "UTF-8";

/* スクロールBox要素内のテーブルヘッダ用 */
.listScrollHead table{
	border-collapse: collapse;
	border-right: 1px solid black;
}
.listScrollHead th{
	line-height:130%;
	vertical-align:bottom;
	background-color:#ddf1f9;
	border-top: 1px solid black;
	border-left: 1px solid black;
	text-indent: 4px;
	padding: 0px;
	text-align: center;
	/* グラデーションフィルター */
	filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#FFa0baa0, endcolorstr=#10d5e0d5, gradienttype=0);
	/* filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#FF7EC0EE, endcolorstr=#908DEEEE, gradienttype=0); */
}

/* スクロールエリア */
.areaScroll{
	position: relative;
	overflow-y: scroll;
}

/* データテーブル内の左寄せ */
#content .column .meisai_page td.columnLeft,
#content .column .meisai_scroll td.columnLeft{
	text-align: left;
}

/* データテーブル内の中央寄せ */
#content .column .meisai_page td.columnCenter,
#content .column .meisai_scroll td.columnCenter{
	text-align: center;
	text-indent: 0;
}

/* データテーブル内の右寄せ */
#content .column .meisai_page td.columnRight,
#content .column .meisai_scroll td.columnRight{
	text-align: right;
	text-indent: 0;
	padding-right: 3px;
}

/* 検索条件表示用のデータテーブル */
#content .column .meisai_condition {
	border-collapse: collapse;
	border-top: 1px solid #CCCCCC;
	border-right: 1px solid #CCCCCC;
	border-bottom: 1px solid #CCCCCC;
	border-left: 1px solid #CCCCCC;
}
#content .column .meisai_condition td {
	height:20px;
	text-align:left;
	font-size: 9pt;
	color: #333;
	background-color: #fff;
	border: 1px solid #CCCCCC;
	padding: 0;
	text-indent:3px;
}
#content .column .meisai_condition td.title {
background-color:#82A3C4;
border-bottom: 1px solid #CCCCCC;
border-right: 1px solid #CCCCCC;
border-top: 1px solid #CCCCCC;
padding-left:2px;
text-align:left;
color: #FFFFFF;
height:20px;
}

/* tableクラス中のデータテーブル */
#content .column .table .meisai_page .oddNumberLine td,
#content .column .table .meisai_scroll .oddNumberLine td{
	background-color: #f4feff;
}
#content .column .table .meisai_page .evenNumberLine td,
#content .column .table .meisai_scroll .evenNumberLine td{
	background-color: #fff;
}

/* list_tableクラスのヘッダをtableクラスのヘッダと同じように */
.list_table th.v_a {
	background-color:white;
	filter: progid:DXImageTransform.Microsoft.gradient(startcolorstr=#FFCCCCCC, endcolorstr=#30CCCCCC, gradienttype=1);
	border-bottom: 1px solid black;
	border-right: 1px solid black;
	border-top: 1px solid black;
	text-indent: 5px;
	text-align:left;
	vertical-align: middle;
}

/* 自由設定画面の前後切り替えボタン */
#content .column .commandExButton_S{
	height: 17px;
	width: 17px;
	border-top: 1px solid #C0C0FF;
	border-left: 1px solid #C0C0FF;
	border-right: 1px solid #004D99;
	border-bottom: 1px solid #004D99;
	cursor: pointer;
	text-align: center;
	color: #1B508C;
	background-color: #F5F5F5;
	margin: 0px 1px 0px 1px;
}

/* button_barクラスの背景無し（差し替えに備えて暫定作成） */
.button_bar_nobg{
	height:23px;
	margin: 0;
	border-collapse: collapse;
}

.button_bar_nobg TR{
	text-align:center;
}

/* リストの選択行 */
.selectedColor {
background-color: #8EEBFF; 
}

