<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb01001.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>

<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<TITLE></TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function openSubWindow() {
	// 物品検索画面（引数：なし）
	
	var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0101.jsp?retFieldName=form1:htmlSubject";
	openModalWindow(url, "pXrc0101", "<%=com.jast.gakuen.rev.xrc.PXrc0101.getWindowOpenOption() %>");

	return true;
}

function doSubjectAjax(thisObj, thisEvent) {
 // 物品名称を取得する

	var servlet = "rev/xrb/XrbBpnAJAX";
	var args = new Array();
	args['code'] = thisObj.value;
	var target = "form1:htmlSubjectName2";

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
 }
 
function loadAction(event){
 
	doSubjectAjax(document.getElementById('form1:htmlSubject'), event);
 
	// changeScrollPosition('scroll', 'listScroll');

}

function confirmCancel(){
	try{
		document.getElementById("form1:htmlConfilm").value = "0";
	} catch (e) {
	}
}
function confirmOk(){
	try{
		document.getElementById("form1:htmlConfilm").value = "1";
		indirectClick(document.getElementById("form1:htmlAction").value);
	} catch (e) {
	}
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrb01001.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<!-- 閉じるボタン --> 
				<hx:commandExButton
					type="submit" 
					value="閉じる" 
					styleClass="commandExButton"
					id="closeDisp" 
					action="#{pc_Xrb01001.doCloseDispAction}">
				</hx:commandExButton> 
				<h:outputText 
					styleClass="outputText"
					id="htmlFuncId" 
					value="#{pc_Xrb01001.funcId}">
				</h:outputText>
				<h:outputText 
					styleClass="outputText"
					id="htmlLoginId" 
					value="#{SYSTEM_DATA.loginID}">
				</h:outputText> 
				<h:outputText 
					styleClass="outputText"
					id="htmlScrnName" 
					value="#{pc_Xrb01001.screenName}">
				</h:outputText>
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->

			<DIV class="head_button_area">
			
			<!-- ↓ここに戻るボタンを配置 --> 
			<hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Xrb01001.doReturnDispAction}">
			</hx:commandExButton> 
			<!-- ↑ここに戻るボタンを配置 --></DIV>

			<!-- ↓ここにコンポーネントを配置 -->
			<DIV id="content">

			<DIV class="column" align="center">
			<TABLE border="0" class="table" width="650" cellspacing="0"
				cellpadding="0">
				<TBODY>
					<TR>

						<TH nowrap class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblEnrollmentYear"
							value="#{pc_Xrb01001.propEnrollmentYear.labelName}"
							style="#{pc_Xrb01001.propEnrollmentYear.labelStyle}">
						</h:outputText></TH>
						<TD nowrap width="175"><h:outputText styleClass="outputText"
							id="htmlEnrollmentYear"
							value="#{pc_Xrb01001.propEnrollmentYear.stringValue}"
							style="#{pc_Xrb01001.propEnrollmentYear.style}">
						</h:outputText></TD>


						<TH nowrap class="v_b" width="150"><h:outputText
							styleClass="outputText" id="lblEnrollmentTerm"
							value="#{pc_Xrb01001.propEnrollmentTerm.labelName}"
							style="#{pc_Xrb01001.propEnrollmentTerm.labelStyle}">
						</h:outputText></TH>
						<TD width="175" nowrap><h:outputText styleClass="outputText"
							id="htmlEnrollmentTerm"
							value="#{pc_Xrb01001.propEnrollmentTerm.stringValue}"
							style="#{pc_Xrb01001.propEnrollmentTerm.style}">
						</h:outputText></TD>
					</TR>

					<TR>
						<TH nowrap class="v_c" width="150"><h:outputText
							styleClass="outputText" id="lblCurriculumOrganize"
							value="#{pc_Xrb01001.propCurriculumOrganize.labelName}"
							style="#{pc_Xrb01001.propCurriculumOrganize.labelStyle}">
						</h:outputText></TH>
						<TD colspan=3 width="500" nowrap><h:outputText
							styleClass="outputText" id="htmlCurriculumOrganize"
							title="#{pc_Xrb01001.propCurriculumOrganize.value}"
							value="#{pc_Xrb01001.propCurriculumOrganize.displayValue}"
							style="#{pc_Xrb01001.propCurriculumOrganize.style}">
						</h:outputText></TD>
					</TR>
					<TR>
						<TH nowrap class="v_d" width="150"><h:outputText
							styleClass="outputText" id="lblScoreSortBySubject"
							value="#{pc_Xrb01001.propScoreSortBySubject.labelName}"
							style="#{pc_Xrb01001.propScoreSortBySubject.labelStyle}">
						</h:outputText></TH>
						<TD colspan=3 width="500" nowrap><h:outputText
							styleClass="outputText" id="htmlScoreSortBySubject"
							value="#{pc_Xrb01001.propScoreSortBySubject.stringValue}"
							style="#{pc_Xrb01001.propScoreSortBySubject.style}">
						</h:outputText></TD>
					</TR>
					<TR>
						<TH nowrap class="v_d" width="150"><h:outputText
							styleClass="outputText" id="lblKamokCodeName"
							value="#{pc_Xrb01001.propKamokCodeName.labelName}"
							style="#{pc_Xrb01001.propKamokCodeName.labelStyle}">
						</h:outputText></TH>
						<TD colspan=3 width="500" nowrap><h:outputText styleClass="outputText"
							id="htmlKamokCodeName"
							value="#{pc_Xrb01001.propKamokCodeName.stringValue}"
							style="#{pc_Xrb01001.propKamokCodeName.style}">
						</h:outputText></TD>
					</TR>
					<TR>
						<TH nowrap class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblKamokMei"
							value="#{pc_Xrb01001.propKamokMei.labelName}"
							style="#{pc_Xrb01001.propKamokMei.labelStyle}">
						</h:outputText></TH>
						<TD colspan=3 width="500" nowrap><h:outputText styleClass="outputText"
							id="htmlKamokMei" value="#{pc_Xrb01001.propKamokMei.stringValue}"
							style="#{pc_Xrb01001.propKamokMei.style}">
						</h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE border="0" width="650">
				<TBODY>
					<TR>
						<TD nowrap align="left"><h:outputText styleClass="outputText" id="listSubject"
							value="科目配本一覧"></h:outputText></TD>
						<TD nowrap align="right"><h:outputText styleClass="outputText"
							id="listCount" value="#{pc_Xrb01001.propSubjectList.listCount}">
						</h:outputText> <h:outputText styleClass="outputText"
							id="listCountOrdinal" value="件">
						</h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE width="650" cellspacing="0" cellpadding="0" border="0">
				<TR>
					<TD>

					<DIV onscroll="setScrollPosition('scroll', this)"
						style="height:275px;" id="listScroll" class="listScroll">
						
						<h:dataTable border="0" cellpadding="2"
							cellspacing="0" columnClasses="columnClass1"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrb01001.propSubjectList.rowClasses}"
							styleClass="meisai_scroll" id="subjectList"
							value="#{pc_Xrb01001.propSubjectList.list}" var="varlist">

						<h:column id="colBpnCd">
							<h:outputText styleClass="outputText" id="htmlBpnCd"
								value="#{varlist.buppinCd}">
							</h:outputText>
							<f:facet name="header">
								<h:outputText id="headBpnCd" styleClass="outputText"
									value="物品コード">
								</h:outputText>
							</f:facet>
							<f:attribute name="width" value="150"  />
							<f:attribute name="style" value="text-align: left"  />
						</h:column>

						<h:column id="colBpnName">
							<h:outputText styleClass="outputText" id="htmlBpnName"
								value="#{varlist.buppinNameShryk}" title="#{varlist.buppinName}">
							</h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="物品名称"
									id="headBpnName">
								</h:outputText>
							</f:facet>
							<f:attribute name="width" value="250"  />
							<f:attribute name="style" value="text-align: left"  />
						</h:column>
						
						<h:column id="colBpnKbn">
							<h:outputText styleClass="outputText" id="htmlBpnKbn"
								value="#{varlist.buppinKbn}">
							</h:outputText>
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="物品区分"
									id="headBpnKbn">
								</h:outputText>
							</f:facet>
							<f:attribute name="width" value="250"  />
							<f:attribute name="style" value="text-align: left"  />
						</h:column>
						<h:column id="colSelectScore">
							<hx:commandExButton type="submit" styleClass="commandExButton"
								id="selectScore" value="選択"
								action="#{pc_Xrb01001.doSelectScoreAction}">
							</hx:commandExButton>
							
							<f:facet name="header">

							</f:facet>
							<f:attribute value="30" name="width" />
							
						</h:column>


					</h:dataTable></DIV>
					</TD>
				</TR>
			</TABLE>
			
			<BR>
			
			<TABLE border="0" width="650" class="table">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblSubject"
							value="#{pc_Xrb01001.propSubject.labelName}"
							style="#{pc_Xrb01001.propSubject.labelStyle}"></h:outputText></TH>
						<TD nowrap width="500">
							<h:inputText
								styleClass="inputText" 
								id="htmlSubject"
								value="#{pc_Xrb01001.propSubject.stringValue}"
								onblur="return doSubjectAjax(this, event);"
								style="#{pc_Xrb01001.propSubject.style}" size="17"
								maxlength="#{pc_Xrb01001.propSubject.maxLength}">
							</h:inputText>
							<hx:commandExButton
									type="button" 
									styleClass="commandExButton_search"
									id="button2"
									onclick="return openSubWindow(this, event);">
							</hx:commandExButton>
							<h:outputText 
								styleClass="outputText"
								id="htmlSubjectName2">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center" >
							<hx:commandExButton
								type="submit" 
								value="確定" 
								styleClass="commandExButton_dat"
								id="submit" 
								action="#{pc_Xrb01001.doSubmitAction}"
								confirm="#{msg.SY_MSG_0002W}">
							</hx:commandExButton>
							<hx:commandExButton
								type="submit" 
								value="削除" 
								styleClass="commandExButton_dat"
								id="unselect" 
								action="#{pc_Xrb01001.doUnselectAction}"
								confirm="#{msg.SY_MSG_0004W}">
							</hx:commandExButton>
							<hx:commandExButton
								type="submit" 
								value="クリア" 
								styleClass="commandExButton_etc"
								id="clear" 
								action="#{pc_Xrb01001.doClearAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
			</DIV>
			<!-- ↑ここにコンポーネントを配置 --> <!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrb01001.propSubjectList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrb01001.propConfilm.stringValue}"
				id="htmlConfilm">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrb01001.propAction.stringValue}"
				id="htmlAction">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>