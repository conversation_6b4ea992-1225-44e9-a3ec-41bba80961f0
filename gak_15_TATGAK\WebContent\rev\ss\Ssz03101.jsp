<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz03101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz03101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz03101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz03101.doCloseDispAction}"
></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz03101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz03101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><BR>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="10%"></TD>
						<TD width="80%" align="right"><h:outputText styleClass="outputText" id="text2" value="#{pc_Ssz03101.propCount.stringValue}" style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="text5" value="件" style="font-size: 8pt"></h:outputText></TD>
						<TD width="10%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="10%"></TD>
						<TD align="center" width="80%"><div class="listScroll" style="height:296px;"
						id="listScroll" onscroll="setScrollPosition('scroll',this);">
						<h:dataTable border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz03101.propHisykList.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz03101.propHisykList.list}" var="varlist"
							 width="729">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text1" styleClass="outputText" value="区分"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text3"
									value="#{varlist.hisykKbn}"
									style="text-align: left; vertical-align: baseline"></h:outputText>
								<f:attribute value="56" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text7"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text4"
									value="#{varlist.hisykName.displayValue}"
									title="#{varlist.hisykName.value}"></h:outputText>
								<f:attribute value="324" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="非就職区分分類" id="text8"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text9"
									value="#{varlist.hsykKbnBunruiName.displayValue}"
									title="#{varlist.hsykKbnBunruiName.value}"></h:outputText>
								<f:attribute value="326" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz03101.doSelectAction1}"></hx:commandExButton>
								<f:attribute value="24" name="width" />
							</h:column>
						</h:dataTable></div></TD>
						<TD width="10%"></TD>
					</TR>
				</TBODY>
			</TABLE>			
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="10%"></TD>
						<TD width="80%">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
							class="table">
							<TBODY>
								<TR>
									<TH width="201" class="v_a"><h:outputText
										styleClass="outputText" id="lblHisykKbn"
										value="#{pc_Ssz03101.propHisykKbn.labelName}"
										style="#{pc_Ssz03101.propHisykKbn.labelStyle}"></h:outputText></TH>
									<TD width="222"><h:inputText styleClass="inputText"
										id="htmlHisykKbn"
										value="#{pc_Ssz03101.propHisykKbn.stringValue}"
										maxlength="#{pc_Ssz03101.propHisykKbn.maxLength}"
										style="#{pc_Ssz03101.propHisykKbn.style}" size="3">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="201" class="v_b"><h:outputText
										styleClass="outputText" id="lblHisykName"
										value="#{pc_Ssz03101.propHisykName.labelName}"
										style="#{pc_Ssz03101.propHisykName.labelStyle}"></h:outputText></TH>
									<TD width="222"><h:inputText styleClass="inputText"
										id="htmlHisykName"
										value="#{pc_Ssz03101.propHisykName.stringValue}"
										style="#{pc_Ssz03101.propHisykName.style}"
										maxlength="#{pc_Ssz03101.propHisykName.maxLength}" size="50">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH width="201" class="v_c"><h:outputText
										styleClass="outputText" id="lblHsykKbnBunrui"
										value="#{pc_Ssz03101.propHsykKbnBunrui.labelName}"
										style="#{pc_Ssz03101.propHsykKbnBunrui.labelStyle}"></h:outputText></TH>
									<TD width="222"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlHsykKbnBunrui"
										value="#{pc_Ssz03101.propHsykKbnBunrui.value}"
										style="#{pc_Ssz03101.propHsykKbnBunrui.style}">
										<f:selectItems value="#{pc_Ssz03101.propHsykKbnBunrui.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="10%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0"
							class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD align="center" height="16" width="100%"><hx:commandExButton
										type="submit" value="確定" styleClass="commandExButton_dat"
										id="register" action="#{pc_Ssz03101.doRegisterAction}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Ssz03101.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Ssz03101.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz03101.propHisykList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

