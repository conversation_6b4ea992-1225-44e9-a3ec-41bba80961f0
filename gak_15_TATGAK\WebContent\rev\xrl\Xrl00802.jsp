<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00802.java" --%><%-- /jsf:pagecode --%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@ taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK href="../../theme/stylesheet.css" rel="stylesheet" type="text/css">
<TITLE>Xrl00201.jsp</TITLE>
<script language="JavaScript">
    //ボタンの活性化制御
    function fncButtonActive(){
		//フォーカスの設定
		setFocus();

		//スクロール位置保持
		changeScrollPosition('scroll', 'listScroll');
	}
	
	//フォーマット数字
	function fmoney(s)   
	{    
	   s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(0) + "";   
	   var l = s.split(".")[0].split("").reverse(),    
	   t = "";   
	   for(i = 0; i < l.length; i ++ )   
	   {   
		  t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");   
	   }   
	   return t.split("").reverse().join("");   
	} 
	
	//金額の値をセット
	function setKingaku() {
		var tanka = document.getElementById('form1:htmlTeateTanka').value;
		var tankaValue = tanka.replace(/\,/g, "");
		var suryo = document.getElementById('form1:htmlTeateSuryo').value;
		var suryoValue = suryo.replace(/\,/g, "");
		document.getElementById('form1:htmlTeateKingaku').innerHTML = fmoney(tankaValue*suryoValue);
	}
	
	//フォーカスの設定
	function setFocus(){
		
		var id = null;

		//フォーカス設定ボタン取得
		id = document.getElementById('form1:htmlScrollPos').value;

		//選択ボタン押下時
		if (document.getElementById('form1:htmlMenjList:0:select') != null) {
			if ((id != null) && (id != "")) {
				document.getElementById(id).focus();
			} else {
				document.getElementById("form1:htmlMenjList:0:select").focus();				
			}
		}
	}
	</script>


</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncButtonActive();">

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00802.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00802.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrl00802.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00802.screenName}"></h:outputText></div>


			<!--↓OUTER↓-->
			<DIV class="outer">
			<table border="0" width="98%">
				<tr>
					<td align="left">
						<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
							id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
							styleClass="outputText" escape="false">
						</h:outputText></FIELDSET>
					</td>
					<td align="right"><%-- 戻るボタン --%><hx:commandExButton type="submit"
								value="戻る" styleClass="commandExButton" id="returnDisp"
								style="width:80px;"
								action="#{pc_Xrl00802.doReturnDispAction}">
							</hx:commandExButton>
					</td>
					</tr>
			</table>
			<DIV class="head_button_area">　</DIV>

			<!--↓CONTENT↓-->
			<DIV id="content">
			<DIV class="column">
			<TABLE border="0" cellpadding="0" cellspacing="0" width="850px">
				<TBODY>
					<TR>
						<TD>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="680px" class="table" align="left">
								<TBODY>
									<TR>
									    <TH width="70px" nowrap class="v_a">
									   		<h:outputText
												styleClass="outputText" id="lblTeateNendo"
												value="#{pc_Xrl00802.propTeateNendo.labelName}"
												style="#{pc_Xrl00802.propTeateNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD nowrap>
											<h:outputText styleClass="outputText"
												id="htmlTeateNendo"
												value="#{pc_Xrl00802.propTeateNendo.stringValue}"
												style="#{pc_Xrl00802.propTeateNendo.style}"
												>
					                  		</h:outputText>
										</TD>
										<TH width="70px" nowrap class="v_a">
									   		<h:outputText
												styleClass="outputText" 
												id="lblJinjiCd"
												style="#{pc_Xrl00802.propJinjiCd.labelStyle}"
												value="#{pc_Xrl00802.propJinjiCd.labelName}">
											</h:outputText>
										</TH>
										<TD nowrap>
											<h:outputText styleClass="outputText"
												id="htmlJinjiCd"
												value="#{pc_Xrl00802.propJinjiCd.stringValue}"
												style="#{pc_Xrl00802.propJinjiCd.style}"
												>
					                  		</h:outputText>
										</TD>
										<TH width="70px" nowrap class="v_a">
									   		<h:outputText
												styleClass="outputText" 
												id="lblJinjiNm"
												style="#{pc_Xrl00802.propJinjiNm.labelStyle}"
												value="#{pc_Xrl00802.propJinjiNm.labelName}">
											</h:outputText>
										</TH>
										<TD nowrap>
											<h:outputText styleClass="outputText"
												id="htmlJinjiNm"
												value="#{pc_Xrl00802.propJinjiNm.stringValue}"
												style="#{pc_Xrl00802.propJinjiNm.style}"
												>
					                  		</h:outputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText styleClass="outputText"
										id="text5" value="#{pc_Xrl00802.propTeateList.listCount}">
									</h:outputText> <h:outputText styleClass="outputText"
										id="text6" value="件">
									</h:outputText></TD>
								</TR>
								<TR>
									<TD>
									<DIV style="height: 200px; width=100%;" id="listScroll"
										onscroll="setScrollPosition('scroll',this);"
										class="listScroll"><h:dataTable styleClass="meisai_scroll"
										id="htmlTeateList"
										value="#{pc_Xrl00802.propTeateList.list}" var="varlist"
										footerClass="footerClass"
										rows="#{pc_Xrl00802.propTeateList.rows}" width="850px"
										rowClasses="#{pc_Xrl00802.propTeateList.rowClasses}"
										headerClass="headerClass">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="lblTeateNo"
													value="#{pc_Xrl00802.propTeateNo.name}"
													styleClass="outputText"></h:outputText>
											</f:facet>
											<h:outputText id="lblTeateNoValue" value="#{varlist.teateNo}"
												styleClass="outputText"></h:outputText>
											<f:attribute value="90px" name="width" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText id="lblTeateGyomuCd"
													value="#{pc_Xrl00802.propTeateGyomuCd.name}"
													styleClass="outputText"></h:outputText>
											</f:facet>
											<h:outputText id="lblTeateGyomuCdValue" value="#{varlist.teateGyomuCd}"
												styleClass="outputText"></h:outputText>
											<f:attribute value="140px" name="width" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText value="#{pc_Xrl00802.propTeateGyomu.name}"
													styleClass="outputText" id="lblTeateGyomuNm"></h:outputText>
											</f:facet>
											<h:outputText id="lblTeateGyomuNmValue"
												value='#{varlist.teateGyomuNm}' styleClass="outputText"></h:outputText>
											<f:attribute value="210px" name="width" />
										</h:column>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText id="lblTeateAppendDate"
													value="#{pc_Xrl00802.propTeateAppendDate.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<h:outputText id="lblTeateAppendDateValue"
												value="#{varlist.teateAppendDate}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="90px" name="width" />
										</h:column>
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText id="lblTeateSakuseiDate"
													value="#{pc_Xrl00802.propTeateSakuseiDate.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<h:outputText id="lblTeateSakuseiDateValue"
												value="#{varlist.teateSakuseiDate}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="100px" name="width" />
										</h:column>
										<h:column id="column6">
											<f:facet name="header">
												<h:outputText id="lblTeateSiharaiDate"
													value="#{pc_Xrl00802.propTeateSiharaiDate.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<h:outputText id="lblTeateSiharaiDateValue"
												value="#{varlist.teateSiharaiDate}" styleClass="outputText">
											</h:outputText>
											<f:attribute value="90px" name="width" />
										</h:column>
										<h:column id="column7">
											<f:facet name="header">
												<h:outputText id="lblTeateTanka"
													value="#{pc_Xrl00802.propTeateTanka.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<f:attribute value="90px" name="width" />
											<h:outputText id="lblTeateTankaValue"
												value="#{varlist.teateTanka}" styleClass="outputText">
												<f:convertNumber pattern="###,###,##0" />
											</h:outputText>
											<f:attribute value="text-align:right;width:90px" name="style" />
										</h:column>
										<h:column id="column8">
											<f:facet name="header">
												<h:outputText id="lblTeateSuryo"
													value="#{pc_Xrl00802.propTeateSuryo.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<f:attribute value="70px" name="width" />
											<h:outputText id="lblTeateSuryoVlaue"
												value="#{varlist.teateSuryo}" styleClass="outputText">
												<f:convertNumber pattern="##,##0.0" />
											</h:outputText>
											<f:attribute value="text-align:right;" name="style" />
										</h:column>
										<h:column id="column9">
											<f:facet name="header">
												<h:outputText id="lblTeateKingaku"
													value="#{pc_Xrl00802.propTeatekingaku.name}"
													styleClass="outputText">
												</h:outputText>
											</f:facet>
											<f:attribute value="150px" name="width" />
											<h:outputText id="lblTeateKingakuVlaue"
												value="#{varlist.teateKingaku}" styleClass="outputText">
												<f:convertNumber pattern="###,###,###,###,##0" />
											</h:outputText>
											<f:attribute value="text-align:right;" name="style" />
										</h:column>
										<h:column id="column10">
											<f:facet name="header">
											</f:facet>
											<f:attribute value="40px" name="width" />
											<hx:commandExButton type="submit" value="選択"
												styleClass="cmdBtn_dat_s" id="select"
												action="#{pc_Xrl00802.doSelectAction}">
											</hx:commandExButton>
										</h:column>
									</h:dataTable></DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="30">
							<TABLE width="100%" border="0" class="button_bar" cellpadding="0"
							cellspacing="0">
								<TBODY>
									<TR>
										<TD>
											<hx:commandExButton
												type="submit" 
												value="新規登録" 
												styleClass="commandExButton_dat" 
												id="insert"
												action="#{pc_Xrl00802.doInsertAction}">
											</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="100%">
							<TBODY>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateNoMeisai"
										value="#{pc_Xrl00802.propTeateNo.labelName}"
										style="#{pc_Xrl00802.propTeateNo.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:outputText
										styleClass="outputText" id="texTeateNoMeisai"
										value="#{pc_Xrl00802.propTeateNo.stringValue}">
									</h:outputText></TD>
								</TR>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTantoGyomuMeisai"
										value="#{pc_Xrl00802.propTeateGyomu.name}"
										style="#{pc_Xrl00802.propTeateGyomu.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:selectOneMenu
										styleClass="selectOneMenu" id="cmbTantoGyomu"
										disabled="#{pc_Xrl00802.propTeateGyomu.disabled}"
										value="#{pc_Xrl00802.propTeateGyomu.stringValue}">
										<f:selectItems value="#{pc_Xrl00802.propTeateGyomu.list}" />
									</h:selectOneMenu>
									<hx:commandExButton type="submit"
										style="margin-left:5px;"
										value="選択" styleClass="commandExButton" id="selectTeateGyomu"
										action="#{pc_Xrl00802.doSelectTeateGyomuAction}"
										disabled="#{pc_Xrl00802.propSelectTeateGyomu.disabled}">
									</hx:commandExButton>
									<hx:commandExButton type="submit"
										value="解除" styleClass="commandExButton" id="releaseTeateGyomu"
										action="#{pc_Xrl00802.doReleaseTeateGyomuAction}"
										disabled="#{pc_Xrl00802.propReleaseTeateGyomu.disabled}">
									</hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateAppendDateMeisai"
										value="#{pc_Xrl00802.propTeateAppendDate.labelName}"
										style="#{pc_Xrl00802.propTeateAppendDate.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:outputText
										styleClass="outputText" id="texTeateAppendDateMeisai"
										value="#{pc_Xrl00802.propTeateAppendDate.stringValue}">
									</h:outputText></TD>
								</TR>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateSakuseiDateMeisai"
										value="#{pc_Xrl00802.propTeateSakuseiDate.labelName}"
										style="#{pc_Xrl00802.propTeateSakuseiDate.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="*"><h:inputText styleClass="inputText"
											id="htmlTeateSakusei"
											value="#{pc_Xrl00802.propTeateSakuseiDate.dateValue}"
											style="width:108px"
											disabled="#{pc_Xrl00802.propTeateSakuseiDate.disabled}">
											<hx:inputHelperDatePicker />
											<f:convertDateTime />
											<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateSiharaiDateMeisai"
										value="#{pc_Xrl00802.propTeateSiharaiDate.labelName}"
										style="#{pc_Xrl00802.propTeateSiharaiDate.labelStyle}"></h:outputText></TH>
									<TD nowrap width="*"><h:outputText
										styleClass="outputText" id="texTeateSiharaiDateMeisai"
										value="#{pc_Xrl00802.propTeateSiharaiDate.stringValue}">
									</h:outputText></TD>
								</TR>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateTankaMeisai"
										value="#{pc_Xrl00802.propTeateTanka.labelName}"
										style="#{pc_Xrl00802.propTeateTanka.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="*"><h:inputText styleClass="inputText"
										id="htmlTeateTanka"
										value="#{pc_Xrl00802.propTeateTanka.integerValue}"
										style="#{pc_Xrl00802.propTeateTanka.style}" size="11"
										onblur="setKingaku()"
										style="text-align:right"
										maxlength="#{pc_Xrl00802.propTeateTanka.maxLength}"
										disabled="#{pc_Xrl00802.propTeateTanka.disabled}">
										<f:convertNumber type="number" pattern="###,###,##0" />
														<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
									</h:inputText>
									<h:outputText styleClass="outputText"
										id="text7" value="円"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateSuryoMeisai"
										value="#{pc_Xrl00802.propTeateSuryo.labelName}"
										style="#{pc_Xrl00802.propTeateSuryo.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="*"><h:inputText styleClass="inputText"
										id="htmlTeateSuryo"
										value="#{pc_Xrl00802.propTeateSuryo.doubleValue}"
										style="#{pc_Xrl00802.propTeateSuryo.style}" size="11"
										style="text-align:right"
										maxlength="#{pc_Xrl00802.propTeateSuryo.maxLength}"
										onblur="setKingaku()"
										disabled="#{pc_Xrl00802.propTeateSuryo.disabled}">
										<f:convertNumber type="number" pattern="##,##0.0" />
														<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
									</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH width="200px" nowrap class="v_a"><h:outputText
										styleClass="outputText" id="lblTeateKingakuMeisai"
										value="#{pc_Xrl00802.propTeatekingaku.labelName}"
										style="#{pc_Xrl00802.propTeatekingaku.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="*">
										<div style="float: left;text-align: right;min-width: 95px;">
										<h:outputText styleClass="outputText"
											id="htmlTeateKingaku"
											value="#{pc_Xrl00802.propTeatekingaku.doubleValue}">
											<f:convertNumber pattern="###,###,###,###,##0" />
										</h:outputText>
										</div>
										<div style="float: left;margin-left: 3px;">
										<h:outputText styleClass="outputText"
											id="text8" value="円"></h:outputText>
										</div>
									</TD>
								</TR>
								<TR>
									<!-- 備考 -->
									<TH nowrap class="v_a" width="200px">
										<h:outputText styleClass="outputText" id="lblTeateBikoMeisai"
										value="#{pc_Xrl00802.propTeateBiko.labelName}"
										style="#{pc_Xrl00802.propTeateBiko.labelStyle}"></h:outputText><BR>
										<hx:graphicImageEx styleClass="graphicImageEx" id="imageEx2"
										value="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
									<TD><h:inputTextarea styleClass="inputTextarea"
										id="htmlTeateBiko" cols="70" rows="3"
										disabled="#{pc_Xrl00802.propTeateBiko.disabled}"
										value="#{pc_Xrl00802.propTeateBiko.stringValue}"
										style="#{pc_Xrl00802.propTeateBiko.style}; height: 56px;"></h:inputTextarea>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="100%">
						<TABLE width="100%" border="0" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="regist"
										disabled="#{pc_Xrl00802.propRegist.disabled}"
										confirm="#{msg.SY_MSG_0001W}"
										action="#{pc_Xrl00802.doRegistAction}">
									</hx:commandExButton>&nbsp; <hx:commandExButton type="submit"
										value="クリア" styleClass="commandExButton_etc" id="clear"
										action="#{pc_Xrl00802.doClearAction}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrl00802.propTeateList.scrollPosition}" id="scroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrl00802.propScrollPos.stringValue}"
				id="htmlScrollPos"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>

	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
