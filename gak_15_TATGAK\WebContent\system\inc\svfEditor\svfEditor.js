// global SVF Editor object
var SVFED = new Object();

// browser information
SVFED.browser = queryBrowserInfo();

// SVF editor config
SVFED.config = {
	charset: "UTF-8",
	basepath: "system/inc/svfEditor/",
	stylesheet: "svfEditor.css",
	imagepath: "image",
	framejspath: "svfEditorFrame.js"
};

// SVF editor object
SVFED.editor = new Object();

// common function for cross-browser

var flgDisabled;
flgDisabled = false;
SVFED.createEditor_disabled = function(textAreaId,keywordId,oid,contextBase,size,direction,width,height,disabled)
{
	flgDisabled = disabled;
	SVFED.createEditor(textAreaId,keywordId,oid,contextBase,size,direction,width,height);
}


SVFED.createEditor = function(textAreaId,keywordId,oid,contextBase,size,direction,width,height)
{

	var txtDisabled = "";
	if(flgDisabled == "true"){
		txtDisabled = 'disabled="true"';
	}
	
	if (!SVFED.browser.isIE){
		alert("この機能は、Internet Explorer 6.0以上のみお使いいただけます。");
		return null;
	}
	if (!SVFED.browser.editable){
		alert("この機能は、Internet Explorer 6.0以上のみお使いいただけます。");
		return null;
	}

	var contextPath = contextBase;
	if (contextBase.charAt(contextBase.length - 1) != '/'){
		contextPath += '/';
	}

	// check
	if (!SVFED.browser.editable) throw "this browser isn't supported";
	if (SVFED.editor[textAreaId]) throw "svfeditor:"+textAreaId+" already exists";

	// find a specified textarea
	var ta = document.getElementById(textAreaId);
	if (!ta || ta.tagName != "TEXTAREA") throw "textarea:"+textAreaId+" isn't found";
	ta.style.display = 'none';

	// create htmlarea
	var bgcolor = "#dddddd";
	var imgbtn = function (src, cmd, title) {
		src = contextPath + SVFED.config.basepath + SVFED.config.imagepath +"/"+src;
		cmd = "SVFED.editor."+oid+".exec('"+cmd+"')";
		return '<img class="imgbtn" src="'+src+'" onclick="'+cmd+'" title="'+title+'" ' + txtDisabled + '>';
	}
	var text = '<CENTER>';
	text += '<table border="0" cellpadding="0" cellspacing="0" width="'+width+'"><tr><td align="left">';
	text += '<table bgcolor="'+bgcolor+'" border="0" cellpadding="0" cellspacing="0"><tr>';

	var kewordsXML = document.getElementById(keywordId);
	var XMLroot = kewordsXML.selectSingleNode("/");
	var groupList =	XMLroot.getElementsByTagName("GROUP");
	var keywordList;

	for (var i = 0; i < groupList.length; i++){
		text += '<td><select onchange="SVFED.editor.'+oid+'.insertKeyWord(this.value.split(\'!\')[0],this.value.split(\'!\')[1],this.value.split(\'!\')[2],this.value.split(\'!\')[3],this.options(this.selectedIndex).text,this.value.split(\'!\')[4],this.value.split(\'!\')[5]);this.selectedIndex = 0;" ' + txtDisabled + '>';
		text += '<option>--予約語（'+ groupList(i).getAttribute("DESC") +'）--</option>';
		keywordList = groupList(i).getElementsByTagName("KEYWORD");
		for (var j = 0; j < keywordList.length; j++){
			var sFormat = "";
			if (keywordList(j).getAttribute("FORMAT")){
				sFormat = keywordList(j).getAttribute("FORMAT");
			}
			
			text += '<option value="'+keywordList(j).getAttribute("ID")+'!'+keywordList(j).getAttribute("TYPE")+'!'+keywordList(j).getAttribute("LENGTH")+'!'+keywordList(j).getAttribute("SLENGTH")+'!'+keywordList(j).getAttribute("PREVIEW")+'!'+sFormat+'">'+keywordList(j).getAttribute("DESC")+'</option>';
		}
		text += '</select></td>';
	}
	text += '</tr></table>';

	text += '<table id="'+oid+'_toolbar" bgcolor="'+bgcolor+'" border="0" cellpadding="0" cellspacing="0"><tr>'+
		'<td><select id="'+oid+'_paper_size" onchange="SVFED.editor.'+oid+'.chagePaperSize(this.value);">'+
		'<option value="A3">A3</option>'+
		'<option value="A4" selected>A4</option>'+
		'<option value="A5">A5</option>'+
		'<option value="B4">B4</option>'+
		'<option value="B5">B5</option>'+
		'<option value="LETTER">レター</option>'+
		'<option value="POSTCARD">はがき</option>'+
		'</select></td>';
		
	text += '<td><select id="'+oid+'_page_direction" onchange="SVFED.editor.'+oid+'.chagePaperdirection(this.value)">'+
		'<option value="portrait">縦</option>'+
		'<option value="landscape">横</option>'+
		'</select></td>';
	text += '<td width="1" bgcolor="gray"></td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("textNoWrap.gif","insertTextNW","テキストを追加する（Enterで折返し）")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("textWrap.gif","insertText","テキストを追加する（行末で折返し）")+'</td>'+
		'<td width="1" bgcolor="gray"></td>';
	text += '<td>'+imgbtn("inserthorizontalrule.gif","inserthorizontalrule","水平線を追加する")+'</td>'+
		'<td width="1" bgcolor="gray"></td>';
		
	text += '<td>'+imgbtn("insertverticalrule.gif","insertverticalrule","垂直線を追加する")+'</td>'+
		'<td width="1" bgcolor="gray"></td>';
	text += '<td>'+imgbtn("insertrectangle.gif","insertrectangle","矩形を追加する")+'</td>'+
		'<td width="1" bgcolor="gray"></td>';
		
	text += '<td width="1" bgcolor="gray"></td>';

	text += '<td>'+imgbtn("imageBox.gif","insertImage","イメージを追加する")+'</td>'+
		'<td width="1" bgcolor="gray"></td>';

/*		
	text += '<td><select onchange="SVFED.editor.'+oid+'.exec(\'fontSize\',this.value);this.selectedIndex = 0;">'+
		'<option value="">--文字サイズ--</option>'+
		'<option value="10px">7.2pt</option>'+
		'<option value="12px">8.6pt</option>'+
		'<option value="14px">10.1pt</option>'+
		'<option value="16px">11.5pt</option>'+
		'<option value="18px">13.0pt</option>'+
		'<option value="20px">14.4pt</option>'+
		'<option value="22px">15.8pt</option>'+
		'<option value="26px">18.7pt</option>'+
		'<option value="28px">20.1pt</option>'+
		'<option value="32px">23.0pt</option>'+
		'<option value="36px">25.9pt</option>'+
		'<option value="40px">28.8pt</option>'+
		'<option value="50px">36.0pt</option>'+
		'<option value="60px">43.2pt</option>'+
		'<option value="70px">50.4pt</option>'+
		'</select></td>';
*/
	text += '<td><select onchange="SVFED.editor.'+oid+'.exec(\'fontSize\',this.value);this.selectedIndex = 0;">'+
//		'<option value="">--文字サイズ--</option>'+
		'<option value="">文字サイズ</option>'+
		'<option value="10px">7.2pt</option>'+
		'<option value="15px">10.8pt</option>'+
		'<option value="20px">14.4pt</option>'+
		'<option value="25px">18.0pt</option>'+
		'<option value="30px">21.6pt</option>'+
		'<option value="35px">25.2pt</option>'+
		'<option value="40px">28.8pt</option>'+
		'<option value="45px">32.4pt</option>'+
		'<option value="50px">36.0pt</option>'+
		'<option value="60px">43.2pt</option>'+
		'<option value="70px">50.4pt</option>'+
		'</select></td>';
		
	text += '<td><select onchange="SVFED.editor.'+oid+'.exec(\'fontName\',this.value);this.selectedIndex = 0;">'+
//		'<option value="">--フォント--</option>'+
		'<option value="">フォント</option>'+
		'<option value="ＭＳ 明朝">明朝</option>'+
		'<option value="ＭＳ ゴシック">ゴシック</option>'+
		'</select></td>';
	text += '<td>'+imgbtn("undo.gif","undo","アンドゥ")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("redo.gif","redo","リドゥ")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td width="1" bgcolor="gray"></td>';
	text += '<td>'+imgbtn("bold.gif","bold","太字")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("italic.gif","italic","斜体")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("justifyBottom.gif","justifyBottom","下端を揃える")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("concatVertical.gif","concatVertical","下端に上端を合せる")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("justifyLeft.gif","justifyleft","左端を揃える")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("justifyCenter.gif","justifycenter","中央に揃える")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("justifyRight.gif","justifyright","右端を揃える")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("concatLeft.gif","concatLeft","右端に左端を合せる")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("concatCenter.gif","concatCenter","全体を中央に揃える")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("concatRight.gif","concatRight","左端に右端を合せる")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("alignLeft.gif","alignleft","文字を枠の左に寄せる")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("alignCenter.gif","aligncenter","文字を枠の中央に寄せる")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("alignJustify.gif","alignjustify","文字を均等配置する")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td>'+imgbtn("alignRight.gif","alignright","文字を枠の右に寄せる")+'</td>'+
		'<td width="1" bgcolor="gray"></td>'+
		'<td width="1" bgcolor="gray"></td>';

/*	for test */
/*
	text += '<td bgcolor="white"><input type="checkbox" id="'+oid+'_mode" onclick="SVFED.editor.'+oid+'.changeMode(this.checked)"></td>'+
		'<td bgcolor="white"><label for="'+oid+'_mode"></label></td>';
*/		
	text += '</tr></table>';
	text += '</td></tr>'+
		'<tr><td>'+
		'<iframe id="'+oid+'_iframe" style="width: 100%; height: '+height+'" scrolling=yes ' + txtDisabled + '></iframe>'+
		'</td></tr>'+
		'<tr><td align="left">'+
		'</td></tr>'+
		'</table>';
	text += '</CENTER>';
		
	
	SVFED.insertAdjacentHTML(ta, 'BeforeBegin', text);

	// initialize toolbar
	var tbar = document.getElementById(oid+"_toolbar");
	var list = tbar.getElementsByTagName("IMG");
	var mover = function() {this.style.borderStyle = "outset";};
	var mout  = function() {this.style.borderStyle = "solid";};
	var mdown = function() {this.style.borderStyle = "inset";};
	for (var i=0; i<list.length; i++) {
		if (list[i].className != "imgbtn") continue;
		list[i].onmouseover = mover
		list[i].onmouseout  = mout;
		list[i].onmousedown = mdown;
		list[i].onmouseup   = mover;
		list[i].style.borderWidth = "1px";
		list[i].style.borderStyle = "solid";
		list[i].style.borderColor = bgcolor;
	}
	
	var sSize = "";
	var sDirection = "";
	if(size != null){
		sSize = size;
	}
	if(direction != null){
		sDirection = direction;
	}

	// initialize iframe contents
	var ha = document.getElementById(oid+"_iframe");
	text = '<html><head>';
	text += '<meta http-equiv="Pragma" content="no-cache">';
	text += '<meta http-equiv="content-type" content="text/html; charset='+SVFED.config.charset+'">';
	//ie8対応
	text += '<meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />';
	
	text += '<link rel="stylesheet" href="'+contextPath+SVFED.config.basepath+SVFED.config.stylesheet+'" type="text/css">';
	text += '<base href="'+document.location.protocol+'//'+document.location.host+contextPath+SVFED.config.basepath+SVFED.config.framejspath+'">';
	text += '<script type="text/javascript" src="'+contextPath+SVFED.config.basepath+SVFED.config.framejspath+'" defer="defer" charset="UTF-8"></script>';
	text += '</head><body onload="Init(\''+sSize+'\',\''+sDirection+'\',\''+keywordId+'\',\''+kewordsXML.SRC+'\')" unselectable="On">';
	text += ta.value;
	text += '</body></html>';
	with (ha.contentWindow.document) {open(); write(text); close();}


	//PAGE DIRECTION
	if(size != null){
		document.getElementById(oid+"_paper_size").disabled = true;
		document.getElementById(oid+"_paper_size").value = size;
	}
	if(direction != null){
		document.getElementById(oid+"_page_direction").disabled = true;	
		document.getElementById(oid+"_page_direction").value = direction;
	}
	
	if(ha.contentWindow.document.getElementById("PAGE")){
		if (ha.contentWindow.document.getElementById("SIZE")){
			document.getElementById(oid+"_paper_size").value = ha.contentWindow.document.getElementById("SIZE").value;
		}
		if (ha.contentWindow.document.getElementById("DIRECTION")){
			document.getElementById(oid+"_page_direction").value = ha.contentWindow.document.getElementById("DIRECTION").value;
		}
		if (ha.contentWindow.document.getElementById("TEMPLATE")){
			document.getElementById(oid+"_paper_size").disabled = true;
			document.getElementById(oid+"_page_direction").disabled = true;			
		}
	}
	
	// create editor object
	SVFED.editor[oid] = new htmleditor(ta, ha,document.getElementById(oid+"_paper_size"),document.getElementById(oid+"_page_direction"));

	return SVFED.editor[oid];
}

SVFED.insertAdjacentHTML = function(obj, where, text)
{
	if (obj.insertAdjacentHTML) {
		obj.insertAdjacentHTML(where, text);
	} else {
		var range = obj.ownerDocument.createRange();
		if (where == 'AfterBegin' || where == 'BeforeEnd') {
			range.selectNodeContents(obj);
			range.collapse(where == 'AfterBegin');
		} else {
			range.selectNode(obj);
			range.collapse(where == 'BeforeBegin');
		}
		range.insertNode(range.createContextualFragment(text));
	}
}


//-----------------------------------------------------------------------
// constructor
//function htmleditor(ta, ha)
function htmleditor(ta, ha, oPsize, oPdirect)
{
	this.textarea = ta;
	this.htmlarea = ha;
	this.focused = false;
	this.srcmode = false;
	
	this.oPsize = oPsize;
	this.oPdirect = oPdirect;
	
}


htmleditor.prototype.beforeSubmit = function()
{

	this.htmlarea.contentWindow.bNewItem = 0; 

	this.htmlarea.contentWindow.fMode = true;
	this.htmlarea.contentWindow.changeMode();
	this.textarea.value = this.htmlarea.contentWindow.document.body.innerHTML;
	
	var elms = this.htmlarea.contentWindow.document.body.childNodes;
	var CountField = 0;
	
	var pageLeft = 0;
	var pageTop = 0;
	var pageRight = 0;
	var pageBottom = 0;
	
	var subforms;
	var records;
	var countSubforms = 0;
	var countRecords = 0;

	var countSvfFields = 0;

	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "field" || elms(i).className == "picturefield"){
			CountField += elms(i).childNodes.length;
		}
		if (elms(i).className == "textfield"){
			countSvfFields++;
		}
		if (elms(i).tagName == "P" && elms(i).id == "PAGE"){
			pageLeft = elms(i).offsetLeft;
			pageTop = elms(i).offsetTop;
			pageRight = pageLeft + elms(i).offsetWidth;
			pageBottom = pageTop + elms(i).offsetHeight;
		}
		if (elms(i).className == "subform"){
			countSubforms++;
		}
		if (elms(i).className == "HEADER" || elms(i).className == "DETAIL" || elms(i).className == "SUM" || elms(i).className == "TOTAL"){
			countRecords++;
		}		
	}	
	if (CountField < 1){
//		if (!confirm("文面が何も入力されていません。続行しますか？")){
//			return false;
//		}		
		if (countSvfFields < 1){
			alert("文面が何も入力されていません。");
			return false;
		}else{
			if (!confirm("文面が何も入力されていません。続行しますか？")){
				return false;
			}
		}		


	}
	subforms = new Array(countSubforms);
	records = new Array(countRecords);
	
	var rng = this.htmlarea.contentWindow.document.body.createControlRange();



	for (var i=0 ; i <rng.length; i++){
		rng.remove(i);
	}
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "field" && elms(i).style.whiteSpace != "nowrap"){
		
			var edits = elms(i).childNodes;
			var countEdits = 0
			for (var j=0 ; j <edits.length; j++){
				if (edits(j).nodeType == 3){
					countEdits = countEdits + Math.ceil(edits(j).length / 1024);
				}else{
					countEdits++;
				}
			}
			if (countEdits > 99){
				rng.add(elms(i));
			}
		}
	}	
	if (rng.length > 0){
		this.htmlarea.contentWindow.document.body.contentEditable = true;
		rng.select();
		alert("入力文字数、又は、予約語の数が多すぎます。")
		return false;
	}



	for (var i=0 ; i <rng.length; i++){
		rng.remove(i);
	}
	countSubforms = 0;
	countRecords = 0;
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "field" || elms(i).className == "line"){
			if (!this.htmlarea.contentWindow.isFullContained(elms(i),pageLeft,pageTop,pageRight,pageBottom)){
				rng.add(elms(i));
			}
		}
		if (elms(i).className == "subform"){
			subforms[countSubforms] = elms(i);
			countSubforms++;
		}
		if (elms(i).className == "recordArea"){
			records[countRecords] = elms(i);
			countRecords++;
		}		
	}	
	if (rng.length > 0){
		this.htmlarea.contentWindow.document.body.contentEditable = true;
		rng.select();
		if (!confirm("用紙からはみ出しています。続行しますか？")){
			return false;
		}
	}
	
	for (var i=0 ; i <rng.length; i++){
		rng.remove(i);
	}
	for (var i=0 ; i <elms.length; i++){
		if (elms(i).className == "field" || elms(i).className == "line"){
			for (var j=0; j < countSubforms; j++){

				var subformLeft = subforms[j].offsetLeft;
				var subformTop = subforms[j].offsetTop;
				var subformRight = subformLeft + subforms[j].offsetWidth;
				var subformBottom = subformTop + subforms[j].offsetHeight;
			
				if (this.htmlarea.contentWindow.isContained(elms(i),subformLeft,subformTop,subformRight,subformBottom)){

					var bContained = false;
					for (var k=0; k < countRecords; k++){
						var recordLeft = records[k].offsetLeft;
						var recordTop = records[k].offsetTop;
						var recordRight;
						var recordBottom;
						for (var l = 0; l <	records[k].childNodes.length; l = records[k].childNodes.length){
							recordRight = recordLeft + records[k].childNodes(l).offsetWidth;
							recordBottom = recordTop + records[k].childNodes(l).offsetHeight;
						}
						
						if (this.htmlarea.contentWindow.isFullContained(elms(i),recordLeft,recordTop,recordRight,recordBottom)){
							bContained = true;
							break;
						}
					}
					if (!bContained){
						rng.add(elms(i));
					}
				}
			}		
		}
	}	
	if (rng.length > 0){
		this.htmlarea.contentWindow.document.body.contentEditable = true;
		rng.select();
		if (!confirm("編集領域外に配置されています。続行しますか？")){
			return false;
		}		
	}

	return true;
}


htmleditor.prototype.getItems = function()
{
	var sel, rng, text, elm;

	var rng = this.htmlarea.contentWindow.document.body.createControlRange();	
	for (var i=0 ; i <rng.length; i++){
		rng.remove(i);
	}
	
	sel = this.htmlarea.contentWindow.document.selection;

	if (sel.type.toLowerCase() == "control"){
		rng = sel.createRange();
	
		for (var i=0 ; i <rng.length; i++){
			if (rng(i).className != "field" && rng(i).className != "line" && rng(i).className != "picturefield" && rng(i).className != "verticalrule" && rng(i).className != "rectangle"){
				rng.remove(i);
			}
		}
	}else{
		text = sel.createRange();
		if (text.parentElement()){
			elm = text.parentElement();
			if (elm.className == "field" || elm.className == "picturefield"){
				rng.add(elm);
			}else if(elm.tagName.toLowerCase() == "a"){
				rng.add(elm.parentElement);
			}
		}
	}
	
	return rng;
}

htmleditor.prototype.fontSize = function(fontSize)
{
	var items = this.getItems();
	var elm;
	var lines = 0;
	var nfontSize = parseInt(fontSize);
//	var sBgImage = 'image/bg' + fontSize + '.gif';

	for (var i = 0; i < items.length; i++){
		elm = items(i);
		if (elm.className == "field"){
			var lineHeight = new Number(elm.style.lineHeight.substr(0,elm.style.lineHeight.indexOf("px")));
			elm.LINECOUNT = Math.round(elm.clientHeight / lineHeight);

			elm.style.fontSize = fontSize;
//			eml.style.backgroundImage = 'url(' + sBgImage + ')';
			
			this.htmlarea.contentWindow.FieldAdjustSize(elm);
		}
	}

	return;
}

htmleditor.prototype.fontName = function(fontName)
{
	var items = this.getItems();
	var eml;

	for (var i = 0; i < items.length; i++){
		eml = items(i);
		if (eml.className == "field"){
			eml.style.fontFamily  = fontName;
		}
	}

	return;
}



htmleditor.prototype.italic = function()
{
	var items = this.getItems();
	var eml;

	for (var i = 0; i < items.length; i++){
		eml = items(i);
		if (eml.className == "field"){
			if ( eml.style.fontStyle == 'italic' ) {
				eml.style.fontStyle  = 'normal';
			} else {
				eml.style.fontStyle  = 'italic';
			}
		}
	}

	return;
}

htmleditor.prototype.bold = function()
{
	var items = this.getItems();
	var eml;

	for (var i = 0; i < items.length; i++){
		eml = items(i);
		if (eml.className == "field"){
			if ( eml.style.fontWeight == 'bold' ) {
				eml.style.fontWeight = 'normal';
				eml.style.letterSpacing  = '0px';
				eml.style.wordSpacing = '0px';
			} else {
				eml.style.fontWeight = 'bold';
				eml.style.letterSpacing  = '-1px';
				eml.style.wordSpacing = '-1px';
			}
			this.htmlarea.contentWindow.FieldAdjustSize(eml);
		}	
	}

	return;
}

htmleditor.prototype.align = function(op)
{
	var items = this.getItems();


	for (var i = 0; i < items.length; i++){
		eml = items(i);
		if (eml.className == "field"){
			eml.style.textAlign = op;
		}
	}

	return;
}

htmleditor.prototype.justifyLeft = function(op)
{
	var items = this.getItems();
	var width = 0;
	var eml;
	var left = 0;
	
	if (items.length < 1){
		return true;
	}

	if (op == 'concat'){
		left = items(i).style.pixelLeft;
	}else{
		for (var i = 0; i < items.length; i++){
			eml = items(i);
			if (left < 1) {
				left = eml.style.pixelLeft;
			}
			if (left > eml.style.pixelLeft) {
					left = eml.style.pixelLeft;
			}
		}
	}

	for (var i = 0; i < items.length; i++){
		eml = items(i);
		eml.style.pixelLeft = left;

		eml.style.pixelRight = left + eml.style.pixelWidth;

		if (op == 'concat'){

			if ( eml.style.fontWeight == 'bold' ) {
				left += (eml.style.pixelWidth - 2);
			}else{
				left += (eml.style.pixelWidth - 1);
			}			
		}
	}

	return;
}

htmleditor.prototype.justifyCenter = function(op)
{

	var items = this.getItems();
	var width = 0;
	var eml;
	var left = 1;

	if (items.length < 1){
		return true;
	}

	var paperWidth = this.htmlarea.contentWindow.document.getElementById("PAGE").offsetWidth;

	if (op == 'concat'){
		for (var i = 0; i < items.length; i++){
			if ( items(i).style.fontWeight == 'bold' ) {
				width += (items(i).style.pixelWidth - 2);
			}else{
				width += (items(i).style.pixelWidth - 1);
			}			
		}
		left = paperWidth / 2 - width / 2;
	}

	for (var i = 0; i < items.length; i++){
		eml = items(i);
		if (op == 'concat'){
			eml.style.pixelLeft = left;
			if ( eml.style.fontWeight == 'bold' ) {
				left += (eml.style.pixelWidth - 2);
			}else{
				left += (eml.style.pixelWidth - 1);
			}			
		}else{
			if ( eml.style.fontWeight == 'bold' ) {
				eml.style.pixelLeft = paperWidth / 2 - (eml.style.pixelWidth - 2) / 2;
			}else{
				eml.style.pixelLeft = paperWidth / 2 - (eml.style.pixelWidth - 1) / 2;
			}			

			eml.style.pixelRight = eml.style.pixelLeft + eml.style.pixelWidth;

		}
	}


	return;
}

htmleditor.prototype.justifyRight = function(op)
{
	var items = this.getItems();
	var width = 0;
	var eml;
	var right = 0;

	if (items.length < 1){
		return true;
	}

	if (op == 'concat'){
		if ( items(0).style.fontWeight == 'bold' ) {
			right = items(0).style.pixelLeft + (items(0).style.pixelWidth - 1);
		}else{
			//right = items(0).style.pixelLeft + (items(0).clientWidth - 0);
			right = items(0).style.pixelLeft + (items(0).style.pixelWidth - 0);
		}			
	}else{
		for (var i = 0; i < items.length; i++){
			eml = items(i);
			if ( eml.style.fontWeight == 'bold' ) {
				//width = (eml.clientWidth - 1);
				width = (eml.style.pixelWidth - 1);
			}else{
				//width = (eml.clientWidth - 0);
				width = (eml.style.pixelWidth - 0);
			}			
			if (right < eml.style.pixelLeft + width) {
				right = eml.style.pixelLeft + width;
			}
		}
		width = 0;
	}

	if (op == 'concat'){
		for (var i = 0; i < items.length; i++){
			if ( items(i).style.fontWeight == 'bold' ) {
				//width += (items(i).clientWidth - 2);
				width += (items(i).style.pixelWidth - 2);
			}else{
				//width += (items(i).clientWidth - 1);
				width += (items(i).style.pixelWidth - 1);
			}			
		}
		left = (right - width);
	}


	for (var i = 0; i < items.length; i++){
		eml = items(i);
		if ( eml.style.fontWeight == 'bold' ) {
			//eml.style.pixelLeft = right - (eml.clientWidth - 1);
			eml.style.pixelLeft = right - (eml.style.pixelWidth - 1);
		}else{
			//eml.style.pixelLeft = right - (eml.clientWidth - 0);
			eml.style.pixelLeft = right - (eml.style.pixelWidth - 0);
		}			

		//eml.style.pixelRight = eml.style.pixelLeft + eml.clientWidth;
		eml.style.pixelRight = eml.style.pixelLeft + eml.style.pixelWidth;

		if (op == 'concat'){
			if ( eml.style.fontWeight == 'bold' ) {
				//right -= (eml.clientWidth - 2);
				right -= (eml.style.pixelWidth - 2);
			}else{
				//right -= (eml.clientWidth - 1);
				right -= (eml.style.pixelWidth - 1);
			}			
		}
	}


	return;
}

htmleditor.prototype.justifyBottom = function(items)
{

	var items = this.getItems();
	var eml;
	
	if (items.length < 1){
		return true;
	}
	
	//var bottom = items(0).style.pixelTop + items(0).clientHeight;
	var bottom = items(0).style.pixelTop + items(0).style.pixelHeight;


	for (var i = 0; i < items.length; i++){
		eml = items(i);
		//eml.style.pixelTop = bottom - eml.clientHeight;
		eml.style.pixelTop = bottom - eml.style.pixelHeight;
	}
	return;
}

htmleditor.prototype.concatVertical = function()
{
	var items = this.getItems();
	var eml;

	if (items.length < 1){
		return true;
	}
	
	var top = items(0).style.pixelTop;


	for (var i = 0; i < items.length; i++){
		eml = items(i);
		eml.style.pixelTop = top;
//		top += eml.clientHeight + 1;
		//top += eml.clientHeight;
		top += eml.style.pixelHeight;
	}
	return;
}

htmleditor.prototype.insertKeyWord = function(id,type,length,flength,desc,preview,dFormat)
{
	this.htmlarea.contentWindow.bNewItem = 0; 

	var format;
	//defalt format
    switch(type){
    case   'i':
		//無編集
		format = '0';
		for (var i = 1; i < length; i++){
			format = '0' + format;
		}
		break;
    case   'f':
		//無編集
		format = '0';
		for (var i = 1; i < length; i++){
			format = '0' + format;
		}
		format += '.'
		for (var i = 0; i < flength; i++){
			format += '0';
		}
		break;
    case   'd':
		//無編集
		format = 'yyyy/MM/dd';
		break;
    case   'g':
		//無編集
		format = '女';
		break;
	case  'a':
		format = "画像イメージ";
		break;
    default:
		format = 'X';
		for (var i = 2; i < length; i++){
			format += '-';
		}
		format += 'X';
		break;
	}
	
	if (dFormat != ""){
		format = dFormat;
	}

//	desc = '[' + desc + ']'
	var sTitle, sText;
	if (this.htmlarea.contentWindow.fMode){
		sTitle = desc;
		sText = format;
	}else{
		sTitle = format;
		sText = desc;
	}

	var html = '<A class="reserved" id="'+id+'" length='+length+' slength='+flength+' format="'+format+'" type="'+type+'" desc="'+desc+'" preview="'+preview+'" TITLE="'+sTitle+'" oncontextmenu="Format();" onfocus="Cancel()" onbeforeactivate ="Cancel()">';

	html += sText;

	html += '</A>';

	this.htmlarea.contentWindow.focus();

	//alert(this.htmlarea.contentWindow.currentTextRange);
	
	var sel, rng, el, dup;
	if (sel = this.htmlarea.contentWindow.document.selection) {
	
		if(sel.type.toLowerCase() == "none"){
			//2009-11-09
			//rng = sel.createRange();
			rng = this.htmlarea.contentWindow.currentTextRange;
			if(rng == null){
				return;
			}
			
			if (rng.parentElement()){
				//フィールド内にカーソルが入ってる状態での予約語挿入************************************
				el = rng.parentElement();

				//FieldFocus時(フィールド内にカーソルが入った時点)のフィールドHTML
				//(フィールドに設定している予約語、固定文言)を取得
				var tmpTargetStr = this.htmlarea.contentWindow.currentTextRangeActHtml;
				if (chkPageElm(tmpTargetStr ,id) == false ){
					//頁予約語と通常の予約語が混在する場合はエラーとする
					return;
				}

				if (el.className == "field"){
					if(type == "a"){
						alert("テキストフィールドには画像型の予約語は設定できません。");
					}else{
						rng.pasteHTML(html);
						el.focus();
					}
				}else if(el.className == "picturefield"){
					if(type == "a"){
						el.innerText = "";
						rng.pasteHTML(html);
					}else{
						alert("画像フィールドにはテキスト型の予約語は設定できません。");
					}
				}else if(el.tagName.toLowerCase() == "a"){
					//2009-11-09
					if(type == "a"){
						alert("テキストフィールドには画像型の予約語は設定できません。");
					}else{
						el.insertAdjacentHTML("beforeBegin",html);
						el.parentElement.focus();
					}
				}else if(el.tagName.toLowerCase() == "span"){
					//2009-11-09
					if(type == "a"){
						alert("テキストフィールドには画像型の予約語は設定できません。");
					}else{
						el.insertAdjacentHTML("beforeBegin",html);
						el.parentElement.focus();
					}
				}
			}
		}else{
			if(sel.type.toLowerCase() == "control"){
				//フィールドが選択状態での予約語挿入************************************
				rng = sel.createRange();

				for (var i=0 ; i <rng.length; i++){
				
					var tmpTargetStr = rng(i).innerHTML;
					if (chkPageElm(tmpTargetStr ,id) == false ){
						//頁予約語と通常の予約語が混在する場合はエラーとする
						return;
					}
					
					
					if (rng(i).className == "field"){

						if(type == "a"){
							alert("テキストフィールドには画像型の予約語は設定できません。");
						}else{
							rng(i).focus();
							rng(i).innerHTML += html;
						}
					}
					else if (rng(i).className == "picturefield"){
						if(type == "a"){
							//rng(i).focus();
							rng(i).innerHTML ="";
							rng(i).innerHTML += html;

							this.htmlarea.contentWindow.Resume();

						}else{
							alert("画像フィールドにはテキスト型の予約語は設定できません。");
						}
					}						
				}
			}
		}
	}

	return;
}

//************************************************************************************
//***同一フィールドに頁予約語とその他の予約語の混在を許さないためのチェック関数 
//************************************************************************************
function chkPageElm(targetStr ,id)
{
	var PAGECNT = "_PAGE_GROUP_COUNT";
	var PAGETOTAL = "_TOTAL_PAGE_COUNT";

	if (id.indexOf(PAGECNT) > -1 || id.indexOf(PAGETOTAL) > -1 ){
		//入力予約語が"頁番号"または"総頁数"の場合**************************
		
		if(targetStr.indexOf("<A") > -1){
			//Aタグ(予約語)が含まれている場合
			if(targetStr.indexOf(PAGECNT) < 0 && targetStr.indexOf(PAGETOTAL) < 0 ){
				//フィールドに頁以外の予約語が存在している場合はエラー
				alert("テキストフィールドに頁番号または総頁数の予約語と他の予約語は混在できません。");
				return false;
			}
		}

	}else{
		//入力予約語が"頁番号"または"総頁数"ではない場合********************
		
		if (targetStr.indexOf(PAGECNT) > -1 || targetStr.indexOf(PAGETOTAL) > -1){
			//フィールドに頁の予約語が存在している場合はエラー
			alert("テキストフィールドに頁番号または総頁数の予約語と他の予約語は混在できません。");
			return false;
		}
	}
	
	return true;
}



htmleditor.prototype.insertText = function(wrap)
{

	this.htmlarea.contentWindow.bNewItem = 1;
	this.htmlarea.contentWindow.bNoWrap = wrap;

/*
	var html = '<DIV class="field"';
	html += ' style="FONT-SIZE: 18px; FONT-FAMILY: ＭＳ ゴシック;';
	html += ' position : absolute; width: 10em; height: 1.0em; line-height: 1.0em; letter-spacing: 0px;"';
	html += ' onbeforepaste="fieldBeforePaste()" onpaste="fieldPaste()"';
	html += ' onresizestart="Resizestart()"';
	html += ' onresizeend="FieldResize();Resizeend();"';
	html += ' onmove="FieldMove();"';
	html += ' onblur="FieldBlur();"';
	html += '>';

	var elm = this.htmlarea.contentWindow.document.createElement(html);
		
	this.htmlarea.contentWindow.document.body.insertAdjacentElement("beforeEnd",elm);


	elm.innerText = "abc";
	elm.style.pixelWidth = elm.style.pixelWidth + 1;

	elm.style.pixelLeft = 1;
	elm.style.pixelTop = 1;
	elm.style.pixelRight = elm.style.pixelLeft + elm.clientWidth;

	var sBgImage = 'image/bg18px.gif';

	elm.style.backgroundImage = 'url(' + sBgImage + ')';


	this.htmlarea.contentWindow.focus();

	this.htmlarea.contentWindow.Elm = elm;
	this.htmlarea.contentWindow.drag_switch = 2;
	this.htmlarea.contentWindow.startX = 0;
	this.htmlarea.contentWindow.startY = 0;
*/
	return;

}

htmleditor.prototype.insertImage = function()
{
	this.htmlarea.contentWindow.bNewItem = 3;
	return;
}

htmleditor.prototype.insertHr = function()
{
	this.htmlarea.contentWindow.bNewItem = 2; 

	return;

}

htmleditor.prototype.insertVr = function()
{
	this.htmlarea.contentWindow.bNewItem = 4; 

	return;

}

htmleditor.prototype.insertRect = function()
{
	this.htmlarea.contentWindow.bNewItem = 5; 

	return;

}



htmleditor.prototype.chagePaperSize = function(size)
{
	this.htmlarea.contentWindow.bNewItem = 0; 

	this.htmlarea.contentWindow.document.getElementById("SIZE").value = size;
	this.htmlarea.contentWindow.document.getElementById("PAGE").className = size + this.htmlarea.contentWindow.document.getElementById("DIRECTION").value;
	this.htmlarea.contentWindow.focus();
	this.htmlarea.contentWindow.Resume();
	
}


htmleditor.prototype.chagePaperdirection = function(direction)
{
	this.htmlarea.contentWindow.bNewItem = 0; 

	this.htmlarea.contentWindow.document.getElementById("DIRECTION").value = direction;
	this.htmlarea.contentWindow.document.getElementById("PAGE").className = this.htmlarea.contentWindow.document.getElementById("SIZE").value + direction;
	this.htmlarea.contentWindow.focus();
	this.htmlarea.contentWindow.Resume();
	
}



htmleditor.prototype.focus = function(flag)
{
	if (this.focused == flag) return;
	var win = this.htmlarea.contentWindow;
	var doc = win.document;
	if (SVFED.browser.isIE) {
		if (doc.body.contentEditable != flag)
			doc.body.contentEditable = flag;
		if (flag) win.focus();
	} else {
		var mode = flag? "On": "Off";
		if (doc.designMode != mode)
			doc.designMode = mode;
		if (flag) doc.execCommand("useCSS", false, true);
	}
	this.focused = flag;
}

htmleditor.prototype.exec = function(cmd, data)
{

	this.htmlarea.contentWindow.focus();
	this.htmlarea.contentWindow.bNewItem = 0; 

	switch (cmd) {
	case "insertTextNW":
		this.insertText(true);
		break;
	case "insertText":
		this.insertText(false);
		break;
	case "justifyleft":
		this.justifyLeft("none");
		this.htmlarea.contentWindow.Resume();
		break;
	case "justifycenter":
		this.justifyCenter("none");
		this.htmlarea.contentWindow.Resume();
		break;
	case "justifyright":
		this.justifyRight("none");
		this.htmlarea.contentWindow.Resume();
		break;
	case "justifyBottom":
		this.justifyBottom();
		this.htmlarea.contentWindow.Resume();
		break;
	case "alignleft":
		this.align("left");
		this.htmlarea.contentWindow.Resume();
		break;
	case "aligncenter":
		this.align("center");
		this.htmlarea.contentWindow.Resume();
		break;
	case "alignright":
		this.align("right");
		this.htmlarea.contentWindow.Resume();
		break;
	case "alignjustify":
		this.align("justify");
		this.htmlarea.contentWindow.Resume();
		break;
	case "concatLeft":
		this.justifyLeft('concat');
		this.htmlarea.contentWindow.Resume();
		break;
	case "concatCenter":
		this.justifyCenter('concat');
		this.htmlarea.contentWindow.Resume();
		break;
	case "concatRight":
		this.justifyRight('concat');
		this.htmlarea.contentWindow.Resume();
		break;
	case "concatVertical":
		this.concatVertical();
		this.htmlarea.contentWindow.Resume();
		break;
	case "bold":
		this.bold();
		this.htmlarea.contentWindow.Resume();
		break;
	case "italic":
		this.italic();
		this.htmlarea.contentWindow.Resume();
		break;
	case "fontSize":
		this.fontSize(data);
		this.htmlarea.contentWindow.Resume();
		break;
	case "fontName":
		this.fontName(data);
		this.htmlarea.contentWindow.Resume();
		break;
	case "inserthorizontalrule":
		this.insertHr();
		break;
		
	case "insertverticalrule":
		this.insertVr();
		break;
		
	case "insertrectangle":
		this.insertRect();
		break;
		
	case "undo":
		this.undo();
		break;
	case "redo":
		this.redo();
		break;
	case "insertImage":
		this.insertImage(true);
		break;
		
	default:
		this.execCommand(cmd, false, data);
		this.htmlarea.contentWindow.Resume();
		break;
	}

	this.htmlarea.contentWindow.focus();
}

htmleditor.prototype.undo = function()
{
	this.htmlarea.contentWindow.Undo();

	if(this.htmlarea.contentWindow.document.getElementById("PAGE")){
		if (this.htmlarea.contentWindow.document.getElementById("SIZE")){
			this.oPsize.value = this.htmlarea.contentWindow.document.getElementById("SIZE").value;
		}
		if (this.htmlarea.contentWindow.document.getElementById("DIRECTION")){
			this.oPdirect.value = this.htmlarea.contentWindow.document.getElementById("DIRECTION").value;
		}
	}

	
}

htmleditor.prototype.redo = function()
{
	this.htmlarea.contentWindow.Redo();

	if(this.htmlarea.contentWindow.document.getElementById("PAGE")){
		if (this.htmlarea.contentWindow.document.getElementById("SIZE")){
			this.oPsize.value = this.htmlarea.contentWindow.document.getElementById("SIZE").value;
		}
		if (this.htmlarea.contentWindow.document.getElementById("DIRECTION")){
			this.oPdirect.value = this.htmlarea.contentWindow.document.getElementById("DIRECTION").value;
		}
	}

}

htmleditor.prototype.execCommand = function(cmd, ui, data)
{
	this.htmlarea.contentWindow.document.execCommand(cmd, ui, data);
}

// for debug
htmleditor.prototype.changeMode = function(src)
{
	if (this.srcmode == src) return;
	var ta = this.textarea;
	var ha = this.htmlarea;
	if (src) {
		this.focus(false);
		ta.value = ha.contentWindow.document.body.innerHTML;
		ha.style.display = "none";
		ta.style.display = "inline";
		ta.focus();
	} else {
		ha.contentWindow.document.body.innerHTML = ta.value;
		ta.style.display = "none";
		ha.style.display = "inline";
		this.focus(true);
		ha.contentWindow.initItems();
	}
	this.srcmode = src;
}

//-----------------------------------------------------------------------
function queryBrowserInfo()
{
	var ua = navigator.userAgent.toLowerCase();
	var info = new Object();
	info.isIE = (ua.indexOf("msie") != -1 && ua.indexOf("opera") == -1);
	info.isGecko = (navigator.product == "Gecko");
	info.editable = false;
	if (info.isIE && navigator.appVersion.match(/MSIE (\d+\.\d+)/)) {
		info.editable = (RegExp.$1 > 5.5);
	} else if (info.isGecko) {
		info.editable = (navigator.productSub >= 20030210);
	}
	return info;
}


