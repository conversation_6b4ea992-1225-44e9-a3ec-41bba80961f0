﻿<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz02301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz02301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz02301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz02301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz02301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz02301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="56%" class="">
				<TBODY>
					<TR>
						<TH
							width="100" class=""><h:outputText styleClass="outputText"
							id="text1" value="大分類（上" style="font-size: 8pt"></h:outputText></TH>
						<TH width="6"><h:inputText styleClass="inputText_dis" id="text7"
							size="1" maxlength="1"
							value="#{pc_Ssz02301.propKindL.stringValue}"
							style="font-size: 8pt; width: 12px" readonly="true"></h:inputText></TH>
						<TH
							width="32"><h:outputText styleClass="outputText" id="text2"
							value="桁）" style="font-size: 8pt"></h:outputText></TH>
						<TH width="128"></TH>
						<TH
							width="100"><h:outputText styleClass="outputText" id="text3"
							value="中分類（上" style="font-size: 8pt"></h:outputText></TH>
						<TH width="6"><h:inputText styleClass="inputText_dis" id="text8"
							size="1" maxlength="1"
							value="#{pc_Ssz02301.propKindM.stringValue}"
							style="font-size: 8pt; width: 12px" readonly="true"></h:inputText></TH>
						<TH
							width="32"><h:outputText styleClass="outputText" id="text4"
							value="桁）" style="font-size: 8pt"></h:outputText></TH>
						<TH width="128"></TH>
						<TH
							width="100"><h:outputText styleClass="outputText" id="text5"
							value="小分類（上" style="font-size: 8pt"></h:outputText></TH>
						<TH width="6"><h:inputText styleClass="inputText_dis" id="text9"
							size="1" maxlength="1"
							value="#{pc_Ssz02301.propKindS.stringValue}" disabled="true"
							style="font-size: 8pt; width: 12px"></h:inputText></TH>
						<TH
							width="32"><h:outputText styleClass="outputText" id="text6"
							value="桁）" style="font-size: 8pt"></h:outputText></TH>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD></TD>
						<TD width="900"></TD>
						<TD width="410"><h:outputText styleClass="outputText" id="text13"
							style="font-size: 8pt"
							value="#{pc_Ssz02301.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text21" style="font-size: 8pt"
							value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="14%"></TD>
						<TD width="56%" align="center">

						<div class="listScroll" style="height:296px;" id="listScroll" onscroll="setScrollPosition('scroll',this);">
						<h:dataTable border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz02301.propGyos.list}" var="varlist" width="650"
							rowClasses="#{pc_Ssz02301.propGyos.rowClasses}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text10" styleClass="outputText" value="コード"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text14"
									value="#{varlist.gyosyuCD}"></h:outputText>
								<f:attribute value="80" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="区分" id="text11"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text15"
									value="#{varlist.gyosyuKBN}"></h:outputText>
								<f:attribute value="80" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text12"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text16"
									value="#{varlist.gyosyuName.displayValue}"
									title="#{varlist.gyosyuName.value}"
									styleClass="outputText"></h:outputText>
								<f:attribute value="245" name="width" />
							</h:column>
							<h:column id="column99">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="業種分類" id="text99"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text98"
									value="#{varlist.gyosyuBnrName.displayValue}"
									title="#{varlist.gyosyuBnrName.value}"></h:outputText>
								<f:attribute value="245" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz02301.doSelectAction}" value="選択"></hx:commandExButton>
								<f:attribute value="24" name="width" />
							</h:column>
						</h:dataTable>
						</DIV>
						</TD>
						<TD width="14%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="table" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TH class="v_a" width="147"><h:outputText styleClass="outputText"
							id="text17" value="#{pc_Ssz02301.propGyosyuCD.labelName}"
							style="#{pc_Ssz02301.propGyosyuCD.labelStyle}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText"
							id="htmlGyosyuCD" size="6"
							value="#{pc_Ssz02301.propGyosyuCD.stringValue}"
							maxlength="#{pc_Ssz02301.propGyosyuCD.maxLength}"
							style="#{pc_Ssz02301.propGyosyuCD.style}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="147"><h:outputText styleClass="outputText"
							id="text18" value="#{pc_Ssz02301.propGyosyuName.labelName}"
							style="#{pc_Ssz02301.propGyosyuName.labelStyle}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText"
							id="htmlGyosyuName" size="50"
							value="#{pc_Ssz02301.propGyosyuName.stringValue}"
							maxlength="#{pc_Ssz02301.propGyosyuName.maxLength}"
							style="#{pc_Ssz02301.propGyosyuName.style}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="147"><h:outputText styleClass="outputText"
							id="text19" value="#{pc_Ssz02301.propGyosyuNameKana.labelName}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText"
							id="htmlGyosyuNameKana" size="50"
							value="#{pc_Ssz02301.propGyosyuNameKana.stringValue}"
							maxlength="#{pc_Ssz02301.propGyosyuNameKana.maxLength}"
							style="#{pc_Ssz02301.propGyosyuNameKana.style}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_d" width="147"><h:outputText styleClass="outputText"
							id="text20" value="#{pc_Ssz02301.propGyosyuNameRyak.labelName}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText"
							id="htmlGyosyuNameRyak" size="20"
							value="#{pc_Ssz02301.propGyosyuNameRyak.stringValue}"
							maxlength="#{pc_Ssz02301.propGyosyuNameRyak.maxLength}"
							style="#{pc_Ssz02301.propGyosyuNameRyak.style}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_d" width="147"><h:outputText styleClass="outputText"
							id="text97" value="#{pc_Ssz02301.propGyosyuBnrCD.labelName}"></h:outputText></TH>
						<TD width="368"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlGyosyBunrui" 
							value="#{pc_Ssz02301.propGyosyuBnrCD.stringValue}">
							<f:selectItems value="#{pc_Ssz02301.propGyosyuBnrCD.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH class="v_d" width="147"><h:outputText styleClass="outputText"
							id="text96" value="#{pc_Ssz02301.propRefresh.labelName}"></h:outputText></TH>
						<TD width="520" style = "display:inline-flex"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRefresh"
										value="#{pc_Ssz02301.propRefresh.stringValue}">
										<f:selectItem itemValue="1"
											itemLabel="下位分類を更新する" />
										<f:selectItem itemValue="0"
											itemLabel="下位分類は更新しない" />
									</h:selectOneRadio></TD>						
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz02301.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}" value="確定"></hx:commandExButton><hx:commandExButton
							type="submit" styleClass="commandExButton_dat" id="delete"
							action="#{pc_Ssz02301.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}" value="削除"></hx:commandExButton><hx:commandExButton
							type="submit" styleClass="commandExButton_etc" id="clear"
							action="#{pc_Ssz02301.doClearAction}" value="クリア"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<DIV align="left">
			
			</DIV></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz02301.propGyos.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE = "JavaScript">
changeScrollPosition("scroll","listScroll")
</SCRIPT>
</HTML>
