<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz01801.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz01801.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz01801.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz01801.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><BR>

			<TABLE width="100%">
				<TBODY>
					<TR>
						<TD width="10%"></TD>
						<TD width="700">
							<TABLE border="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="200"><B><h:outputText
														styleClass="outputText" id="lblName"
														value="#{pc_Ssz01801.propName.labelName}"
														style="#{pc_Ssz01801.propName.labelStyle}"></h:outputText></B></TH>
										<TD width="500"><B><h:inputText styleClass="inputText"
														id="htmlName" value="#{pc_Ssz01801.propName.stringValue}"
														style="#{pc_Ssz01801.propName.style}"
														maxlength="#{pc_Ssz01801.propName.maxLength}" size="70"></h:inputText></B></TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><B><hx:commandExButton type="submit" value="PDF作成" styleClass="commandExButton_out" id="pdfout" action="#{pc_Ssz01801.doPdfOutAction}" confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton><hx:commandExButton type="submit" value="CSV作成" styleClass="commandExButton_out" id="csvout" confirm="#{msg.SY_MSG_0020W}" action="#{pc_Ssz01801.doCsvOutAction}"></hx:commandExButton><hx:commandExButton type="submit" value="出力項目指定" styleClass="commandExButton_out" id="setoutput" action="#{pc_Ssz01801.doSetoutputAction}"></hx:commandExButton></B></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

