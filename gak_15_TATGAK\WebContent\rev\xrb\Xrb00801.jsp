<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<html>
<head>
  <%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="GENERATOR" content="IBM Software Development Platform">
  <meta http-equiv="Content-Style-Type" content="text/css">
  <title>Xrb00801.jsp</title>
  <link rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
  <link rel="stylesheet" type="text/css" href="../inc/gakuen.css">
</head>

<f:view locale=#{SYSTEM_DATA.locale}>
  <f:loadBundle basename="properties.message" var="msg"/>
  <body>
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00801.onPageLoadBegin}">

      <h:form styleClass="form" id="form1">
        <!-- ヘッダーインクルード -->
        <jsp:include page="../inc/header.jsp" />

        <!-- 閉じるボタン -->
        <div style="display:none;">
          <hx:commandExButton type="submit" value="閉じる" styleClass="commandExButton" id="closeDisp" action="#{pc_Xrb00801.doCloseDispAction}" />
          <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00801.funcId}" />
          <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}" /> 
          <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00801.screenName}" />
        </div>
        
        <!--↓outer↓-->
        <DIV class="outer">

        <fieldset class="fieldset_err">
          <legend>エラーメッセージ</legend>
          <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}" styleClass="outputText" escape="false" />
        </fieldset>

        <!--↓content↓-->
        <div id="content">

          <div class="column" align="center">

            <table width="870" class="table">
              <tr>
                <%-- 入学年度(outptText) --%>
                <th width="185">
                  <h:outputText
                    styleClass="outputText"
                    id="lblNyugakNendo"
                    style="#{pc_Xrb00801.propNyugakNendo.labelStyle}"
                    value="#{pc_Xrb00801.propNyugakNendo.labelName}"/>
                </th>
                <%-- 入学年度(inputText) --%>
                <td width="185">
                  <h:inputText
                    styleClass="inputText"
                    id="htmlNyugakNendo"
                    disabled="#{pc_Xrb00801.propNyugakNendo.disabled}"
                    value="#{pc_Xrb00801.propNyugakNendo.dateValue}"
                    style="#{pc_Xrb00801.propNyugakNendo.style}"
                    size="10"
                    tabindex="1">
                    <f:convertDateTime pattern="yyyy"/>
                    <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_"/>
                  </h:inputText>
                </td>
                <%-- 入学学期NO(outputText) --%>
                <th width="185">
                  <h:outputText
                    styleClass="outputText"
                    id="lblNyugakGakkiNo"
                    style="#{pc_Xrb00801.propNyugakGakkiNo.labelStyle}"
                    value="#{pc_Xrb00801.propNyugakGakkiNo.labelName}"/>
                </th>
                <%-- 入学学期NO(inputText) --%>
                <td width="185">
                  <h:inputText
                    styleClass="inputText"
                    id="htmlNyugakGakkiNo"
                    disabled="#{pc_Xrb00801.propNyugakGakkiNo.disabled}"
                    value="#{pc_Xrb00801.propNyugakGakkiNo.integerValue}"
                    style="#{pc_Xrb00801.propNyugakGakkiNo.style}"
                    size="10"
                    tabindex="2">
                    <f:convertNumber type="number" pattern="#0" />
                    <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
                  </h:inputText>
                </td>
                <td align="left" style="border-left-style: none;">
                  <%-- 選択(commandExButton) --%>
                  <hx:commandExButton
                    type="submit"
                    value="選択"
                    styleClass="commandExButton"
                    id="select"
                    action="#{pc_Xrb00801.doSelectAction}"
                    disabled="#{pc_Xrb00801.propSelect.disabled}"
                    tabindex="3"/>
                  <%-- 解除(commandExButton) --%>
                  <hx:commandExButton
                    type="submit"
                    value="解除"
                    styleClass="commandExButton"
                    id="unselect"
                    action="#{pc_Xrb00801.doUnselectAction}"
                    disabled="#{pc_Xrb00801.propUnselect.disabled}"
                    tabindex="4"/>
                </td>
              </tr>
            </table>

            <hr class="hr" noshade>

            <%-- 表示・非表示切り替えエリア --%>
            <hx:jspPanel rendered="#{pc_Xrb00801.dispArea}">

              <table class="table" width="870">
                <tr align="center" valign="middle">
                  <%-- カリキュラム学科組織(outputText) --%>
                  <th width="185">
                    <h:outputText
                      styleClass="outputText"
                      id="lblCurGakkaCd"
                      style="#{pc_Xrb00801.propCurGakkaCd.labelStyle}"
                      value="#{pc_Xrb00801.propCurGakkaCd.labelName}"/>
                  </th>
                  <%-- カリキュラム学科組織(inputText) --%>
                  <td width="*">
                    <h:selectOneMenu
                      styleClass="selectOneMenu"
                      id="htmlCurGakkaCd"
                      tabindex="7"
                      disabled="#{pc_Xrb00801.propCurGakkaCd.disabled}"
                      value="#{pc_Xrb00801.propCurGakkaCd.value}">
                      <f:selectItems value="#{pc_Xrb00801.propCurGakkaCd.list}" />
                    </h:selectOneMenu>
                  </td>
                </tr>
                <tr>
                  <%-- 部数(outputText) --%>
                  <th>
                    <h:outputText
                      styleClass="outputText"
                      id="lblPrintCnt"
                      style="#{pc_Xrb00801.propPrintCnt.labelStyle}"
                      value="#{pc_Xrb00801.propPrintCnt.labelName}"/>
                  </th>
                  <%-- 部数(inputText) --%>
                  <td>
                    <h:inputText
                      styleClass="inputText"
                      id="htmlPrintCnt"
                      disabled="#{pc_Xrb00801.propPrintCnt.disabled}"
                      value="#{pc_Xrb00801.propPrintCnt.integerValue}"
                      style="#{pc_Xrb00801.propPrintCnt.style}"
                      size="10"
                      tabindex="8">
                      <f:convertNumber type="number" pattern="#0" />
                      <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
                    </h:inputText>
                    <span style="color:red;">　※PDF作成の場合は入力不要</span>
                  </td>
                </tr>
              </table>

              <br>

              <%-- ボタンエリア --%>
              <table width="100%" class="button_bar" border="0" cellpadding="0" cellspacing="0">
                <tbody>
                  <tr>
                    <td>
                    <%-- 印刷(commandExButton) --%>
                    <hx:commandExButton
                      type="submit"
                      value="印刷"
                      styleClass="commandExButton_out"
                      id="print"
                      tabindex="8"
                      confirm="#{msg.SY_MSG_0022W}"
                      action="#{pc_Xrb00801.printAction}" />
                    <%-- PDF作成(commandExButton) --%>
                    <hx:commandExButton
                      type="submit"
                      value="PDF作成"
                      styleClass="commandExButton_out"
                      id="makePfd"
                      tabindex="9"
                      action="#{pc_Xrb00801.makePdfAction}"
                      confirm="#{msg.SY_MSG_0019W}" />
                    </td>
                  </tr>
                </tbody>
              </table>

            </hx:jspPanel>

          </div>
          <!--↑content↑-->

        </div>
        <!--↑outer↑-->

        <!-- フッダーインクルード -->
        <jsp:include page="../inc/footer.jsp" />

      </h:form>

    </hx:scriptCollector>

  </body>
  <jsp:include page="../inc/common.jsp" />
</f:view>
</html>
