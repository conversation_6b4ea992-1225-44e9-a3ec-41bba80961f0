<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00402T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	// 戻るボタン押下時処理
	function onClickReturnDisp(id) {
		var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
		if(changeDataFlg == "1"){
			return confirm(id);
		}
		
		return true;
	}
	
	// データ変更時
	function onChangeData() {
		document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
	}

	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfilm").value = "1";
			var action = document.getElementById("form1:htmlAction").value;
			indirectClick(action);
		} catch (e) {
		}
	}

	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfilm").value = "0";
		} catch (e) {
		}
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00402T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00402T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00402T01.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00402T01.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"><hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="53"
						action="#{pc_Xrh00402T01.xrh00402.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
					</hx:commandExButton></TD>
				</TR>
			</TABLE>
			<!-- ↑ここにボタンを配置 --></DIV>

			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="800" valign="top"><!-- ↓タブ間共有テーブル↓ -->
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="190"><!--年度 --> <h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Xrh00402T01.xrh00402.propNendo.labelName}"
										style="#{pc_Xrh00402T01.xrh00402.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNendo" size="4" tabindex="1"
										value="#{pc_Xrh00402T01.xrh00402.propNendo.dateValue}"
										readonly="#{pc_Xrh00402T01.xrh00402.propNendo.readonly}"
										disabled="#{pc_Xrh00402T01.xrh00402.propNendo.disabled}"
										style="#{pc_Xrh00402T01.xrh00402.propNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TD width="100" align="right" rowspan="2"
										style="background-color: transparent; text-align: right"
										class="clear_border"><hx:commandExButton type="submit"
										value="#{pc_Xrh00402T01.xrh00402.propSelect.caption}"
										styleClass="commandExButton" id="select" tabindex="4"
										disabled="#{pc_Xrh00402T01.xrh00402.propSelect.disabled}"
										rendered="#{pc_Xrh00402T01.xrh00402.propSelect.rendered}"
										style="#{pc_Xrh00402T01.xrh00402.propSelect.style}"
										action="#{pc_Xrh00402T01.xrh00402.doSelectAction}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="#{pc_Xrh00402T01.xrh00402.propUnSelect.caption}"
										styleClass="commandExButton" id="unselect" tabindex="5"
										disabled="#{pc_Xrh00402T01.xrh00402.propUnSelect.disabled}"
										rendered="#{pc_Xrh00402T01.xrh00402.propUnSelect.rendered}"
										style="#{pc_Xrh00402T01.xrh00402.propUnSelect.style}"
										action="#{pc_Xrh00402T01.xrh00402.doUnSelectAction}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenKaisu"
										style="#{pc_Xrh00402T01.xrh00402.propSikenKaisu.labelStyle}"
										value="#{pc_Xrh00402T01.xrh00402.propSikenKaisu.labelName}">
									</h:outputText></TH>
									<TD width="200"><h:inputText id="htmlSikenKaisu"
										styleClass="inputText" size="4" tabindex="2"
										value="#{pc_Xrh00402T01.xrh00402.propSikenKaisu.integerValue}"
										readonly="#{pc_Xrh00402T01.xrh00402.propSikenKaisu.readonly}"
										disabled="#{pc_Xrh00402T01.xrh00402.propSikenKaisu.disabled}"
										style="#{pc_Xrh00402T01.xrh00402.propSikenKaisu.style}">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenbiYoubi"
										style="#{pc_Xrh00402T01.xrh00402.propSikenbiYoubi.labelStyle}"
										value="#{pc_Xrh00402T01.xrh00402.propSikenbiYoubi.labelName}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSikenbiYoubi" tabindex="3"
										value="#{pc_Xrh00402T01.xrh00402.propSikenbiYoubi.stringValue}"
										style="#{pc_Xrh00402T01.xrh00402.propSikenbiYoubi.style};width:128px"
										disabled="#{pc_Xrh00402T01.xrh00402.propSikenbiYoubi.disabled}">
										<f:selectItems
											value="#{pc_Xrh00402T01.xrh00402.propSikenbiYoubi.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						<!-- ↑タブ間共有テーブル↑ --> <BR>
						<TABLE border="0" cellpadding="20" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="800" align="left">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD class="tab_head_on"><hx:commandExButton type="button"
													value="日程・時間割" id="moveNiteiJikanTab" tabindex="6"
													disabled="#{pc_Xrh00402T01.xrh00402.propMoveNiteiJikanTab.disabled}"
													styleClass="tab_head_on">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="場所情報" styleClass="tab_head_off" id="moveLocateTab"
													tabindex="7"
													disabled="#{pc_Xrh00402T01.xrh00402.propMoveLocateTab.disabled}"
													action="Xrh00402T02">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="試験監督" styleClass="tab_head_off" tabindex="8"
													disabled="#{pc_Xrh00402T01.xrh00402.propMoveSikenKantokuTab.disabled}"
													id="moveSikenKantokuTab" action="Xrh00402T03">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="作問情報" styleClass="tab_head_off" id="moveSakumonTab"
													tabindex="9"
													disabled="#{pc_Xrh00402T01.xrh00402.propMoveSakumonTab.disabled}"
													action="Xrh00402T04">
												</hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD valign="top">
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="100%">
										<TBODY>
											<TR>
												<TD>
												<CENTER><BR>
												<TABLE width="600" border="0" cellpadding="0"
													cellspacing="0" class="table">
													<TBODY>
														<TR>
															<TD width="300" align="left" colspan="4"
																style="background-color: transparent; text-align:left"
																class="clear_border"><h:outputText
																styleClass="outputText" id="lblNitei" value="日程">
															</h:outputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="100"><!--試験名称 --> <h:outputText
																styleClass="outputText" id="lblSikenName"
																value="#{pc_Xrh00402T01.propSikenName.labelName}"
																style="#{pc_Xrh00402T01.propSikenName.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSikenName" size="40" tabindex="11"
																onchange="onChangeData();"
																value="#{pc_Xrh00402T01.propSikenName.stringValue}"
																disabled="#{pc_Xrh00402T01.propSikenName.disabled}"
																maxlength="#{pc_Xrh00402T01.propSikenName.maxLength}"
																style="#{pc_Xrh00402T01.propSikenName.style}">
															</h:inputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="100"><!--試験日 --> <h:outputText
																styleClass="outputText" id="lblSikenbi"
																value="#{pc_Xrh00402T01.propSikenbi.labelName}"
																style="#{pc_Xrh00402T01.propSikenbi.labelStyle}">
															</h:outputText></TH>
															<TD><h:inputText styleClass="inputText" id="htmlSikenbi"
																tabindex="12" onchange="onChangeData();"
																value="#{pc_Xrh00402T01.propSikenbi.dateValue}"
																disabled="#{pc_Xrh00402T01.propSikenbi.disabled}"
																style="#{pc_Xrh00402T01.propSikenbi.style}">
																<f:convertDateTime pattern="yyyy/MM/dd" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
																<hx:inputHelperDatePicker />
															</h:inputText></TD>
															<TH nowrap class="v_a" width="160"><!--レポート提出締切日 --> <h:outputText
																styleClass="outputText" id="lblSimebi"
																value="#{pc_Xrh00402T01.propSimebi.labelName}"
																style="#{pc_Xrh00402T01.propSimebi.labelStyle}">
															</h:outputText></TH>
															<TD><h:inputText styleClass="inputText" id="htmlSimebi"
																tabindex="13" onchange="onChangeData();"
																value="#{pc_Xrh00402T01.propSimebi.dateValue}"
																disabled="#{pc_Xrh00402T01.propSimebi.disabled}"
																style="#{pc_Xrh00402T01.propSimebi.style}">
																<f:convertDateTime pattern="yyyy/MM/dd" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
																<hx:inputHelperDatePicker />
															</h:inputText></TD>
														</TR>
														<TR>
															<TH class="v_a" width="100"><h:outputText
																styleClass="outputText" id="lblSikenKubun"
																style="#{pc_Xrh00402T01.propSikenKubun.labelStyle}"
																value="#{pc_Xrh00402T01.propSikenKubun.labelName}">
															</h:outputText></TH>
															<TD><h:selectOneMenu styleClass="selectOneMenu"
																id="htmlSikenKubun" tabindex="14"
																onchange="onChangeData();"
																value="#{pc_Xrh00402T01.propSikenKubun.stringValue}"
																style="#{pc_Xrh00402T01.propSikenKubun.style};width:180px"
																disabled="#{pc_Xrh00402T01.propSikenKubun.disabled}">
																<f:selectItems
																	value="#{pc_Xrh00402T01.propSikenKubun.list}" />
															</h:selectOneMenu></TD>
															<TH class="v_a" width="100"><h:outputText
																styleClass="outputText" id="lblTargetGakusei"
																style="#{pc_Xrh00402T01.propTargetGakusei.labelStyle}"
																value="#{pc_Xrh00402T01.propTargetGakusei.labelName}">
															</h:outputText></TH>
															<TD><h:selectOneMenu styleClass="selectOneMenu"
																id="htmlTargetGakusei" tabindex="15"
																onchange="onChangeData();"
																value="#{pc_Xrh00402T01.propTargetGakusei.stringValue}"
																style="#{pc_Xrh00402T01.propTargetGakusei.style};width:180px"
																disabled="#{pc_Xrh00402T01.propTargetGakusei.disabled}">
																<f:selectItems
																	value="#{pc_Xrh00402T01.propTargetGakusei.list}" />
															</h:selectOneMenu></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="190"><!--時間割パターン --> <h:outputText
																styleClass="outputText" id="lblJikanwari"
																value="#{pc_Xrh00402T01.propJikanwari.labelName}"
																style="#{pc_Xrh00402T01.propJikanwari.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlJikanwari"
																tabindex="16" onchange="onChangeData();"
																value="#{pc_Xrh00402T01.propJikanwari.stringValue}"
																style="#{pc_Xrh00402T01.propJikanwari.style};width:256px"
																disabled="#{pc_Xrh00402T01.propJikanwari.disabled}">
																<f:selectItems
																	value="#{pc_Xrh00402T01.propJikanwari.list}" />
															</h:selectOneMenu> <hx:commandExButton type="submit"
																value="#{pc_Xrh00402T01.propSelectJikanwari.caption}"
																styleClass="commandExButton" id="selectJikanwari"
																tabindex="17"
																disabled="#{pc_Xrh00402T01.propSelectJikanwari.disabled}"
																rendered="#{pc_Xrh00402T01.propSelectJikanwari.rendered}"
																style="#{pc_Xrh00402T01.propSelectJikanwari.style}"
																action="#{pc_Xrh00402T01.doSelectAction}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																value="#{pc_Xrh00402T01.propUnSelectJikanwari.caption}"
																styleClass="commandExButton" id="unselectJikanwari"
																tabindex="18"
																disabled="#{pc_Xrh00402T01.propUnSelectJikanwari.disabled}"
																rendered="#{pc_Xrh00402T01.propUnSelectJikanwari.rendered}"
																style="#{pc_Xrh00402T01.propUnSelectJikanwari.style}"
																action="#{pc_Xrh00402T01.doUnSelectAction}">
															</hx:commandExButton></TD>
														</TR>

													</TBODY>
												</TABLE>

												<BR>

												<TABLE border="0" cellpadding="5">
													<TBODY>
														<TR>
															<TD>
															<DIV id="listScroll" class="listScroll"
																style="height:256px;"><h:dataTable
																columnClasses="columnClass" headerClass="headerClass"
																footerClass="footerClass"
																rowClasses="#{pc_Xrh00402T01.propJikanwariList.rowClasses}"
																styleClass="meisai_scroll" id="htmlJikanwariList"
																value="#{pc_Xrh00402T01.propJikanwariList.list}"
																var="varlist">

																<h:column id="column1">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="時限"
																			id="lblListJigenColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.jigen.integerValue}"
																		id="htmlListJigen">
																	</h:outputText>
																	<f:attribute value="32" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>


																<h:column id="column2">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="開始時刻"
																			id="lblListKaisiJikokuColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.startTime.stringValue}"
																		id="htmlListKaisiJikoku">
																	</h:outputText>
																	<f:attribute value="100" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>


																<h:column id="column3">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="終了時刻"
																			id="lblListSyuryoJikokuColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.endTime.stringValue}"
																		id="htmlListSyuryoJikoku">
																	</h:outputText>
																	<f:attribute value="100" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>

															</h:dataTable></DIV>
															</TD>
														</TR>
													</TBODY>
												</TABLE>

												<BR>

												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="button_bar">
													<TBODY>
														<TR>
															<TD><hx:commandExButton type="submit"
																styleClass="commandExButton_dat" id="register"
																confirm="#{msg.SY_MSG_0002W}" tabindex="20"
																action="#{pc_Xrh00402T01.doRegisterAction}" value="確定"
																disabled="#{pc_Xrh00402T01.propRegister.disabled}">
															</hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>


												</CENTER>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<h:inputHidden id="htmlHidChangeDataFlg"
				value="#{pc_Xrh00402T01.propHidChangeDataFlg.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlConfilm"
				value="#{pc_Xrh00402T01.propConfilm.stringValue}"></h:inputHidden>
			<h:inputHidden id="htmlAction"
				value="#{pc_Xrh00402T01.propAction.stringValue}"></h:inputHidden>
			</DIV>

			</DIV>

		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
