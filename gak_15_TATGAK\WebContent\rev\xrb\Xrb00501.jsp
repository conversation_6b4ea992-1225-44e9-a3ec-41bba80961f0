<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00501.jsp</TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	
<SCRIPT type="text/javascript">
// 画面ロード時の学生名称再取得
function loadAction(event){
  doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:htmlGakuseiName');
}
function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	var servlet = "rev/co/CobGakseiAJAX";
 	var args = new Array();
 	args['code1'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

function openSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
				+ "?retFieldName=" + field1;
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return false;
}

function confirmOk() {
	document.getElementById('form1:htmlListCountDialogFlag').value = "1";
	indirectClick('htmlSearchButton');
}

function confirmCancel() {
	document.getElementById('form1:htmlListCountDialogFlag').value = "0";
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00501.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
styleClass="commandExButton" id="closeDisp"
action="#{pc_Xrb00501.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00501.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00501.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
 <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
  <TBODY>
   <TR>
    <TD>
     <TABLE class="table" width="870">
      <TBODY>
       <TR align="center" valign="middle">
        <TH nowrap class="v_a" width="160">
		 <!-- 申請日 -->
		 <h:outputText styleClass="outputText" id="lblSinseiDate" 
		  value="#{pc_Xrb00501.propSinseiDateFrom.labelName}">
		 </h:outputText>
	    </TH>
		<TD width="275">
		 <!-- 申請日FROM -->
		 <h:inputText
		  styleClass="inputText"
		  id="htmlSinseiDateFrom"
		  size="10"
		  disabled="#{pc_Xrb00501.propSinseiDateFrom.disabled}"
		  value="#{pc_Xrb00501.propSinseiDateFrom.dateValue}"
		  style="#{pc_Xrb00501.propSinseiDateFrom.style}"
		  readonly="#{pc_Xrb00501.propSinseiDateFrom.readonly}">
		  <f:convertDateTime />
		  <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
		  <hx:inputHelperDatePicker />
		 </h:inputText>
		 ～
		 <!-- 申請日TO -->
		 <h:inputText
		  styleClass="inputText"
		  id="htmlSinseiDateTo"
		  size="10"
		  disabled="#{pc_Xrb00501.propSinseiDateTo.disabled}"
		  value="#{pc_Xrb00501.propSinseiDateTo.dateValue}"
		  style="#{pc_Xrb00501.propSinseiDateTo.style}"
		  readonly="#{pc_Xrb00501.propSinseiDateTo.readonly}">
		  <f:convertDateTime />
		  <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
		  <hx:inputHelperDatePicker />
		 </h:inputText>
		</TD>
		<TH nowrap class="v_a" width="160">
		 <!-- 学籍番号 -->
		 <h:outputText styleClass="outputText" id="lblGakusekiCd"
          value="#{pc_Xrb00501.propGakusekiCode.labelName}"
          style="#{pc_Xrb00501.propGakusekiCode.labelStyle}">
         </h:outputText>
		</TH>
		<TD width="275">
		 <h:inputText
		  styleClass="inputText"
          id="htmlGakusekiCd" size="10"
          maxlength="#{pc_Xrb00501.propGakusekiCode.maxLength}"
          disabled="#{pc_Xrb00501.propGakusekiCode.disabled}"
          value="#{pc_Xrb00501.propGakusekiCode.stringValue}"
          style="#{pc_Xrb00501.propGakusekiCode.style}"
          readonly="#{pc_Xrb00501.propGakusekiCode.readonly}"
          onblur="return doGakuseiAjax(this, event, 'form1:htmlGakuseiName');">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlGakusekiCdSearchButton"
          disabled="#{pc_Xrb00501.propGakusekiCode.disabled}"
          onclick="openSubWindow('form1:htmlGakusekiCd');">
         </hx:commandExButton>
         <h:inputText
          styleClass="likeOutput"
          id="htmlGakuseiName"
          size="20"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00501.propGakuseiName.stringValue}">
         </h:inputText>
		</TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
		 <!-- 変更区分 -->
		 <h:outputText styleClass="outputText" id="lblHenkoKbn"
          value="#{pc_Xrb00501.propHenkoKbn.labelName}"
          style="#{pc_Xrb00501.propHenkoKbn.labelStyle}">
         </h:outputText>
		</TH>
		<TD>
		 <h:selectOneMenu
          id="htmlHenkoKbn"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00501.propHenkoKbn.value}"
		  disabled="#{pc_Xrb00501.propHenkoKbn.disabled}"
          style="#{pc_Xrb00501.propHenkoKbn.style}"
          readonly="#{pc_Xrb00501.propHenkoKbn.readonly}">
          <f:selectItems value="#{pc_Xrb00501.propHenkoKbn.list}" />
         </h:selectOneMenu>
		</TD>
		<TH nowrap class="v_a">
		 <!-- 申請状態 -->
		 <h:outputText styleClass="outputText" id="lblSinseiJotai"
          value="#{pc_Xrb00501.propSinseiJotai.labelName}"
          style="#{pc_Xrb00501.propSinseiJotai.labelStyle}">
         </h:outputText>
		</TH>
		<TD>
		 <h:selectOneMenu
          id="htmlSinseiJotai"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00501.propSinseiJotai.value}"
		  disabled="#{pc_Xrb00501.propSinseiJotai.disabled}"
          style="#{pc_Xrb00501.propSinseiJotai.style}"
          readonly="#{pc_Xrb00501.propSinseiJotai.readonly}">
          <f:selectItems value="#{pc_Xrb00501.propSinseiJotai.list}" />
         </h:selectOneMenu>
		</TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE width="830">
      <TBODY>
       <TR>
        <TD>
         <hx:commandExButton
          type="submit"
          value="検索"
          styleClass="commandExButton_dat"
          id="htmlSearchButton"
          disabled="#{pc_Xrb00501.propSearchButton.disabled}"
          action="#{pc_Xrb00501.doSearchAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="クリア"
          styleClass="commandExButton_dat"
          id="htmlClearButton"
          disabled="#{pc_Xrb00501.propClearButton.disabled}"
          action="#{pc_Xrb00501.doClearAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="新規"
          styleClass="commandExButton_dat"
          id="htmlNewButton"
          disabled="#{pc_Xrb00501.propNewButton.disabled}"
          action="#{pc_Xrb00501.doRegisterAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE border="0" cellpadding="0" cellspacing="0" width="870">
	  <TBODY>
	   <TR>
		<TD align="right" nowrap class="outputText" width="100%">
		 <h:outputText
	      styleClass="outputText"
	      id="lblSinseiCount"
	      value="#{pc_Xrb00501.propSinseiList.listCount}">
	     </h:outputText>件
	    </TD>
	   </TR>
	   <TR>
		<TD>
		 <div class="listScroll" style="height: 300px">
          <h:dataTable
           border="1"
           cellpadding="2"
           cellspacing="0"
           headerClass="headerClass"
           footerClass="footerClass"
           columnClasses="columnClass1"
           rowClasses="#{pc_Xrb00501.propSinseiList.rowClasses}"
           styleClass="meisai_scroll"
           id="htmlSisneiList"
           var="varlist"
           value="#{pc_Xrb00501.propSinseiList.list}">
           <h:column id="column1">
            <f:facet name="header">
             <h:outputText id="lblSinseiDate_head"
              styleClass="outputText" value="申請日" escape="false">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblSinseiDate_list" value="#{varlist.propSinseiDate.value}">
            </h:outputText>
            <f:attribute value="70" name="width" />
           </h:column>
           <h:column id="column2">
            <f:facet name="header">
             <h:outputText id="lblSinseiNo_head"
              styleClass="outputText" value="申請NO">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblSinseiNo_list" value="#{varlist.propSinseiNo.value}">
            </h:outputText>
            <f:attribute value="110" name="width" />
           </h:column>
           <h:column id="column3">
            <f:facet name="header">
             <h:outputText id="lblGakusekiCode_head"
              styleClass="outputText" value="学籍番号">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblGakusekiCode_list" value="#{varlist.propGakusekiCode.value}">
            </h:outputText>
            <f:attribute value="110" name="width" />
           </h:column>
           <h:column id="column4">
            <f:facet name="header">
             <h:outputText id="lblGakuseiName_head"
              styleClass="outputText" value="学生氏名">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblGakuseiName_list" value="#{varlist.propGakuseiName.value}">
            </h:outputText>
            <f:attribute value="160" name="width" />
           </h:column>
           <h:column id="column5">
            <f:facet name="header">
             <h:outputText id="lblHenkoKbn_head"
              styleClass="outputText" value="変更区分">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblHenkoKbn_list" value="#{varlist.propHenkoKbn.value}">
            </h:outputText>
            <f:attribute value="110" name="width" />
           </h:column>
           <h:column id="column6">
            <f:facet name="header">
             <h:outputText id="lblSinseiJotai_head"
              styleClass="outputText" value="申請状態">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblSinseiJotai_list" value="#{varlist.propSinseiJotai.value}">
            </h:outputText>
            <f:attribute value="130" name="width" />
           </h:column>
           <h:column id="column7">
            <f:facet name="header">
             <h:outputText id="lblHurikomiIRaininCode_head"
              styleClass="outputText" value="振込依頼人コード">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblHurikomiIRaininCode_list" value="#{varlist.propHurikomiIraininCode.value}">
            </h:outputText>
            <f:attribute value="160" name="width" />
           </h:column>
           <h:column id="column8">
            <hx:commandExButton type="submit" value="選択"
             styleClass="commandExButton" id="htmlSelectButton"
             rendered="#{varlist.rendered}"
             style="width: 40px"
             action="#{pc_Xrb00501.doSelectAction}">
            </hx:commandExButton>
            <f:facet name="header">
            <h:outputText
              styleClass="outputText" value="<br>&nbsp;" escape="false">
             </h:outputText>
            </f:facet>
            <f:attribute value="40" name="width" />
           </h:column>
          </h:dataTable>
		 </div>
		</TD>
	   </TR>
      </TBODY>
     </TABLE>
    </TD>
   </TR>
  </TBODY>
 </TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden value="#{pc_Xrb00501.propListCountDialogFlag.integerValue}" id="htmlListCountDialogFlag">
 <f:convertNumber />
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

