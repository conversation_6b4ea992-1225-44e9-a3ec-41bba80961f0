<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrl00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
	// 教員検索画面（引数：なし）
	function openKyoinSubWindow() {
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlJinjiCd";
		openModalWindow(url, "PCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
		return true;
	}

	// 教員名称を取得する
	function doKyoinAjax(thisObj, thisEvent) {
		var servlet = "rev/co/CobJinjAJAX";
		var args = new Array();
		args['code'] = thisObj.value;
		var target = "form1:htmlJinjiName"
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
	
	// 教員名称を取得する
	function doKyoinAjax() {
		var servlet = "rev/co/CobJinjAJAX";
		var args = new Array();
		args['code'] = document.getElementById('form1:htmlJinjiCd').value;
		var target = "form1:htmlJinjiName"
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="doKyoinAjax();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00801.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrl00801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00801.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="700" border="0">
				<TBODY>
					<TR>
						<TD width="700">
						<CENTER><TABLE class="table" width="500">
							<TBODY>
								<TR height="50">
								</TR>
								<TR>
									<TH class="v_a" width="200">
										<h:outputText styleClass="outputText" id="lblTeateNendo"
											 value="#{pc_Xrl00801.propTeateNendo.labelName}" 
											 style="#{pc_Xrl00801.propTeateNendo.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="150"><h:inputText styleClass="inputText"
										id="htmlTeateNendo"
										value="#{pc_Xrl00801.propTeateNendo.dateValue}"
										style="#{pc_Xrl00801.propTeateNendo.style}"
										maxlength="#{pc_Xrl00801.propTeateNendo.maxLength}" size="18">
										<hx:inputHelperAssist errorClass="inputText_Error"
						    			imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_a" width="150">
										<h:outputText
											styleClass="outputText" 
											id="lblJinjiCd"
											style="#{pc_Xrl00801.propJinjiCd.labelStyle}"
											value="#{pc_Xrl00801.propJinjiCd.labelName}">
										</h:outputText>
									</TH>
									<TD nowrap width="200">
										<h:inputText 
											styleClass="inputText"
											id="htmlJinjiCd" 
											size="10"
											maxlength="#{pc_Xrl00801.propJinjiCd.maxLength}"
											value="#{pc_Xrl00801.propJinjiCd.stringValue}"
											onblur="return doKyoinAjax(this, event);">
											<hx:inputHelperAssist imeMode="inactive" errorClass="inputText_Error" />
										</h:inputText> 
										<hx:commandExButton 
											type="button" 
											styleClass="commandExButton_search" 
											id="search"
											onclick="openKyoinSubWindow();">
										</hx:commandExButton>
										<h:outputText 
											styleClass="outputText" 
											value="#{pc_Xrl00801.propJinjiNm.stringValue}"
											id="htmlJinjiName">
										</h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_a" align="left"><h:outputText
										styleClass="outputText" id="lblTeateSakuseiDate"
										value="#{pc_Xrl00801.propTeateSakuseiDateFrom.name}"></h:outputText>
									</TH>
									<TD valign="middle" align="left">
										<h:inputText
											styleClass="inputText" id="htmlTeateSakuseiDateFrom"
											value="#{pc_Xrl00801.propTeateSakuseiDateFrom.dateValue}"
											size="12" >
											<hx:inputHelperDatePicker />
											<f:convertDateTime />
											<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
										</h:inputText>&nbsp～&nbsp
										<h:inputText styleClass="inputText"
											id="htmlTeateSakuseiDateTo"
											value="#{pc_Xrl00801.propTeateSakuseiDateTo.dateValue}"
											size="12">
											<f:convertDateTime />
											<hx:inputHelperDatePicker />
											<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR>
							</TBODY>
						</TABLE></CENTER>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%" class="button_bar" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR height="20px" style="border: medium none;">
					<TR>
					<TR>
						<TD width="100%">
							<hx:commandExButton type="submit" value="検索"
								styleClass="commandExButton_out" id="select"
								action="#{pc_Xrl00801.doSelectAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

