<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00106.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<SCRIPT type="text/javascript" language="JavaScript">
var schSbtCd = "";
function getSchoolingSbtCb() {
	// スクーリング種別コンボボックス取得AJAX
	var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
	var args = new Array();
	args['nendo'] = document.getElementById('form1:Xrx00106:htmlNendo').value;
	var target = "";
	
	comb = document.getElementById('form1:Xrx00106:htmlSchooling');
	schSbtCd = comb.options[comb.selectedIndex].value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
}

function callBackMethod(value){
	var comb = document.getElementById('form1:Xrx00106:htmlSchooling');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
}
</SCRIPT>
<f:subview id="Xrx00106">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrx00106.onPageLoadBegin}">
		<%-- ↓ コンテンツ部 ↓ --%>
		<hx:jspPanel>
			<DIV style="width:870px">
			<TABLE class="table" border="0" cellpadding="5" width="870">
				<TBODY>
					<TR align="center" valign="middle">
						<TH nowrap class="v_a" width="20%"><h:outputText
							styleClass="outputText" id="lblNendo" value="対象年度"
							style="#{pc_Xrx00106.propNendo.labelStyle}">
						</h:outputText></TH>
						<TD width="*"><h:inputText styleClass="inputText" id="htmlNendo"
							size="6" value="#{pc_Xrx00106.propNendo.dateValue}"
							style="#{pc_Xrx00106.propNendo.style}"
							onblur="getSchoolingSbtCb();">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
					</TR>
					<TR align="center" valign="middle">
						<TH nowrap class="v_a" width="20%"><h:outputText
							styleClass="outputText" id="lblSchooling" value="スクーリング種別"
							style="#{pc_Xrx00106.propSchooling.labelStyle}">
						</h:outputText></TH>
						<TD width="*"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSchooling" value="#{pc_Xrx00106.propSchooling.value}"
							style="width:400px;">
							<f:selectItems value="#{pc_Xrx00106.propSchooling.list}" />
						</h:selectOneMenu></TD>
					</TR>
				</TBODY>
			</TABLE>
			<hx:commandExButton type="submit" value="検索"
				styleClass="commandExButton_dat" id="search"
				action="#{pc_Xrx00106.doSearchAction}" style="margin-top: 10px;">
			</hx:commandExButton> <BR>
			<h:dataTable border="0" cellpadding="0" cellspacing="0"
				id="htmlSchoolingList" 
				value="#{pc_Xrx00106.propSchoolingList.list}" var="varlist">
				<h:column id="column1">
					<h:panelGrid columns="1" border="0" style="margin-top: 10px; text-align: left;" width="100%">
						<h:outputText styleClass="outputText" id="htmlSchoolingSbt"
							escape="false"
							value="スクーリング種別　#{varlist.schoolingSbtCd}：#{varlist.schoolingSbtNm}">
						</h:outputText>
					</h:panelGrid>
					<h:dataTable border="1" cellpadding="2" cellspacing="0"
						headerClass="headerClass" footerClass="footerClass"
						columnClasses="columnClass1" 
						rowClasses="#{varlist.propJugyoList.rowClasses}"
						styleClass="meisai_scroll" id="htmlJugyoList" width="870px"
						value="#{varlist.propJugyoList.list}" var="jugyolist">
						<h:column id="column11">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="期" id="lblKi">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlKi" styleClass="outputText"
								value="#{jugyolist.ki}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="40" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column12">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="時限" id="lblJigen">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlJigen" styleClass="outputText"
								value="#{jugyolist.jigen}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="39" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column13">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="科目コード"
									id="lblKamoku">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlKamoku" styleClass="outputText"
								value="#{jugyolist.kamoku}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="68" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column14">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="授業コード"
									id="lblJugyo">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlJugyo" styleClass="outputText"
								value="#{jugyolist.jugyo.displayValue}"
								title="#{jugyolist.jugyo.value}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="text-align: left" name="style" />
						</h:column>
						<h:column id="column15">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="クラス" id="lblClas">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlClas" styleClass="outputText"
								value="#{jugyolist.clas}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="40" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column16">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="評価" id="lblHyoka">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlHyoka" styleClass="outputText"
								value="#{jugyolist.hyoka}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="35" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column17">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="無効理由"
									id="lblMukoriyu">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlMukoriyu" styleClass="outputText"
								value="#{jugyolist.mukoriyu.displayValue}"
								title="#{jugyolist.mukoriyu.value}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="220" name="width" />
							<f:attribute value="text-align: left" name="style" />
						</h:column>
						<h:column id="column18">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="申込方法"
									id="lblHouhou">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlHouhou" styleClass="outputText"
								value="#{jugyolist.houhou}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="68" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column19">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="申込日時" id="lblDate">
								</h:outputText>
							</f:facet>
							<h:outputText id="htmlDate" styleClass="outputText"
								value="#{jugyolist.date}">
							</h:outputText>
							<f:attribute value="true" name="nowrap" />
							<f:attribute value="110" name="width" />
							<f:attribute value="text-align: center" name="style" />
						</h:column>
					</h:dataTable>
				</h:column>
			</h:dataTable></DIV>
		</hx:jspPanel>
		<BR>
		<%-- ↑ コンテンツ部 ↑ --%>
	</hx:scriptCollector>
</f:subview>
