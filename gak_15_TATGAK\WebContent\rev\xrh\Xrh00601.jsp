<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	function func_1(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlKyouinCd";
		 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	}
	
	function getJinjiName(thisObj, thisEvent) {
		// 教員名称を取得する
  		var servlet = "rev/co/CobJinjAJAX";
  		var target = "form1:lblKyouinNm";
  		getCodeName(servlet, target, thisObj.value);
 	}
 	
 	function reloadPage(event){
 		getJinjiName(document.getElementById("form1:htmlKyouinCd"), event);
 	}
 	
 	function confirmOk(){
 		if(document.getElementById("form1:propExecutable").value == 1){
 			indirectClick("release");
 		}else if(document.getElementById("form1:propExecutable").value == 2){
 			indirectClick("btnReg");
 		}else if(document.getElementById("form1:propExecutable").value == 3){
 			indirectClick("select");
 		}
 	}
 	
 	function confirmCancel(){
 	
 		document.getElementById("form1:propExecutable").value = 0;
 		
 	}
 	
 	//チェックボックスAll On
	function fncCheckAll(thisObj, thisEvent) {
		check('htmlSakumonList','htmlListCheck');
	}

	//チェックボックスAll Off
	function fncCheckNone(thisObj, thisEvent) {
		uncheck('htmlSakumonList','htmlListCheck');
	}
 	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="reloadPage(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00601.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00601.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" >
						<TBODY>
							<TR>
							<TD>
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblNendo"
												value="#{pc_Xrh00601.propNendo.labelName}" style="#{pc_Xrh00601.propNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="180">
											<h:inputText id="htmlNendo" styleClass="inputText" 
												readonly="#{pc_Xrh00601.propNendo.readonly}" 
												style="#{pc_Xrh00601.propNendo.style}" 
												value="#{pc_Xrh00601.propNendo.dateValue}"
												disabled="#{pc_Xrh00601.propNendo.disabled}" size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												id="lblKaisu"
												value="#{pc_Xrh00601.propCountFrom.labelName}" style="#{pc_Xrh00601.propCountFrom.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="200">
											<h:inputText id="htmlCountFrom" styleClass="inputText" 
												readonly="#{pc_Xrh00601.propCountFrom.readonly}" 
												value="#{pc_Xrh00601.propCountFrom.integerValue}"
												disabled="#{pc_Xrh00601.propCountFrom.disabled}" size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
								    			imeMode="inactive" promptCharacter="_" />
												<f:convertNumber type="number" pattern="#0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText>
											<h:outputText value="回 ～ " />
											<h:inputText id="htmlCountTo" styleClass="inputText" 
												readonly="#{pc_Xrh00601.propCountTo.readonly}" 
												value="#{pc_Xrh00601.propCountTo.integerValue}"
												disabled="#{pc_Xrh00601.propCountTo.disabled}" size="4">
												<hx:inputHelperAssist errorClass="inputText_Error"
								    			imeMode="inactive" promptCharacter="_" />
												<f:convertNumber type="number" pattern="#0"/>
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
											</h:inputText>
											<h:outputText value="回" />
										</TD>
										<TD rowspan="5" width="100" align="right"
											style="background-color: transparent; text-align: right"
											class="clear_border">
											<hx:commandExButton type="submit"
												value="検索"
												styleClass="commandExButton" id="select"
												disabled="#{pc_Xrh00601.propKensaku.disabled}"
												rendered="#{pc_Xrh00601.propKensaku.rendered}"
												style="#{pc_Xrh00601.propKensaku.style}"
												action="#{pc_Xrh00601.doSearchAction}">
											</hx:commandExButton> 
											<hx:commandExButton type="submit"
												value="解除"
												styleClass="commandExButton" id="release"
												disabled="#{pc_Xrh00601.propRelease.disabled}"
												rendered="#{pc_Xrh00601.propRelease.rendered}"
												style="#{pc_Xrh00601.propRelease.style}"
												action="#{pc_Xrh00601.doReleaseAction}">
											</hx:commandExButton>
										</TD>
									</TR>
									<TR>
						            	<TH nowrap class="v_a" width="150">
											<!--教員コード -->
						                	<h:outputText styleClass="outputText" id="lblKyouinCd"
						                		value="#{pc_Xrh00601.propKyouinCd.labelName}"
						                		style="#{pc_Xrh00601.propKyouinCd.labelStyle}">
						                	</h:outputText>
						              	</TH>
						              	<TD colspan="3">
											<h:inputText id="htmlKyouinCd" styleClass="inputText" 
												readonly="#{pc_Xrh00601.propKyouinCd.readonly}" 
												style="#{pc_Xrh00601.propKyouinCd.style}"
												disabled="#{pc_Xrh00601.propKyouinCd.disabled}"
												maxlength="#{pc_Xrh00601.propKyouinCd.maxLength}"
												size="10"
												value="#{pc_Xrh00601.propKyouinCd.stringValue}"
												onblur="return getJinjiName(this, event);">
											</h:inputText>
											<hx:commandExButton type="button"
												styleClass="commandExButton_search" id="searchKyouin"
												disabled="#{pc_Xrh00601.propSearchKyouin.disabled}"
												rendered="#{pc_Xrh00601.propSearchKyouin.rendered}"
												style="#{pc_Xrh00601.propSearchKyouin.style}"
												onclick="return func_1(this, event);">
											</hx:commandExButton>
											<h:outputText
		              							styleClass="outputText"
		              							id="lblKyouinNm"
		              							value="#{pc_Xrh00601.propKyouinNm.stringValue}">
		              						</h:outputText>
		              					</TD>
		              				</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="作問返却日">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:inputText id="htmlReturnFrom"
												styleClass="inputText" size="12"
												value="#{pc_Xrh00601.propReturnFrom.dateValue}"
												disabled="#{pc_Xrh00601.propReturnFrom.disabled}">
												<f:convertDateTime />
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText>
											
											<h:outputText styleClass="outputText" value="～">
											</h:outputText>
											
											<h:inputText id="htmlReturnTo"
												styleClass="inputText" size="12"
												value="#{pc_Xrh00601.propReturnTo.dateValue}"
												disabled="#{pc_Xrh00601.propReturnTo.disabled}">
												<f:convertDateTime />
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText>
										</TD>
									</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="出力対象">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:selectOneRadio
								              	disabledClass="selectOneRadio_Disabled"
								              	styleClass="selectOneRadio" id="htmlOutput"
								              	value="#{pc_Xrh00601.propOutput.stringValue}"
								              	style="#{pc_Xrh00601.propOutput.style}"
								              	disabled="#{pc_Xrh00601.propOutput.disabled}">
								              	<f:selectItem itemValue="0" itemLabel="作問済" />
								              	<f:selectItem itemValue="1" itemLabel="未作問" />
								              	<f:selectItem itemValue="2" itemLabel="全て" />
									        </h:selectOneRadio>
									    </TD>
									</TR>
									
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="ソート条件">
											</h:outputText>
										</TH>
										<TD colspan="3">
											<h:selectOneRadio
								              	disabledClass="selectOneRadio_Disabled"
								              	styleClass="selectOneRadio" id="htmlSort"
								              	value="#{pc_Xrh00601.propSort.stringValue}"
								              	style="#{pc_Xrh00601.propSort.style}"
								              	disabled="#{pc_Xrh00601.propOutput.disabled}">
								              	<f:selectItem itemValue="0" itemLabel="回数" />
								              	<f:selectItem itemValue="1" itemLabel="科目コード" />
								              	<f:selectItem itemValue="2" itemLabel="教員氏名" />
									        </h:selectOneRadio>
									     </TD>
									</TR>
								</TBODY>
								</TABLE>
								
								
								
								<TABLE border="0" style="margin-top:16px">
								<TBODY>
									<TR>
										<TD align="right">
											<h:outputText styleClass="outputText"
												id="lblListCount"
												value="#{pc_Xrh00601.propListCount.stringValue}"
												style="#{pc_Xrh00601.propListCount.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
									<TD>
									<DIV id="listScroll" class="listScroll" style="height: 256px;">
										<h:dataTable 
											columnClasses="columnClass" 
											headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Xrh00601.propSakumonList.rowClasses}"
											styleClass="meisai_scroll" id="htmlSakumonList"
											value="#{pc_Xrh00601.propSakumonList.list}" var="varlist">
											
											<h:column id="column1">
												<f:facet name="header">
												</f:facet>
												<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlListCheck"
													disabled="#{pc_Xrh00601.propListCheck.disabled}"
													readonly="#{pc_Xrh00601.propListCheck.readonly}"
													rendered="#{pc_Xrh00601.propListCheck.rendered}"
													style="#{pc_Xrh00601.propListCheck.style}"
													value="#{varlist.checked}">
												</h:selectBooleanCheckbox>
												<f:attribute value="32" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="回数"
														id="lblListCountColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListCount"
													style="#{pc_Xrh00601.propListCount.style}"
													value="#{varlist.kamokSikenCnt}">
												</h:outputText>
												<f:attribute value="52" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="試験日曜日"
														id="lblListYoubiColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListYoubi"
													style="#{pc_Xrh00601.propListYoubi.style}"
													value="#{varlist.sikenbiNoNm}">
												</h:outputText>
												<f:attribute value="80" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="科目コード"
														id="lblListKamokuCdColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokuCd"
													style="#{pc_Xrh00601.propListKamokuCd.style}"
													value="#{varlist.kamokCd}">
												</h:outputText>
												<f:attribute value="76" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column5">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="科目名"
														id="lblListKamokuNmColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokuNm"
													value="#{varlist.kamokNm.displayValue}"
													title="#{varlist.kamokNm.stringValue}">
												</h:outputText>
												<f:attribute value="256" name="width" />
											</h:column>
											
											<h:column id="column6">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="新旧刊"
														id="lblListSinkyuColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListSinkyu"
													style="#{pc_Xrh00601.propListSinkyu.style}"
													value="#{varlist.sinkyuKbnNm}">
												</h:outputText>
												<f:attribute value="48" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
											
											<h:column id="column7">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="作問教員名"
														id="lblListKyouinNmColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKyouinNm"
													title="#{varlist.sakmonKyosyokinNm.stringValue}"
													value="#{varlist.sakmonKyosyokinNm.displayValue}">
												</h:outputText>
												<f:attribute value="128" name="width" />
												
											</h:column>
											
											<h:column id="column8">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="作問返却日"
														id="lblListReturnDtColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListReturnDt"
													style="#{pc_Xrh00601.propListReturnDt.style}"
													value="#{varlist.sakmonHenkyakDate}">
												</h:outputText>
												<f:attribute value="96" name="width" />
												<f:attribute value="text-align: center" name="style" />
											</h:column>
										</h:dataTable>
									</DIV>
									</TD>
									</TR>
									<TR>
									<TD align="left">
										<hx:commandExButton type="button"
									 		styleClass="check" id="btnCheck"
									 		disabled="#{pc_Xrh00601.propBtnCheck.disabled}"
											onclick="return fncCheckAll(this, event);">
										</hx:commandExButton> 
										<hx:commandExButton
											type="button" styleClass="uncheck" id="btnUnCheck"
											disabled="#{pc_Xrh00601.propBtnUnCheck.disabled}"
											onclick="return fncCheckNone(this, event);">
										</hx:commandExButton>
										<hx:commandExButton type="submit"
											styleClass="commandExButton_dat" id="btnCsv"
											value="CSV作成"
											action="#{pc_Xrh00601.doCsvOutAction}"
											disabled="#{pc_Xrh00601.propCsv.disabled}"
											rendered="#{pc_Xrh00601.propCsv.rendered}"
											style="margin-left:256px;">
										</hx:commandExButton>
										<hx:commandExButton type="submit"
											styleClass="commandExButton_dat" id="btnItem"
											value="出力項目指定"
											action="#{pc_Xrh00601.doSetoutputAction}"
											disabled="#{pc_Xrh00601.propItem.disabled}"
											rendered="#{pc_Xrh00601.propItem.rendered}"
											style="#{pc_Xrh00601.propItem.style}">
										</hx:commandExButton>
									</TD>
									</TR>
								</TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="150">
											<h:outputText styleClass="outputText"
												value="作問返却日">
											</h:outputText>
										</TH>
										<TD width="640">
											<h:inputText id="htmlReturnDt"
												styleClass="inputText" size="12"
												value="#{pc_Xrh00601.propReturnDt.dateValue}"
												disabled="#{pc_Xrh00601.propReturnDt.disabled}">
												<f:convertDateTime />
												<hx:inputHelperAssist errorClass="inputText_Error"
													promptCharacter="_" />
												<hx:inputHelperDatePicker />
											</h:inputText>
										</TD>
									</TR>
								</TBODY>
								</TABLE>
								
								<BR>
								
								<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
									<TBODY>
										<TR>
											<TD>
												<hx:commandExButton type="submit"
													styleClass="commandExButton_dat" id="btnReg"
													value="登録"
													action="#{pc_Xrh00601.doRegisterAction}"
													disabled="#{pc_Xrh00601.propReg.disabled}"
													rendered="#{pc_Xrh00601.propReg.rendered}"
													style="#{pc_Xrh00601.propReg.style}">
												</hx:commandExButton>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
							</TR>
						</TBODY>
						</TABLE>
						
				</DIV>
			</DIV>
			
		</DIV>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		<h:inputHidden
				value="#{pc_Xrh00601.propExecutable.integerValue}"
				id="propExecutable">
		</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
				