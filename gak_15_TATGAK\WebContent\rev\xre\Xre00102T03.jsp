<%-- 
	介護等体験登録（日誌）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xre/Xre00102T03.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">


function loadAction(event){
// 画面ロード時の学生氏名再取得
  doGakuseiAjax(document.getElementById('form1:htmlGaksekiCd'), event, 'form1:htmlGakseiName');
// 画面ロード時の教員氏名再取得
  doKyoinAjax(document.getElementById('form1:htmlNissiKyoinCd'), event, 'form1:htmlKyoinName');
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  if (confirm(messageCreate(id, args))) {
  	  onChangeData();
  	  return true;
  }
  return false;
}

// 解除ボタン押下時処理
function onClickUnselect(id) {
	var changeDataFlgT01 = document.getElementById("form1:htmlHidChangeDataFlgT01").value;
	if(changeDataFlgT01 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT02 = document.getElementById("form1:htmlHidChangeDataFlgT02").value;
	if(changeDataFlgT02 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT03 = document.getElementById("form1:htmlHidChangeDataFlgT03").value;
	if(changeDataFlgT03 == "1"){
	  return confirm(id);
	}
	return true;
}

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlgT01 = document.getElementById("form1:htmlHidChangeDataFlgT01").value;
	if(changeDataFlgT01 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT02 = document.getElementById("form1:htmlHidChangeDataFlgT02").value;
	if(changeDataFlgT02 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT03 = document.getElementById("form1:htmlHidChangeDataFlgT03").value;
	if(changeDataFlgT03 == "1"){
	  return confirm(id);
	}
	return true;
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlgT03").value = "1";
}


// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById("form1:htmlHidButtonKbnAuth").value = "0";
	document.getElementById("form1:htmlHidKengenCheckFlg").value = "0";
}


function openSubWindow(thisObj, thisEvent) {
	// 学生検索子画面（引数：なし）
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGaksekiCd";
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return true;
}


// 学生氏名を取得する
function doGakuseiAjax(thisObj, thisEven){
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;
    args['code2'] = "";
    args['code3'] = "";
    var target = "form1:htmlGakseiName";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// 教員検索画面（引数：なし）
function openKyoinSubWindow() {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlNissiKyoinCd";
	openModalWindow(url, "PCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return true;
}

// 教員氏名を取得する
function doKyoinAjax(thisObj, thisEvent) {
	var servlet = "rev/co/CobJinjAJAX";
	var args = new Array();
	args['code'] = thisObj.value;
	var target = "form1:htmlKyoinName"
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xre00102T03.onPageLoadBegin}">
<gakuen:itemStateCtrlDef managedbean="pc_Xre00102T01" property="xre00102" />
<gakuen:itemStateCtrl managedbean="pc_Xre00102T03">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xre00102T01.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xre00102T03.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xre00102T03.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
			<hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');"
				action="#{pc_Xre00102T03.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置--></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="950">
							<TABLE class="table" width="100%">
								<TBODY>
									<TR>
										<TH nowrap class="v_a" width="180"><!-- 体験年度 -->
											<h:outputText 
												styleClass="outputText"
												id="lblTiknNendo"
												value="#{pc_Xre00102T03.xre00102.propTiknNendo.name}"
												style="#{pc_Xre00102T03.xre00102.propTiknNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD nowrap width="670">
											<h:inputText 
												styleClass="inputText"
												id="htmlTiknNendo"
												size="4"
												value="#{pc_Xre00102T03.xre00102.propTiknNendo.dateValue}"
												disabled="#{pc_Xre00102T03.xre00102.propTiknNendo.disabled}">
												<hx:inputHelperAssist 
													errorClass="inputText_Error"
													imeMode="inactive" 
													promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
										<TD 
											rowspan="4" 
											style="background-color: transparent; text-align: right; vertical-align: bottom"
											class="clear_border">
											<hx:commandExButton
												type="submit" 
												styleClass="commandExButton" 
												id="select"
												value="選択" 
												disabled="#{pc_Xre00102T03.xre00102.propSelect.disabled}"
												action="#{pc_Xre00102T03.xre00102.doSelectAction}">
											</hx:commandExButton> 
											<hx:commandExButton
												type="submit" 
												value="解除" 
												styleClass="commandExButton"
												id="unselectEnrollment"
												action="#{pc_Xre00102T03.xre00102.doUnselectAction}"
												disabled="#{pc_Xre00102T03.xre00102.propUnSelect.disabled}"
												onclick="return onClickUnselect('#{msg.SY_MSG_0014W}');">
											</hx:commandExButton>
										</TD>
									</TR>								
									<TR align="center" valign="middle">
										<TH nowrap class="v_a" width="180"><!-- 学籍番号 -->
											<h:outputText 
												styleClass="outputText"
												id="lblGaksekiCd_head"
												value="#{pc_Xre00102T03.xre00102.propGaksekiCd.labelName}"
												style="#{pc_Xre00102T03.xre00102.propGaksekiCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="670">
											<h:inputText 
												styleClass="inputText"
												id="htmlGaksekiCd"
												value="#{pc_Xre00102T01.xre00102.propGaksekiCd.value}"
												disabled="#{pc_Xre00102T01.xre00102.propGaksekiCd.disabled}"
												style="#{pc_Xre00102T01.xre00102.propGaksekiCd.style}"
												maxlength="#{pc_Xre00102T01.xre00102.propGaksekiCd.maxLength}"
												readonly="#{pc_Xre00102T01.xre00102.propGaksekiCd.readonly}"
												onblur="return doGakuseiAjax(this, event);"
												size="20">
											</h:inputText>
											<hx:commandExButton 
	 											type="button" 
												value="検"
												styleClass="commandExButton_search" 
												id="btnGakusekiF"
												onclick="openSubWindow(this, event);">
											</hx:commandExButton>
											<h:inputText 
												styleClass="likeOutput"
												id="htmlGakseiName" 
												readonly="true"
												value="#{pc_Xre00102T03.xre00102.propGakseiName.stringValue}">
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap class="v_b"><!-- 体験先種別 -->
											<h:outputText
												styleClass="outputText" 
												id="lblTiknskSbt"
												value="#{pc_Xre00102T03.xre00102.propTiknskSbtList.name}"
												style="#{pc_Xre00102T03.xre00102.propTiknskSbtList.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="670">
											<h:selectOneMenu 
												styleClass="selectOneMenu"
												id="htmlTiknskSbt"
												style="width:150px"
												value="#{pc_Xre00102T03.xre00102.propTiknskSbtList.value}"
												disabled="#{pc_Xre00102T03.xre00102.propTiknskSbtList.disabled}">
													<f:selectItems value="#{pc_Xre00102T03.xre00102.propTiknskSbtList.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						<BR>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
								<TBODY>
									<TR>
										<TD>
											<TABLE 
												border="0" 
												cellpadding="0" 
												cellspacing="0" 
												align="left"
												style="border-bottom-style: none; ">
												<TBODY>
													<TR>
														<TD width="150px">
															<hx:commandExButton 
																type="submit" 
																styleClass="tab_head_off" 
																id="tabXre00102T01" 
																style="width:100%"
																value="基本情報" action="#{pc_Xre00102T03.doTabXre00102T01Action}">
															</hx:commandExButton></TD>
														<TD width="150px">
															<hx:commandExButton
																type="submit" 
																styleClass="tab_head_off" 
																id="tabXre00102T02" 
																style="width:100%"
																value="施設情報" action="#{pc_Xre00102T03.doTabXre00102T02Action}">
															</hx:commandExButton></TD>
														<TD width="150px">
															<hx:commandExButton
																type="button" 
																styleClass="tab_head_on" 
																id="tabXre00102T03" 
																style="width:100%"
																value="日誌">
															</hx:commandExButton>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD>
											<TABLE 
												class="tab_body" 
												border="0" 
												cellpadding="20" 
												cellspacing="0" 
												width="100%" 
												style="border-top-style: none; ">
												<TR>
													<TD align="center" width="100%">
														<div style="height: 400px">
														<BR>
															<TABLE class="table" width="850">
																<TBODY>
																	<TR>
																		<TH nowrap class="v_b" width="180"><!-- 日誌添削依頼日 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblNissiTnskIraiDate"
																				value="#{pc_Xre00102T03.propNissiTnskIraiDate.labelName}">
																			</h:outputText>
																		</TH>																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlNissiTnskIraiDate"
																				value="#{pc_Xre00102T03.propNissiTnskIraiDate.dateValue}"
																				size="10"
																				onkeydown="onChangeData();">
																					<f:convertDateTime />
																					<hx:inputHelperDatePicker />
																					<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																		    </h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_g" width="180"><!-- 日誌添削教員コード --> 
																			<h:outputText
																				styleClass="outputText" 
																				id="lblNissiKyoinCd"
																				value="#{pc_Xre00102T03.propNissiKyoinCd.labelName}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText 
																				styleClass="inputText"
																				id="htmlNissiKyoinCd" 
																				size="20"
																				value="#{pc_Xre00102T03.propNissiKyoinCd.value}"
																				maxlength="#{pc_Xre00102T03.propNissiKyoinCd.maxLength}"
																				onblur="return doKyoinAjax(this, event);"
																				onchange="onChangeData();">
																				<hx:inputHelperAssist imeMode="inactive" errorClass="inputText_Error" />
																			</h:inputText>
																			<hx:commandExButton
																				type="button"
																				value="検"
																				styleClass="commandExButton_search" 
																				id="btnRga"
																				onclick="openKyoinSubWindow('form1:htmlNissiKyoinCd');">
																			</hx:commandExButton>
																			<h:inputText 
																				styleClass="likeOutput" 
																				id="htmlKyoinName"
																				readonly="true" 
																				value="#{pc_Xre00102T03.propKyoinName.stringValue}">
																			</h:inputText>
																		</TD>
																	</TR>																	<TR>
																		<TH nowrap class="v_b" width="180"><!-- 日誌返却日-->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblNissiRtnDate"
																				value="#{pc_Xre00102T03.propNissiRtnDate.labelName}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlNissiRtnDate"
																				value="#{pc_Xre00102T03.propNissiRtnDate.dateValue}"
																				size="10"
																				onkeydown="onChangeData();">
																					<f:convertDateTime />
																					<hx:inputHelperDatePicker />
																					<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																		    </h:inputText>
																		</TD>
																	</TR>																</TBODY>
															</TABLE>
														<TABLE class="button_bar" cellspacing="1" cellpadding="1">
															<TBODY>
																<TR>
																	<TD width="819" align="center">
																	<hx:commandExButton
																		type="submit" 
																		value="確定" 
																		styleClass="commandExButton_dat"
																		id="kakutei"
																		confirm="#{msg.SY_MSG_0001W}"
																		disabled="#{pc_Xre00102T03.propKakutei.disabled}"
																		action="#{pc_Xre00102T03.doKakuteiAction}">
																	</hx:commandExButton>
																	<hx:commandExButton
																		type="submit" 
																		value="クリア"
																		styleClass="commandExButton_etc" 
																		id="clear"
																		onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
																		disabled="#{pc_Xre00102T03.propClear.disabled}"
																		action="#{pc_Xre00102T03.doClearAction}">
																	</hx:commandExButton>
																	<hx:commandExButton	
																		type="submit" 
																		value="削除" 
																		styleClass="commandExButton_dat"
																		id="delete"
																		confirm="#{msg.SY_MSG_0004W}"
																		disabled="#{pc_Xre00102T03.propDelete.disabled}"
																		action="#{pc_Xre00102T03.doDeleteAction}">
																	</hx:commandExButton>														
																</TD>
															</TR>
														</TBODY>
													</TABLE>
												</div>
											</TD>
										</TR>
									</TABLE>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>
		<h:inputHidden 
			id="htmlHidErrMessage"
			value="#{pc_Xre00102T01.xre00102.propHidErrMessage.value}">
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidChangeDataFlgT01" 
			value="#{pc_Xre00102T01.propHidChangeDataFlgT01.stringValue}" >
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidChangeDataFlgT02" 
			value="#{pc_Xre00102T02.propHidChangeDataFlgT02.stringValue}" >
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidChangeDataFlgT03" 
			value="#{pc_Xre00102T03.propHidChangeDataFlgT03.stringValue}" >
		</h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 -->
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		

	</h:form>
</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
