<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

		// バーコード入力：KeyDown処理
	function fncKeyEvt() {
		if (event.keyCode == 13) {
			var btnObj = document.getElementById('form1:readBarcode');
			//btnObj.click();
			indirectClick('readBarcode');
		}
	}	

	function func_1(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlSyokuinCd&kyoShokuin=2";
		 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	}
	
	function getJinjiName(thisObj, thisEvent) {
		// 教員名称を取得する
  		var servlet = "rev/co/CobJinjAJAX";
  		var target = "form1:lblSyokuinName";
  		getCodeName(servlet, target, thisObj.value);
 	}
 	
	 function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}

 
	function doKamokuAjax(thisObj, thisEvent, targetLabel, targetLabel2) {
	// 科目名称,単位数を取得する
		var servlet = "rev/xrf/XrfKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;

	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);


		// 単位数
		if ( targetLabel2 != "" ) {
		    var args2 = new Array();
		    args2['code'] = thisObj.value;
		    args2['tanisu'] = "GET";
		    args2['addString'] = " 単位";

			ajaxUtil.getCodeName(servlet, targetLabel2, args2);
		}
	}
	
	function func_2(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

		// 時間割リスト一括チェック

		check("htmlKadaiTantouList","htmlListCheckBox");
	}
	
	function func_3(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

		// 時間割リスト一括チェック解除

		uncheck("htmlKadaiTantouList","htmlListCheckBox");
	}
	
	// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}

	// 学生氏名を取得する1111
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}

	//ページ初期化
	function reloadPage( thisEvent ){
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiNo'), thisEvent, 'form1:lblName');
		
		getJinjiName(document.getElementById('form1:htmlSyokuinCd'),thisEvent);
		
		doKamokuAjax(document.getElementById('form1:htmlKamokuCd'), thisEvent,
			'form1:lblKamokuName', 'form1:htmlTani');
	}

	function confirmOk() {
		//document.getElementById('form1:htmlButtonKind').value = 1;
		//indirectClick('kensaku');
		indirectClick("kensaku");
	}

	function confirmCancel() {
		//document.getElementById('form1:htmlButtonKind').value = 9;
		//indirectClick('kensaku');
		document.getElementById("form1:htmlConfirmFlg").value = 0;
	}



	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="reloadPage(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00401.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00401.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここに新規登録ボタンを配置 -->
				<hx:commandExButton
					type="submit" value="新規登録"
					styleClass="commandExButton" id="entryNewData"
					disabled="#{pc_Xrf00401.propEntryNewData.disabled}"
					action="#{pc_Xrf00401.doEntryNewDataAction}">
				</hx:commandExButton>
				<!-- ↑ここに新規登録ボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
						<TBODY>
				    		<TR>
				        		<TH class="v_a" width="190">
					            	<!--バーコード -->
				                	<h:outputText styleClass="outputText" id="lblBarcode"
				                		value="#{pc_Xrf00401.propBarcode.labelName}"
				                		style="#{pc_Xrf00401.propBarcode.labelStyle}">
				                	</h:outputText>
				            	</TH>
								<TD>
									<h:inputText styleClass="inputText" id="htmlBarcode" size="25"
										onkeydown="fncKeyEvt();" style="#{pc_Xrf00401.propBarcode.style}"
										tabindex="5"
										maxlength="#{pc_Xrf00401.propBarcode.maxLength}"
										value="#{pc_Xrf00401.propBarcode.stringValue}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" imeMode="disabled"/>
									</h:inputText>
									<hx:commandExButton type="submit" value="読取"
										tabindex="6"
										styleClass="cmdBtn_dat_s" id="readBarcode"
										action="#{pc_Xrf00401.doReadBarcodeAction}">
									</hx:commandExButton>
								</TD>
              				</TR>
						</TBODY>
				</TABLE>
				
				<BR>

				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
						<TBODY>
				           <TR>
				              <TH nowrap class="v_a" width="190">
				              		<!--学籍番号 -->
				                	<h:outputText styleClass="outputText" id="lblGaksekiNo"
				                		value="#{pc_Xrf00401.propGakusekiNo.labelName}"
				                		style="#{pc_Xrf00401.propGakusekiNo.labelStyle}">
				                	</h:outputText>
				              </TH>
				              <TD colspan="4">
				              		<h:inputText styleClass="inputText"
				                		id="htmlGakusekiNo" size="18"
				                		value="#{pc_Xrf00401.propGakusekiNo.stringValue}"
				                		readonly="#{pc_Xrf00401.propGakusekiNo.readonly}"
				                		style="#{pc_Xrf00401.propGakusekiNo.style}"
				                		maxlength="#{pc_Xrf00401.propGakusekiNo.maxLength}"
				                		onblur="return doGakuseiAjax(this, event, 'form1:lblName');">
				                	</h:inputText>
				                	<hx:commandExButton type="button" value=""
				                		styleClass="commandExButton_search" id="searchGakuseiNo"
				                		disabled="#{pc_Xrf00401.propSearchGakusekiNo.disabled}"
				                		onclick="openSubWindow('form1:htmlGakusekiNo');">
				                	</hx:commandExButton>
									<h:outputText styleClass="outputText"
										id="lblName"
										value="#{pc_Xrf00401.propName.stringValue}" 
										style="#{pc_Xrf00401.propName.labelStyle}">
									</h:outputText>
				              </TD>
              				</TR>
              				
              			   <TR>
				              <TH nowrap class="v_a" width="190">
									<!--レポート登録職員 -->
				                	<h:outputText styleClass="outputText" id="lblSyokuinCd"
				                		value="#{pc_Xrf00401.propSyokuinCd.labelName}"
				                		style="#{pc_Xrf00401.propSyokuinCd.labelStyle}">
				                	</h:outputText>
				              </TH>
				              <TD colspan="4">
									<h:inputText id="htmlSyokuinCd" styleClass="inputText" 
										readonly="#{pc_Xrf00401.propSyokuinCd.readonly}" 
										style="#{pc_Xrf00401.propSyokuinCd.style}"
										disabled="#{pc_Xrf00401.propSyokuinCd.disabled}"
										size="10"
										value="#{pc_Xrf00401.propSyokuinCd.stringValue}"
										maxlength="#{pc_Xrf00401.propSyokuinCd.maxLength}"
										onblur="return getJinjiName(this, event);">
									</h:inputText>
									<hx:commandExButton type="button" value=""
										styleClass="commandExButton_search" id="searchSyokuin"
										disabled="#{pc_Xrf00401.propSearchSyokuin.disabled}"
										rendered="#{pc_Xrf00401.propSearchSyokuin.rendered}"
										style="#{pc_Xrf00401.propSearchSyokuin.style}"
										onclick="return func_1(this, event);">
									</hx:commandExButton>
									<h:outputText
              							styleClass="outputText"
              							id="lblSyokuinName"
              							value="#{pc_Xrf00401.propSyokuinName.stringValue}">
              						</h:outputText>
              					</TD>
              				</TR>
              				
							<TR>
								<TH class="v_a" width="150">
									<!--科目コード -->
									<h:outputText styleClass="outputText"
										id="lblKamokuCd"
										value="#{pc_Xrf00401.propKamokuCd.labelName}" >
									</h:outputText>
								</TH>
								<TD colspan="3">
									<h:inputText id="htmlKamokuCd" styleClass="inputText" 
                                        size="10"
                                        maxlength="#{pc_Xrf00401.propKamokuCd.maxLength}"
                                        onblur="return doKamokuAjax(this, event, 'form1:lblKamokuName', 'form1:htmlTani');"
										value="#{pc_Xrf00401.propKamokuCd.stringValue}">
									</h:inputText>
									<hx:commandExButton type="button"
										styleClass="commandExButton_search" id="searchKamoku"
										onclick="return openKamokuSearchWindow(this, event);"
										disabled="#{pc_Xrf00401.propSearchKamoku.disabled}">
									</hx:commandExButton>									
              						<h:outputText
              							styleClass="outputText" id="lblKamokuName"
              							value="#{pc_Xrf00401.propKamokuName.stringValue}">
              						</h:outputText>				
              					</TD>
              					<TD>
              						<h:outputText styleClass="outputText"
										id="htmlTani"
										value="#{pc_Xrf00401.propTani.stringValue}">
									</h:outputText>
								</TD>	
              					
							</TR>
							<TR>
								<TH class="v_a" width="190">
									<h:outputText styleClass="outputText" id="lblTesyutuKbn" 
										value="提出区分">
									</h:outputText>
								</TH>
								<TD colspan="4">
									<h:selectOneMenu styleClass="selectOneMenu" id="htmlTesyutuKbn"
										value="#{pc_Xrf00401.propTesyutuKbn.stringValue}"
										style="#{pc_Xrf00401.propTesyutuKbn.style};width:150px"
										disabled="#{pc_Xrf00401.propTesyutuKbn.disabled}">
										<f:selectItems value="#{pc_Xrf00401.propTesyutuKbn.list}" />
									</h:selectOneMenu>
								</TD>		
							</TR>
							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblUketukebi"
										value="レポート受付日"
										style="#{pc_Xrf00401.propUketukebiFrom.labelStyle}">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText id="htmlUketukebiFrom"
										styleClass="inputText" size="12"
										value="#{pc_Xrf00401.propUketukebiFrom.dateValue}">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText>
									
									<h:outputText styleClass="outputText" id="text7" value="～">
									</h:outputText>
									
									<h:inputText id="htmlUketukebiTo"
										styleClass="inputText" size="12"
										value="#{pc_Xrf00401.propUketukebiTo.dateValue}">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText>
								</TD>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText" id="lblSyoribi" value="処理日">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:inputText id="htmlSyoribi"
										styleClass="inputText" size="12"
										value="#{pc_Xrf00401.propSyoribi.dateValue}">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText>
								</TD>				
							</TR>
							
							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblTouroku"
										value="#{pc_Xrf00401.propTouroku.stringValue}">
									</h:outputText>
								</TH>
								<TD colspan="4">
									<h:selectBooleanCheckbox 
										styleClass="selectBooleanCheckbox"
                                		id="checkboxUketuke" 
                                		value="#{pc_Xrf00401.propCheckBoxUketuke.checked}">
                                	</h:selectBooleanCheckbox>
                                	<h:outputText
                                		styleClass="outputText" 
                                		id="lblUketuke" 
                                		value="受付登録">
                                	</h:outputText>

									<h:selectBooleanCheckbox 
										styleClass="selectBooleanCheckbox"
                                		id="checkboxMukou" 
                                		value="#{pc_Xrf00401.propCheckBoxMukou.checked}">
                                	</h:selectBooleanCheckbox>
                                	<h:outputText
                                		styleClass="outputText" 
                                		id="lblMukou" 
                                		value="無効登録">
                                	</h:outputText>
                                	
								</TD>	
							</TR>		
													
							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblSortjyun"
										value="ソート順">
									</h:outputText>
								</TH>
								<TD colspan="4">
									<h:selectOneRadio
						              	disabledClass="selectOneRadio_Disabled"
						              	styleClass="selectOneRadio" id="htmlSortjyun"
						              	value="#{pc_Xrf00401.propRadioSortjyun.stringValue}"
						              	style="#{pc_Xrf00401.propRadioSortjyun.style}">
						              	<f:selectItem itemValue="0" itemLabel="科目コード、分冊、新旧刊区分"/>
						              	<f:selectItem itemValue="1" itemLabel="学籍番号" />
						              	<f:selectItem itemValue="2" itemLabel="レポート受付日" />
							        </h:selectOneRadio>
							     </TD>	
							</TR>																					
						</TBODY>
					</TABLE>
					
					<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
						<TBODY>
							<TR>
						
								<TD>
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="kensaku"
										value="#{pc_Xrf00401.propKensaku.caption}"
										disabled="#{pc_Xrf00401.propKensaku.disabled}"
										action="#{pc_Xrf00401.doKensakuAction}">
									</hx:commandExButton>
							
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="clear"
										value="#{pc_Xrf00401.propClear.caption}"
										disabled="#{pc_Xrf00401.propClear.disabled}"
										action="#{pc_Xrf00401.doClearAction}">
									</hx:commandExButton>
								</TD>	
								
							</TR>
						</TBODY>
					</TABLE>								
					
					<TABLE border="0" cellpadding="5">
						<TBODY>
							<TR>
								<TD width="828" align="right">
									<h:outputText styleClass="outputText"
										id="lblKensakuListCount"
										value="#{pc_Xrf00401.propKensakuListCount.stringValue}">
									</h:outputText>
								</TD>
							</TR>
							
							<TR>
							<!-- ↓データテーブル部↓ -->
							<TD>
								<DIV id="listScroll" class="listScroll" style="height: 330px;">
									<h:dataTable
										columnClasses="columnClass" headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_Xrf00401.propKensakuList.rowClasses}"
										styleClass="meisai_scroll" id="htmlKensakuList" width="888"
										value="#{pc_Xrf00401.propKensakuList.list}" var="varlist">
										
										<h:column id="column0">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="区分"
													id="lblListKbnColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListKbn"
													value="#{varlist.kbn.displayValue}"
													title="#{varlist.kbn.stringValue}">
											</h:outputText>
											<f:attribute value="60" name="width" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
										
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													value="科目コード"
													id="lblListKamokuCdColumn">
												</h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlListKamokuCd"
													value="#{varlist.kamokuCd}">
											</h:outputText>
											<f:attribute value="64" name="width" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
										
										<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="科目名"
														id="lblListKamokuNameColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokuName"
													value="#{varlist.kamokuNm.displayValue}"
													title="#{varlist.kamokuNm.stringValue}"
												>
												</h:outputText>
												<f:attribute value="256" name="width" />
										</h:column>
	
										<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="分冊"
														id="lblListBunsatuColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListBunsatu"
													value="#{varlist.bunsatu}">
												</h:outputText>
												<f:attribute value="32" name="width" />
										</h:column>

										<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="新旧刊区分"
														id="lblListSinkyuColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListSinkyu"
													value="#{varlist.sinkyu}">
												</h:outputText>
												<f:attribute value="80" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>
										
										<h:column id="column5">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="学籍番号"
														id="lblListGakusekiNoColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListGakusekiNo"
													value="#{varlist.gakusekiNo}">
												</h:outputText>
												<f:attribute value="74" name="width" />
										</h:column>
												
										<h:column id="column6">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="氏名"
														id="lblListSimeiColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListSimei"
													value="#{varlist.simei.displayValue}"
													title="#{varlist.simei.stringValue}"
												>


												</h:outputText>
												<f:attribute value="128" name="width" />
										</h:column>
														
										<h:column id="column7">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="受付"
														id="lblListUketukeColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListUketuke"
													value="#{varlist.uketuke}">
												</h:outputText>
												<f:attribute value="32" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>
										
										<h:column id="column8">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="レポート受付日"
														id="lblListUketukebiColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListUketukebi"
													value="#{varlist.uketukebi}"
													style="text-align: center; vertical-align: middle" >
													<f:convertDateTime pattern="yyyy/MM/dd" />
												</h:outputText>
												<f:attribute value="128" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>
										
										<h:column id="column9">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit"
												value="#{pc_Xrf00401.propListEdit.caption}"
												styleClass="commandExButton" 
												id="edit"
												action="#{pc_Xrf00401.doEditAction}">
											</hx:commandExButton>
											<f:attribute value="true" name="nowrap" />
											<f:attribute value="32" name="width" />
										</h:column>							
										
									</h:dataTable>
								</DIV>
							</TD>
							</TR>
										
						</TBODY>
					</TABLE>	
					
					<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
						<TBODY>
							<TR>
						
								<TD>
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="csv"
										value="CSV作成"
										
										action="#{pc_Xrf00401.doCsvOutAction}">
									</hx:commandExButton>
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="sitei"
										value="出力項目指定"
										
										action="#{pc_Xrf00401.doSetoutputAction}">
									</hx:commandExButton>
								</TD>	
								
							</TR>
						</TBODY>
					</TABLE>					

				</DIV>
			</DIV>
			
		</DIV>

			<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrf00401.propBackPhase.integerValue}">
			</h:inputHidden>

			<h:inputHidden
				id="htmlButtonKind"
				value="#{pc_Xrf00401.propButtonKind.integerValue}">
			</h:inputHidden>

			<h:inputHidden
				id="htmlClickSerch"
				value="#{pc_Xrf00401.propClickSerch.integerValue}">
			</h:inputHidden>
		
			<h:inputHidden
				value="#{pc_Xrf00401.propConfirmFlg.integerValue}"
				id="htmlConfirmFlg">
			</h:inputHidden>
		
		</h:form>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
