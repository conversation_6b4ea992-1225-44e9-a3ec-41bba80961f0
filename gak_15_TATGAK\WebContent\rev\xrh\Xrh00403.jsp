<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00403.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	//	画面ロード時の処理
	function formLoad(thisEvent) {
		//	科目名称再表示
		doKamokuAjax(document.getElementById('form1:htmlKamokuCd'),
			thisEvent, 'form1:lblKamokuName');
	}
	
 	//	科目検索
	 function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	}
 
	// 科目名称を取得する
	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
		//var servlet = "rev/km/KmzKmkAJAX";
		var servlet = "rev/km/KmKamokuDetailAJAX";
	    var args = new Array();
	    //args['code'] = thisObj.value;
	    args['kamokuCd'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		//ajaxUtil.getCodeName(servlet, targetLabel, args);
		ajaxUtil.getPluralValueSetMethod(servlet, targetLabel, args, "setKamokuHyojunName");
	}
	
	// 科目名称を取得する（CallBack関数）
	function setKamokuHyojunName(value){
		//var lblName = value['kamokNameHyojun'];
		//var nameObj = document.getElementById('form1:lblKamokuName');
		//nameObj.value = lblName;
		document.getElementById('form1:lblKamokuName').innerHTML = value['kamokNameHyojun'];
	}

	// 戻るボタン押下時処理
	function onClickReturnDisp(id) {
		var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
		if(changeDataFlg == "1"){
			return confirm(id);
		}
		
		return true;
	}

	// データ変更時
	function onChangeData() {
		document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
	}
	// ポップアップメッセージを表示
	function doPopupMsg(id, msg) {
		var args = new Array();
		args[0] = msg;
		if (confirm(messageCreate(id, args))) {
			onChangeData();
			return true;
		}
	  
		return false;
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="formLoad(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00403.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00403.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00403.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00403.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"><hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="53" action="#{pc_Xrh00403.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
					</hx:commandExButton></TD>
				</TR>
			</TABLE>
			<!-- ↑ここにボタンを配置 --></DIV>

			<DIV id="content">
			<DIV class="column" align="center">

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="700">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="190"><!--年度 --> <h:outputText
							styleClass="outputText" id="lblNendo"
							value="#{pc_Xrh00403.propNendo.labelName}"
							style="#{pc_Xrh00403.propNendo.labelStyle}">
						</h:outputText></TH>
						<TD><h:inputText styleClass="inputText" id="htmlNendo" size="8"
							value="#{pc_Xrh00403.propNendo.dateValue}"
							disabled="#{pc_Xrh00403.propNendo.disabled}"
							style="#{pc_Xrh00403.propNendo.style}" tabindex="1">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
						<TD width="100" align="right"
							style="background-color: transparent; text-align: right"
							class="clear_border"><hx:commandExButton type="submit"
							value="#{pc_Xrh00403.propSelect.caption}"
							styleClass="commandExButton" id="select"
							disabled="#{pc_Xrh00403.propSelect.disabled}"
							rendered="#{pc_Xrh00403.propSelect.rendered}"
							style="#{pc_Xrh00403.propSelect.style}"
							action="#{pc_Xrh00403.doSelectAction}" tabindex="2">
						</hx:commandExButton> <hx:commandExButton type="submit"
							value="#{pc_Xrh00403.propUnSelect.caption}"
							styleClass="commandExButton" id="unselect"
							disabled="#{pc_Xrh00403.propUnSelect.disabled}"
							rendered="#{pc_Xrh00403.propUnSelect.rendered}"
							style="#{pc_Xrh00403.propUnSelect.style}"
							action="#{pc_Xrh00403.doUnSelectAction}" tabindex="3">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>


			<TABLE border="0">
				<TBODY>
					<TR>
						<TD width="700" align="left"><h:outputText styleClass="outputText"
							id="lblKensakuListName"
							value="#{pc_Xrh00403.propKensakuListName.stringValue}">
						</h:outputText></TD>
					</TR>

					<TR>
						<!-- ↓データテーブル部↓ -->
						<TD>
						<DIV id="listScroll" class="listScroll" style="height: 256px;"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrh00403.propKensakuList.rowClasses}"
							styleClass="meisai_scroll" id="htmlKensakuList" width="700"
							value="#{pc_Xrh00403.propKensakuList.list}" var="varlist">

							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="科目コード"
										id="lblListKamokuCdColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									value="#{varlist.propListKamokuCd.stringValue}"
									id="htmlListKamokuCd">
								</h:outputText>
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="科目名称"
										id="lblListKamokuNameColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListamokuName"
									title="#{varlist.propListKamokuName.stringValue}"
									value="#{varlist.propListKamokuName.displayValue}">
								</h:outputText>
								<f:attribute value="350" name="width" />
								<f:attribute value="true" name="nowrap" />
							</h:column>

							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="試験区分"
										id="lblListSikenKubunColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListSikenKubun"
									title="#{varlist.propListSikenKubun.stringValue}"
									value="#{varlist.propListSikenKubun.displayValue}">
								</h:outputText>
								<f:attribute value="200" name="width" />
							</h:column>


							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="時限"
										id="lblListJigenColumn">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListJigen"
									value="#{varlist.propListJigen.integerValue}">
								</h:outputText>
								<f:attribute value="40" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>


							<h:column id="column5">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit"
									value="#{pc_Xrh00403.propListEdit.caption}"
									action="#{pc_Xrh00403.doEditAction}"
									styleClass="commandExButton" id="edit" tabindex="4">
								</hx:commandExButton>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="30" name="width" />
								<f:attribute value="center" name="align" />
							</h:column>

						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
				width="700">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><!--科目コード --> <h:outputText
							styleClass="outputText" id="lblKamokuCd"
							value="#{pc_Xrh00403.propKamokuCd.labelName}"
							style="#{pc_Xrh00403.propKamokuCd.labelStyle}">
						</h:outputText></TH>
						<TD><h:inputText id="htmlKamokuCd" styleClass="inputText"
							maxlength="#{pc_Xrh00403.propKamokuCd.maxLength}"
							disabled="#{pc_Xrh00403.propKamokuCd.disabled}" size="10"
							onchange="onChangeData();"
							onblur="return doKamokuAjax(this, event, 'form1:lblKamokuName');"
							value="#{pc_Xrh00403.propKamokuCd.stringValue}" tabindex="5">
							<hx:inputHelperAssist imeMode="disabled"
								errorClass="inputText_Error" />
						</h:inputText> <hx:commandExButton type="button"
							disabled="#{pc_Xrh00403.propKamokuCd.disabled}"
							styleClass="commandExButton_search" id="searchKamoku"
							onclick="return openKamokuSearchWindow(this, event);"
							tabindex="6">
						</hx:commandExButton> <h:outputText styleClass="outputText"
							id="lblKamokuName"
							value="#{pc_Xrh00403.propKamokuName.stringValue}">
						</h:outputText></TD>
					</TR>

					<TR>
						<TH nowrap class="v_a" width="150"><!--試験区分 --> <h:outputText
							styleClass="outputText" id="lblHyoukaKaisu"
							value="#{pc_Xrh00403.propSikenKubun.labelName}"
							style="#{pc_Xrh00403.propSikenKubun.labelStyle}">
						</h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSikenKubun" onchange="onChangeData();"
							value="#{pc_Xrh00403.propSikenKubun.stringValue}"
							style="#{pc_Xrh00403.propSikenKubun.style};width:200px"
							disabled="#{pc_Xrh00403.propSikenKubun.disabled}" tabindex="7">
							<f:selectItems value="#{pc_Xrh00403.propSikenKubun.list}" />
						</h:selectOneMenu></TD>
					</TR>

					<TR>
						<TH nowrap class="v_a" width="150"><!--時限 --> <h:outputText
							styleClass="outputText" id="lblJigen"
							value="#{pc_Xrh00403.propJigen.labelName}"
							style="#{pc_Xrh00403.propJigen.labelStyle}">
						</h:outputText></TH>
						<TD><h:inputText styleClass="inputText" id="htmlJigen" size="4"
							disabled="#{pc_Xrh00403.propJigen.disabled}"
							onkeyup="onChangeData();"
							maxlength="#{pc_Xrh00403.propJigen.maxLength}"
							value="#{pc_Xrh00403.propJigen.integerValue}"
							style="#{pc_Xrh00403.propJigen.style}" tabindex="8">
							<f:convertNumber integerOnly="true" type="number" pattern="#0" />
							<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" imeMode="disabled" />
						</h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE width="700" border="0" cellpadding="0" cellspacing="0"
				class="button_bar">
				<TBODY>
					<TR>

						<TD><hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="register"
							onclick="return confirm('#{msg.SY_MSG_0002W}');"
							disabled="#{pc_Xrh00403.propRegister.disabled}"
							action="#{pc_Xrh00403.doRegisterAction}" value="確定" tabindex="9">
						</hx:commandExButton> <hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="delete"
							onclick="return confirm('#{msg.SY_MSG_0004W}');"
							disabled="#{pc_Xrh00403.propDelete.disabled}"
							action="#{pc_Xrh00403.doDeleteAction}" value="削除" tabindex="10">
						</hx:commandExButton> <hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="clear"
							onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '表示内容');"
							disabled="#{pc_Xrh00403.propClear.disabled}"
							action="#{pc_Xrh00403.doClearAction}" value="クリア" tabindex="11">
						</hx:commandExButton></TD>

					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<h:inputHidden id="htmlHidChangeDataFlg"
				value="#{pc_Xrh00403.propHidChangeDataFlg.stringValue}"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrh00403.propKensakuList.scrollPosition}"
				id="scroll"></h:inputHidden></DIV>

			</DIV>
		</h:form>

		<!-- フッターインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
	changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
