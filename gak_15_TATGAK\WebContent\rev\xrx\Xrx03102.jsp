<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx03102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Kmb02001.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--    title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >  
<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
 var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlSidoKyoinCd&kyoShokuin=3";
 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");

}

function getSidoKyoinNm(thisObj, thisEvent) {
  var servlet = "rev/co/CobJinjAJAX";
  var target = "form1:lblSidoKyoinNm";
  getCodeName(servlet, target, thisObj.value);
  return false;
}


</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx03102.onPageLoadBegin}">
    <h:form styleClass="form" id="form1">
        <!-- ヘッダーインクルード -->
        <jsp:include page ="../inc/header.jsp" />
        <!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrx03102.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrx03102.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrx03102.screenName}"></h:outputText></div>
    <!--↓outer↓-->
    <DIV class="outer">
    
					<table border="0" width="98%">
						<tr>
							<td align="left">
								<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
									id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
									styleClass="outputText" escape="false">
								</h:outputText></FIELDSET>
							</td>
							<td align="right"><%-- 戻るボタン --%><hx:commandExButton type="submit"
										value="戻る" styleClass="commandExButton" id="returnDisp"
										style="width:80px;"
										action="#{pc_Xrx03102.doReturnDispAction}">
									</hx:commandExButton>
							</td>
							</tr>
					</table>
    <br>

    <!--↓content↓-->
					<DIV class="head_button_area" >　
					</DIV>
					
					<DIV id="content">
					<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" width="750px">
					<TBODY>
					<TR>
					<TD>
					<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
					<TBODY>
					<TR>
					<TD>        
						<TABLE class="table" width="750px">
				          <TBODY>
							<TR>
							<!--学生氏名-->
								<TH nowrap class="v_a" width="250px">
									&nbsp;									
									<h:outputText styleClass="outputText" 
										id="lblName"
										value="#{pc_Xrx03102.propName.name}">
									</h:outputText>
								</TH>
								<TD width="500" colspan=5>
									<h:outputText id="htmlName" 
									  styleClass="outputText" 
									  value="#{pc_Xrx03102.propName.stringValue}">
									</h:outputText>
								</TD>																	
							</TR>
				          </TBODY>
				        </TABLE>
				        <div style = "height:22px"></div>
						<TABLE class="table "  width="750px" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
								<!--並び順-->
									<TH >
										&nbsp;										
										<h:outputText
										styleClass="outputText" id="lblNarabijun"
										value="#{pc_Xrx03102.propNarabijun.name}"></h:outputText></TH>
									<TD colspan=5><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNarabijun" style="width: 180px"
										disabled="#{pc_Xrx03102.propNarabijun.disabled}"
										value="#{pc_Xrx03102.propNarabijun.value}">
										<f:selectItems
										value="#{pc_Xrx03102.propNarabijun.list}" />
										</h:selectOneMenu></TD>
								</TR>
								<TR>
								<!--問い合わせ日-->
									<TH nowrap class="v_b" width="250">
										&nbsp;										
										<h:outputText
										styleClass="outputText" id="lblToiawaseDate"
										value="#{pc_Xrx03102.propToiawaseDateFromDt.name}"></h:outputText></TH>
									<TD width="*" colspan=5><h:inputText styleClass="inputText" id="htmlToiawaseDateFromDt"
										style="#{pc_Xrx03102.propToiawaseDateFromDt.style}"
										value="#{pc_Xrx03102.propToiawaseDateFromDt.dateValue}"
										size="12">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										</h:inputText> <h:outputText styleClass="outputText" id="text1"
											value="～"></h:outputText> <h:inputText styleClass="inputText"
										id="htmlToiawaseDateToDt"
										style="#{pc_Xrx03102.propToiawaseDateToDt.style}"
										size="12"
										value="#{pc_Xrx03102.propToiawaseDateToDt.dateValue}">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR> 								
								<TR>
								<!--業務分類-->
									<TH >
										&nbsp;										
										<h:outputText
										styleClass="outputText" id="lblGyomuBunrui" value="#{pc_Xrx03102.propGyoumuList.name}"></h:outputText></TH>
									<TD width="100px" style="border-right-style: none;"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlGyoumuList"
										value="#{pc_Xrx03102.propGyoumuList.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propGyoumuList.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propGyoumuList.list}" />
									</h:selectManyCheckbox></TD>
									<TD width="100px" style="border-left-style: none; border-right-style: none;"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlGyoumuList2"
										value="#{pc_Xrx03102.propGyoumuList2.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propGyoumuList2.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propGyoumuList2.list}" />
									</h:selectManyCheckbox></TD>
									<TD width="100px" style="border-left-style: none; border-right-style: none;"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlGyoumuList3"
										value="#{pc_Xrx03102.propGyoumuList3.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propGyoumuList3.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propGyoumuList3.list}" />
									</h:selectManyCheckbox></TD>
									<TD width="100px" style="border-left-style: none; border-right-style: none;"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlGyoumuList4"
										value="#{pc_Xrx03102.propGyoumuList4.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propGyoumuList4.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propGyoumuList4.list}" />
									</h:selectManyCheckbox></TD>
									<TD width="100px" style="
    									padding-bottom:40px;border-left-style: none;" valign="top"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlGyoumuList5"
										value="#{pc_Xrx03102.propGyoumuList5.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propGyoumuList5.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propGyoumuList5.list}" />
									</h:selectManyCheckbox></TD>
								</TR>
								<TR>
								<!--要件-->
									<TH >
										&nbsp;										
										<h:outputText
										styleClass="outputText" id="lblYoukenName"
										value="#{pc_Xrx03102.propYoukenName.name}"></h:outputText></TH>
									<TD colspan=5><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlYoukenName" style="width: 300px"
										disabled="#{pc_Xrx03102.propYoukenName.disabled}"
										value="#{pc_Xrx03102.propYoukenName.value}">
										<f:selectItems
										value="#{pc_Xrx03102.propYoukenName.list}" />
										</h:selectOneMenu></TD>
								</TR>
								<TR>
								<!--回答状況-->
									<TH class="v_c" >
										&nbsp;										
										<h:outputText
										styleClass="outputText" id="lblSyokuinStatus" value="#{pc_Xrx03102.propKaitouJouKyouList.name}"></h:outputText></TH>
									<TD width="100px" style="border-right-style: none;"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlKaitouJouKyouList"
										value="#{pc_Xrx03102.propKaitouJouKyouList.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propKaitouJouKyouList.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propKaitouJouKyouList.list}" />
									</h:selectManyCheckbox></TD>
									<TD width="100px" style="border-left-style: none; border-right-style: none;"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlKaitouJouKyouList2"
										value="#{pc_Xrx03102.propKaitouJouKyouList2.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propKaitouJouKyouList2.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propKaitouJouKyouList2.list}" />
									</h:selectManyCheckbox></TD>	
									<TD width="100px" style="border-left-style: none; border-right-style: none;"><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlKaitouJouKyouList3"
										value="#{pc_Xrx03102.propKaitouJouKyouList3.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propKaitouJouKyouList3.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propKaitouJouKyouList3.list}" />
									</h:selectManyCheckbox></TD>
									<TD width="100px" style="border-left-style: none; " colspan=2><h:selectManyCheckbox
										disabledClass="selectManyCheckbox_Disabled"
										styleClass="selectManyCheckbox" id="htmlKaitouJouKyouList4"
										value="#{pc_Xrx03102.propKaitouJouKyouList4.stringValue}"
										layout="pageDirection"
										style="#{pc_Xrx03102.propKaitouJouKyouList4.style}">
										<f:selectItems
											value="#{pc_Xrx03102.propKaitouJouKyouList4.list}" />
									</h:selectManyCheckbox></TD>
								</TR>  
								<TR align="center" valign="middle">
								<!--指導要員-->
								  <TH class="v_c" width="250">
									&nbsp;									  
									<h:outputText styleClass="outputText" id="lblSidoKyoinCd"
									  value="#{pc_Xrx03102.propSidoKyoinCd.name}"></h:outputText>
								  </TH>
								  <TD width="*" colspan=5><h:inputText styleClass="inputText"
									  id="htmlSidoKyoinCd" size="20" maxlength="20"
									  maxlength="#{pc_Xrx03102.propSidoKyoinCd.maxLength}"
									  value="#{pc_Xrx03102.propSidoKyoinCd.stringValue}"
									  style="#{pc_Xrx03102.propSidoKyoinCd.style}"
									  readonly="#{pc_Xrx03102.propSidoKyoinCd.readonly}"
									  maxlength="#{pc_Xrx03102.propSidoKyoinCd.maxLength}"
									  onblur="return getSidoKyoinNm(this, event);">
									  <hx:inputHelperAssist imeMode="disabled"
										errorClass="inputText_Error" />
									  </h:inputText>
									<hx:commandExButton type="button" value="検"
									  styleClass="commandExButton_search" id="select"
									  onclick="return func_1(this, event);"></hx:commandExButton>
									<h:outputText id="lblSidoKyoinNm" 
									  styleClass="outputText" 
									  value="#{pc_Xrx03102.propSidoKyoinNm.stringValue}"></h:outputText>
								  </TD>
								</TR>							         															           															                     											
							</TBODY>
						</TABLE>
						<div style = "height:22px"></div>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE cellspacing="1" cellpadding="1" class="button_bar" width="100%">
							<TBODY>
								<TR align="right">
									<TD align="center" width="835">
										<hx:commandExButton type="submit" 
											value="検索" 
											styleClass="commandExButton_dat"
											id="search" 
											action="#{pc_Xrx03102.doSearchAction}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD width="835">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD align="right">
										<h:outputText styleClass="outputText"
											id="lblCount"
											value="#{pc_Xrx03102.propToiawaseList.listCount}">
										</h:outputText>
										<h:outputText styleClass="outputText"
											id="text4" value="件">
										</h:outputText>
									</TD>
								</TR>
								<TR>
									<TD>
										<DIV style="height: 230px; width=100%;" id="listScroll" class="listScroll">
											<h:dataTable
												rows="#{pc_Xrx03102.propToiawaseList.rows}"
												rowClasses="#{pc_Xrx03102.propToiawaseList.rowClasses}"
												headerClass="headerClass"
												footerClass="footerClass"
												styleClass="meisai_scroll" id="htmlGakuseiList"
												value="#{pc_Xrx03102.propToiawaseList.list}" var="varlist"
												width="835px">
												<h:column id="column1">
													<f:facet name="header">
														<h:outputText id="txtLabel1"
															styleClass="outputText"
															value="#{pc_Xrx03102.propToiawaseDate.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="70px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblToiawaseDate_list"
														value="#{varlist.propToiawaseDate}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column2">
													<f:facet name="header">
														<h:outputText id="txtLabel2"
															styleClass="outputText"
															value="#{pc_Xrx03102.fubanNo.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="35px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblNO_list"
														value="#{varlist.fubanNo}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column3">
													<f:facet name="header">
														<h:outputText id="txtLabel3"
															styleClass="outputText"
															value="#{pc_Xrx03102.propYoukenNamelist.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="250px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblYoukenName_list"
														value="#{varlist.propYoukenName}">
													</h:outputText>
												</h:column>
												<h:column id="column4">
													<f:facet name="header">
														<h:outputText id="txtLabel4"
															styleClass="outputText"
															value="#{pc_Xrx03102.propYokenbetuCount.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="80px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblYokenbetuCount_list"
														value="#{varlist.propYokenbetuCount}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column5">
													<f:facet name="header">
														<h:outputText id="txtLabel5"
															styleClass="outputText"
															value="#{pc_Xrx03102.propGakuseiKokai.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="60px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblGakuseiKokai_list"
														value="#{varlist.propGakuseiKokai}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column6">
													<f:facet name="header">
														<h:outputText id="txtLabel6"
															styleClass="outputText"
															value="#{pc_Xrx03102.propTatoDate.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="70px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblTatoDate_list"
														value="#{varlist.propTatoDate}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column7">
													<f:facet name="header">
														<h:outputText id="txtLabel7"
															styleClass="outputText"
															value="#{pc_Xrx03102.propKaitouJouKyouList.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="90px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblSyokuinStatus_list"
														value="#{varlist.propSyokuinStatus}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>												
												<h:column id="column8">
													<f:facet name="header">
														<h:outputText id="txtLabel8"
															styleClass="outputText"
															value="#{pc_Xrx03102.propTaiosya.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="90px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblTaiosya_list"
														value="#{varlist.propTaiosya}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column9">
													<f:facet name="header">
														<h:outputText id="txtLabel9"
															styleClass="outputText"
															value="#{pc_Xrx03102.propSidoKyoinCd.name}">
														</h:outputText>
													</f:facet>
													<f:attribute value="90px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblSidoKyoinNm_list"
														value="#{varlist.propSidoKyoinNm}">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>																					
											</h:dataTable>
										</DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
				</TD>
				</TR>
				</TBODY>
				</TABLE>
				</TD>
				</TR>
				</TBODY>
				</TABLE>
				<!-- ↑ここにコンポーネントを配置 -->
				</DIV>
				</DIV>

    <!--↑content↑-->
    </DIV>
    <!--↑outer↑-->
    </h:form>
    <!-- フッターインクルード -->
    <jsp:include page ="../inc/footer.jsp" />
    </hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
