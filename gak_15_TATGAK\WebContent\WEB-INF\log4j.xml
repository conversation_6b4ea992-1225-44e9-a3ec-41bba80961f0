<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

	<appender name="STDOUT" class="org.apache.log4j.ConsoleAppender">
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern"
				value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%-100c,%m%n" />
		</layout>
	</appender>

	<!-- ***** DailyRollingFileAppender Setting. ***** -->

	<appender name="ERROR"
		class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen.error" />
		<param name="DatePattern" value="'.'yyyy-MM-dd" />
		<param name="Append" value="true" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern"
				value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%-100c,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelMatchFilter">
			<param name="LevelToMatch" value="ERROR" />
			<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
	</appender>

	<appender name="LOG"
		class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen.log" />
		<param name="DatePattern" value="'.'yyyy-MM-dd" />
		<param name="Append" value="true" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern"
				value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%-100c,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelRangeFilter">
			<param name="LevelMin" value="DEBUG" />
			<param name="LevelMax" value="WARN" />
			<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
	</appender>

	<appender name="REQUEST_LOG"
		class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen-request.log" />
		<param name="DatePattern" value="'.'yyyy-MM-dd" />
		<param name="Append" value="true" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern"
				value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelMatchFilter">
			<param name="LevelToMatch" value="INFO" />
			<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
	</appender>

	<appender name="SQL_LOG"
		class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen-sql.log" />
		<param name="DatePattern" value="'.'yyyy-MM-dd" />
		<param name="Append" value="true" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern"
				value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelRangeFilter">
			<param name="LevelMin" value="DEBUG" />
			<param name="LevelMax" value="WARN" />
			<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
	</appender>

	<appender name="DEBUG_LOG"
		class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen-debug.log" />
		<param name="DatePattern" value="'.'yyyy-MM-dd" />
		<param name="Append" value="true" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern"
				value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%-100c,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelRangeFilter">
			<param name="LevelMin" value="DEBUG" />
			<param name="LevelMax" value="WARN" />
			<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
	</appender>

	<!-- ***** ROLLINGFILEAPPEDER SETTING. ***** -->

	<!--
		
		<appender name="ERROR"
		class="org.apache.log4j.RollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen.error" />
		<param name="Append" value="true" />
		<param name="MaxFileSize" value="1MB" />
		<param name="MaxBackupIndex" value="10" />
		<layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern"
		value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%-100c,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelMatchFilter">
		<param name="LevelToMatch" value="ERROR" />
		<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
		</appender>
		
		<appender name="LOG" class="org.apache.log4j.RollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen.log" />
		<param name="Append" value="true" />
		<param name="MaxFileSize" value="1MB" />
		<param name="MaxBackupIndex" value="10" />
		<layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern"
		value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%-100c,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelRangeFilter">
		<param name="LevelMin" value="DEBUG" />
		<param name="LevelMax" value="WARN" />
		<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
		</appender>
		
		<appender name="REQUEST_LOG"
		class="org.apache.log4j.RollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen-request.log" />
		<param name="Append" value="true" />
		<param name="MaxFileSize" value="1MB" />
		<param name="MaxBackupIndex" value="10" />
		<layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern"
		value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelMatchFilter">
		<param name="LevelToMatch" value="INFO" />
		<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
		</appender>
		
		<appender name="SQL_LOG"
		class="org.apache.log4j.RollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen-sql.log" />
		<param name="Append" value="true" />
		<param name="MaxFileSize" value="1MB" />
		<param name="MaxBackupIndex" value="10" />
		<layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern"
		value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelRangeFilter">
		<param name="LevelMin" value="DEBUG" />
		<param name="LevelMax" value="WARN" />
		<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
		</appender>
		
		<appender name="DEBUG_LOG"
		class="org.apache.log4j.RollingFileAppender">
		<param name="File" value="D:/RAD8.5/TATEX/GAKUEN/errorLog/gakuen-debug.log" />
		<param name="Append" value="true" />
		<param name="MaxFileSize" value="1MB" />
		<param name="MaxBackupIndex" value="10" />
		<layout class="org.apache.log4j.PatternLayout">
		<param name="ConversionPattern"
		value="%d{yyyy-MM-dd HH:mm:ss.SSS},%-5p,%-100c,%m%n" />
		</layout>
		<filter class="org.apache.log4j.varia.LevelRangeFilter">
		<param name="LevelMin" value="DEBUG" />
		<param name="LevelMax" value="WARN" />
		<param name="AcceptOnMatch" value="true" />
		</filter>
		<filter class="org.apache.log4j.varia.DenyAllFilter" />
		</appender>
		
	-->

	<logger name="com.jast.gakuen.framework.ActionBase"
		additivity="false">
		<level value="info" />
		<appender-ref ref="ERROR" />
		<appender-ref ref="REQUEST_LOG" />
	</logger>

	<logger name="com.jast.gakuen.framework.db.LoggingPreparedStatement"
		additivity="false">
		<level value="debug" />
		<appender-ref ref="ERROR" />
		<appender-ref ref="SQL_LOG" />
	</logger>

	<logger name="com.jast.gakuen.rev" additivity="false">
		<level value="debug" />
		<appender-ref ref="ERROR" />
		<appender-ref ref="DEBUG_LOG" />
	</logger>

	<logger name="com.jast.gakuen.up" additivity="false">
		<level value="debug" />
		<appender-ref ref="ERROR" />
		<appender-ref ref="DEBUG_LOG" />
	</logger>

	<logger name="com.jast.gakuen.sample" additivity="false">
		<level value="debug" />
		<appender-ref ref="ERROR" />
		<appender-ref ref="DEBUG_LOG" />
	</logger>

	<root>
		<level value="warn" />
		<appender-ref ref="STDOUT" />
		<appender-ref ref="ERROR" />
		<appender-ref ref="LOG" />
	</root>

</log4j:configuration>
