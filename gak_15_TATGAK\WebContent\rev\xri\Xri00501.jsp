<%-- 
	学籍情報一括登録
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xri/Xri00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xri00501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >

<SCRIPT type="text/javascript">
// 入力項目指定画面を開く
function openPCos0401() {
	//var type = document.getElementById("form1:htmlFileSelectList").value;
	//if(type == "10") {
		//return true;
	//}
	setTarget("xxxx");
	openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
	return true;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xri00501.onPageLoadBegin}">
<h:form styleClass="form" id="form1">
	
<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xri00501.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xri00501.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xri00501.screenName}"></h:outputText>
</div>	
	
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">

<!-- ↓ここにコンポーネントを配置 -->
<TABLE width="600" border="0" cellpadding="3" cellspacing="0">
	<TBODY>
		<TR>
			<TD align="center">
			<TABLE class="table" width="650">
				<TBODY>
					<!--  
					<TR>
						<TH class="v_a" width="150">
						<h:outputText styleClass="outputText" id="lblFileSelect"
										value="入力ファイル選択" style="#{pc_Xri00501.propFileSelectList.labelStyle}"></h:outputText><BR>
						</TH>
						<TD nowrap width="500">
							<h:selectOneMenu styleClass="selectOneMenu"
								id="htmlFileSelectList"
								value="#{pc_Xri00501.propFileSelectList.value}"
								required="#{pc_Xri00501.propFileSelectList.required}" style="width:250px">
								<f:selectItems value="#{pc_Xri00501.propFileSelectList.list}" />
							</h:selectOneMenu><BR>
						</TD>
					</TR>
					-->
					<TR>
						<TH class="v_b" width="150">
						<h:outputText styleClass="outputText" id="lblFile"
							value="入力ファイル"
							style="#{pc_Xri00501.propInputFile.labelStyle}"></h:outputText><BR>
						<h:outputText styleClass="outputText" id="lblOldFile" value="（前回ファイル）"></h:outputText>
						</TH>
						<TD nowrap width="500">
						<hx:fileupload styleClass="fileupload" id="htmlInputFile" 
							style="width: 495px" value="#{pc_Xri00501.propInputFile.value}">
						<hx:fileProp name="fileName" value="#{pc_Xri00501.propInputFile.fileName}" />
						<hx:fileProp name="contentType" value="#{pc_Xri00501.propInputFile.contentType}" /></hx:fileupload><BR>
						<h:outputText styleClass="outputText" id="htmlInputFileOld" 
							value="#{pc_Xri00501.propInputFileOld.stringValue}"></h:outputText>
						<BR>
						</TD>
					</TR>
					<TR>
						<TH class="v_c" width="150">
						<h:outputText styleClass="outputText" id="lblDataInsertKbn" value="データ登録区分指定"></h:outputText>
						</TH>
						<TD nowrap width="500"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlRegKbn"
										layout="pageDirection"
										value="#{pc_Xri00501.propRegKbn.stringValue}">
										<f:selectItem itemValue="1"
											itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
										<f:selectItem itemValue="4" itemLabel="入力ファイルに指定されたデータを削除します。" />
									</h:selectOneRadio></TD>
					</TR>
					<!--  
					<TR>
						<TH class="v_d" width="150" rowspan="2">
						<h:outputText styleClass="outputText" id="lblKoshinMode" value="更新モード指定"></h:outputText>
						</TH>
						<TD nowrap width="500" style="border-bottom:none;">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" id="htmlKoshinMode"
						 						 value="#{pc_Xri00501.propKoshinMode.checked}">
						</h:selectBooleanCheckbox>差分取り込み</TD>
					</TR>				
					<TR>
						<TD nowrap width="500" style="border-top:none;padding-left:15px">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" id="htmlKoshinModeNew"
						 						 value="#{pc_Xri00501.propKoshinModeNew.checked}">
						</h:selectBooleanCheckbox>同一データが存在しない場合は、新規登録します。<BR>　　（学籍保証人データの差分取り込み時のみ有効）</TD>
					</TR>
					-->			
					<TR>
						<TH class="v_d" width="150">
						<h:outputText styleClass="outputText" id="lblProcessKbn" value="処理区分指定"></h:outputText>
						</TH>
						<TD nowrap width="500">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
						 						 value="#{pc_Xri00501.propSyoriKbn.checked}">
						</h:selectBooleanCheckbox>チェックのみ（データの登録/更新は行いません）</TD>
					</TR>
					<TR>
						<TH rowspan="3" class="v_e" width="150">
						<h:outputText styleClass="outputText" id="lblChekListOutput" value="チェックリスト出力指定"></h:outputText>
						</TH>
						<TD style="border-top-style:none;border-bottom-style:none" width="500">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
						    id="htmlChkListNormal" value="#{pc_Xri00501.propChkListNormal.checked}"></h:selectBooleanCheckbox>
						正常データ</TD>
					</TR>
					<TR>
						<TD style="border-top-style:none;border-bottom-style:none" width="500">
						<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlChkListError" value="#{pc_Xri00501.propChkListError.checked}"></h:selectBooleanCheckbox>
						エラーデータ</TD>
					</TR>
					<TR>
						<TD style="border-top-style:none" width="500"><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
							id="htmlChkListWarning" value="#{pc_Xri00501.propChkListWarning.checked}"></h:selectBooleanCheckbox>
						ワーニングデータ</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE class="button_bar" cellspacing="1" cellpadding="1" width="100%">
				<TR>
					<TD align="center"><hx:commandExButton
						type="submit" value="入力項目指定" styleClass="commandExButton_etc"
						id="setinput" onclick="return openPCos0401();"
						action="#{pc_Xri00501.doSetinputAction}"
						rendered="#{pc_Xri00501.propSetinputBtn.rendered}"></hx:commandExButton>
						<hx:commandExButton	type="submit" value="実　行" styleClass="commandExButton_dat" id="exec"
						action="#{pc_Xri00501.doExecAction}" 
						confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
				</TR>
			</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>

</DIV></DIV></DIV>

<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>