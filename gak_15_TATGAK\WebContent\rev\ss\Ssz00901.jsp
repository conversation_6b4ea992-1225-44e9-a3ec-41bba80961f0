<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function confirmOk() {

	document.getElementById('form1:htmlConfirmVal').value = "1";

	 indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlConfirmVal').value = "0";
	return false;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz00901.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz00901.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz00901.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz00901.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="500">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
							class="table">
							<TBODY>
								<TR>
									<TH width="129" class="v_a"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoOne.labelStyle}"styleClass="outputText" id="text1"
										value="#{pc_Ssz00901.propKaisoNoOne.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisoone"
										value="#{pc_Ssz00901.propKaisoNoOne.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoOne.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoOne.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text21" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_b"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoTwo.labelStyle}"styleClass="outputText" id="text2"
										value="#{pc_Ssz00901.propKaisoNoTwo.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisotwo" value="#{pc_Ssz00901.propKaisoNoTwo.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoTwo.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoTwo.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text22" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_c"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoThree.labelStyle}"styleClass="outputText" id="text3"
										value="#{pc_Ssz00901.propKaisoNoThree.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisothree" value="#{pc_Ssz00901.propKaisoNoThree.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoThree.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoThree.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text23" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_d"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoFour.labelStyle}"styleClass="outputText" id="text4"
										value="#{pc_Ssz00901.propKaisoNoFour.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisofour" value="#{pc_Ssz00901.propKaisoNoFour.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoFour.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoFour.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text24" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_e"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoFive.labelStyle}"styleClass="outputText" id="text5"
										value="#{pc_Ssz00901.propKaisoNoFive.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisofive" value="#{pc_Ssz00901.propKaisoNoFive.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoFive.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoFive.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text25" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_f"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoSix.labelStyle}"styleClass="outputText" id="text6"
										value="#{pc_Ssz00901.propKaisoNoSix.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisosix" value="#{pc_Ssz00901.propKaisoNoSix.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoSix.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoSix.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text26" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_g"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoSeven.labelStyle}"styleClass="outputText" id="text7"
										value="#{pc_Ssz00901.propKaisoNoSeven.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisoseven" value="#{pc_Ssz00901.propKaisoNoSeven.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoSeven.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoSeven.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text27" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_a"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoEight.labelStyle}"styleClass="outputText" id="text8"
										value="#{pc_Ssz00901.propKaisoNoEight.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisoeight" value="#{pc_Ssz00901.propKaisoNoEight.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoEight.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoEight.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text28" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_b"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoNine.labelStyle}"styleClass="outputText" id="text9"
										value="#{pc_Ssz00901.propKaisoNoNine.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisonine" value="#{pc_Ssz00901.propKaisoNoNine.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoNine.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoNine.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text29" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="129" class="v_c"><h:outputText
										style="#{pc_Ssz00901.propKaisoNoTen.labelStyle}"styleClass="outputText" id="text10"
										value="#{pc_Ssz00901.propKaisoNoTen.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisoten" value="#{pc_Ssz00901.propKaisoNoTen.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoNoTen.maxLength}"
										style="#{pc_Ssz00901.propKaisoNoTen.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text30" value="千円未満"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="227"></TD>
					</TR>
				</TBODY>
			</TABLE><BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="500">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_d" width="129"><h:outputText
										style="#{pc_Ssz00901.propKaisoName.labelStyle}"styleClass="outputText" id="lblkaisoname"
										value="#{pc_Ssz00901.propKaisoName.labelName}"></h:outputText></TH>
									<TD width="321"><h:inputText styleClass="inputText"
										id="htmlkaisoname" style="#{pc_Ssz00901.propKaisoName.style}"
										value="#{pc_Ssz00901.propKaisoName.stringValue}"
										maxlength="#{pc_Ssz00901.propKaisoName.maxLength}" size="23">
									</h:inputText><h:outputText styleClass="outputText" id="text33"
										value="（標準は入社２年目です）"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="228"></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR><TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz00901.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
</DIV> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ssz00901.propConfirmVal.stringValue}" id="htmlConfirmVal"></h:inputHidden>

		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

