<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg01501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<TITLE>Xrg01501.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--	title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<SCRIPT type="text/javascript">
// 入力項目指定画面を開く
function openPCos0401() {
	setTarget("xxxx");
	openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
	return true;
}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg01501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg01501.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg01501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg01501.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD nowrap width="80%">
									<CENTER>
									<TABLE class="table" width="650" border="0" cellpadding="0"
										cellspacing="0" style="">
										<TBODY>
											<TR>
												<TH class="v_c" width="150"><h:outputText
													styleClass="outputText" id="lblInputFile"
													value="#{pc_Xrg01501.propInputFile.labelName}"
													style="#{pc_Xrg01501.propInputFile.labelStyle}">
												</h:outputText> <BR>
												(前回ファイル)</TH>
												<TD nowrap width="500"><hx:fileupload
													styleClass="fileupload" id="htmlInputFile" tabindex="1"
													value="#{pc_Xrg01501.propInputFile.value}"
													style="#{pc_Xrg01501.propInputFile.style};width:485px"
													size="50">
													<hx:fileProp name="fileName"
														value="#{pc_Xrg01501.propInputFile.fileName}" />
													<hx:fileProp name="contentType"
														value="#{pc_Xrg01501.propInputFile.contentType}" />
												</hx:fileupload> <BR>
												<h:outputText styleClass="outputText" id="htmlInputFileOld"
													value="#{pc_Xrg01501.propInputFileOld.stringValue}"
													style="#{pc_Xrg01501.propInputFileOld.style}">
												</h:outputText> <BR>
												</TD>
											</TR>
											<TR>
												<TH class="v_d" width="150">
													<h:outputText
														styleClass="outputText" id="lblRegKbn" value="データ登録区分指定">
													</h:outputText>
												</TH>
												<TD nowrap class="" width="500">
													<h:selectOneRadio 
														disabledClass="selectOneRadio_Disabled"
														styleClass="selectOneRadio" id="htmlRegKbn"
														layout="pageDirection"  tabindex="2" 
														value="#{pc_Xrg01501.propRegKbn.stringValue}"
														readonly="#{pc_Xrg01501.propRegKbn.readonly}"
														disabled="#{pc_Xrg01501.propRegKbn.disabled}"
														style="#{pc_Xrg01501.propRegKbn.style}">
														<f:selectItem itemValue="1"
															itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
														<f:selectItem itemValue="2"
															itemLabel="データを登録または更新します。同一データが存在すれば上書き更新します。" />
													</h:selectOneRadio>
												</TD>
											</TR>
											<TR>
												<TH class="v_e" width="150"><h:outputText
													styleClass="outputText" id="lblSyoriKbn" value="処理区分指定">
												</h:outputText></TH>
												<TD nowrap width="500"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
													tabindex="3" value="#{pc_Xrg01501.propSyoriKbn.checked}"
													readonly="#{pc_Xrg01501.propSyoriKbn.readonly}"
													disabled="#{pc_Xrg01501.propSyoriKbn.disabled}"
													style="#{pc_Xrg01501.propSyoriKbn.style}">
												</h:selectBooleanCheckbox> チェックのみ（データの登録/更新は行いません）</TD>
											</TR>
											<TR>
												<TH class="v_e" width="150"><h:outputText
													styleClass="outputText" id="lblCheckList"
													value="チェックリスト出力指定">
												</h:outputText></TH>
												<TD nowrap width="500">
												<TABLE border="0" cellpadding="0" cellspacing="0">
													<TR>
														<TD class="clear_border"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlChkListNormal"
															tabindex="4"
															value="#{pc_Xrg01501.propChkListNormal.checked}"
															readonly="#{pc_Xrg01501.propChkListNormal.readonly}"
															disabled="#{pc_Xrg01501.propChkListNormal.disabled}"
															style="#{pc_Xrg01501.propChkListNormal.style}">
														</h:selectBooleanCheckbox> <h:outputText
															styleClass="outputText" id="lblChkListNormal"
															value="正常データ">
														</h:outputText></TD>
													</TR>
													<TR>
														<TD class="clear_border"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlChkListError"
															tabindex="5"
															value="#{pc_Xrg01501.propChkListError.checked}"
															readonly="#{pc_Xrg01501.propChkListError.readonly}"
															disabled="#{pc_Xrg01501.propChkListError.disabled}"
															style="#{pc_Xrg01501.propChkListError.style}">
														</h:selectBooleanCheckbox> <h:outputText
															styleClass="outputText" id="lblChkListError"
															value="エラーデータ">
														</h:outputText></TD>
													</TR>
													<TR>
														<TD class="clear_border"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox"
															id="htmlChkListWarning" tabindex="6"
															value="#{pc_Xrg01501.propChkListWarning.checked}"
															readonly="#{pc_Xrg01501.propChkListWarning.readonly}"
															disabled="#{pc_Xrg01501.propChkListWarning.disabled}"
															style="#{pc_Xrg01501.propChkListWarning.style}">
														</h:selectBooleanCheckbox> <h:outputText
															styleClass="outputText" id="lblChkListWarning"
															value="ワーニングデータ">
														</h:outputText></TD>
													</TR>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</CENTER>
									</TD>
								</TR>
								<TR>
									<TD nowrap width="80%"></TD>
								</TR>
							</TBODY>
						</TABLE>
						<CENTER>
						<TABLE class="button_bar" width="650">
							<TBODY>
								<TR>
									<TD width="650"><hx:commandExButton type="submit" value="実　行"
										styleClass="commandExButton_dat" id="exec"
										action="#{pc_Xrg01501.doExecAction}"
										confirm="#{msg.SY_MSG_0001W}" tabindex="7"
										disabled="#{pc_Xrg01501.propExec.disabled}"
										style="#{pc_Xrg01501.propExec.style}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</CENTER>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
