<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00202.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg00202.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
}
// 科目検索子画面を呼び出す。
function openKamokuCdSearchWindow(thisObj, thisEvent) {
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	return true;

}
// 科目名称を取得する。
function doKamokuAjax(thisObj, thisEvent) {
	var servlet = "rev/km/KmzKmkAJAX";
	var target = "form1:htmlKamokuName";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function getClassFrwkKbn(thisObj, thisEvent) {
	document.getElementById("form1:htmlHidClassFrwkKbn").value = thisObj.value;
}

// 教室検索子画面を呼び出す。
function openKyoshitsuCdSearchWindow(thisObj, thisEvent) {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0401.jsp?retFieldName=form1:htmlKyoshitsuCd";
	openModalWindow(url, "pCob0401", "<%= com.jast.gakuen.rev.co.PCob0401.getWindowOpenOption() %>");
	return true;

}
// 教室名称・受講可能人数を取得する。
function doKyoshitsuAjax(thisObj, thisEvent) {
	var servlet = "rev/co/CojSistAJAX";
	var target = "form1:htmlKyoshitsuName";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);

}

// 教室コード検索アイコン押下時専用Ajax
function doKyoshitsuJukoNinzuAjax(thisObj, thisEvent) {

	doKyoshitsuAjax(thisObj, thisEvent);

	servlet = "rev/xrg/XrgCojSistJukokanoninzuAJAX";
	target = "form1:htmlJukoKanoNinzu";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// 教員検索子画面を呼び出す。
function openKyoinCdSearchWindow(thisObj, thisEvent) {
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlKyoinCd";
	openModalWindow(url, "pCob0301", "<%= com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return true;

}
// 教員名称を取得する。
function doKyoinAjax(thisObj, thisEvent) {
	var servlet = "rev/co/CoiJinjAJAX";
	var target = "form1:htmlKyoinName";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// 物品検索子画面を呼び出す。
function openBuppinCdSearchWindow(thisObj, thisEvent) {
	var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0101.jsp?retFieldName=form1:htmlBuppinCd";
	openModalWindow(url, "pXrc0101", "<%= com.jast.gakuen.rev.xrc.PXrc0101.getWindowOpenOption() %>");
	return true;

}
// 物品名称を取得する。
function doBuppinAjax(thisObj, thisEvent) {
	var servlet = "rev/xrc/XrcBpnAJAX";
	var target = "form1:htmlBuppinName";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function loadAction(event){
	if (document.getElementById('form1:moveKihonTab').className == "tab_head_on") {
		doKamokuAjax(document.getElementById('form1:htmlKamokuCd'), event);
	} else if (document.getElementById('form1:moveClassTab').className == "tab_head_on") {
		doKyoshitsuAjax(document.getElementById('form1:htmlKyoshitsuCd'), event);
	} else if (document.getElementById('form1:moveKyoinTab').className == "tab_head_on") {
		doKyoinAjax(document.getElementById('form1:htmlKyoinCd'), event);
	} else {
		doBuppinAjax(document.getElementById('form1:htmlBuppinCd'), event);
	}
}

function confirmOk() {
	if (document.getElementById("form1:determinateKyoin")) {
		//削除処理の実行
		indirectClick('determinateKyoin');					
	}
}
function confirmCancel() {
	if (document.getElementById("form1:determinateKyoin")) {
		//更新処理パラメタの初期化
		document.getElementById("form1:executable").value = "0";
	}
}


// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
	if(changeDataFlg == "1"){
		return doPopupMsg(id);
	}else{
		return true;
	}
	return true;
}

// クラス振分有無によるクラス振分区分の制御
function classFrwkControl() {
	var classFrwkUmu = document.getElementsByName("form1:htmlClassFrwkFlg");
	var classFrwkUmuFlg = 1;
	
	for (var i = 0; i < classFrwkUmu.length; i++) {
		if (classFrwkUmu[i].checked) {
			classFrwkUmuFlg = classFrwkUmu[i].value;
		}
	}
	
	if (classFrwkUmuFlg == 1) {
		document.getElementById("form1:htmlClassFrwkKbn").disabled = false;
	} else {
		document.getElementById("form1:htmlClassFrwkKbn").disabled = true;
		document.getElementById("form1:htmlClassFrwkKbn").value = "|no select|";
		document.getElementById("form1:htmlHidClassFrwkKbn").value = "|no select|";
	}
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00202.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00202.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00202.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00202.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right">
						<hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="27" action="#{pc_Xrg00202.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここに戻るボタンを配置 --></DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="800" valign="top">
							<!--↓タブ間共有テーブル↓-->
							<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
								<TBODY>
									<TR>
										<TH class="v_a" width="120">
											<h:outputText
												styleClass="outputText" id="lblTargetNendo"
												value="#{pc_Xrg00202.propTargetNendo.labelName}" 
												style="#{pc_Xrg00202.propTargetNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD colspan="3" width="580">
											<h:outputText styleClass="outputText" id="htmlTargetNendo"
												value="#{pc_Xrg00202.propTargetNendo.stringValue}"
												style="#{pc_Xrg00202.propTargetNendo.style}">
											</h:outputText>
										</TD>
										<TD width="100" rowspan="3"
											style="background-color: transparent; text-align: right"
											class="clear_border">
											<hx:commandExButton type="submit"
												value="確定" styleClass="commandExButton" id="determinateJugyo"
												action="#{pc_Xrg00202.doDeterminateJugyoAction}"
												tabindex="4"
												disabled="#{pc_Xrg00202.propDeterminateJugyo.disabled}"
												style="#{pc_Xrg00202.propDeterminateJugyo.style}"
												rendered="#{pc_Xrg00202.propDeterminateJugyo.rendered}">
											</hx:commandExButton>
											<hx:commandExButton type="submit"
												value="解除" styleClass="commandExButton" id="releaseJugyo"
												action="#{pc_Xrg00202.doReleaseJugyoAction}" tabindex="5"
												style="#{pc_Xrg00202.propReleaseJugyo.style}"
												disabled="#{pc_Xrg00202.propReleaseJugyo.disabled}"
												rendered="#{pc_Xrg00202.propReleaseJugyo.rendered}">
											</hx:commandExButton>
										</TD>
									</TR>
									<TR>
										<TH class="v_b" width="120">
											<h:outputText styleClass="outputText" id="lblSchoolingSbtCd" 
												value="#{pc_Xrg00202.propSchoolingSbtCd.labelName}" 
												style="#{pc_Xrg00202.propSchoolingSbtCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlSchoolingSbtCd"
												value="#{pc_Xrg00202.propSchoolingSbtCd.stringValue}"
												tabindex="1" style="width: 320px"
												readonly="#{pc_Xrg00202.propSchoolingSbtCd.readonly}"
												disabled="#{pc_Xrg00202.propSchoolingSbtCd.disabled}">
												<f:selectItems
													value="#{pc_Xrg00202.propSchoolingSbtCd.list}" />
											</h:selectOneMenu>
										</TD>
										<TH class="v_a" width="120">
											<h:outputText
												styleClass="outputText" id="lblGakunaigaiKbn"
												value="#{pc_Xrg00202.propGakunaigaiKbn.labelName}" 
												style="#{pc_Xrg00202.propGakunaigaiKbn.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:outputText styleClass="outputText" id="htmlGakunaigaiKbn"
												value="#{pc_Xrg00202.propGakunaigaiKbn.stringValue}"
												style="#{pc_Xrg00202.propGakunaigaiKbn.style}">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TH class="v_c" width="120">
											<h:outputText styleClass="outputText" id="lblKaisaiki"
												value="#{pc_Xrg00202.propKaisaiki.labelName}"
												style="#{pc_Xrg00202.propKaisaiki.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:selectOneMenu styleClass="selectOneMenu"
												id="htmlKaisaiki"
												value="#{pc_Xrg00202.propKaisaiki.stringValue}"
												tabindex="2" style="width: 100px"
												readonly="#{pc_Xrg00202.propKaisaiki.readonly}"
												disabled="#{pc_Xrg00202.propKaisaiki.disabled}">
												<f:selectItems
													value="#{pc_Xrg00202.propKaisaiki.list}" />
											</h:selectOneMenu>
										</TD>
										<TH class="v_c" width="120">
											<h:outputText styleClass="outputText" id="lblJugyoCd"
												value="#{pc_Xrg00202.propJugyoCd.labelName}"
												style="#{pc_Xrg00202.propJugyoCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD>
											<h:inputText styleClass="inputText" id="htmlJugyoCd"
												onchange="onChangeData();"
												value="#{pc_Xrg00202.propJugyoCd.stringValue}"
												size="10" tabindex="3"
												disabled="#{pc_Xrg00202.propJugyoCd.disabled}"
												readonly="#{pc_Xrg00202.propJugyoCd.readonly}"
												maxlength="#{pc_Xrg00202.propJugyoCd.max}"
												style="#{pc_Xrg00202.propJugyoCd.style}">
											</h:inputText>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<!--↑タブ間共有テーブル↑-->
							<BR>
							<!--↓タブ用テーブル↓-->
							<TABLE border="0" cellpadding="20" cellspacing="0">
								<TBODY>
									<TR>
										<TD width="800" align="left">
										<TABLE border="0" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveKihonTab"
															value="基本情報" styleClass="#{pc_Xrg00202.propMoveKihonTab.style}" tabindex="6"
															action="#{pc_Xrg00202.doMoveKihonTabAction}"
															disabled="#{pc_Xrg00202.propMoveKihonTab.disabled}"
															style="#{pc_Xrg00202.propMoveKihonTab.style}">
														</hx:commandExButton>
													</TD>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveClassTab"
															value="クラス情報" styleClass="#{pc_Xrg00202.propMoveClassTab.style}" tabindex="7"
															action="#{pc_Xrg00202.doMoveClassTabAction}"
															disabled="#{pc_Xrg00202.propMoveClassTab.disabled}"
															style="#{pc_Xrg00202.propMoveClassTab.style}">
														</hx:commandExButton>
													</TD>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveKyoinTab"
															value="教員情報" styleClass="#{pc_Xrg00202.propMoveKyoinTab.style}" tabindex="8"
															action="#{pc_Xrg00202.doMoveKyoinTabAction}"
															disabled="#{pc_Xrg00202.propMoveKyoinTab.disabled}"
															style="#{pc_Xrg00202.propMoveKyoinTab.style}">
														</hx:commandExButton>
													</TD>
													<TD class="tab_head_off">
														<hx:commandExButton type="submit" id="moveHaihonTab"
															value="配本情報" styleClass="#{pc_Xrg00202.propMoveHaihonTab.style}" tabindex="9"
															action="#{pc_Xrg00202.doMoveHaihonTabAction}"
															disabled="#{pc_Xrg00202.propMoveHaihonTab.disabled}"
															style="#{pc_Xrg00202.propMoveHaihonTab.style}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
									<!-- ↓タブ内表示項目↓ -->
									<!-- 基本情報タブ -->
									<TR>
										<hx:jspPanel id="kihonTabPanel"
											rendered="#{pc_Xrg00202.propKihonTabPanel.rendered}">
											<TD valign="top">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="tab_body" width="100%">
													<TBODY>
														<TR>
															<TD valign="top" height="370">
																<CENTER>
																	<BR>
																	<!-- 1つ目 -->
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		class="table" width="90%">
																		<TBODY>
																			<TR>
																				<TH class="v_a" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblKamokuCd" 
																						value="#{pc_Xrg00202T01.propKamokuCd.labelName}"
																						style="#{pc_Xrg00202T01.propKamokuCd.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText"
																						id="htmlKamokuCd" tabindex="10"
																						value="#{pc_Xrg00202T01.propKamokuCd.stringValue}"
																						readonly="#{pc_Xrg00202T01.propKamokuCd.readonly}"
																						style="#{pc_Xrg00202T01.propKamokuCd.style}"
																						disabled="#{pc_Xrg00202T01.propKamokuCd.disabled}"
																						maxlength="#{pc_Xrg00202T01.propKamokuCd.max}"
																						onblur="return doKamokuAjax(this, event);"
																						onkeyup="onChangeData();"
																						size="15">
																					</h:inputText>
																					<hx:commandExButton type="button"
																						styleClass="commandExButton_search" id="searchKamokuCd"
																						onclick="return openKamokuCdSearchWindow(this, event);"
																						tabindex="11"
																						disabled="#{pc_Xrg00202T01.propSearchKamokuCd.disabled}"
																						style="#{pc_Xrg00202T01.propSearchKamokuCd.style}">
																					</hx:commandExButton>
																					<h:outputText styleClass="outputText"
																						id="htmlKamokuName" 
																						value="#{pc_Xrg00202T01.propKamokuName.stringValue}"
																						style="#{pc_Xrg00202T01.propKamokuName.style}">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblJigen"
																						value="#{pc_Xrg00202T01.propJigen.labelName}" 
																						style="#{pc_Xrg00202T01.propJigen.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:selectOneMenu styleClass="selectOneMenu"
																						id="htmlJigen"
																						value="#{pc_Xrg00202T01.propJigen.value}"
																						tabindex="12" style="width: 80px"
																						readonly="#{pc_Xrg00202T01.propJigen.readonly}"
																						disabled="#{pc_Xrg00202T01.propJigen.disabled}"
																						onkeyup="onChangeData();">
																						<f:selectItems
																							value="#{pc_Xrg00202T01.propJigen.list}" />
																					</h:selectOneMenu>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblJugyoName"
																						value="#{pc_Xrg00202T01.propJugyoName.labelName}" 
																						style="#{pc_Xrg00202T01.propJugyoName.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText"
																						id="htmlJugyoName" tabindex="13"
																						value="#{pc_Xrg00202T01.propJugyoName.stringValue}"
																						readonly="#{pc_Xrg00202T01.propJugyoName.readonly}"
																						style="#{pc_Xrg00202T01.propJugyoName.style}"
																						disabled="#{pc_Xrg00202T01.propJugyoName.disabled}"
																						maxlength="#{pc_Xrg00202T01.propJugyoName.maxLength}"
																						onkeyup="onChangeData();"
																						size="60">
																					</h:inputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblJugyoNameKana"
																						value="#{pc_Xrg00202T01.propJugyoNameKana.labelName}" 
																						style="#{pc_Xrg00202T01.propJugyoNameKana.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText"
																						id="htmlJugyoNameKana" tabindex="14"
																						value="#{pc_Xrg00202T01.propJugyoNameKana.stringValue}"
																						readonly="#{pc_Xrg00202T01.propJugyoNameKana.readonly}"
																						style="#{pc_Xrg00202T01.propJugyoNameKana.style}"
																						disabled="#{pc_Xrg00202T01.propJugyoNameKana.disabled}"
																						maxlength="#{pc_Xrg00202T01.propJugyoNameKana.maxLength}"
																						onkeyup="onChangeData();"
																						size="60">
																					</h:inputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblJugyoNameEng"
																						value="#{pc_Xrg00202T01.propJugyoNameEng.labelName}" 
																						style="#{pc_Xrg00202T01.propJugyoNameEng.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText"
																						id="htmlJugyoNameEng" tabindex="15"
																						value="#{pc_Xrg00202T01.propJugyoNameEng.stringValue}"
																						readonly="#{pc_Xrg00202T01.propJugyoNameEng.readonly}"
																						style="#{pc_Xrg00202T01.propJugyoNameEng.style}"
																						disabled="#{pc_Xrg00202T01.propJugyoNameEng.disabled}"
																						maxlength="#{pc_Xrg00202T01.propJugyoNameEng.maxLength}"
																						onkeyup="onChangeData();"
																						size="60">
																					</h:inputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblJugyoNameWeb"
																						value="#{pc_Xrg00202T01.propJugyoNameWeb.labelName}" 
																						style="#{pc_Xrg00202T01.propJugyoNameWeb.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText"
																						id="htmlJugyoNameWeb" tabindex="16"
																						value="#{pc_Xrg00202T01.propJugyoNameWeb.stringValue}"
																						readonly="#{pc_Xrg00202T01.propJugyoNameWeb.readonly}"
																						style="#{pc_Xrg00202T01.propJugyoNameWeb.style}"
																						disabled="#{pc_Xrg00202T01.propJugyoNameWeb.disabled}"
																						maxlength="#{pc_Xrg00202T01.propJugyoNameWeb.maxLength}"
																						onkeyup="onChangeData();"
																						size="60">
																					</h:inputText>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<BR>
																	<!-- 2つ目 -->
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		class="table" width="90%">
																		<TBODY>
																			<TR>
																				<TH class="v_a" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblJugyoKaisu"
																						value="#{pc_Xrg00202T01.propJugyoKaisu.labelName}"
																						style="#{pc_Xrg00202T01.propJugyoKaisu.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText"
																						id="htmlJugyoKaisu"
																						value="#{pc_Xrg00202T01.propJugyoKaisu.integerValue}" size="10"
																						tabindex="17"
																						disabled="#{pc_Xrg00202T01.propJugyoKaisu.disabled}"
																						readonly="#{pc_Xrg00202T01.propJugyoKaisu.readonly}"
																						style="#{pc_Xrg00202T01.propJugyoKaisu.style}"
																						maxlength="#{pc_Xrg00202T01.propJugyoKaisu.max}"
																						onkeyup="onChangeData();">
																						<f:convertNumber type="number" pattern="##0"/>
																						<hx:inputHelperAssist errorClass="inputText_Error"
																							 promptCharacter="_" />
																					</h:inputText>
																					<h:outputText styleClass="outputText"
																						id="lblKai"
																						value="回"
																						style="#{pc_Xrg00202T01.propJugyoKaisu.style}">
																					</h:outputText>
																				</TD>
																				<TH class="v_a" width="170">
																					<h:outputText styleClass="outputText"
																						id="lblKomasu"
																						value="#{pc_Xrg00202T01.propKomasu.labelName}"
																						style="#{pc_Xrg00202T01.propKomasu.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText"
																						id="htmlKomasu"
																						value="#{pc_Xrg00202T01.propKomasu.doubleValue}" size="10"
																						tabindex="18"
																						disabled="#{pc_Xrg00202T01.propKomasu.disabled}"
																						readonly="#{pc_Xrg00202T01.propKomasu.readonly}"
																						style="#{pc_Xrg00202T01.propKomasu.style}"
																						maxlength="#{pc_Xrg00202T01.propKomasu.max}"
																						onkeyup="onChangeData();">
																						<f:convertNumber type="number" pattern="##0.0"/>
																						<hx:inputHelperAssist errorClass="inputText_Error"
																							 promptCharacter="_" />
																					</h:inputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="200">
																					<h:outputText styleClass="outputText"
																						id="lblClassFrwkFlg"
																						value="#{pc_Xrg00202T01.propClassFrwkFlg.labelName}" 
																						style="#{pc_Xrg00202T01.propClassFrwkFlg.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																						styleClass="selectOneRadio" id="htmlClassFrwkFlg" tabindex="19"
																						value="#{pc_Xrg00202T01.propClassFrwkFlg.value}" 
																						style="#{pc_Xrg00202T01.propClassFrwkFlg.style}"
																						disabled="#{pc_Xrg00202T01.propClassFrwkFlg.disabled}"
																						readonly="#{pc_Xrg00202T01.propClassFrwkFlg.readonly}"
																						onclick="classFrwkControl();"
																						onkeyup="onChangeData();">
																						<f:selectItems value="#{pc_Xrg00202T01.propClassFrwkFlg.list}" />
																					</h:selectOneRadio>
																				</TD>
																				<TH class="v_b" width="170">
																					<h:outputText styleClass="outputText"
																						id="lblClassFrwkKbn"
																						value="#{pc_Xrg00202T01.propClassFrwkKbn.labelName}" 
																						style="#{pc_Xrg00202T01.propClassFrwkKbn.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:selectOneMenu styleClass="selectOneMenu"
																						id="htmlClassFrwkKbn"
																						value="#{pc_Xrg00202T01.propClassFrwkKbn.value}"
																						tabindex="20" style="width: 200px"
																						readonly="#{pc_Xrg00202T01.propClassFrwkKbn.readonly}"
																						disabled="#{pc_Xrg00202T01.propClassFrwkKbn.disabled}"
																						onchange="return getClassFrwkKbn(this, event);"
																						onkeyup="onChangeData();">
																						<f:selectItems
																							value="#{pc_Xrg00202T01.propClassFrwkKbn.list}" />
																					</h:selectOneMenu>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_c" width="200">
																					<h:outputText styleClass="outputText" 
																						id="lblShussekirt" 
																						value="#{pc_Xrg00202T01.propShussekirt.labelName}" 
																						style="#{pc_Xrg00202T01.propShussekirt.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:inputText styleClass="inputText"
																						id="htmlShussekirt" size="6"
																						onchange="onChangeData();"
																						value="#{pc_Xrg00202T01.propShussekirt.doubleValue}"
																						tabindex="21"
																						disabled="#{pc_Xrg00202T01.propShussekirt.disabled}"
																						readonly="#{pc_Xrg00202T01.propShussekirt.readonly}"
																						maxlength="#{pc_Xrg00202T01.propShussekirt.max}"
																						style="#{pc_Xrg00202T01.propShussekirt.style}">
																						<f:convertNumber type="number" pattern="##0.0"/>
																						<hx:inputHelperAssist errorClass="inputText_Error"
																							 promptCharacter="_" />
																					</h:inputText>
																					<h:outputText styleClass="outputText"
																						id="lblPercent"
																						value="%"
																						style="#{pc_Xrg00202T01.propShussekirt.style}">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_d" width="200">
																					<h:outputText styleClass="outputText" 
																						id="lblKaisaichi" 
																						value="#{pc_Xrg00202T01.propKaisaichi.labelName}" 
																						style="#{pc_Xrg00202T01.propKaisaichi.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:selectOneMenu styleClass="selectOneMenu"
																						id="htmlKaisaichi"
																						value="#{pc_Xrg00202T01.propKaisaichi.value}"
																						tabindex="22" style="width: 300px"
																						readonly="#{pc_Xrg00202T01.propKaisaichi.readonly}"
																						disabled="#{pc_Xrg00202T01.propKaisaichi.disabled}"
																						onkeyup="onChangeData();">
																						<f:selectItems
																							value="#{pc_Xrg00202T01.propKaisaichi.list}" />
																					</h:selectOneMenu>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_e" width="200">
																					<h:outputText styleClass="outputText" 
																						id="lblSchCalcPtn" 
																						value="#{pc_Xrg00202T01.propSchCalcPtn.labelName}" 
																						style="#{pc_Xrg00202T01.propSchCalcPtn.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:selectOneMenu styleClass="selectOneMenu"
																						id="htmlSchCalcPtn"
																						value="#{pc_Xrg00202T01.propSchCalcPtn.value}"
																						tabindex="23" style="width: 400px"
																						readonly="#{pc_Xrg00202T01.propSchCalcPtn.readonly}"
																						disabled="#{pc_Xrg00202T01.propSchCalcPtn.disabled}"
																						onkeyup="onChangeData();">
																						<f:selectItems
																							value="#{pc_Xrg00202T01.propSchCalcPtn.list}" />
																					</h:selectOneMenu>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="200">
																					<h:outputText styleClass="outputText" 
																						id="lblReportFlg" 
																						value="#{pc_Xrg00202T01.propReportFlg.labelName}" 
																						style="#{pc_Xrg00202T01.propReportFlg.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:selectBooleanCheckbox
																						styleClass="selectBooleanCheckbox"
																						id="htmlReportFlg"
																						onchange="onChangeData();"
																						value="#{pc_Xrg00202T01.propReportFlg.checked}"
																						tabindex="24"
																						disabled="#{pc_Xrg00202T01.propReportFlg.disabled}"
																						readonly="#{pc_Xrg00202T01.propReportFlg.readonly}"
																						style="#{pc_Xrg00202T01.propReportFlg.style}">
																					</h:selectBooleanCheckbox>
																					レポート有
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<TABLE border="0" cellspacing="0" class="button_bar" width="90%">
																		<TBODY>
																			<TR>
																				<TD nowrap align="left">
																					<hx:commandExButton type="submit"
																						value="クラス設定" styleClass="commandExButton" id="setClass"
																						action="#{pc_Xrg00202.doSetClassAction}"
																						tabindex="26"
																						disabled="#{pc_Xrg00202T01.propSetClass.disabled}"
																						style="#{pc_Xrg00202T01.propSetClass.style}"
																						rendered="#{pc_Xrg00202T01.propSetClass.rendered}">
																					</hx:commandExButton>
																				</TD>
																				<TD nowrap align="right">
																					<hx:commandExButton type="submit" value="確定"
																						styleClass="commandExButton_dat" id="determinateBase"
																						confirm="#{msg.SY_MSG_0002W}" tabindex="25"
																						action="#{pc_Xrg00202.doDeterminateBaseAction}"
																						disabled="#{pc_Xrg00202T01.propDeterminateBase.disabled}"
																						style="#{pc_Xrg00202T01.propDeterminateBase.style}"
																						rendered="#{pc_Xrg00202T01.propDeterminateBase.rendered}">
																					</hx:commandExButton>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																</CENTER>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
											</TD>
										</hx:jspPanel>
									</TR>
									<!-- クラス情報タブ -->
									<TR>
										<hx:jspPanel id="classTabPanel"
											rendered="#{pc_Xrg00202.propClassTabPanel.rendered}">
											<TD valign="top">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="tab_body" width="100%">
													<TBODY>
														<TR>
															<TD valign="top" height="370">
																<CENTER>
																	<!-- ↓データテーブル部↓ -->
																	<TABLE border="0" cellpadding="5" width="737">
																		<TBODY>
																			<TR>
																				<TD align="left">
																					<h:outputText styleClass="outputText"
																							id="lblClassListCount"
																							value="クラス情報">
																					</h:outputText>
																				</TD>
																				<TD align="right">
																					<h:outputText styleClass="outputText"
																							id="htmlClassListCount"
																							value="#{pc_Xrg00202T02.propClassListCount.stringValue}">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TD colspan="2">
																					<DIV style="height:145px" class="listScroll" id="classListScroll" onscroll="setScrollPosition('htmlClassTableScroll',this);">
																						<h:dataTable border="0" cellpadding="0"
																							cellspacing="0" columnClasses="columnClass"
																							headerClass="headerClass" footerClass="footerClass"
																							rowClasses="#{pc_Xrg00202T02.propClassList.rowClasses}"
																							styleClass="meisai_scroll" id="htmlClassList" width="720"
																							value="#{pc_Xrg00202T02.propClassList.list}"
																							var="varlist"
																							style="#{pc_Xrg00202T02.propClassList.style}">
																							<h:column id="column01">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText" value="クラス番号"
																										id="lblClassNoColumn">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlClassNoColumn"
																									styleClass="outputText"
																									value="#{varlist.classNo}" style="width: 70px">
																								</h:outputText>
																								<f:attribute value="70" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column02">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblClassNameColumn" value="クラス名">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlClassNameColumn"
																									styleClass="outputText"
																									value="#{varlist.propClassNameColumn.displayValue}"
																									title="#{varlist.propClassNameColumn.stringValue}"
																									style="width: 165px">
																									<f:convertNumber />
																								</h:outputText>
																								<f:attribute value="165" name="width" />
																							</h:column>
																							<h:column id="column03">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblKyoshitsuNameColumn" value="教室名">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlKyoshitsuNameColumn"
																									styleClass="outputText"
																									value="#{varlist.propKyoshitsuNameColumn.displayValue}"
																									title="#{varlist.propKyoshitsuNameColumn.stringValue}"
																									style="width: 165px">
																								</h:outputText>
																								<f:attribute value="165" name="width" />
																							</h:column>
																							<h:column id="column04">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblJukoKanoNinzuColumn" value="受講可能人数">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlJukoKanoNinzuColumn"
																									styleClass="outputText" style="width: 90px"
																									value="#{varlist.jukoKanoNinzu}"
																									escape="false">
																								</h:outputText>
																								<f:attribute value="90" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column05">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblTsuishiKbnColumn" value="追試方法区分">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlTsuishiKbnColumn"
																									styleClass="outputText"
																									value="#{varlist.tsuishiKbnColumn}"
																									style="width: 90px">
																								</h:outputText>
																								<f:attribute value="90" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column06">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblShucchoKogiFlgColumn" value="出張講義フラグ">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlShucchoKogiFlgColumn"
																									styleClass="outputText" style="width: 105px" escape="false"
																									value="#{varlist.shucchoKogiFlgColumn}">
																								</h:outputText>
																								<f:attribute value="105" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column07">
																								<f:facet name="header">
																								</f:facet>
																								<hx:commandExButton type="submit" value="選択"
																									onclick="onChangeData();"
																									styleClass="commandExButton" id="select" tabindex="27"
																									action="#{pc_Xrg00202T02.doSelectAction}"
																									style="width: 35px">
																								</hx:commandExButton>
																								<f:attribute value="35" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																						</h:dataTable>
																					</DIV>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<!-- ↑データテーブル部↑ -->
																	<HR width="100%" class="hr" noshade>
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		class="table" width="737">
																		<TBODY>
																			<TR>
																				<TH class="v_a" width="150">
																					<h:outputText styleClass="outputText" id="lblClassNo"
																						value="#{pc_Xrg00202T02.propClassNo.labelName}"
																						style="#{pc_Xrg00202T02.propClassNo.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:inputText styleClass="inputText" id="htmlClassNo"
																						onkeyup="onChangeData();"
																						value="#{pc_Xrg00202T02.propClassNo.stringValue}"
																						size="2" tabindex="28"
																						disabled="#{pc_Xrg00202T02.propClassNo.disabled}"
																						readonly="#{pc_Xrg00202T02.propClassNo.readonly}"
																						maxlength="#{pc_Xrg00202T02.propClassNo.max}"
																						style="#{pc_Xrg00202T02.propClassNo.style}">
																					</h:inputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="150">
																					<h:outputText styleClass="outputText" id="lblClassName"
																						value="#{pc_Xrg00202T02.propClassName.labelName}"
																						style="#{pc_Xrg00202T02.propClassName.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:inputText styleClass="inputText"
																						id="htmlClassName" size="50"
																						onchange="onChangeData();"
																						value="#{pc_Xrg00202T02.propClassName.stringValue}"
																						tabindex="29"
																						disabled="#{pc_Xrg00202T02.propClassName.disabled}"
																						readonly="#{pc_Xrg00202T02.propClassName.readonly}"
																						maxlength="#{pc_Xrg00202T02.propClassName.maxLength}"
																						style="#{pc_Xrg00202T02.propClassName.style}">
																					</h:inputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="150">
																					<h:outputText styleClass="outputText" id="lblKyoshitsuCd"
																						value="#{pc_Xrg00202T02.propKyoshitsuCd.labelName}"
																						style="#{pc_Xrg00202T02.propKyoshitsuCd.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:inputText styleClass="inputText"
																						id="htmlKyoshitsuCd" size="10"
																						onchange="onChangeData();"
																						value="#{pc_Xrg00202T02.propKyoshitsuCd.stringValue}"
																						tabindex="30"
																						disabled="#{pc_Xrg00202T02.propKyoshitsuCd.disabled}"
																						readonly="#{pc_Xrg00202T02.propKyoshitsuCd.readonly}"
																						maxlength="#{pc_Xrg00202T02.propKyoshitsuCd.max}"
																						onblur="return doKyoshitsuJukoNinzuAjax(this, event);"
																						style="#{pc_Xrg00202T02.propKyoshitsuCd.style}">
																					</h:inputText>
																					<hx:commandExButton type="button"
																						styleClass="commandExButton_search" id="searchKyoshitsuCd"
																						onclick="return openKyoshitsuCdSearchWindow(this, event);"
																						tabindex="31"
																						disabled="#{pc_Xrg00202T02.propSearchKyoshitsuCd.disabled}"
																						style="#{pc_Xrg00202T02.propSearchKyoshitsuCd.style}">
																					</hx:commandExButton>
																					<h:outputText styleClass="outputText" id="htmlKyoshitsuName"
																						style="#{pc_Xrg00202T02.propKyoshitsuCd.labelStyle}">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_c" width="150">
																					<h:outputText styleClass="outputText" id="lblJukoKanoNinzu"
																						value="#{pc_Xrg00202T02.propJukoKanoNinzu.labelName}"
																						style="#{pc_Xrg00202T02.propJukoKanoNinzu.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText" id="htmlJukoKanoNinzu"
																						onkeyup="onChangeData();"
																						value="#{pc_Xrg00202T02.propJukoKanoNinzu.integerValue}"
																						size="5" tabindex="32"
																						disabled="#{pc_Xrg00202T02.propJukoKanoNinzu.disabled}"
																						readonly="#{pc_Xrg00202T02.propJukoKanoNinzu.readonly}"
																						maxlength="#{pc_Xrg00202T02.propJukoKanoNinzu.max}"
																						style="#{pc_Xrg00202T02.propJukoKanoNinzu.style}">
																						<f:convertNumber type="number" pattern="####0"/>
																						<hx:inputHelperAssist errorClass="inputText_Error"
																							 promptCharacter="_" />
																					</h:inputText>
																					<h:outputText styleClass="outputText"
																						id="lblNin"
																						value="人"
																						style="#{pc_Xrg00202T02.propJukoKanoNinzu.labelStyle}">
																					</h:outputText>
																				</TD>
																				<TH class="v_c" width="150">
																					<h:outputText styleClass="outputText" id="lblTsuishiKbn"
																						value="#{pc_Xrg00202T02.propTsuishiKbn.labelName}"
																						style="#{pc_Xrg00202T02.propTsuishiKbn.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:selectOneMenu styleClass="selectOneMenu"
																						id="htmlTsuishiKbn"
																						onchange="onChangeData();"
																						value="#{pc_Xrg00202T02.propTsuishiKbn.value}"
																						tabindex="33" style="width: 200px"
																						disabled="#{pc_Xrg00202T02.propTsuishiKbn.disabled}">
																						<f:selectItems
																							value="#{pc_Xrg00202T02.propTsuishiKbn.list}" />
																					</h:selectOneMenu>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_d" width="150">
																					<h:outputText styleClass="outputText" id="lblShucchoKogiFlg"
																						value="#{pc_Xrg00202T02.propShucchoKogiFlg.labelName}"
																						style="#{pc_Xrg00202T02.propShucchoKogiFlg.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:selectBooleanCheckbox
																						styleClass="selectBooleanCheckbox"
																						id="htmlShucchoKogiFlg"
																						onchange="onChangeData();"
																						value="#{pc_Xrg00202T02.propShucchoKogiFlg.checked}"
																						tabindex="34"
																						disabled="#{pc_Xrg00202T02.propShucchoKogiFlg.disabled}"
																						readonly="#{pc_Xrg00202T02.propShucchoKogiFlg.readonly}"
																						style="#{pc_Xrg00202T02.propShucchoKogiFlg.style}">
																					</h:selectBooleanCheckbox>
																					出張講義
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<TABLE border="0" cellspacing="0" class="button_bar" width="737">
																		<TBODY>
																			<TR>
																				<TD nowrap align="left">
																					<hx:commandExButton type="submit"
																						value="教員設定" styleClass="commandExButton" id="setKyoin"
																						action="#{pc_Xrg00202.doSetKyoinAction}"
																						tabindex="37"
																						disabled="#{pc_Xrg00202T02.propSetKyoin.disabled}"
																						style="#{pc_Xrg00202T02.propSetKyoin.style}"
																						rendered="#{pc_Xrg00202T02.propSetKyoin.rendered}">
																					</hx:commandExButton>
																					<hx:commandExButton type="submit"
																						value="配本設定" styleClass="commandExButton" id="setHaihon"
																						action="#{pc_Xrg00202.doSetHaihonAction}"
																						tabindex="38"
																						disabled="#{pc_Xrg00202T02.propSetHaihon.disabled}"
																						style="#{pc_Xrg00202T02.propSetHaihon.style}"
																						rendered="#{pc_Xrg00202T02.propSetHaihon.rendered}">
																					</hx:commandExButton>
																				</TD>
																				<TD nowrap align="right">
																					<hx:commandExButton type="submit" value="確定"
																						styleClass="commandExButton_dat" id="determinateClass"
																						confirm="#{msg.SY_MSG_0002W}" tabindex="35"
																						action="#{pc_Xrg00202.doDeterminateClassAction}"
																						disabled="#{pc_Xrg00202T02.propDeterminateClass.disabled}"
																						style="#{pc_Xrg00202T02.propDeterminateClass.style}"
																						rendered="#{pc_Xrg00202T02.propDeterminateClass.rendered}">
																					</hx:commandExButton>
																					<hx:commandExButton type="submit" value="削除"
																						styleClass="commandExButton_dat" id="deleteClass"
																						confirm="#{msg.SY_MSG_0004W}" tabindex="36"
																						action="#{pc_Xrg00202.doDeleteClassAction}"
																						disabled="#{pc_Xrg00202T02.propDeleteClass.disabled}"
																						style="#{pc_Xrg00202T02.propDeleteClass.style}"
																						rendered="#{pc_Xrg00202T02.propDeleteClass.rendered}">
																					</hx:commandExButton>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																</CENTER>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
											</TD>
										</hx:jspPanel>
									</TR>
									<!-- 教員情報タブ -->
									<TR>
										<hx:jspPanel id="kyoinTabPanel"
											rendered="#{pc_Xrg00202.propKyoinTabPanel.rendered}">
											<TD valign="top">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="tab_body" width=100%">
													<TBODY>
														<TR>
															<TD valign="top" height="370" width="90%">
																<CENTER>
																	<TABLE border="0" cellpadding="5" width="720">
																		<TBODY>
																			<TR>
																				<TD align="left">
																					<h:outputText styleClass="outputText"
																							id="lblSetteiTaisho_Kyoin"
																							value="設定対象">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0"
																						class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH class="v_a" width="80">
																									<h:outputText styleClass="outputText" id="lblJugyoName_Kyoin"
																										value="#{pc_Xrg00202T03.propJugyoName_Kyoin.labelName}"
																										style="#{pc_Xrg00202T03.propJugyoName_Kyoin.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD>
																									<h:outputText styleClass="outputText" id="htmlJugyoName_Kyoin"
																										value="#{pc_Xrg00202T03.propJugyoName_Kyoin.displayValue}"
																										style="#{pc_Xrg00202T03.propJugyoName_Kyoin.style}">
																									</h:outputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_b" width="80">
																									<h:outputText styleClass="outputText" id="lblClassName_Kyoin"
																										value="#{pc_Xrg00202T03.propClassName_Kyoin.labelName}"
																										style="#{pc_Xrg00202T03.propClassName_Kyoin.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD>
																									<h:outputText styleClass="outputText" id="htmlClassName_Kyoin"
																										value="#{pc_Xrg00202T03.propClassName_Kyoin.displayValue}"
																										title="#{pc_Xrg00202T03.propClassName_Kyoin.stringValue}"
																										style="#{pc_Xrg00202T03.propClassName_Kyoin.style}">
																									</h:outputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<BR>
																	<!-- ↓データテーブル部↓ -->
																	<TABLE border="0" cellpadding="5" width="720">
																		<TBODY>
																			<TR>
																				<TD align="left">
																					<h:outputText styleClass="outputText"
																							id="lblKyoinListCount"
																							value="教員情報">
																					</h:outputText>
																				</TD>
																				<TD align="right">
																					<h:outputText styleClass="outputText"
																							id="htmlKyoinListCount"
																							value="#{pc_Xrg00202T03.propKyoinListCount.stringValue}">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TD colspan="2">
																					<DIV style="height:145px" class="listScroll" id="kyoinListScroll" onscroll="setScrollPosition('htmlKyoinTableScroll',this);">
																						<h:dataTable border="0" cellpadding="0"
																							cellspacing="0" columnClasses="columnClass"
																							headerClass="headerClass" footerClass="footerClass"
																							rowClasses="#{pc_Xrg00202T03.propKyoinList.rowClasses}"
																							styleClass="meisai_scroll" id="htmlKyoinList" width="700"
																							value="#{pc_Xrg00202T03.propKyoinList.list}"
																							var="varlist"
																							style="#{pc_Xrg00202T03.propKyoinList.style}">
																							<h:column id="column11">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText" value="教員コード"
																										id="lblKyoinCdColumn">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlKyoinCdColumn"
																									styleClass="outputText"
																									value="#{varlist.kyoinCd}" style="width: 90px">
																								</h:outputText>
																								<f:attribute value="90" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column12">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblKyoinNameColumn" value="教員名">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlKyoinNameColumn"
																									styleClass="outputText"
																									value="#{varlist.propKyoinNameColumn.displayValue}"
																									title="#{varlist.propKyoinNameColumn.stringValue}"
																									style="width: 320px">
																								</h:outputText>
																								<f:attribute value="300" name="width" />
																							</h:column>
																							<h:column id="column13">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblKomasuColumn" value="コマ数">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlKomasuColumn"
																									styleClass="outputText"
																									value="#{varlist.komasu}" style="width: 55px">
																								</h:outputText>
																								<f:attribute value="55" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column14">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblIshokuShodakubiColumn" value="委嘱承諾日">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlIshokuShodakubiColumn"
																									styleClass="outputText" style="width: 100px"
																									value="#{varlist.ishokuShodakubi}">
																								</h:outputText>
																								<f:attribute value="100" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column15">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblIshokuIraibiColumn" value="委嘱依頼日">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlIshokuIraibiColumn"
																									styleClass="outputText" value="#{varlist.ishokuIraibi}"
																									style="width: 100px">
																								</h:outputText>
																								<f:attribute value="100" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																							<h:column id="column16">
																								<f:facet name="header">
																								</f:facet>
																								<hx:commandExButton type="submit" value="選択"
																									onclick="onChangeData();"
																									styleClass="commandExButton" id="select_Kyoin" tabindex="39"
																									action="#{pc_Xrg00202T03.doSelectAction}"
																									style="width: 35px">
																								</hx:commandExButton>
																								<f:attribute value="35" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																						</h:dataTable>
																					</DIV>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<!-- ↑データテーブル部↑ -->
																	<HR width="100%" class="hr" noshade>
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		class="table" width="90%">
																		<TBODY>
																			<TR>
																				<TH class="v_a" width="150">
																					<h:outputText styleClass="outputText" id="lblKyoinCd"
																						value="#{pc_Xrg00202T03.propKyoinCd.labelName}"
																						style="#{pc_Xrg00202T03.propKyoinCd.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD colspan="3">
																					<h:inputText styleClass="inputText" id="htmlKyoinCd"
																						onkeyup="onChangeData();"
																						value="#{pc_Xrg00202T03.propKyoinCd.stringValue}"
																						size="20" tabindex="40"
																						disabled="#{pc_Xrg00202T03.propKyoinCd.disabled}"
																						readonly="#{pc_Xrg00202T03.propKyoinCd.readonly}"
																						maxlength="#{pc_Xrg00202T03.propKyoinCd.max}"
																						onblur="return doKyoinAjax(this, event);"
																						style="#{pc_Xrg00202T03.propKyoinCd.style}">
																					</h:inputText>
																					<hx:commandExButton type="button"
																						styleClass="commandExButton_search" id="searchKyoinCd"
																						onclick="return openKyoinCdSearchWindow(this, event);"
																						tabindex="41"
																						disabled="#{pc_Xrg00202T03.propSearchKyoinCd.disabled}"
																						style="#{pc_Xrg00202T03.propSearchKyoinCd.style}">
																					</hx:commandExButton>
																					<h:outputText styleClass="outputText" id="htmlKyoinName"
																						value="#{pc_Xrg00202T03.propKyoinName.value}"
																						style="#{pc_Xrg00202T03.propKyoinName.style}">
																					</h:outputText>
																				</TD>
																				</TR>
																				<TR>
																					<TH class="v_b" width="150">
																						<h:outputText styleClass="outputText" id="lblKomasu_Kyoin"
																							value="#{pc_Xrg00202T03.propKomasu_Kyoin.labelName}"
																							style="#{pc_Xrg0020T03.propKomasu_Kyoin.labelStyle}">
																						</h:outputText>
																					</TH>
																					<TD colspan="3">
																						<h:inputText styleClass="inputText"
																							id="htmlKomasu_Kyoin" size="8"
																							onchange="onChangeData();"
																							value="#{pc_Xrg00202T03.propKomasu_Kyoin.doubleValue}"
																							tabindex="42"
																							disabled="#{pc_Xrg00202T03.propKomasu_Kyoin.disabled}"
																							readonly="#{pc_Xrg00202T03.propKomasu_Kyoin.readonly}"
																							maxlength="#{pc_Xrg0202T03.propKomasu_Kyoin.max}"
																							style="#{pc_Xrg00202T03.propKomasu_Kyoin.style}">
																							<f:convertNumber type="number" pattern="##0.0"/>
																							<hx:inputHelperAssist errorClass="inputText_Error"
																								 promptCharacter="_" />
																						</h:inputText>
																					</TD>
																				</TR>
																				<TR>
																					<TH class="v_c" width="150">
																						<h:outputText styleClass="outputText" id="lblIshokuShodakubi"
																							value="#{pc_Xrg00202T03.propIshokuShodakubi.labelName}"
																							style="#{pc_Xrg0202T03.propIshokuShodakubi.labelStyle}">
																						</h:outputText>
																					</TH>
																					<TD>
																						<h:inputText id="htmlIshokuShodakubi"
																							styleClass="inputText" tabindex="43"
																							value="#{pc_Xrg00202T03.propIshokuShodakubi.dateValue}"
																							disabled="#{pc_Xrg00202T03.propIshokuShodakubi.disabled}"
																							onkeydown="onChangeData();">
																							<f:convertDateTime/>
																							<hx:inputHelperDatePicker />
																							<hx:inputHelperAssist errorClass="inputText_Error"
																							promptCharacter="_" />
																						</h:inputText>
																					</TD>
																					<TH class="v_c" width="150">
																						<h:outputText styleClass="outputText" id="lblIshokuIraibi"
																							value="#{pc_Xrg00202T03.propIshokuIraibi.labelName}"
																							style="#{pc_Xrg00202T03.propIshokuIraibi.labelStyle}">
																						</h:outputText>
																					</TH>
																					<TD>
																						<h:inputText id="htmlIshokuIribi"
																							styleClass="inputText" tabindex="44"
																							value="#{pc_Xrg00202T03.propIshokuIraibi.dateValue}"
																							disabled="#{pc_Xrg0020T03.propIshokuIraibi.disabled}"
																							onkeydown="onChangeData();">
																							<f:convertDateTime/>
																							<hx:inputHelperDatePicker />
																							<hx:inputHelperAssist errorClass="inputText_Error"
																															promptCharacter="_" />
																						</h:inputText>
																					</TD>
																				</TR>
																		</TBODY>
																	</TABLE>
																	<TABLE border="0" cellspacing="0" class="button_bar" width="90%">
																		<TBODY>
																			<TR>
																				<TD nowrap align="right">
																					<hx:commandExButton type="submit" value="確定"
																						styleClass="commandExButton_dat" id="determinateKyoin"
																						tabindex="45" confirm="#{msg.SY_MSG_0002W}"
																						disabled="#{pc_Xrg00202T03.propDeterminateKyoin.disabled}"
																						style="#{pc_Xrg00202T03.propDeterminateKyoin.style}"
																						action="#{pc_Xrg00202.doDeterminateKyoinAction}">
																					</hx:commandExButton>
																					<hx:commandExButton type="submit" value="削除"
																						styleClass="commandExButton_dat" id="deleteKyoin"
																						tabindex="46" confirm="#{msg.SY_MSG_0004W}"
																						disabled="#{pc_Xrg00202T03.propDeleteKyoin.disabled}"
																						style="#{pc_Xrg00202T03.propDeleteKyoin.style}"
																						action="#{pc_Xrg00202.doDeleteKyoinAction}">
																					</hx:commandExButton>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																</CENTER>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
											</TD>
										</hx:jspPanel>
									</TR>
									<!-- 配本情報タブ -->
									<TR>
										<hx:jspPanel id="haihonTabPanel"
											rendered="#{pc_Xrg00202.propHaihonTabPanel.rendered}">
											<TD valign="top">
												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="tab_body" width="100%">
													<TBODY>
														<TR>
															<TD valign="top" height="370" width="90%">
																<CENTER>
																	<TABLE border="0" cellpadding="5" width="720">
																		<TBODY>
																			<TR>
																				<TD align="left">
																					<h:outputText styleClass="outputText"
																							id="lblSetteiTaisho"
																							value="設定対象">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<TABLE border="0" cellpadding="0" cellspacing="0"
																						class="table" width="100%">
																						<TBODY>
																							<TR>
																								<TH class="v_a" width="80">
																									<h:outputText styleClass="outputText" id="lblJugyoName_Haihon"
																										value="#{pc_Xrg00202T04.propJugyoName_Haihon.labelName}"
																										style="#{pc_Xrg00202T04.propJugyoName_Haihon.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD>
																									<h:outputText styleClass="outputText" id="htmlJugyoName_Haihon"
																										value="#{pc_Xrg00202T04.propJugyoName_Haihon.stringValue}"
																										style="#{pc_Xrg00202T04.propJugyoName_Haihon.style}">
																									</h:outputText>
																								</TD>
																							</TR>
																							<TR>
																								<TH class="v_b" width="80">
																									<h:outputText styleClass="outputText" id="lblClassName_Haihon"
																										value="#{pc_Xrg00202T04.propClassName_Haihon.labelName}"
																										style="#{pc_Xrg00202T04.propClassName_Haihon.labelStyle}">
																									</h:outputText>
																								</TH>
																								<TD>
																									<h:outputText styleClass="outputText" id="htmlClassName_Haihon"
																										value="#{pc_Xrg00202T04.propClassName_Haihon.stringValue}"
																										style="#{pc_Xrg00202T04.propClassName_Haihon.style}">
																									</h:outputText>
																								</TD>
																							</TR>
																						</TBODY>
																					</TABLE>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<BR>
																	<!-- ↓データテーブル部↓ -->
																	<TABLE border="0" cellpadding="5" width="90%">
																		<TBODY>
																			<TR>
																				<TD align="left">
																					<h:outputText styleClass="outputText"
																							id="htmlHaihonListCount"
																							value="配本情報">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TD>
																					<DIV style="height:145px" class="listScroll" id="haihonListScroll" onscroll="setScrollPosition('htmlHaihonTableScroll',this);">
																						<h:dataTable border="0" cellpadding="0"
																							cellspacing="0" columnClasses="columnClass"
																							headerClass="headerClass" footerClass="footerClass"
																							rowClasses="#{pc_Xrg00202T04.propHaihonList.rowClasses}"
																							styleClass="meisai_scroll" id="htmlHaihonList" width="700"
																							value="#{pc_Xrg00202T04.propHaihonList.list}"
																							var="varlist"
																							style="#{pc_Xrg00202T04.propHaihonList.style}">
																							<h:column id="column21">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText" value="物品コード"
																										id="lblBuppinCdColumn">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlBuppinCdColumn"
																									styleClass="outputText"
																									value="#{varlist.buppinCd}" style="width: 65px">
																								</h:outputText>
																								<f:attribute value="text-align: center" name="style" />
																								<f:attribute value="65" name="width" />
																							</h:column>
																							<h:column id="column22">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblBuppinNameColumn" value="物品名">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlBuppinNameColumn"
																									styleClass="outputText"
																									value="#{varlist.propBuppinName.displayValue}"
																									title="#{varlist.propBuppinName.stringValue}"
																									style="width: 300px">
																								</h:outputText>
																								<f:attribute value="300" name="width" />
																							</h:column>
																							<h:column id="column23">
																								<f:facet name="header">
																									<h:outputText styleClass="outputText"
																										id="lblTextNameColumn" value="他テキスト名">
																									</h:outputText>
																								</f:facet>
																								<h:outputText id="htmlTextNameColumn"
																									styleClass="outputText"
																									value="#{varlist.propTextName.displayValue}"
																									title="#{varlist.propTextName.stringValue}"
																									style="width: 300px">
																								</h:outputText>
																								<f:attribute value="300" name="width" />
																							</h:column>
																							<h:column id="column24">
																								<f:facet name="header">
																								</f:facet>
																								<hx:commandExButton type="submit" value="選択"
																									onclick="onChangeData();"
																									styleClass="commandExButton" id="select_Haihon" tabindex="47"
																									action="#{pc_Xrg00202T04.doSelectAction}"
																									style="width: 35px">
																								</hx:commandExButton>
																								<f:attribute value="35" name="width" />
																								<f:attribute value="text-align: center" name="style" />
																							</h:column>
																						</h:dataTable>
																					</DIV>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<!-- ↑データテーブル部↑ -->
																	<HR width="100%" class="hr" noshade>
																	<TABLE border="0" cellpadding="0" cellspacing="0"
																		class="table" width="90%">
																		<TBODY>
																			<TR>
																				<TH class="v_a" width="150">
																					<h:outputText styleClass="outputText" id="lblBuppinCd"
																						value="#{pc_Xrg00202T04.propBuppinCd.labelName}"
																						style="#{pc_Xrg00202T04.propBuppinCd.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText" id="htmlBuppinCd"
																						onkeyup="onChangeData();"
																						value="#{pc_Xrg00202T04.propBuppinCd.stringValue}"
																						size="8" tabindex="48"
																						disabled="#{pc_Xrg00202T04.propBuppinCd.disabled}"
																						readonly="#{pc_Xrg00202T04.propBuppinCd.readonly}"
																						maxlength="#{pc_Xrg00202T04.propBuppinCd.max}"
																						onblur="return doBuppinAjax(this, event);"
																						style="#{pc_Xrg00202T04.propBuppinCd.style}">
																					</h:inputText>
																					<hx:commandExButton type="button"
																						styleClass="commandExButton_search" id="searchBuppinCd"
																						onclick="return openBuppinCdSearchWindow(this, event);"
																						tabindex="49"
																						disabled="#{pc_Xrg00202T04.propSearchBuppinCd.disabled}"
																						style="#{pc_Xrg00202T04.propSearchBuppinCd.style}">
																					</hx:commandExButton>
																					<h:outputText styleClass="outputText" id="htmlBuppinName"
																						style="#{pc_Xrg00202T04.propBuppinCd.labelStyle}">
																					</h:outputText>
																				</TD>
																			</TR>
																			<TR>
																				<TH class="v_b" width="150">
																					<h:outputText styleClass="outputText" id="lblTextName"
																						value="#{pc_Xrg00202T04.propTextName.labelName}"
																						style="#{pc_Xrg00202T04.propTextName.labelStyle}">
																					</h:outputText>
																				</TH>
																				<TD>
																					<h:inputText styleClass="inputText" id="htmlTextName"
																						onkeyup="onChangeData();"
																						value="#{pc_Xrg00202T04.propTextName.stringValue}"
																						size="50" tabindex="50"
																						disabled="#{pc_Xrg00202T04.propTextName.disabled}"
																						readonly="#{pc_Xrg00202T04.propTextName.readonly}"
																						maxlength="#{pc_Xrg00202T04.propTextName.maxLength}"
																						style="#{pc_Xrg00202T04.propTextName.style}">
																					</h:inputText>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	<TABLE border="0" cellspacing="0" class="button_bar" width="90%">
																		<TBODY>
																			<TR>
																				<TD nowrap align="right">
																					<hx:commandExButton type="submit" value="確定"
																						styleClass="commandExButton_dat" id="determinateHaihon"
																						tabindex="51" confirm="#{msg.SY_MSG_0002W}"
																						disabled="#{pc_Xrg00202T04.propDeterminateHaihon.disabled}"
																						style="#{pc_Xrg00202T04.propDeterminateHaihon.style}"
																						action="#{pc_Xrg00202.doDeterminateHaihonAction}">
																					</hx:commandExButton>
																					<hx:commandExButton type="submit" value="削除"
																						styleClass="commandExButton_dat" id="deleteHaihon"
																						tabindex="52" confirm="#{msg.SY_MSG_0004W}"
																						disabled="#{pc_Xrg00202T04.propDeleteHaihon.disabled}"
																						style="#{pc_Xrg00202T04.propDeleteHaihon.style}"
																						action="#{pc_Xrg00202.doDeleteHaihonAction}">
																					</hx:commandExButton>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																</CENTER>
															</TD>
														</TR>
													</TBODY>
												</TABLE>
											</TD>
										</hx:jspPanel>
									</TR>
								</TBODY>
							</TABLE>
							<!--↑タブ用テーブル↑-->
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<h:inputHidden id="executable"
				value="#{pc_Xrg00202.propExecutable.integerValue}">
			</h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Xrg00202.changeDataFlg}" ></h:inputHidden>
			<h:inputHidden id="htmlHidClassFrwkKbn" value="#{pc_Xrg00202T01.classFrwkKbn}" ></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg00202T02.propClassList.scrollPosition}" id="htmlClassTableScroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg00202T03.propKyoinList.scrollPosition}" id="htmlKyoinTableScroll"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrg00202T04.propHaihonList.scrollPosition}" id="htmlHaihonTableScroll"></h:inputHidden>
		<!-- フッターインクルード -->
		<jsp:include page ="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
	
</f:view>
<SCRIPT language="javaScript">
changeScrollPosition('htmlClassTableScroll','classListScroll');
changeScrollPosition('htmlKyoinTableScroll','kyoinListScroll');
changeScrollPosition('htmlHaihonTableScroll','haihonListScroll');
</SCRIPT>
</HTML>
