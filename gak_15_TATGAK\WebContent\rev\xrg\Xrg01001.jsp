<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg01001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg01001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >


<SCRIPT type="text/javascript">

function init() {
	//許可状態の活性区分を制御する変数を取得
	var pdfLayoutKbn = document.getElementById('form1:htmlHidPdfLayoutKbn').value;

	if (pdfLayoutKbn == "1") {
		document.getElementById('form1:htmlKyokaStatusTrue').disabled = true; 
		document.getElementById('form1:htmlKyokaStatusFalse').disabled = true; 
	} else if(pdfLayoutKbn == "2") {
		document.getElementById('form1:htmlKyokaStatusTrue').disabled = false; 
		document.getElementById('form1:htmlKyokaStatusFalse').disabled = false; 
	} 
}

var schSbtCd = "";
function getSchoolingSbtCb() {
	// スクーリング種別コンボボックス取得AJAX
	var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
	var args = new Array();
	args['nendo'] = document.getElementById('form1:htmlNendo').value;
	args['bunrui'] = "";
	args['jukoSekyuFlg'] = "false";
	args['textdelFlg'] = "true";
	args['propName'] = 'propSchoolingSbtCd';
	var target = "";
	
	comb = document.getElementById('form1:htmlSchooling');
	schSbtCd = comb.options[comb.selectedIndex].value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
}

function callBackMethod(value){
	var comb = document.getElementById('form1:htmlSchooling');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
}

// 学籍番号検索アイコン押下時処理
function openGakusekiNoSearchWindow(thisObj, thisEvent) {
	
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakusekiNo&gakuseiSearchKbn=1";
	openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return true;
}

// 学生氏名を取得する
function getGakuseiNmOnAjax(thisObj, thisEven, targetLabel) {

    var servlet = "rev/co/CobGakseiAJAX";
    var args = new Array();
    args['code1'] = thisObj.value;

    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, targetLabel, args);
}


function modeChange(thisObj) {
	var mode = thisObj.value;
	if (mode == "1") {
		var kyokaStatTrueFlg = 0;
		var kyokaStatFalseFlg = 0;

		if (document.getElementById('form1:htmlKyokaStatusTrue').checked) {
			kyokaStatTrueFlg = 1;
		}
		
		if (document.getElementById('form1:htmlKyokaStatusFalse').checked) {
			kyokaStatFalseFlg = 1;
		}
		
		document.getElementById('form1:htmlHidPdfLayoutKbn').value = mode;
		document.getElementById('form1:htmlHidKyokaStatTrueFlg').value = kyokaStatTrueFlg;
		document.getElementById('form1:htmlHidKyokaStatFalseFlg').value = kyokaStatFalseFlg;
		
		document.getElementById('form1:htmlKyokaStatusTrue').disabled = true; 
		document.getElementById('form1:htmlKyokaStatusFalse').disabled = true; 
	} else {
		document.getElementById('form1:htmlHidPdfLayoutKbn').value = mode;
		document.getElementById('form1:htmlKyokaStatusTrue').disabled = false; 
		document.getElementById('form1:htmlKyokaStatusFalse').disabled = false; 
	}

}

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します

//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

check('htmlKyokaStatusList','htmlSelected');

}
function func_2(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します

//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

uncheck('htmlKyokaStatusList','htmlSelected');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="init()">
		<hx:scriptCollector id="scriptCollector1"
			preRender="#{pc_Xrg01001.onPageLoadBegin}">
			<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
				<jsp:include page ="../inc/header.jsp" />
				<!-- ヘッダーへのデータセット領域 -->
				<div style="display:none;"><hx:commandExButton type="submit"
					value="閉じる" styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrg01001.doCloseDispAction}"
					></hx:commandExButton> <h:outputText
					styleClass="outputText" id="htmlFuncId"
					value="#{pc_Xrg01001.funcId}"></h:outputText> <h:outputText
					styleClass="outputText" id="htmlLoginId"
					value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
					styleClass="outputText" id="htmlScrnName"
					value="#{pc_Xrg01001.screenName}"></h:outputText></div>
				<!--↓outer↓-->
				<DIV class="outer">

					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
						id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText></FIELDSET>

					<DIV class="head_button_area">　
						<!-- ↓ここに戻るボタンを配置 -->
						<!-- ↑ここに戻るボタンを配置 -->
					</DIV>

					<!--↓content↓-->
					<DIV id="content">
						<DIV class="column" align="center">
							<TABLE width="700">
								<TBODY>
									<TR>
										<TD>
											<TABLE class="table">
												<TBODY>
													<TR>
														<TH class="v_a" width="150">
															<h:outputText styleClass="outputText" id="lblNendo"
																value="#{pc_Xrg01001.propNendo.labelName}"
																style="#{pc_Xrg01001.propNendo.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3" >
															<h:inputText id="htmlNendo" styleClass="inputText" tabindex="1"
																onblur="getSchoolingSbtCb();"
																value="#{pc_Xrg01001.propNendo.dateValue}" size="4">
																<hx:inputHelperAssist errorClass="inputText_Error"
										    						imeMode="inactive" promptCharacter="_" />
																<f:convertDateTime pattern="yyyy" />
															</h:inputText>
														</TD>
													</TR>
													<TR>
														<TH class="v_a" width="150">
															<h:outputText styleClass="outputText" id="lblSchoolingSbtCd"
																value="#{pc_Xrg01001.propSchoolingSbtCd.labelName}"
																style="#{pc_Xrg01001.propSchoolingSbtCd.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3" >
															<h:selectOneMenu styleClass="selectOneMenu" tabindex="2"
																id="htmlSchooling" value="#{pc_Xrg01001.propSchoolingSbtCd.value}"
																readonly="#{pc_Xrg01001.propSchoolingSbtCd.readonly}"
																style="#{pc_Xrg01001.propSchoolingSbtCd.style}">
																<f:selectItems value="#{pc_Xrg01001.propSchoolingSbtCd.list}" />
															</h:selectOneMenu>
														</TD>
													</TR>
													<TR>
														<TH class="v_a" width="150">
															<h:outputText styleClass="outputText" id="lblPDFLayout"
																value="#{pc_Xrg01001.propPDFLayout.labelName}"
																style="#{pc_Xrg01001.propPDFLayout.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3" >
															<h:selectOneRadio disabledClass="selectOneRadio_Disabled" tabindex="3"
																styleClass="selectOneRadio" id="htmlPDFLayout"
																layout="lineDirection"
																value="#{pc_Xrg01001.propPDFLayout.stringValue}"
																style="#{pc_Xrg01001.propPDFLayout.style}"
																onclick="modeChange(this);">
																<f:selectItem itemValue="1"
																	itemLabel="請求書 兼 許可通知書" />
																<f:selectItem itemValue="2"
																	itemLabel="許可・不許可通知書" />
															</h:selectOneRadio>
														</TD>
													</TR>
													<TR>
														<TH class="v_c" width="200">
															<h:outputText styleClass="outputText"
																id="lblGakusekiNo"
																value="#{pc_Xrg01001.propGakusekiNo.labelName}"
																style="#{pc_Xrg01001.propGakusekiNo.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3" >
															<h:inputText styleClass="inputText"
																id="htmlGakusekiNo" tabindex="4"
																value="#{pc_Xrg01001.propGakusekiNo.stringValue}"
																readonly="#{pc_Xrg01001.propGakusekiNo.readonly}"
																style="#{pc_Xrg01001.propGakusekiNo.style}"
																disabled="#{pc_Xrg01001.propGakusekiNo.disabled}"
																maxlength="#{pc_Xrg01001.propGakusekiNo.maxLength}" size="20"
																onblur="return getGakuseiNmOnAjax(this, event, 'form1:htmlGakuseiName');">
															</h:inputText>
															<hx:commandExButton type="button"
																styleClass="commandExButton_search" id="searchGakusekiNo"
																onclick="openGakusekiNoSearchWindow(this, event);"
																tabindex="5"
																disabled="#{pc_Xrg01001.propSearchGakusekiNo.disabled}"
																style="#{pc_Xrg01001.propSearchGakusekiNo.style}">
															</hx:commandExButton>
															<h:outputText styleClass="outputText" id="htmlGakuseiName"
																value="#{pc_Xrg01001.propGakuseiName.value}"
																style="#{pc_Xrg01001.propGakuseiName.style}">
															</h:outputText>

														</TD>
													</TR>
													<TR>
														<TH class="v_a" width="150">
															<h:outputText styleClass="outputText" id="lblKyokaStatus"
																value="#{pc_Xrg01001.propKyokaStatus.labelName}"
																style="#{pc_Xrg01001.propKyokaStatus.labelStyle}">
															</h:outputText>
														</TH>
														<TD nowrap width="200" >
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlKyokaStatusTrue" tabindex="6" 
																value="#{pc_Xrg01001.propKyokaStatusTrue.checked}"
																style="#{pc_Xrg01001.propKyokaStatusTrue.style}">
															</h:selectBooleanCheckbox>
															許可
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlKyokaStatusFalse" tabindex="7" 
																value="#{pc_Xrg01001.propKyokaStatusFalse.checked}"
																style="#{pc_Xrg01001.propKyokaStatusFalse.style}">
															</h:selectBooleanCheckbox>
															不許可
														</TD>
														<TH class="v_a" width="150">
															<h:outputText styleClass="outputText" id="lblTargetOutput"
																value="#{pc_Xrg01001.propTargetOutput.labelName}"
																style="#{pc_Xrg01001.propTargetOutput.labelStyle}">
															</h:outputText>
														</TH>
														<TD nowrap width="230" >
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlTargetOutputYet" tabindex="8" 
																value="#{pc_Xrg01001.propTargetOutputYet.checked}"
																readonly="#{pc_Xrg01001.propTargetOutputYet.readonly}"
																disabled="#{pc_Xrg01001.propTargetOutputYet.disabled}"
																style="#{pc_Xrg01001.propTargetOutputYet.style}">
															</h:selectBooleanCheckbox>
															未出力
															<h:selectBooleanCheckbox
																styleClass="selectBooleanCheckbox" id="htmlTargetOutputDone" tabindex="9" 
																value="#{pc_Xrg01001.propTargetOutputDone.checked}"
																readonly="#{pc_Xrg01001.propTargetOutputDone.readonly}"
																disabled="#{pc_Xrg01001.propTargetOutputDone.disabled}"
																style="#{pc_Xrg01001.propTargetOutputDone.style}">
															</h:selectBooleanCheckbox>
															出力済
														</TD>
													</TR>
													<TR>
														<TH class="v_a" width="150">
															<h:outputText styleClass="outputText" id="lblSortOrder"
																value="#{pc_Xrg01001.propSortOrder.labelName}"
																style="#{pc_Xrg01001.propSortOrder.labelStyle}">
															</h:outputText>
														</TH>
														<TD colspan="3">
															<h:selectOneRadio disabledClass="selectOneRadio_Disabled" tabindex="10"
																styleClass="selectOneRadio" id="htmlSortOrder"
																layout="lineDirection"
																value="#{pc_Xrg01001.propSortOrder.stringValue}"
																style="#{pc_Xrg01001.propSortOrder.style}">
																<f:selectItems value="#{pc_Xrg01001.propSortOrder.list}" />
															</h:selectOneRadio>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD align="left">
											<h:outputText styleClass="outputText" id="lblTargetStudentList"
												value="※「請求書 兼 許可通知書」は許可かつ請求データ作成済みの学生のみ出力できます。">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TD align="center">
											<hx:commandExButton type="submit" value="検索"
												styleClass="commandExButton_dat" id="search"  tabindex="11"
												action="#{pc_Xrg01001.doSearchAction}"
												disabled="#{pc_Xrg01001.propSearch.disabled}"
												style="#{pc_Xrg01001.propSearch.style}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="クリア"
												styleClass="commandExButton_dat" id="clear"  tabindex="12"
												action="#{pc_Xrg01001.doClearAction}"
												disabled="#{pc_Xrg01001.propClear.disabled}"
												style="#{pc_Xrg01001.propClear.style}">
											</hx:commandExButton>
											<BR/>
										</TD>
									</TR>
									<TR>
										<TD align="right">
											<h:outputText styleClass="outputText" id="htmlListCount"
												style="font-size: 8pt" value="#{pc_Xrg01001.propListCount.stringValue}">
											</h:outputText>
											<h:outputText styleClass="outputText" id="text1"
												value="件" style="font-size: 8pt">
											</h:outputText>
										</TD>
									</TR>
									<TR>
										<TD>
											<DIV style="height:220px" class="listScroll" 
												id="listScroll" onscroll="setScrollPosition('scroll',this);">
												<h:dataTable border="0" cellpadding="0" cellspacing="0"
													columnClasses="columnClass"
													headerClass="headerClass"
													footerClass="footerClass"
													rowClasses="#{pc_Xrg01001.propKyokaStatusList.rowClasses}"
													styleClass="meisai_scroll" id="htmlKyokaStatusList"
													width="780" value="#{pc_Xrg01001.propKyokaStatusList.list}"
													var="varlist">
													<!-- サンプル:Kmc01101 -->
													<h:column id="column01">
														<f:facet name="header">
														</f:facet>
														<hx:jspPanel rendered="#{varlist.selected.disabled == false}">
															<h:selectBooleanCheckbox tabindex="13"
																styleClass="selectBooleanCheckbox" id="htmlSelected"
																value="#{varlist.selected.checked}">
															</h:selectBooleanCheckbox>
														</hx:jspPanel>
														<f:attribute value="35" name="width" />
														<f:attribute value="center" name="align" />
														<f:attribute value="text-align: center" name="style" />
													</h:column>
													<h:column id="column02">
														<f:facet name="header">
															<h:outputText styleClass="outputText" value="学籍番号"
																id="lblGakusekiNoColumn">
															</h:outputText>
														</f:facet>
														<h:outputText id="htmlGakusekiNoColumn" value="#{varlist.gaksekiCd}" 
															styleClass="outputText" 
															style="width: 115px">
														</h:outputText>
														<f:attribute value="true" name="nowrap" />
														<f:attribute value="center" name="align" />
														<f:attribute value="115" name="width" />
													</h:column>
													<h:column id="column03">
														<f:facet name="header">	
															<h:outputText styleClass="outputText" id="lblNameColumn"
																value="氏名">
															</h:outputText>
														</f:facet>
														<h:outputText id="htmlNameColumn" 
															value="#{varlist.propGakseiNm.displayValue}" 
															title="#{varlist.propGakseiNm.stringValue}"
															styleClass="outputText" 
															style="width: 280px">
														</h:outputText>
														<f:attribute value="280" name="width" />
													</h:column>
													<h:column id="column04">
														<f:facet name="header">
															<h:outputText styleClass="outputText"
																id="lblGakuseiMbnColumn" value="学生身分">
															</h:outputText>
														</f:facet>
														<h:outputText id="htmlGakuseiMbnColumn"
															value="#{varlist.propGakuseiMbn.displayValue}" 
															title="#{varlist.propGakuseiMbn.stringValue}"
															styleClass="outputText" 
															style="width: 150px">
														</h:outputText>
														<f:attribute value="true" name="nowrap" />
														<f:attribute value="150" name="width" />
													</h:column>
													<h:column id="column05">
														<f:facet name="header">	
															<h:outputText styleClass="outputText" id="lblKyokaStatusColumn"
																value="許可状態">
															</h:outputText>
														</f:facet>
														<h:outputText id="htmlKyokaStatusColumn" 
															value="#{varlist.kyokaStatNm}" styleClass="outputText" 
															style="width: 80px">
														</h:outputText>
														<f:attribute value="true" name="nowrap" />
														<f:attribute value="center" name="align" />
														<f:attribute value="80" name="width" />
													</h:column>
													<h:column id="column06">
														<f:facet name="header">
															<h:outputText styleClass="outputText"
																id="lblHakkobiColumn" value="発行日">
															</h:outputText>
														</f:facet>
														<h:outputText id="htmlHakkobiColumn" styleClass="outputText" 
															value="#{varlist.kyokasyoHakkoDate}"
															style="width: 120px">
														</h:outputText>
														<f:attribute value="true" name="nowrap" />
														<f:attribute value="120" name="width" />
													</h:column>
												</h:dataTable>
											</DIV>
										</TD>
									</TR>
									<TR>
										<TD align="left">
											<hx:commandExButton type="button" styleClass="check" 
												id="check" onclick="return func_1(this, event);" tabindex="14">
											</hx:commandExButton>
											<hx:commandExButton type="button" styleClass="uncheck" 
												id="uncheck" onclick="return func_2(this, event);" tabindex="15">
											</hx:commandExButton>
										</TD>
									</TR>
									<TR>
										<TD width="800" valign="top">
											<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
												<TBODY>
													<TR>
														<TH class="v_a" width="200">
															<h:outputText styleClass="outputText" id="lblHakkobi"
																value="発行日" 
																style="#{pc_Xrg01001.propHakkobi.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="600">
															<h:inputText id="htmlHakkobi"
																styleClass="inputText" tabindex="16"
																value="#{pc_Xrg01001.propHakkobi.dateValue}"
																disabled="#{pc_Xrg01001.propHakkobi.disabled}"
																size="12">
																<f:convertDateTime />
																<hx:inputHelperDatePicker styleClass="width:100px;"/>
																<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
															</h:inputText> 
														</TD>
													</TR>
													<TR>
														<TH class="v_a" width="200">
															<h:outputText styleClass="outputText" id="lblTsushinran"
																value="通信欄 (1行25文字4行以内)"
																style="#{pc_Xrg01001.propTsushinran.labelStyle}">
															</h:outputText>
														</TH>
														<TD width="600">
															<h:inputTextarea
																styleClass="inputTextarea" id="htmlTsushinran" tabindex="17" cols="45" rows="4"
																value="#{pc_Xrg01001.propTsushinran.stringValue}"
																style="#{pc_Xrg01001.propTsushinran.style}"
																disabled="#{pc_Xrg01001.propTsushinran.disabled}">
															</h:inputTextarea>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
										</TD>
									</TR>
									<TR>
										<TD align="center">
											<hx:commandExButton type="submit" value="PDF作成"
												styleClass="commandExButton_dat" id="makePDF" tabindex="18"
												action="#{pc_Xrg01001.doMakePDFAction}"
												disabled="#{pc_Xrg01001.propMakePDF.disabled}"
												style="#{pc_Xrg01001.propMakePDF.style}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="CSV作成"
												styleClass="commandExButton_dat" id="makeCSV" tabindex="19"
												action="#{pc_Xrg01001.doMakeCSVAction}"
												disabled="#{pc_Xrg01001.propMakeCSV.disabled}"
												style="#{pc_Xrg01001.propMakeCSV.style}">
											</hx:commandExButton>
											<hx:commandExButton type="submit" value="出力項目指定"
												styleClass="commandExButton_dat" id="outputShitei"
												disabled="#{pc_Xrg01001.propOutputColumn.disabled}"
												tabindex="20"
												action="#{pc_Xrg01001.doOutputColumnAction}">
											</hx:commandExButton>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</DIV>
					</DIV>
				</DIV>
				<h:inputHidden id="htmlHidPdfLayoutKbn" value="#{pc_Xrg01001.hidPropPdfLayoutKbn.stringValue}" ></h:inputHidden>
				<h:inputHidden id="htmlHidKyokaStatTrueFlg" value="#{pc_Xrg01001.hidPropKyokaStatTrueFlg.stringValue}" ></h:inputHidden>
				<h:inputHidden id="htmlHidKyokaStatFalseFlg" value="#{pc_Xrg01001.hidPropKyokaStatFalseFlg.stringValue}" ></h:inputHidden>
			</h:form>
			<!-- フッターインクルード -->
			<jsp:include page ="../inc/footer.jsp" />
		</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
