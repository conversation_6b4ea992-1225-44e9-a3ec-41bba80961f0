<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT type="text/javascript">

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00701.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>
			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellpadding="0" cellspacing="0" width="600"
				class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblNendo"
							value="#{pc_Xrg00701.propNendo.labelName}"
							style="#{pc_Xrg00701.propNendo.labelStyle}"></h:outputText></TH>
						<TD width="450"><h:inputText styleClass="inputText"
							id="htmlNendo"
							value="#{pc_Xrg00701.propNendo.dateValue}"
							style="#{pc_Xrg00701.propNendo.style}" tabindex="1"
							disabled="#{pc_Xrg00701.propNendo.disabled}" size="4">
							<hx:inputHelperAssist errorClass="inputText_Error"
								imeMode="inactive" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" /></h:inputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE class="button_bar" width="600">
				<TBODY>
					<TR>
						<TD width="600"
							style="background-color: transparent; text-align: center" nowrap
							class="clear_border" align="center"><hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search" tabindex="2"
							action="#{pc_Xrg00701.doSearchAction}">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="600px">
				<TBODY>
					<TR>
						<TD align="right">
							<h:outputText
								styleClass="outputText" id="lblCount"
								value="#{pc_Xrg00701.propCount.value}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>				
			<TABLE border="0" cellpadding="0" cellspacing="0"  width="600px">
				<TBODY>
					<TR>
						<TD>
						<div id="listScroll" class="listScroll"
							onscroll="setScrollPosition('scroll',this);"
							style="height:338px;"><h:dataTable border="0"
							cellpadding="2" cellspacing="0" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrg00701.propSchoolingList.rowClasses}"
							styleClass="meisai_scroll" id="tableSchooling"
							value="#{pc_Xrg00701.propSchoolingList.list}" var="varlist">
							<h:column id="column1" >
								<f:facet name="header">
									<h:outputText id="text3" styleClass="outputText" value="スクーリング種別"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlSchoolingSbt"
									value="#{varlist.propSchSbtNm.displayValue}"
									title="#{varlist.propSchSbtNm.stringValue}">
								</h:outputText>
								<f:attribute value="280" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="授業" tabindex="3"
									styleClass="commandExButton" id="jugyo"
									action="#{pc_Xrg00701.doJugyoAction}"
									disabled="#{pc_Xrg00701.propJugyo.disabled}"
									style="#{pc_Xrg00701.propJugyo.style}"></hx:commandExButton>
								<f:attribute value="38" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="抽選区分"
										id="lblTyusenKbn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlTyusenKbn"
									value="#{varlist.propTyusenKbnNm.displayValue}"></h:outputText>
								<f:attribute value="60" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="center" name="align" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="ステータス"
										id="lblStatusColumn"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlStatusColumn"
									value="#{varlist.propStatusNm.displayValue}"
									title="#{varlist.propStatusNm.value}"></h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="left" name="align" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="許可処理"
										id="lblListStatusNm"></h:outputText>
								</f:facet>
								<hx:commandExButton type="submit" value="実行"
									styleClass="commandExButton" id="execution"
									action="#{pc_Xrg00701.doExecuteAction}" tabindex="3"
									onclick="return doPopupMsg('#{msg.SY_MSG_0001W}');"
									disabled="#{varlist.propExecution.disabled}"
									style="#{varlist.propExecution.style}"
									rendered="#{varlist.propExecution.rendered}">
								</hx:commandExButton>
								<hx:commandExButton type="submit" value="確定"
									styleClass="commandExButton" id="determinate"
									action="#{pc_Xrg00701.doDeterminateAction}" tabindex="3"
									onclick="return doPopupMsg('#{msg.SY_MSG_0003W}');"
									disabled="#{varlist.propDeterminate.disabled}"
									style="#{varlist.propDeterminate.style}"
									rendered="#{varlist.propDeterminate.rendered}">
								</hx:commandExButton>
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle; align: center"
									name="style" />
							</h:column>
						</h:dataTable></div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrg00701.propSchoolingList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

