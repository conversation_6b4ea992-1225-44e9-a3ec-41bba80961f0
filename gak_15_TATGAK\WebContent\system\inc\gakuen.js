// gakuen.js


// パラメーター付きの文字を出力する
function messageCreate(message, param) {
	var paramLength = param.length;
	var newMessage = message;
	for (var i = 0 ; i < paramLength ; i++) {
		newMessage = newMessage.replace('{' + i + '}', param[i]);
	}
	return newMessage;
}

function indirectClick(id) {
	document.getElementById('form1:' + id).click();
}

// スクロール位置をhiddenに設定する
function setScrollPosition(hiddenId, thisObj) {
	try{
		var scroll = document.getElementById('form1:' + hiddenId);
		if (scroll){
			scroll.value = thisObj.scrollTop;
		}
	} catch(e) {
	}
}

// hiddenの値を利用し、スクロールを制御する
function changeScrollPosition(hiddenId, scrollId){
	try{
		var scroll = document.getElementById(scrollId);
		var scrollHidden = document.getElementById('form1:' + hiddenId);
		if (scroll && scrollHidden){
			scroll.scrollTop = scrollHidden.value;
		}
	} catch(e) {
	}
}

// 指定コンポーネントのdisplay属性を切り替える
// none→block  block→none
function visibleChange(target) {
	if(document.getElementById) {
		if(document.getElementById(target).style.display == 'none') {
			document.getElementById(target).style.display = 'block';
		} else {
			document.getElementById(target).style.display = 'none';
		}
	}
}

// 指定コンポーネントのdisplay属性を変更する
// visible:true→block,false→none
function visibleSet(target, visible) {
	if(document.getElementById) {
		if(visible) {
			document.getElementById(target).style.display = 'block';
		} else {
			document.getElementById(target).style.display = 'none';
		}
	}
}



// データ変更確認にスクリプトを登録
function addCheckEvent(listName){
	try{
		document.getElementById('form1:' + listName + ':deluxe1__pagerFirst').onclick = cancelConfirm;
		document.getElementById('form1:' + listName + ':deluxe1__pagerPrevious').onclick = cancelConfirm;
		document.getElementById('form1:' + listName + ':deluxe1__pagerNext').onclick = cancelConfirm;
		document.getElementById('form1:' + listName + ':deluxe1__pagerLast').onclick = cancelConfirm;
		document.getElementById('form1:' + listName + ':goto1__pagerGoButton').onclick = cancelConfirm;
	} catch(e) {
	}
}

// 対象コンポーネントの値が同じかチェックを行なう
// 値が変更されているかチェックを行なう
// 変更されていた場合true、以外falseを返す
function listChangeCheck(listName, target, orgin){
	try{
	    elms = document.getElementById('form1').elements;
	    for (var i = 0; i < elms.length; i++) {
	        var elm = elms[i];
	        if (elm.id.indexOf('form1:' + listName) > -1){
	            if (elm.id.indexOf(target) > -1){
	                var p = elm.id.split(':');
	                var original = document.getElementById("form1:" + listName + ":" + p[2] + ":" + orgin);
	                if (original){
	                    if (elm.nodeName.toUpperCase() == "INPUT" && elm.type == "checkbox"){
	                        // 比較対照がチェックボックスの場合
	                        if (original.type == "checkbox"){
	                            if (elm.checked != original.value){
	                                return true;
	                            }
	                        } else if (original.type == "hidden"){
	                            if (original.value == "true"){
	                                if (elm.checked != true){
	                                    return true;
	                                }
	                            } else {
	                                if (elm.checked != false){
	                                    return true;
	                                }
	                            }
	                        }
	                    } else {
	                        if (elm.value != original.value){
	                            return true;
	                        }
	                    }
	                }
	            }
	        }else if (elm.name.indexOf('form1:' + listName) > -1){
	            // 比較対象がラジオボタンの場合
	            if (elm.type == "radio"){
	                if (elm.checked == true){
	                    var p = elm.name.split(':');
	                    var original = document.getElementById("form1:" + listName + ":" + p[2] + ":" + orgin);
	                    if (original){
	                        if (elm.value != original.value){
	                            return true;
	                        }
	                    }
	                }
	            }
	        }
	    }
	    
	} catch(e) {
	}
    return false;
}

// データの変更を戻す
function listDateReset(listName, target, orgin){
	try{
		elms = document.getElementById('form1').elements;
		for (var i = 0; i < elms.length; i++) {
			var elm = elms[i];
	        if (elm.id.indexOf('form1:' + listName) > -1){
	        	if (elm.id.indexOf(target) > -1){
	        		p = elm.id.split(':');
	        		// 画面ロード時の値を保持(入力コンポーネントの数だけ用意)
	                var original = document.getElementById("form1:" + listName + ":" + p[2] + ":" + orgin);
	                if (original){
	                    if (elm.nodeName.toUpperCase() == "INPUT" && elm.type == "checkbox"){
	                        // チェックボックスの場合
	                        if (original.value == "1" || original.value == "true") {
	                            elm.checked = true;
	                        } else {
	                            elm.checked = false;
	                        }                    
	                    } else {
	                        elm.value = original.value;
	                    }
	                }
	        	}
	        }else if (elm.name.indexOf('form1:' + listName) > -1){
	            // 比較対象がラジオボタンの場合
	            if (elm.type == "radio"){
	                var p = elm.name.split(':');
	                var original = document.getElementById("form1:" + listName + ":" + p[2] + ":" + orgin);
	                if (original){
	                    if (elm.value == original.value){
	                        elm.checked = true;
	                    }
	                }
	            }
	        }	        
		}
	} catch(e) {
	}
	return 0;
}


// 現在のページを取得する
function getPage(listId)
{
	/*
	elms = document.getElementById('form1').elements;

	for (var i = 0; i < elms.length; i++) {
		var elm = elms[i];
        if (elm.id.indexOf('form1:' + listname) > -1){
        	if (elm.id.indexOf(chackboxname) > -1){
        		p = elm.id.split(':');
        		
        		// 行の先頭を返す
        		return p[2];
        	}
        }
	}
	*/
	/* form1:htmlUserList:goto1__pagerGoButton */
	// 現在のページを返す
	try{
		var presentPage = document.getElementById('form1:' + listId + ':goto1__pagerGoText');
		if (presentPage){
			return presentPage.value;
		}
	} catch(e) {
	}
	
	return null;
}

// スクロール位置をhidden項目に設定する
// hiddenName 
function setPage(hiddenId, listId)
{
	try{
		var scrollPosition = document.getElementById('form1:'+ hiddenId);
		if (scrollPosition){
			scrollPosition.value = getPage(listId);
		}
	} catch(e) {
	}
}



// チェックボックスを全てチックする。
function check(listname, chackboxname)
{	
	try{
		elms = document.getElementById('form1').elements;
		for (var i = 0; i < elms.length; i++) {	
			var elm = elms[i];
			if (elm.type == 'checkbox') {
		        if (elm.id.indexOf('form1:' + listname) > -1){
		        	if (elm.id.indexOf(chackboxname) > -1){
		        		if (elm.disabled == false){
		        			elm.checked = true;
		        		}
		        	}
		        }
			}
		}
	} catch(e) {
	}
}

// チェックボックスのチェックを全て外す
function uncheck(listname, chackboxname)
{
	try{
		elms = document.getElementById('form1').elements;
		for (var i = 0; i < elms.length; i++) {
			var elm = elms[i];
			if (elm.type == 'checkbox') {
		        if (elm.id.indexOf('form1:' + listname) > -1){
		        	if (elm.id.indexOf(chackboxname) > -1){
		        		if (elm.disabled == false){
			        		elm.checked = false;
			        	}
		        	}
		        }
			}
		}
	} catch(e) {
	}
}



// エラー領域にメッセージをセットする場合に利用
// 設定エラー数が1つの場合は、Stringで設定
// 複数エラーが存在する場合は、Array配列で設定を行う
function setErrMsg(arg){
	try{
	    var errList;
		if( arguments[0].constructor == Array ){
			errList = arg;
		} else {
			errList = new Array(1);
			errList[0] = arg;
		}
		var err = "";
		var flg = false;
	    for (var i = 0; errList.length > i; i++) {
	        if (i == 0) {
	        err = err.concat("<span class=\"firstErr\">");
	        err = err.concat(errList[i]);
	        err = err.concat("</span>");
	        } else {
	            if (flg == false) {
	                err = err.concat("<input id=\"errButton\" class=\"errButton\" type=\"button\" value=\"\" onClick=\"errDisp();\"></input>");
	                err = err.concat("<DIV class=\"otherErr\" id=\"otherErr\">");
	                flg = true;
	            }
				err = err.concat(errList[i]);
	            err = err.concat("<BR>");
	        }
	    }
	    if (flg == true) {
	        err = err.concat("</DIV>");
	    }
		document.getElementById("form1:message").innerHTML = err;
	} catch(e) {
	}
}

function errDisp(){
	try{
		if (document.getElementById('otherErr').style.display == 'block'){
			document.getElementById('otherErr').style.display = 'none';
            //IE6のzindexのバグに対応
            if (typeof document.body.style.maxHeight == "undefined") {
                closeShim(document.getElementById('otherErr'));
            }
		} else {
			document.getElementById('otherErr').style.display = 'block';
            //IE6のzindexのバグに対応            
            if (typeof document.body.style.maxHeight == "undefined") {
                openShim(document.getElementById('otherErr'));
            }
		}
	} catch(e) {
	}    
}

// SELECTリストの全角を半角に置き換える
function convertNormalWidth(id){
	try{
		var target = document.getElementById(id);
	    for (var i = 0;i < target.options.length; i++ ){
	        var henkan = target.options[i].text.replace(/　/g,'  ');
	        target.options[i].text = henkan;
		}
	}catch(e){
	}
}

///////////////////////////////////////////////////////////////////
// IEのレイヤーバグ(SELECT要素が常に前面に出る)対応JavaScript
// START
///////////////////////////////////////////////////////////////////
function openShim(otherErr)
{
    if (otherErr==null) return;
    var shim = getShim(otherErr);

    otherErr.style.zIndex = 100;
    
    var width = otherErr.offsetWidth;
    var height = otherErr.offsetHeight;
        
    shim.style.width = width;
    shim.style.height = height;
    shim.style.top = otherErr.offsetTop;
    shim.style.left = otherErr.offsetLeft;
    shim.style.zIndex = otherErr.style.zIndex - 1;
    shim.style.position = "absolute";
    shim.style.display = "block";
}

// 作成されたシムオブジェクトを取得する
function getShim()
{
    return document.getElementById('kakusi');
}

//シムを閉じる
function closeShim(otherErr)
{
    if (otherErr==null) return;
    var shim = getShim(otherErr);
    if (shim!=null) shim.style.display = "none";
}

///////////////////////////////////////////////////////////////////
// IEのレイヤーバグ(SELECT要素が常に前面に出る)対応JavaScript
// END
///////////////////////////////////////////////////////////////////

