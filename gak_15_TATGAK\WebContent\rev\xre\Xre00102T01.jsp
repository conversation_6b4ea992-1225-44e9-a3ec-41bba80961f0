<%-- 
	介護等体験登録（基本）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xre/Xre00102T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">



function loadAction(event){
// 画面ロード時の学生氏名再取得
  doGakuseiAjax(document.getElementById('form1:htmlGaksekiCd'), event, 'form1:htmlGakseiName');
  
}


// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  if (confirm(messageCreate(id, args))) {
  	  onChangeData();
  	  return true;
  }
  return false;
}


function openSubWindow(thisObj, thisEvent) {
	// 学生検索子画面（引数：なし）
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGaksekiCd";
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return true;
}


// 学生氏名を取得する
function doGakuseiAjax(thisObj, thisEven){
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;
    args['code2'] = "";
    args['code3'] = "";
    var target = "form1:htmlGakseiName";
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

// 解除ボタン押下時処理
function onClickUnselect(id) {
	var changeDataFlgT01 = document.getElementById("form1:htmlHidChangeDataFlgT01").value;
	if(changeDataFlgT01 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT02 = document.getElementById("form1:htmlHidChangeDataFlgT02").value;
	if(changeDataFlgT02 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT03 = document.getElementById("form1:htmlHidChangeDataFlgT03").value;
	if(changeDataFlgT03 == "1"){
	  return confirm(id);
	}
	return true;
}

// 戻るボタン押下時処理
function onClickReturnDisp(id) {
	var changeDataFlgT01 = document.getElementById("form1:htmlHidChangeDataFlgT01").value;
	if(changeDataFlgT01 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT02 = document.getElementById("form1:htmlHidChangeDataFlgT02").value;
	if(changeDataFlgT02 == "1"){
	  return confirm(id);
	}
	var changeDataFlgT03 = document.getElementById("form1:htmlHidChangeDataFlgT03").value;
	if(changeDataFlgT03 == "1"){
	  return confirm(id);
	}
	return true;
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlgT01").value = "1";
}


// confirmメッセージ後「はい」を選択した時の処理
function confirmOk() {
	var action = document.getElementById("form1:htmlHidShoriKbn").value;
	indirectClick(action);
	return true;
}


// confirmメッセージ後「キャンセル」を選択した時の処理
function confirmCancel() {
	document.getElementById("form1:htmlHidWarnMessage1").value = "0";
	document.getElementById("form1:htmlHidWarnMessage2").value = "0";
	document.getElementById("form1:htmlHidWarnMessage3").value = "0";
	document.getElementById("form1:htmlHidWarnMessage4").value = "0";
	document.getElementById("form1:htmlHidShoriKbn").value = "0";

}

// 項目制御(活性非活性)
function setDesable(thisObj, thisEvent) {
	var value = document.getElementById('form1:htmlTiknKen').value;
	if( value ){	
		document.getElementById('form1:htmlDesable').click();
	}
}

</SCRIPT>
</HEAD>

<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xre00102T01.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Xre00102T01" property="xre00102">
	
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit"
					value="閉じる" 
					styleClass="commandExButton" 
					id="closeDisp"
					action="#{pc_Xre00102T01.doCloseDispAction}">
				</hx:commandExButton> 
				<h:outputText
					styleClass="outputText" 
					id="htmlFuncId"
					value="#{pc_Xre00102T01.funcId}">
				</h:outputText> 
				<h:outputText
					styleClass="outputText" 
					id="htmlLoginId"
					value="#{SYSTEM_DATA.loginID}">
				</h:outputText> <h:outputText
					styleClass="outputText" 
					id="htmlScrnName"
					value="#{pc_Xre00102T01.screenName}">
				</h:outputText>
			</div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
				<hx:commandExButton
					type="submit" 
					value="戻る" 
					styleClass="commandExButton"
					id="returnDisp" 
					onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');"
					action="#{pc_Xre00102T01.doReturnDispAction}">
				</hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="3" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="950">
	 						<TABLE class="table" width="100%">
								<TBODY>
									<TR>
										<TH nowrap class="v_a" width="180"><!-- 体験年度 -->
											<h:outputText 
												styleClass="outputText"
												id="lblTiknNendo"
												value="#{pc_Xre00102T01.xre00102.propTiknNendo.name}"
												style="#{pc_Xre00102T01.xre00102.propTiknNendo.labelStyle}">
											</h:outputText>
										</TH>
										<TD nowrap width="670">
											<h:inputText 
												styleClass="inputText"
												id="htmlTiknNendo"
												size="4"
												value="#{pc_Xre00102T01.xre00102.propTiknNendo.dateValue}"
												disabled="#{pc_Xre00102T01.xre00102.propTiknNendo.disabled}">
												<hx:inputHelperAssist 
													errorClass="inputText_Error"
													imeMode="inactive" 
													promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" />
											</h:inputText>
										</TD>
										<TD 
											rowspan="4" 
											style="background-color: transparent; text-align: right; vertical-align: bottom"
											class="clear_border">
											<hx:commandExButton
												type="submit" 
												styleClass="commandExButton" 
												id="select"
												value="選択" 
												disabled="#{pc_Xre00102T01.xre00102.propSelect.disabled}"
												action="#{pc_Xre00102T01.xre00102.doSelectAction}">
											</hx:commandExButton> 
											<hx:commandExButton
												type="submit" 
												value="解除" 
												styleClass="commandExButton"
												id="unselectEnrollment"
												disabled="#{pc_Xre00102T01.xre00102.propUnSelect.disabled}"
												action="#{pc_Xre00102T01.xre00102.doUnselectAction}"
												onclick="return onClickUnselect('#{msg.SY_MSG_0014W}');">
											</hx:commandExButton>
										</TD>
									</TR>								
									<TR align="center" valign="middle">
										<TH nowrap class="v_a" width="180"><!-- 学籍番号 -->
											<h:outputText 
												styleClass="outputText"
												id="lblGaksekiCd_head"
												value="#{pc_Xre00102T01.xre00102.propGaksekiCd.labelName}"
												style="#{pc_Xre00102T01.xre00102.propGaksekiCd.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="670">
											<h:inputText 
												styleClass="inputText"
												id="htmlGaksekiCd"
												value="#{pc_Xre00102T01.xre00102.propGaksekiCd.value}"
												disabled="#{pc_Xre00102T01.xre00102.propGaksekiCd.disabled}"
												style="#{pc_Xre00102T01.xre00102.propGaksekiCd.style}"
												maxlength="#{pc_Xre00102T01.xre00102.propGaksekiCd.maxLength}"
												readonly="#{pc_Xre00102T01.xre00102.propGaksekiCd.readonly}"
												onblur="return doGakuseiAjax(this, event);"
												size="20">
											</h:inputText>
											<hx:commandExButton 
	 											type="button" 
												value="検"
												styleClass="commandExButton_search" 
												id="btnGakusekiF"
												onclick="openSubWindow(this, event);">
											</hx:commandExButton>
											<h:inputText 
												styleClass="likeOutput"
												id="htmlGakseiName" 
												readonly="true"
												value="#{pc_Xre00102T01.xre00102.propGakseiName.stringValue}">
											</h:inputText>
										</TD>
									</TR>
									<TR>
										<TH nowrap class="v_b"><!-- 体験先種別 -->
											<h:outputText
												styleClass="outputText" 
												id="lblTiknskSbt"
												value="#{pc_Xre00102T01.xre00102.propTiknskSbtList.name}"
												style="#{pc_Xre00102T01.xre00102.propTiknskSbtList.labelStyle}">
											</h:outputText>
										</TH>
										<TD width="670">
											<h:selectOneMenu 
												styleClass="selectOneMenu"
												id="htmlTiknskSbt"
												style="width:150px"
												value="#{pc_Xre00102T01.xre00102.propTiknskSbtList.value}"
												disabled="#{pc_Xre00102T01.xre00102.propTiknskSbtList.disabled}">
													<f:selectItems value="#{pc_Xre00102T01.xre00102.propTiknskSbtList.list}" />
											</h:selectOneMenu>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
							<BR>
							<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD>
										<TABLE 
											border="0" 
											cellpadding="0" 
											cellspacing="0" 
											align="left"
											style="border-bottom-style: none; ">
												<TBODY>
													<TR>
														<TD width="150px" >
															<hx:commandExButton 
																type="button" 
																styleClass="tab_head_on" 
																id="tabXre00102T01" 
																style="width:100%"
																value="基本情報" >
															</hx:commandExButton>
														</TD>
														<TD width="150px">
															<hx:commandExButton
																type="submit" 
																styleClass="tab_head_off" 
																id="tabXre00102T02" 
																style="width:100%"
																value="施設情報" 
																action="#{pc_Xre00102T01.doTabXre00102T02Action}"
																disabled="#{pc_Xre00102T01.xre00102.propSisetuBtn.disabled}">
															</hx:commandExButton></TD>
														<TD width="150px">
															<hx:commandExButton
																type="submit" 
																styleClass="tab_head_off" 
																id="tabXre00102T03" 
																style="width:100%"
																value="日誌" 
																action="#{pc_Xre00102T01.doTabXre00102T03Action}"
																disabled="#{pc_Xre00102T01.xre00102.propNissiBtn.disabled}">
															</hx:commandExButton>
														</TD>
													</TR>
											</TBODY>
										</TABLE>				
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE 
											class="tab_body" 
											border="0" 
											cellpadding="20" 
											cellspacing="0" 
											width="100%" 
											style="border-top-style: none; "><!-- タブ内のテーブルここから▼▼ -->
											<TR>
												<TD align="center" width="100%">
													<div style="height: 550px">
														<BR>
															<TABLE class="table" width="850">
																<TBODY>
																	<TR>
																		<TH nowrap class="v_a" width="185"><!-- 生年月日 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblBirth"
																				value="#{pc_Xre00102T01.propBirth.labelName}"
																				style="#{pc_Xre00102T01.propBirth.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlBirth"
																				value="#{pc_Xre00102T01.propBirth.dateValue}"
																				style="#{pc_Xre00102T01.propBirth.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 年齢 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblAge"
																				value="#{pc_Xre00102T01.propAge.labelName}"
																				style="#{pc_Xre00102T01.propAge.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlAge"
																				value="#{pc_Xre00102T01.propAge.stringValue}"
																				style="#{pc_Xre00102T01.propAge.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_c" width="185"><!-- 性別 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblSeibetu"
																				value="#{pc_Xre00102T01.propSeibetu.labelName}"
																				style="#{pc_Xre00102T01.propSeibetu.style}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlSeibetu"
																				value="#{pc_Xre00102T01.propSeibetu.stringValue}"
																				style="#{pc_Xre00102T01.propSeibetu.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_e" width="185"><!-- 郵便番号 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblAddrCd"
																				value="#{pc_Xre00102T01.propAddrCd.labelName}"
																				style="#{pc_Xre00102T01.propAddrCd.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlAddrCd"
																				value="#{pc_Xre00102T01.propAddrCd.stringValue}"
																				style="#{pc_Xre00102T01.propAddrCd.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_f" width="185"><!-- 現在住所(都道府県) -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblNowKenName"
																				value="#{pc_Xre00102T01.propNowKenName.labelName}"
																				style="#{pc_Xre00102T01.propNowKenName.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlNowKenName"
																				value="#{pc_Xre00102T01.propNowKenName.stringValue}"
																				style="#{pc_Xre00102T01.propNowKenName.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_g" width="185"><!-- 現在住所1 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblAddr1"
																				value="#{pc_Xre00102T01.propAddr1.labelName}"
																				style="#{pc_Xre00102T01.propAddr1.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlAddr1"
																				value="#{pc_Xre00102T01.propAddr1.stringValue}"
																				style="#{pc_Xre00102T01.propAddr1.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_a" width="185"><!-- 現在住所2 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblAddr2"
																				value="#{pc_Xre00102T01.propAddr2.labelName}"
																				style="#{pc_Xre00102T01.propAddr2.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlAddr2"
																				value="#{pc_Xre00102T01.propAddr2.stringValue}"
																				style="#{pc_Xre00102T01.propAddr2.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_e" width="185"><!-- 現在住所3 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblAddr3"
																				value="#{pc_Xre00102T01.propAddr3.labelName}"
																				style="#{pc_Xre00102T01.propAddr3.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlAddr3"
																				value="#{pc_Xre00102T01.propAddr3.stringValue}"
																				style="#{pc_Xre00102T01.propAddr3.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_f" width="185"><!-- 自宅電話番号 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblTel1"
																				value="#{pc_Xre00102T01.propTel1.labelName}"
																				style="#{pc_Xre00102T01.propTel1.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText 
																				styleClass="outputText"
																				id="htmlTel1"
																				value="#{pc_Xre00102T01.propTel1.stringValue}"
																				style="#{pc_Xre00102T01.propTel1.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 携帯電話番号 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblKeitaiTel"
																				value="#{pc_Xre00102T01.propKeitaiTel.labelName}"
																				style="#{pc_Xre00102T01.propKeitaiTel.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:outputText
																				styleClass="outputText" 
																				id="htmlKeitaiTel"
																				value="#{pc_Xre00102T01.propKeitaiTel.stringValue}"
																				style="#{pc_Xre00102T01.propKeitaiTel.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<BR>
															<TABLE class="table" width="850">
																<TBODY>
																	<TR>
																		<TH nowrap class="v_e" width="180"><!-- 申請時期 -->
																			<h:outputText
																				styleClass="outputText"
																				id="lblSinseiJiki"
																				value="#{pc_Xre00102T01.proplblSinseiJiki.labelName}">
																			</h:outputText>
																		</TH>
																		<TD width="720" colspan="4">
																			<h:selectOneMenu
																				styleClass="selectOneMenu"
																				id="htmlSinseiJiki"
																				value="#{pc_Xre00102T01.propSinseiJikiList.value}"
																				style="width:180px;"
																				onchange="onChangeData();">
																					<f:selectItems value="#{pc_Xre00102T01.propSinseiJikiList.list}" />
																			</h:selectOneMenu>
																		</TD>								
																	</TR>
																	<TR>
																		<TH nowrap class="v_e" width="180"><!-- 本籍地 -->
																			<h:outputText
																				styleClass="outputText"
																				id="lblHonsekiList"
																				value="#{pc_Xre00102T01.propHonsekiList.labelName}"
																				style="#{pc_Xre00102T01.propHonsekiList.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="720" colspan="4">
																			<h:selectOneMenu
																				styleClass="selectOneMenu"
																				id="htmlHonseki"
																				value="#{pc_Xre00102T01.propHonsekiList.value}"
																				style="width:180px;"
																				onchange="onChangeData();">
																					<f:selectItems value="#{pc_Xre00102T01.propHonsekiList.list}" />
																			</h:selectOneMenu>
																		</TD>								
																	</TR>
																	<TR>
																		<TH nowrap class="v_e" width="180"><!-- 希望地 -->
																			<h:outputText
																				styleClass="outputText"
																				id="lblKiboChiList"
																				value="#{pc_Xre00102T01.propKiboChiList.labelName}"
																				style="#{pc_Xre00102T01.propKiboChiList.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="720" colspan="4">
																			<h:selectOneMenu
																				styleClass="selectOneMenu"
																				id="htmlKiboChi"
																				value="#{pc_Xre00102T01.propKiboChiList.value}"
																				style="width:180px;"
																				onchange="onChangeData();">
																					<f:selectItems value="#{pc_Xre00102T01.propKiboChiList.list}" />
																			</h:selectOneMenu>
																		</TD>								
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 体験郵便番号 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblTiknaddrCd"
																				value="#{pc_Xre00102T01.propTiknaddrCd.labelName}"
																				style="#{pc_Xre00102T01.propTiknaddrCd.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlTiknaddrCd"
																				value="#{pc_Xre00102T01.propTiknaddrCd.stringValue}"
																				style="#{pc_Xre00102T01.propTiknaddrCd.style}"
																				onchange="onChangeData();">
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_e" width="180"><!-- 体験住所(都道府県) -->
																			<h:outputText
																				styleClass="outputText"
																				id="lblTiknKenList"
																				value="#{pc_Xre00102T01.propTiknKenList.labelName}"
																				style="#{pc_Xre00102T01.propTiknKenList.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD width="720" colspan="4">
																			<h:selectOneMenu
																				styleClass="selectOneMenu"
																				id="htmlTiknKen"
																				value="#{pc_Xre00102T01.propTiknKenList.value}"
																				style="width:180px;"
																				onblur="return setDesable(this, event);"
																				onchange="onChangeData();">
																					<f:selectItems value="#{pc_Xre00102T01.propTiknKenList.list}" />
																			</h:selectOneMenu>
																		</TD>								
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 体験住所 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblTiknaddr"
																				value="#{pc_Xre00102T01.propTiknaddr.labelName}"
																				style="#{pc_Xre00102T01.propTiknaddr.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlTiknaddr"
																				size="100"
																				value="#{pc_Xre00102T01.propTiknaddr.stringValue}"
																				style="#{pc_Xre00102T01.propTiknaddr.style}"
																				disabled="#{pc_Xre00102T01.propTiknaddr.disabled}"
																				maxlength="#{pc_Xre00102T01.propTiknaddr.maxLength}"
																				onchange="onChangeData();">
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_b"><!-- 申込書受領日 --> 
																			<h:outputText
																				styleClass="outputText" 
																				id="lblMskmJuryoDate"
																				value="#{pc_Xre00102T01.propMskmJuryoDate.labelName}"
																				style="#{pc_Xre00102T01.propMskmJuryoDate.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlMskmJuryoDate"
																				value="#{pc_Xre00102T01.propMskmJuryoDate.dateValue}"
																				size="10"
																				onkeydown="onChangeData();">
																					<f:convertDateTime />
																						<hx:inputHelperDatePicker />
																						<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																		    </h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 取消・辞退日 --> 
																			<h:outputText
																				styleClass="outputText" 
																				id="lblTorikesiJitaiDate"
																				value="#{pc_Xre00102T01.propTorikesiJitaiDate.labelName}"
																				style="#{pc_Xre00102T01.propTorikesiJitaiDate.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlTorikesiJitaiDate"
																				value="#{pc_Xre00102T01.propTorikesiJitaiDate.dateValue}"
																				size="10"
																				onkeydown="onChangeData();">
																					<f:convertDateTime />
																						<hx:inputHelperDatePicker />
																							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
																		    </h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 特定対象 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblTokuteiTaisyoFindType"
																				value="#{pc_Xre00102T01.propTokuteiTaisyoFindType.labelName}"
																				style="#{pc_Xre00102T01.propTokuteiTaisyoFindType.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:selectOneRadio
																				styleClass="selectOneRadio" 
																				id="htmlTokuteiTaisyoFindType"
																				value="#{pc_Xre00102T01.propTokuteiTaisyoFindType.value}"
																				style="#{pc_Xre00102T01.propTokuteiTaisyoFindType.style}"
																				onchange="onChangeData();">
																					<f:selectItems value="#{pc_Xre00102T01.propTokuteiTaisyoFindType.list}" />
																			</h:selectOneRadio>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 備考1 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblKhonNote1"
																				value="#{pc_Xre00102T01.propKhonNote1.labelName}"
																				style="#{pc_Xre00102T01.propKhonNote1.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlKhonNote1"
																				size="100"
																				value="#{pc_Xre00102T01.propKhonNote1.stringValue}"
																				style="#{pc_Xre00102T01.propKhonNote1.style}"
																				maxlength="#{pc_Xre00102T01.propKhonNote1.maxLength}"
																				onchange="onChangeData();">
																			</h:inputText>
																		</TD>
																	</TR>
																	<TR>
																		<TH nowrap class="v_b" width="185"><!-- 備考2 -->
																			<h:outputText
																				styleClass="outputText" 
																				id="lblKhonNote2"
																				value="#{pc_Xre00102T01.propKhonNote2.labelName}"
																				style="#{pc_Xre00102T01.propKhonNote2.labelStyle}">
																			</h:outputText>
																		</TH>
																		<TD>
																			<h:inputText
																				styleClass="inputText" 
																				id="htmlKhonNote2"
																				size="100"
																				value="#{pc_Xre00102T01.propKhonNote2.stringValue}"
																				style="#{pc_Xre00102T01.propKhonNote2.style}"
																				maxlength="#{pc_Xre00102T01.propKhonNote2.maxLength}"
																				onchange="onChangeData();">
																			</h:inputText>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
															<TABLE width="95%" class="button_bar" cellspacing="1"　cellpadding="1">
																<TBODY>
																	<TR>
																		<TD width="100%" align="center">
																			<hx:commandExButton
																				type="submit" 
																				value="確定" 
																				styleClass="commandExButton_dat"
																				id="kakutei"
																				disabled="#{pc_Xre00102T01.propKakutei.disabled}"
																				action="#{pc_Xre00102T01.doKakuteiAction}"
																				confirm="#{msg.SY_MSG_0001W}">
																			</hx:commandExButton>
																			<hx:commandExButton
																				type="submit" 
																				value="クリア"
																				styleClass="commandExButton_etc" 
																				id="clear"
																				onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '選択されているタブ情報');"
																				disabled="#{pc_Xre00102T01.propClear.disabled}"
																				action="#{pc_Xre00102T01.doClearAction}">
																			</hx:commandExButton>
																			<hx:commandExButton	
																				type="submit" 
																				value="削除" 
																				styleClass="commandExButton_dat"
																				id="delete"
																				confirm="#{msg.SY_MSG_0004W}"
																				disabled="#{pc_Xre00102T01.propDelete.disabled}"
																				action="#{pc_Xre00102T01.doDeleteAction}">
																			</hx:commandExButton>
																			<!-- Hidden項目制御(活性・非活性)ボタン -->	
																			<hx:commandExButton
																				id="htmlDesable"
																				style="display:none"
																				action="#{pc_Xre00102T01.doDesable}">
																			</hx:commandExButton>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
													</div>
												</TD>
											</TR>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
		</TABLE>
		<h:inputHidden
			id="htmlHidWarnMessage1"
			value="#{pc_Xre00102T01.propExecutableHidden1.integerValue}">
		</h:inputHidden>
		<h:inputHidden
			id="htmlHidWarnMessage2"
			value="#{pc_Xre00102T01.propExecutableHidden2.integerValue}">
		</h:inputHidden>
		<h:inputHidden
			id="htmlHidWarnMessage3"
			value="#{pc_Xre00102T01.propExecutableHidden3.integerValue}">
		</h:inputHidden>
		<h:inputHidden
			id="htmlHidWarnMessage4"
			value="#{pc_Xre00102T01.propExecutableHidden4.integerValue}">
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidErrMessage" 
			value="#{pc_Xre00102T01.xre00102.propHidErrMessage.value}">
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidXre001Flg" 
			value="#{pc_Xre00102T01.xre00102.propHidXre001Flg.integerValue}">
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidCoc001Flg" 
			value="#{pc_Xre00102T01.xre00102.propHidCoc001Flg.integerValue}">
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidChangeDataFlgT01" 
			value="#{pc_Xre00102T01.propHidChangeDataFlgT01.stringValue}" >
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidChangeDataFlgT02" 
			value="#{pc_Xre00102T02.propHidChangeDataFlgT02.stringValue}" >
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidChangeDataFlgT03" 
			value="#{pc_Xre00102T03.propHidChangeDataFlgT03.stringValue}" >
		</h:inputHidden>
		<h:inputHidden 
			id="htmlHidShoriKbn" 
			value="#{pc_Xre00102T01.xre00102.propHidShoriKbn.stringValue}" >
		</h:inputHidden>
		<!-- ↑ここにコンポーネントを配置 -->
		</DIV>
		</DIV>
		<!--↑content↑-->
		</DIV>
		<!--↑outer↑-->
		<!-- フッダーインクルード -->
		<jsp:include page="../inc/footer.jsp" />

		</h:form>
	</gakuen:itemStateCtrl>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
