<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="${pageContext.request.contextPath}/theme/Master.css" rel="stylesheet" type="text/css">
<TITLE>Xrc00401.jsp</TITLE>
<!--<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css"-->
<!--	title="Style">-->
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY><hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrc00401.onPageLoadBegin}">
	<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc00401.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc00401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc00401.screenName}"></h:outputText></div>
	<!--↓outer↓-->
	<DIV class="outer">
	
	<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
	<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
		styleClass="outputText" escape="false">
	</h:outputText>
	</FIELDSET>

	<DIV class="head_button_area" >　
	<!-- ↓ここに戻る／閉じるボタンを配置 -->
	<!-- ↑ここに戻る／閉じるボタンを配置 -->
	</DIV>
	
	<!--↓content↓-->
	<DIV id="content">
		<DIV class="column" align="center">
			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD nowrap width="80%" onclick="return func_1(this, event);">
									<CENTER>
									<TABLE class="table" width="650" border="0" cellpadding="0"
										cellspacing="0" style="">
										<TBODY>
											<TR>	
											    <TH class="v_d" width="150"><h:outputText
												      styleClass="outputText" id="text3"
												      value="納入期限"
												      style="#{pc_Xrc00401.propPayLimit.labelStyle}"></h:outputText>
												</TH>
											    <TD nowrap width="500"><h:inputText id="htmlPayLimit"
											    		styleClass="inputText" size="13"
												    	value="#{pc_Xrc00401.propPayLimit.dateValue}">
													    <f:convertDateTime />
													    <hx:inputHelperDatePicker />
												    	<hx:inputHelperAssist errorClass="inputText_Error"
												    	promptCharacter="_" />
												    </h:inputText>
											    </TD>
											</TR>
											<TR>	
											    <TH class="v_d" width="150"><h:outputText
												      styleClass="outputText" id="text4"
												      value="請求データ作成日"
												      style="#{pc_Xrc00401.propSeikyuDate.labelStyle}"></h:outputText>
												</TH>
											    <TD nowrap width="500"><h:inputText id="htmlSeikyuDate"
											    		styleClass="inputText" size="13"
												    	value="#{pc_Xrc00401.propSeikyuDate.dateValue}">
													    <f:convertDateTime />
													    <hx:inputHelperDatePicker />
												    	<hx:inputHelperAssist errorClass="inputText_Error"
												    	promptCharacter="_" />
												    </h:inputText>
											    </TD>
											</TR>
										</TBODY>
									</TABLE>
									</CENTER>
									</TD>
								</TR>
								<TR>
									<TD nowrap width="80%"></TD>
								</TR>
							</TBODY>
						</TABLE><CENTER>
						<BR>
						<HR noshade class="hr">
						<BR>
						<TABLE class="button_bar" width="650">
							<TBODY>
								<TR>
									<TD width="80%"><hx:commandExButton type="submit" value="実行"
									    styleClass="commandExButton_dat" id="exec"
									    action="#{pc_Xrc00401.doExecAction}" confirm="#{msg.SY_MSG_0001W}"
									    disabled="#{pc_Xrc00401.propExecButton.disabled}"></hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</CENTER></TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
