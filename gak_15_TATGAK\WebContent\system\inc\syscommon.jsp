<%-- 共通include用JSP (</f:view>タグの直前でincludeすること) --%>

<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="com.jast.gakuen.framework.*" %>
<%@ page import="com.jast.gakuen.framework.util.*" %>
<%@ page import="java.util.ArrayList" %>

<% 
	DisplayInfo displayInfo = UtilSystem.getDisplayInfo();
%>


<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/system/inc/gakuen.js"></SCRIPT>
<iframe id='kakusi' scrolling='no' frameborder='0' style='position:absolute; top:0px;left:0px; display:none' src='${pageContext.request.contextPath}/system/inc/dummy.html'></iframe>
<SCRIPT type="text/javascript" language="JavaScript">
//---------------------------- 
// Submit先指定

// (submit時にtargetを動的に切り替える)

// ボタン二度押し対策
//----------------------------
var _target = "";
function setTarget(t) {
	_target = t;
}
var _control = true;
function cancelSubmitCtrl() {
	_control = false;
}
var submitFlug = true;
for (var i = 0; i < document.forms.length; ++i) {
    if(document.forms[i].onsubmit) continue;
    
    document.forms[i].onsubmit = function() {
        try{
        document.getElementById('form1').target = _target;
        }catch(e){}
        if(_target != "" || !_control) {
            _target = "";
            _control = true;
            return true;
        }
        
        document.body.style.cursor = "wait";
        
        if (!submitFlug){
            return false;
        } else {
            
            try {
                if ( submitMethod() == false) {
                    document.body.style.cursor = "default";
                    return false;
                }
            } catch(e){}

            submitFlug = false;
            return true;
        }
    }
}

<%
	if ( displayInfo.getPopupMessage() != null && !displayInfo.getPopupMessage().equals("")){
%>
		alert("<%= displayInfo.getPopupMessage() %>");
<%			
	}
%>
<% 
    // JSFのエラーが発生した場合にメッセージを表示する
	java.util.Iterator  b = javax.faces.context.FacesContext.getCurrentInstance().getMessages();
	if (b.hasNext()){
		
        if (UtilSystem.isUP()){
            out.write("document.getElementById('htmlErrorMessage').innerHTML =\"<div class='info2'><span class='err_cmt'><img src='"+ UtilSystem.getContextPath() 
							+ "/image/err.gif'><span class='firstErr'>不正な値が入力されています。</span></span></div>\"");
        } else {
        	if (UtilSystem.getRequestParam().get("form1:closeDisp") == null) {
	            out.write("setErrMsg('不正な値が入力されています')\n");
	            out.write("alert('エラーが発生しています。\\n詳細は上部のメッセージを確認してください。');");
	        } else {
	        	// クローズボタンが押下された場合は、エラーメッセージを表示せずにウインドウを閉じる
				displayInfo.getPagecode().removeFromSession();
				out.write("self.close();");
	        }
        }
	}
%>

	function fwOnloadEvent() {
	
<%-- アラートダイアログの出力 --%>
<%-- TODO メッセージを表示後閉じるパターンを追加 --%>
<% UtilLog.debug(this.getClass(), "check infomationMessage"); %>

<%-- エラー項目へのフォーカス --%>
<% 
	if ( displayInfo.getTargetFocusId() != null && !"".equals(displayInfo.getTargetFocusId())) {
%>

		<%-- ネイティブタブに項目が存在していた場合の対応 --%>
		var tabflg = false;
		try {
			var target = document.getElementById("form1:<%= displayInfo.getTargetFocusId() %>");
			var parentTag = target.parentNode;
			for (var j = 0;j < 15;j++){
				if (parentTag.id != ""){
					var i = parentTag.id.slice(-1);
					var list = parentTag.id.split(":");
					if (list[1] != ""){	
						if (document.getElementById("form1:" + list[1] + "tabbedPaneltablable_" + i) != null){
							document.getElementById("form1:" + list[1] + "tabbedPaneltablable_" + i).click();
							tabflg = true;
							break;
						}
					}
				}
				parentTag = parentTag.parentNode;
			}			
		} catch(e){
		}
	
		try {
			if (!tabflg){
				var target = document.getElementById("form1:<%= displayInfo.getTargetFocusId() %>");
				if (target.nodeName.toUpperCase() == 'TABLE'){
					var check = document.getElementsByName("form1:<%= displayInfo.getTargetFocusId() %>");
					var i;
					for (i = 0;check.length > i;i++){
						if (check[i].nodeName.toUpperCase() == 'INPUT'){
							check[i].focus();
							break;
						}
					}
				} else if(target.nodeName.toUpperCase() == 'SELECT') {
					if (document.getElementById('content') != null){
						document.getElementById('content').focus();
					}
				} else {
					target.focus();
				}
			}
		} catch(e) {
		}
<%
	}
%>

    <%-- エラー項目のスタイルの変更 --%>
<% 
	if ( displayInfo.getErrList() != null && displayInfo.getErrList().size() != 0) {
%>
		try {
<%
		ArrayList errTarget = displayInfo.getErrList();
		for (int i = 0; errTarget.size() > i; i++) {
%>
			try {		
				document.getElementById("form1:<%= (String)errTarget.get(i) %>").style.backgroundColor ='#FF8E8E';
			} catch(e) {
			}
<%
		}
%>			
		} catch(e) {}
<%
	}
%>

<%-- 画面ロード時実行スクリプト --%>
<% UtilLog.debug(this.getClass(), "check getOnloadScript"); %>
<% if (displayInfo.getOnloadScript() != null) { %>
<%= displayInfo.getOnloadScript() %>
<% } %>

		<%-- windowOnload関数が定義されている場合はコールバック --%>
		if (window.windowOnload && typeof window.windowOnload == 'function') {
			window.windowOnload();
		}
	}

	if(window.addEventListener) {
		window.addEventListener('load', fwOnloadEvent, false);
	}else{
		window.attachEvent('onload', fwOnloadEvent);
	}

<%-- 確認ダイアログの出力 --%>
<%-- confirm() で確認メッセージを表示する。
 "OK"ボタンがクリックされた場合、confirmOk()関数を呼び出す。
 "キャンセル"ボタンがクリックされた場合、confirmCancel()関数を呼び出す。
 confirmOk() 関数、confirmCancel() 関数は、
 呼び出し元のJSPにあらかじめ実装しておく必要がある。 --%>
<% UtilLog.debug(this.getClass(), "check confirmMessage"); %>
<%
	if ( displayInfo.getConfirmMessage() != null && !displayInfo.getConfirmMessage().equals("")){
%>
		if (confirm("<%= displayInfo.getConfirmMessage() %>")) {
			confirmOk(); 
		} else {
			confirmCancel();
		}
<%			
	}
%>
	
<%-- TODO レスポンス後PDF、CSVなどを表示する機能 --%>
<%-- TODO ダウンロードサーブレットの実装 --%>
<%-- TODO メッセージを表示後閉じる処理を実装 --%>

</SCRIPT>


<%-- 共通JavaScript --%>
<SCRIPT type="text/javascript" language="JavaScript">


// 以下のイベントに処理を登録すると、RADの機能が正しく動作しない模様。
// window.onload

//---------------------------- 
// モーダルウィンドウオープン
//---------------------------- 
var _modalWin;
window.onfocus = null;
function openModalWindow(pUrl, pName, pOption) {
	_modalWin = openWindow(pUrl, pName, pOption);
	_modalWin.focus();
	window.onfocus = _modalCheck;
}
function _modalCheck(){
	try{
		if(_modalWin.closed){
			window.onfocus = null;
			_modalWin = null;
		} else {
			_modalWin.focus();
		}
	}catch(e){
		window.onfocus = null;
		_modalWin = null;	
	}
}
/**	
 *  セッションを消去後、子画面を開きます。
 * 
 *  cls：起動する子画面のページコードクラス（例　com.jast.gakuen.rev.ss.PSsc0101）
 *  url,name,opt：openWindow関数の引数
 *  funcID:起動元画面ＩＤ
 */	

var subWinCom = null;
function removeSessionAndOpenWindow(cls ,url,name,opt,funcID) {

	var reOpenSubWinFlg = "0";
	// 起動元の画面が異なる場合は、いったん子画面をクローズしておく
	setSubWinUtil(funcID,name);
	if(subWinCom){
		reOpenSubWinFlg = "1";
		subWinCom.close();
	}

	var ajaxServlet = "rev/co/RemoveFromSessionAjax";
	var args = new Array();
		args['pcClass'] = cls;
		args['motoFuncId'] = '';
	var engine      = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) { 
		var windowPointer = openModalWindow(url,name,opt);
			//opener.setWindowObject(windowPointer, funcId);
			if(reOpenSubWinFlg=="0"){
				focus();
			}
		}
	);
	engine.send(ajaxServlet,null,args);
}

//検索子画面のオブジェクト取得
function setSubWinUtil(funcID,name){
	//IE8対応 2009-11-26 ->
	//var windowList = opener.windowList;
	var windowList = opener.getOpenWins();
	//IE8対応 2009-11-26 <-
	for(i=0;i<windowList.length;i++) {
		tmpWin = windowList[i];
		if(tmpWin) {
			if (!tmpWin.winObj.closed) {
				//if (tmpWin.funcId != funcID && tmpWin.winObj.name == name ) {
				if (tmpWin.winObj.name == name) {
					subWinCom = tmpWin.winObj;
					return true;
				}
			}
		}
	}
	return false;
}

//---------------------------- 
// Ajax通信用
//---------------------------- 
function AjaxUtil() {
}

function getCodeName(servlet, target, code) {
    var args = new Array();
    args['code'] = code;
    var ajaxUtil = new AjaxUtil();
    ajaxUtil.getCodeName(servlet, target, args);
}

/**
 * 複数の値を戻します
 * callBackMethodを呼び出します
 */
AjaxUtil.prototype.getPluralValue = function (ajaxServlet, target, args) {
	var engine = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) {
			try{
				callBackMethod(value);
			}catch(e){
				// 実装のバグを警告する
				alert('Ajaxの戻り関数『callBackMethod』を実装してください。');
			}
		}
	);
	engine.send(ajaxServlet, target, args);
}

/**
 * 複数の値を戻します
 * callBackMethodを呼び出します
 */
AjaxUtil.prototype.getPluralValueSetMethod = function (ajaxServlet, target, args, methodName) {
	var engine = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) {
			try{
				eval('('+ methodName +')(value)');
			}catch(e){
				alert(e);
				// 実装のバグを警告する
				alert('Ajaxの戻り関数『'+methodName+'』を実装してください。');
			}
		}
	);
	engine.send(ajaxServlet, target, args);
}

/**
 * コードに対応する名称を取得します。
 */
AjaxUtil.prototype.getCodeName = function (ajaxServlet, target, args) {
	var engine = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) {
			if (document.getElementById(target).nodeName == "SPAN"){
				document.getElementById(target).innerHTML = value["name"];
				document.getElementById(target).title = value["name"];
			} else {
				document.getElementById(target).value = value["name"];
				document.getElementById(target).title = value["name"];
			}
		}
	);
	engine.send(ajaxServlet, target, args);
}

/**	
 * セッションを消去後、子画面を開きます。
 */	
function removeSession(cls,url,name,opt) {
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.removeSession(cls,url,name,opt);
}

/**	
 * セッションを消去後、子画面を開きます。
 */	
AjaxUtil.prototype.removeSession = function (cls,url,name,opt) {
	var ajaxServlet = "up/co/RemoveSessionAjax";
	var args = new Array();
		args['pcClass'] = cls;
	var engine      = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) {
			var windowPointer = window.open(url,name,opt);
			windowPointer.focus();
		}
	);
	engine.send(ajaxServlet,null,args);
}

/**
 * セレクトボックスの要素を置換します。
 */
AjaxUtil.prototype.replaceSelectboxItem = function (ajaxServlet, target, args) {
	var engine = new AjaxEngine();
	engine.setCallbackMethod(
		
		function(value) {
			listvalue = value["name"];
			var targetSelectbox = document.getElementById(target);
			if (listvalue == '' || listvalue == null) {
				targetSelectbox.options.length = 0;
			} else {
				var itemList = listvalue.split("$@,@$");
				targetSelectbox.options.length = itemList.length;
				for (i = 0, max = itemList.length; i < max; i++) {
					var rowItem = itemList[i].split('$@:@$');
					targetSelectbox.options[i].value = rowItem[0];
					targetSelectbox.options[i].text = rowItem[1];
				}

				// 先頭のデータを選択状態にする。
				targetSelectbox.options[0].selected = true;
			}
		}
	);
	engine.send(ajaxServlet, target, args);
}

//---------------------------------
// 非同期通信実行部
//---------------------------------

/**
 * コンストラクタ。
 */
function AjaxEngine() {
	this.callbackMethod = null;
}

/**
 * XmlHttpオブジェクトを取得する。
 */
AjaxEngine.prototype.getXmlHttp = function() {
	var xmlHttp = null;
    /*@cc_on
    @if (@_jscript_version >= 5)
        try {
            xmlHttp = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
            try {
                xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
            } catch (E) {
                xmlHttp = false;
            }
        }
    @else
        xmlHttp = false;
    @end @*/
    if (!xmlHttp && typeof XMLHttpRequest != 'undefined') {
        try {
            xmlHttp = new XMLHttpRequest();
            xmlHttp.overrideMimeType("text/xml"); 
        } catch (e) {
            xmlHttp = false;
        }
    }
	
	return xmlHttp;
}

/**
 * リクエストを送信する。
 */
AjaxEngine.prototype.send = function(ajaxServlet, target, args) {
	
	var params = '';
	for (key in args) {
		params += key + "=" + encodeURIComponent(args[key]) + "&";
	}
	params = params.substr(0, params.length -1);

	// URLの組み立て
	url = "${pageContext.request.contextPath}/faces/ajax/" + ajaxServlet
		+ "?target=" + target 
		+ "&windowName=" + window.name
		+ "&" + params;

	// XmlHttpオブジェクトの取得
	var xmlHttp = this.getXmlHttp();
	if (xmlHttp) {
		var callback = this.callbackMethod;
		xmlHttp.onreadystatechange = function() {
		    if (xmlHttp.readyState == 4 && xmlHttp.status == 200) {
		        var xmlDoc = xmlHttp.responseXML;
		        if (xmlDoc.documentElement) {
		            var child  = xmlDoc.getElementsByTagName('value').item(0).childNodes;
					var value = new Array();
					for (i=0;i<child.length;i++)
					{
						if (child.item(i).childNodes.length != 0){
							value[child.item(i).nodeName] = child.item(i).childNodes[0].nodeValue;
						} else {
							value[child.item(i).nodeName] = "";
						}
					}
					// コールバックメソッドを実行
					if (callback) {
						callback(value);
					}
		        }
			}
		}
	}
	// リクエスト送信
	xmlHttp.open('GET', url, true);
	xmlHttp.send(null);
}

/**
 * コールバックメソッドを設定する
 */
AjaxEngine.prototype.setCallbackMethod = function (handler) {
	this.callbackMethod = handler;
}


<%-- 入力フォームでENTERキー押下時の、SUBMITを抑制 --%>
try{
document.getElementById("form1").onkeydown  = customOnKeyDown;
}catch(e){}
function customOnKeyDown(e){
    if (document.all) {  // IE
        if (event.keyCode==13){
            if(window.event.srcElement.type == null ||
               (window.event.srcElement.type.toUpperCase() !='SUBMIT' && 
               window.event.srcElement.type.toUpperCase() !='TEXTAREA' &&
               window.event.srcElement.type.toUpperCase() !='BUTTON' &&
               window.event.srcElement.type.toUpperCase() !='IMAGE' &&
               window.event.srcElement.tagName.toUpperCase() !='A')){
               return false;
            }
        }
        
        if(((event.altKey) && (event.keyCode==37))) {
            event.returnValue = false;
            false;
        }
        
        if(window.event.srcElement.type == null ||
           (window.event.srcElement.type.toUpperCase() !='TEXT' && 
            window.event.srcElement.type.toUpperCase() !='TEXTAREA' &&
            window.event.srcElement.type.toUpperCase() !='FILE' &&
            window.event.srcElement.type.toUpperCase() !='PASSWORD')){
            if((event.keyCode==8)) {
                event.returnValue = false;
                false;
            }
        }
    } else { // 以外
        if (e.keyCode==13){
            if(e.target.type == null ||
               (e.target.type.toUpperCase() !='SUBMIT' && 
               e.target.type.toUpperCase() !='TEXTAREA' &&
               e.target.type.toUpperCase() !='BUTTON' &&
               e.target.type.toUpperCase() !='IMAGE' &&
               e.target.tagName.toUpperCase() !='A')){
               return false;
            }
        }
    }
}
window.document.onkeydown = F5KeyDown;
function F5KeyDown(k){
	try {
	    if (document.all) {  // IE
			if (event.keyCode == 116) {
				event.keyCode = null;
				return false;
			}
			if (event.ctrlKey && event.keyCode == 82) {
				event.keyCode = null;
				return false;
			}
		} else {
			if (k.keyCode == 116) {
				k.keyCode = null;
				return false;
			}
			if (k.ctrlKey && k.keyCode == 82) {
				k.keyCode = null;
				return false;
			}
		}
	}catch(e){
		return false;
	}
}

// IEで画面を閉じるときにでる確認メッセージを抑制
if (!window.opener) {
    if (typeof document.body.style.maxHeight != "undefined") {
        // IE7
        window.open('','_parent','');
    } else {
        // IE6
        window.opener = "_dummy";
    }    
}

<%-- ページロード時に呼ばれるファンクション --%>
<%-- onLoadイベントはRADが利用しているため独自に定義を行なう --%>
try{
	onLoad();
}catch(e){}

</SCRIPT>