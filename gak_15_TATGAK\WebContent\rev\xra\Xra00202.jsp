<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00202.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00202.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}

function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}
function doAllSelect(thisObj, thisEvent) {
// チェックボックス一括チェック
	check('htmlNyugakSenkoList','htmlListCheckBox');
}
function doAllUnSelect(thisObj, thisEvent) {
// チェックボックス一括解除
	uncheck('htmlNyugakSenkoList','htmlListCheckBox');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xra00202.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xra00202.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xra00202.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xra00202.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
			<hx:commandExButton type="submit" value="戻る" 
				styleClass="commandExButton" id="returnDisp" 
				action="#{pc_Xra00202.doReturnDispAction}"
				tabindex="5">
			</hx:commandExButton>
            <!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
            <TABLE width="900">
                <TR>
                    <TD width="900" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblNyugakNendo" value="入学年度">
									</h:outputText>
								</TH>
	                            <TD width="300">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlNyugakNendo"
	                                	value="#{pc_Xra00202.propNyugakNendo.stringValue}">
		                            </h:outputText>
		                        </TD>
	                           	<TH class="v_b" width="140">
	                           		<h:outputText styleClass="outputText"
	                                	id="lblNyugakGakkiNo" value="入学期NO">
	                                </h:outputText>
	                            </TH>
	                            <TD width="300">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlNyugakGakkiNo"
										value="#{pc_Xra00202.propNyugakGakkiNo.stringValue}">
		                            </h:outputText>
		                        </TD>
	                        </TR>
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblSyugakSbt" value="就学種別">
	                                </h:outputText>
	                            </TH>
	                            <TD width="300">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlSyugakSbt"
	                                	value="#{pc_Xra00202.propSyugakSbt.stringValue}">
		                            </h:outputText>
		                        </TD>
	                           	<TH class="v_b" width="140">
	                           		<h:outputText styleClass="outputText"
	                                	id="lblNyugakNenji" value="入学年次">
	                                </h:outputText>
	                            </TH>
	                            <TD width="300">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlNyugakNenji"
	                                	value="#{pc_Xra00202.propNyugakNenji.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                        </TR>
	                        <TR>
	                            <TH class="v_c" width="140" nowrap>
	                            	<h:outputText styleClass="outputText"
	                            		id="lblUketukeNo" value="受付番号">
	                            	</h:outputText>
	                            </TH>
	                            <TD colspan=3>
	                            	<h:outputText styleClass="outputText"
	                            		id="htmlUketukeNo"
										value="#{pc_Xra00202.propUketukeNo.stringValue}">
									</h:outputText>
								</TD>
	                        </TR>
	                    </TABLE>
                    </TD>
                </TR>
            </TABLE>
            <HR class="hr" noshade>
			<TABLE width="900" border="0" cellpadding="0">
				<TR>
					<TD align="right" nowrap class="outputText">
						<h:outputText styleClass="outputText"
							id="htmlNyugakSenkoListCount"
							value="#{pc_Xra00202.propNyugakSenkoListCount.value}">
						</h:outputText>
					</TD>
				</TR>
				<tr>
					<td>
						<DIV style="height:180px" class="listScroll"
							onscroll="setScrollPosition('scroll', this);">
							<h:dataTable border="0" cellpadding="0" cellspacing="0"
								columnClasses="columnClass1" headerClass="headerClass"
								footerClass="footerClass"
								rowClasses="#{pc_Xra00202.propNyugakSenkoList.rowClasses}"
								styleClass="meisai_scroll" id="htmlNyugakSenkoList"
								value="#{pc_Xra00202.propNyugakSenkoList.list}" var="varlist">
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value=""
											id="lblListCheckBox">
										</h:outputText>
									</f:facet>
									<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="htmlListCheckBox" value="#{varlist.taisyoChk}"
										disabled="#{varlist.taisyoDisp}">
									</h:selectBooleanCheckbox>
									<f:attribute value="text-align:center" name="style" />
									<f:attribute value="60" name="width" />
								</h:column>
								<h:column id="column2">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="受付番号"
											id="lblListUketukeNo">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListUketukeNo"
										value="#{varlist.uketukeNo}">
									</h:outputText>
									<f:attribute value="text-align:center" name="style" />
									<f:attribute value="100" name="width" />
								</h:column>
								<h:column id="column3">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="振込依頼人コード"
											id="lblListFurikomiIraiCd">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListFurikomiIraiCd"
										value="#{varlist.furikomiIraiCd}">
									</h:outputText>
									<f:attribute value="text-align:center" name="style" />
									<f:attribute value="150" name="width" />
								</h:column>
								<h:column id="column4">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="氏名"
											id="lblListName">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListName"
										value="#{varlist.name}">
									</h:outputText>
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="300" name="width" />
								</h:column>
								<h:column id="column5">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="選考結果"
											id="lblListNyugakKyokKbn">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListNyugakKyokKbn"
										value="#{varlist.nyugakKyokKbnName}">
										<f:convertNumber />
									</h:outputText>
									<f:attribute value="text-align:center" name="style" />
									<f:attribute value="100" name="width" />
								</h:column>
								<h:column id="column6">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="学籍番号"
											id="lblListGaksekiCd">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="htmlListGaksekiCd"
										value="#{varlist.gaksekiCd}">
										<f:convertNumber />
									</h:outputText>
									<f:attribute value="text-align:center" name="style" />
									<f:attribute value="150" name="width" />
								</h:column>
							</h:dataTable>
							<BR>
						</DIV>
					</td>
				</tr>
			</TABLE>
			<TABLE width="900" align="center">
				<TR>
					<TD align="left">
						<hx:commandExButton type="submit" value="一括チェック"
							styleClass="check" id="btnAllSelect"  onclick="return doAllSelect(this, event);">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="一括解除"
							styleClass="uncheck" id="btnAllUnSelect" onclick="return doAllUnSelect(this, event);">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
            <HR class="hr" noshade>
            <TABLE width="500" class="table">
                <TBODY>
                    <TR>
                        <TH class="v_c" width="170">
                        	<h:outputText styleClass="outputText"
                        		id="lblSenkokekkaDate"
								style="#{pc_Xra00202.propSenkokekkaDate.labelStyle}"
								value="#{pc_Xra00202.propSenkokekkaDate.labelName}">
							</h:outputText>
						</TH>
						<TD width="250">
							<h:inputText styleClass="inputText"
								id="SenkokekkaDate" size="10" tabindex="1"
								value="#{pc_Xra00202.propSenkokekkaDate.dateValue}"
								style="#{pc_Xra00202.propSenkokekkaDate.style}">
								<f:convertDateTime />
								<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
								<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
                    </TR>
                    <TR>
                        <TH class="v_d">
                        	<h:outputText styleClass="outputText"
                        		id="lblNyugakKyokKbn"
								style="#{pc_Xra00202.propNyugakKyokKbn.labelStyle}"
								value="#{pc_Xra00202.propNyugakKyokKbn.labelName}">
							</h:outputText>
						</TH>
                        <TD colspan="3">
                        	<h:selectOneMenu styleClass="selectOneMenu"
                        		id="htmlNyugakKyokKbn" tabindex="2"
								value="#{pc_Xra00202.propNyugakKyokKbn.value}">
								<f:selectItems value="#{pc_Xra00202.propNyugakKyokKbn.list}" />
							</h:selectOneMenu>
						</TD>
                    </TR>
                </TBODY>
            </TABLE>
            <TABLE width="900" class="button_bar" border="0" cellpadding="0"
                cellspacing="0">
                <TR>
                    <TD >
                    	<hx:commandExButton type="submit" value="実行"
							styleClass="commandExButton_dat" id="register"
							style="#{pc_Xra00202.propRegister.style}"
							disabled="#{pc_Xra00202.propRegister.disabled}"
							action="#{pc_Xra00202.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}" tabindex="3">
						</hx:commandExButton>

						<hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_etc" id="clear"
							style="#{pc_Xra00202.propClear.style}" 
							action="#{pc_Xra00202.doClearAction}"
							tabindex="4">
						</hx:commandExButton>
                    </TD>
                </TR>
            </TABLE>
			<h:inputHidden value="#{pc_Xra00202.propHidSyugakSbt.stringValue}" id="htmlHidSyugakSbt"></h:inputHidden>
			<h:inputHidden value="#{pc_Xra00202.propHidNyugakNenji.stringValue}" id="htmlHidNyugakNenji"></h:inputHidden>
			<h:inputHidden value="#{pc_Xra00202.propHidUketukeNoFrom.stringValue}" id="htmlHidUketukeNoFrom"></h:inputHidden>
			<h:inputHidden value="#{pc_Xra00202.propHidUketukeNoTo.stringValue}" id="htmlHidUketukeNoTo"></h:inputHidden>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

