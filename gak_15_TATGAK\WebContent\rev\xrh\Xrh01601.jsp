<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

// 学生検索画面（引数：①学籍番号）
function openSubWindow(field1) {
  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
    + "?retFieldName=" + field1;

  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
  return false;
}

function init(){

	// 出力対象取得
	var radioList = document.getElementsByName('form1:htmlOutputTarget');
	var checkList = document.getElementsByName('form1:htmlUpdate');
		
	for(var i=0; i < radioList.length; i++) {
		if (radioList[i].checked) {
			val = radioList[i].value;
			break;
		}
	}

//	setGakuseiDisabled(thisEvent, document.getElementById('form1:htmlOutputTarget'));

	//	"学生指定"のとき使用可を設定
	if(val == "1") {
		document.getElementById('form1:htmlUpdate').disabled = false;		// 結果通知処理日
		document.getElementById('form1:htmlGakusekiCd').disabled = false;	// 学籍番号
		document.getElementById('form1:search').disabled = false;			// 学籍番号選択
		document.getElementById('form1:select').disabled = false;			// 追加ボタン
		document.getElementById('form1:allRemove').disabled = false;		// 全て除外ボタン
		document.getElementById('form1:remove').disabled = false;			// 除外ボタン
		document.getElementById('form1:lblName').disabled = false;			// 学生名称
		document.getElementById('form1:htmlStudentList').disabled = false;	// 対象学生一覧
	}
	//	"未出力の学生全て"のとき使用否を設定
	else {
		document.getElementById('form1:htmlUpdate').checked = true; 	// 結果通知処理日
		document.getElementById('form1:htmlUpdate').disabled = true;		// 結果通知処理日
		document.getElementById('form1:htmlGakusekiCd').disabled = true;	// 学籍番号
		document.getElementById('form1:search').disabled = true;			// 学籍番号選択
		document.getElementById('form1:select').disabled = true;			// 追加ボタン
		document.getElementById('form1:allRemove').disabled = true;			// 全て除外ボタン
		document.getElementById('form1:remove').disabled = true;			// 除外ボタン
		document.getElementById('form1:lblName').disabled = true;			// 学生名称
		document.getElementById('form1:htmlStudentList').disabled = true;	// 対象学生一覧
	}

	// 学生氏名表示
	doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'), event, 'form1:lblName');
}

// 学生氏名を取得する
function doGakuseiAjax(thisObj, thisEven, targetLabel) {
  var servlet = "rev/co/CobGakseiAJAX";
  var args = new Array();
  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// ページ初期化
//function reloadPage( thisEvent ){
//	doSikentiAjax(document.getElementById('form1:htmlSikentiCd'), thisEvent);
//}

// 通知可能数確認
function openConfirmSubWindow() {
	// 年度
	var par1 = document.getElementById('form1:htmlnendo').value;
	// 試験回数
	var par2 = document.getElementById('form1:htmlKamokSikenCnt').value;

	var url="${pageContext.request.contextPath}/faces/rev/xrh/Xrh01602.jsp" +
				"?htmlnendo=" + par1 + 
				"&htmlKamokSikenCnt="+par2; 

	openModalWindow(url, "Xrh01602", "<%=com.jast.gakuen.rev.xrh.Xrh01602.getWindowOpenOption() %>");

	return false;
}


//	選択可否切替
function setGakuseiDisabled( thisEvent, thisObj ) {
	//	出力対象取得
	var chkValue = "0";
	//	* onload時はnull
	if (thisObj != null) {
		chkValue = thisObj.value;
		if (typeof chkValue === "undefined") {
			return;
		}
	}

	//	"学生指定"のとき使用可を設定
	if(chkValue == "1") {
		document.getElementById('form1:htmlUpdate').disabled = false;		// 結果通知処理日
		document.getElementById('form1:htmlGakusekiCd').disabled = false;	// 学籍番号
		document.getElementById('form1:search').disabled = false;			// 学籍番号選択
		document.getElementById('form1:select').disabled = false;			// 追加ボタン
		document.getElementById('form1:allRemove').disabled = false;		// 全て除外ボタン
		document.getElementById('form1:remove').disabled = false;			// 除外ボタン
		document.getElementById('form1:lblName').disabled = false;			// 学生名称
		document.getElementById('form1:htmlStudentList').disabled = false;	// 対象学生一覧
	}
	//	"未出力の学生全て"のとき使用否を設定
	else {
		document.getElementById('form1:htmlUpdate').checked = true; 		// 結果通知処理日
		document.getElementById('form1:htmlUpdate').disabled = true;		// 結果通知処理日
		document.getElementById('form1:htmlGakusekiCd').disabled = true;	// 学籍番号
		document.getElementById('form1:search').disabled = true;			// 学籍番号選択
		document.getElementById('form1:select').disabled = true;			// 追加ボタン
		document.getElementById('form1:allRemove').disabled = true;			// 全て除外ボタン
		document.getElementById('form1:remove').disabled = true;			// 除外ボタン
		document.getElementById('form1:lblName').disabled = true;			// 学生名称
		document.getElementById('form1:htmlStudentList').disabled = true;	// 対象学生一覧
	}

}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onload="init();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh01601.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01601.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<hx:commandExButton
					type="submit" value="通知可能数確認"
					styleClass="commandExButton" id="refer"
					disabled="#{pc_Xrh01601.propRefer.disabled}"
					action="#{pc_Xrh01601.doReferAction}" 
					onclick="return openConfirmSubWindow();"
					tabindex="1"
					style="width:160px";>
				</hx:commandExButton>
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
					<TBODY>
						<TR>
							<TH class="v_a" width="150">
								<h:outputText styleClass="outputText"
									id="lblNendo"
									value="#{pc_Xrh01601.propNendo.labelName}" 
									style="#{pc_Xrh01601.propNendo.labelStyle}">
								</h:outputText>
							</TH>
							<TD width="180">
								<h:inputText id="htmlNendo" styleClass="inputText" 
									readonly="#{pc_Xrh01601.propNendo.readonly}" 
									style="#{pc_Xrh01601.propNendo.style}" 
									value="#{pc_Xrh01601.propNendo.dateValue}"
									disabled="#{pc_Xrh01601.propNendo.disabled}" size="4" tabindex="2">
									<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
									<f:convertDateTime pattern="yyyy" />
								</h:inputText>
							</TD>
						</TR>
						
						<TR>
							<TH class="v_a" width="150">
								<h:outputText styleClass="outputText"
									value="#{pc_Xrh01601.propKamokSikenCnt.labelName}"
									style="#{pc_Xrh01601.propKamokSikenCnt.labelStyle}">
								</h:outputText>
							</TH>
							<TD width="520">
								<h:inputText id="htmlKamokSikenCnt" styleClass="inputText" 
									readonly="#{pc_Xrh01601.propKamokSikenCnt.readonly}" 
									value="#{pc_Xrh01601.propKamokSikenCnt.integerValue}"
									disabled="#{pc_Xrh01601.propKamokSikenCnt.disabled}" 
									size="4" tabindex="3">
									<hx:inputHelperAssist errorClass="inputText_Error"
					    			imeMode="inactive" promptCharacter="_" />
									<f:convertNumber type="number" pattern="#0"/>
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
								</h:inputText>
								<h:outputText value="回" />
							</TD>
						</TR>
						
						<TR>
							<TH class="v_a" width="150">
								<h:outputText styleClass="outputText"
									value="発送日">
								</h:outputText>
							</TH>
							<TD width="640">
								<h:inputText 
									id="htmlHassoDate"
									styleClass="inputText" 
									size="12"
									value="#{pc_Xrh01601.propHassoDate.dateValue}"
									disabled="#{pc_Xrh01601.propHassoDate.disabled}"
									tabindex="4">
									<f:convertDateTime />
									<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" />
									<hx:inputHelperDatePicker />
								</h:inputText>(未設定時はシステム日付)
							</TD>
						</TR>
						
						<TR>
					   	 <TH nowrap class="v_a" width="150">
							<!--通信欄 -->
							<h:outputText styleClass="outputText" id="lblTusin"
								value="#{pc_Xrh01601.propTusin.labelName}" 
								style="#{pc_Xrh01601.propTusin.labelStyle}">
							</h:outputText>
						</TH>
						<TD>
							<h:inputTextarea styleClass="inputTextare"
								id="htmlTusin" cols="60" rows="4"
								readonly="#{pc_Xrh01601.propTusin.readonly}"
								value="#{pc_Xrh01601.propTusin.stringValue}"
								style="#{pc_Xrh01601.propTusin.style}" tabindex="5">
							</h:inputTextarea>
						</TD>
					   </TR>
					   
					   <TR>	
							<TH class="v_a" width="150">
								<h:outputText styleClass="outputText"
									id="lblOutputTargetStyle" value="出力対象">
								</h:outputText>
							</TH>
							<TD>
								<h:selectOneRadio
									disabledClass="selectOneRadio_Disabled"
									styleClass="selectOneRadio" id="htmlOutputTarget"
									disabled="#{pc_Xrh01601.propOutputTarget.disabled}"
									value="#{pc_Xrh01601.propOutputTarget.stringValue}"
									style="#{pc_Xrh01601.propOutputTarget.style}" 
									tabindex="6"
									onclick="return setGakuseiDisabled(event, this);">
									<f:selectItem itemValue="0" itemLabel="未出力の学生全て" />
									<f:selectItem itemValue="1" itemLabel="学生指定" />
								</h:selectOneRadio>
							</TD>
						</TR>
						
						<TR>	
							<TH class="v_a" width="150">
								<h:outputText styleClass="outputText"
									value="結果通知処理日">
								</h:outputText>
							</TH>
							<TD>
								<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
									id="htmlUpdate" 
									value="#{pc_Xrh01601.propUpdate.checked}" tabindex="7"
									disabled="#{pc_Xrh01601.propUpdate.disabled}">
								</h:selectBooleanCheckbox>更新する
							　</TD>
						 </TR>
					</TBODY>
					</TABLE>
					
					<BR>
								
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
					<TBODY>
						<TR>
						<TH nowrap class="v_a" width="150">
								<!--学籍番号 -->
			                	<h:outputText 
			                		styleClass="outputText" 
			                		id="lblGakusekiCd"
			                		value="#{pc_Xrh01601.propGakusekiCd.labelName}"
			                		style="#{pc_Xrh01601.propGakusekiCd.labelStyle}">
			                	</h:outputText>
			              	</TH>
			              	<TD width="640">
								<h:inputText id="htmlGakusekiCd" styleClass="inputText" 
									readonly="#{pc_Xrh01601.propGakusekiCd.readonly}" 
									style="#{pc_Xrh01601.propGakusekiCd.style}"
									disabled="#{pc_Xrh01601.propGakusekiCd.disabled}"
									maxlength="#{pc_Xrh01601.propGakusekiCd.maxLength}"
									size="10"
									tabindex="8"
									value="#{pc_Xrh01601.propGakusekiCd.stringValue}"
									onblur="return doGakuseiAjax(this, event, 'form1:lblName');">
								</h:inputText>
								<hx:commandExButton type="button"
									styleClass="commandExButton_search" id="search"
									disabled="#{pc_Xrh01601.propSearch.disabled}"
									rendered="#{pc_Xrh01601.propSearch.rendered}"
									style="#{pc_Xrh01601.propSearch.style}"
									onclick="openSubWindow('form1:htmlGakusekiCd');" tabindex="9">
								</hx:commandExButton>
								<hx:commandExButton type="submit"
									value="追加"
									styleClass="commandExButton" id="select"
									disabled="#{pc_Xrh01601.propAdd.disabled}"
									rendered="#{pc_Xrh01601.propAdd.rendered}"
									style="#{pc_Xrh01601.propAdd.style}"
									action="#{pc_Xrh01601.doAddAction}" tabindex="10">
								</hx:commandExButton> 
								<h:outputText
          							styleClass="outputText"
          							id="lblName"
          							value="#{pc_Xrh01601.propName.stringValue}">
          						</h:outputText>
          					</TD>
          				</TR>
					</TBODY>
					</TABLE>
								
					<TABLE border="0" cellspacing="0" width="800" align="center">
    				<TBODY>
		                  <TR>
		                    <TD width="510" align="left">
		                      <h:outputText styleClass="outputText"
		                  			value="対象学生一覧">
		                      </h:outputText>
		                     </TD>
		                  </TR>
      					　<TR>
        					<TD width="700">
        						<h:selectManyListbox styleClass="selectManyListbox"
        							id="htmlStudentList" size="15" style="width: 100%"
        							value="#{pc_Xrh01601.propObjectStudent.value}" tabindex="11">
        							<f:selectItems value="#{pc_Xrh01601.propObjectStudent.list}" />
      							</h:selectManyListbox>
        					</TD>
        					<TD valign="top" align="center">
        						<hx:commandExButton type="submit" value="全て除外"
			                        styleClass="commandExButton" id="allRemove"
									disabled="#{pc_Xrh01601.propRemoveAll.disabled}"
			                        action="#{pc_Xrh01601.doRemoveallAction}" style="width:60px" tabindex="12">
			                     </hx:commandExButton><BR>
			                     
			                     <BR>
			                     <h:outputText styleClass="outputText"
			                        value="（複数選択可）">
			                     </h:outputText><BR>
          						 
          						 <BR>
			                     
			                     <hx:commandExButton
			                        type="submit" value="除外" styleClass="commandExButton"
			                        id="remove" action="#{pc_Xrh01601.doRemoveAction}"
									disabled="#{pc_Xrh01601.propRemove.disabled}"
			                        style="width:60px" tabindex="13">
          						  </hx:commandExButton>
          
			                    </TD>
			                  </TR>
      				</TBODY>
      				</TABLE>
                  
                    <TABLE width="700" border="0" cellpadding="0" cellspacing="0" 
									class="button_bar">
						<TBODY>
							<TR>
								<TD>
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="pdfout"
										value="PDF作成"
										action="#{pc_Xrh01601.doPdfOutAction}"
										disabled="#{pc_Xrh01601.propPdfOut.disabled}"
										rendered="#{pc_Xrh01601.propPdfOut.rendered}"
										style="#{pc_Xrh01601.propPdfOut.style}" tabindex="14"
								        confirm="#{msg.SY_MSG_0019W}">
									</hx:commandExButton>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
				</DIV>
			</DIV>
		</DIV>
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
				