
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm01001.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
	<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
	<%@ page language="java" contentType="text/html; charset=UTF-8"	pageEncoding="UTF-8"%>
	
	<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<META name="GENERATOR" content="IBM Software Development Platform">
	<META http-equiv="Content-Style-Type" content="text/css">
	
	<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
	<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
	<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
	<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
	
	<TITLE>Xrm01001.jsp</TITLE>
	<SCRIPT type="text/javascript">
	function fncInit() {
	    	//ＰＤＦ作成ボタンの制御
		if(document.getElementById('form1:htmlHidErrorKbn').value == '1'){
			document.getElementById('form1:PdfOut').disabled = true;
		}
	}


	function func_check_on(thisObj, thisEvent) {
		check('htmlPayList','htmlSelectedList');
	}
	
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPayList','htmlSelectedList');
	}
	
	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('search');
	}			
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}
	
	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onload="fncInit();">
	
	<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrm01001.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />		

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;">
				<hx:commandExButton type="submit" value="閉じる"
					styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrm01001.doCloseDispAction}">
				</hx:commandExButton>
				<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrm01001.funcId}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
				<h:outputText styleClass="outputText" id="htmlScrnName"	value="#{pc_Xrm01001.screenName}"></h:outputText>
			</div>

			<!--↓OUTER↓-->
			<DIV class="outer">
				<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
					<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText>
				</FIELDSET>

				<DIV class="head_button_area">
				</DIV>
	
				<!--↓CONTENT↓-->
				<DIV id="content">
					<DIV class="column" align="center">
						<TABLE width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" width="50%" cellpadding="0" class="table" cellspacing="0">
											<TBODY>

												<!-- 学費年度 -->
												<TR>
													<TH nowrap class="v_a" width="150px">
														<h:outputText styleClass="outputText" id="lblGhYear"
															value="#{pc_Xrm01001.propGhYear.labelName}"
															style="#{pc_Xrm01001.propGhYear.labelStyle}">
														</h:outputText>
													</TH>
													<TD width="*" colspan=2>
														<h:inputText styleClass="inputText" id="htmlGhYear"
															size="4" value="#{pc_Xrm01001.propGhYear.value}"
															style="#{pc_Xrm01001.propGhYear.style}"
															disabled="#{pc_Xrm01001.propGhYear.disabled}" tabindex="1">
															<hx:inputHelperAssist imeMode="inactive"
																errorClass="inputText_Error" promptCharacter="_" />
															<f:convertDateTime pattern="yyyy" />
														</h:inputText>
													</TD>
												</TR>
												
											</TBODY>
										</TABLE>
									</TD>
								</TR>
								<TR>
								
								<TD height="20px"></TD>
																
								</TR>

								<TR>
									<TD>
										<TABLE class="button_bar" width="100%">
											<TBODY>
												<TR>
													<TD width="100%">
														<hx:commandExButton type="submit" value="PDF作成"
															styleClass="commandExButton_out" id="pdfout"
															confirm="#{msg.SY_MSG_0019W}"
															action="#{pc_Xrm01001.doPdfoutAction}" tabindex="12">
														</hx:commandExButton>&nbsp; 
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
				<!--↑CONTENT↑-->
			
			</DIV>
			<!--↑outer↑-->

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

			<h:inputHidden value="#{pc_Xrm01001.propErrorKbn.stringValue}"
				id="htmlHidErrorKbn"></h:inputHidden>

			
		</h:form>
	</hx:scriptCollector>
	</BODY>

	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
