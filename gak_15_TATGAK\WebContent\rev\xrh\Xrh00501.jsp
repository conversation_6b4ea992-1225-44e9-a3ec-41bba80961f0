<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrh00501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
<SCRIPT type="text/javascript">

function openKaisaitiSearchWindow(thisObj, thisEvent) {
	var url="${pageContext.request.contextPath}/faces/rev/xrh/pXrh0101.jsp?retFieldName=form1:htmlSikenticd";
	openModalWindow(url, "pXrh0101", "<%= com.jast.gakuen.rev.xrh.PXrh0101.getWindowOpenOption() %>");
	return true;
}

function doSikentiAjax(thisObj, thisEvent) {
	var servlet = "rev/xrh/XrhMeiSikentiNmAJAX";
	var target = "form1:lblSikentiName";
	var args = new Array();
	args['code'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
}

function loadAction(event){
 doSikentiAjax(document.getElementById('form1:htmlSikenticd'), event);
}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY OnLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00501.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">

				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
						<TBODY>
				           <TR>
				              <TH nowrap class="v_a" width="190">
				              		<!--試験地コード -->
				                	<h:outputText styleClass="outputText" id="lblSikentiCd"
				                		value="#{pc_Xrh00501.propSikentiCd.labelName}"
				                		style="#{pc_Xrh00501.propSikentiCd.labelStyle}">
				                	</h:outputText>
				              </TH>
				              <TD>
				              		<h:inputText styleClass="inputText"
				                		id="htmlSikenticd" size="8"
				                		value="#{pc_Xrh00501.propSikentiCd.stringValue}"
				                		readonly="#{pc_Xrh00501.propSikentiCd.readonly}"
				                		style="#{pc_Xrh00501.propSikentiCd.style}" tabindex="1"
				                		maxlength="#{pc_Xrh00501.propSikentiCd.maxLength}"
				                		disabled="#{pc_Xrh00501.propSikentiCd.disabled}"
										onblur="return doSikentiAjax(this, event);">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="disabled"/>
				                	</h:inputText>
				                	<hx:commandExButton type="button"
										styleClass="commandExButton_search" id="search"
										disabled="#{pc_Xrh00501.propSearch.disabled}"
										style="#{pc_Xrh00501.propSearch.style}" 
										tabindex="2" 
										onclick="return openKaisaitiSearchWindow(this, event);">
				                	</hx:commandExButton>
									<h:outputText styleClass="outputText"
										id="lblSikentiName"
										value="#{pc_Xrh00501.propSikentiName.labelName}" 
										style="#{pc_Xrh00501.propSikentiName.labelStyle}">
									</h:outputText>
								</TD>
								<TD width="100" align="right"
									style="background-color: transparent; text-align: right"
									class="clear_border">
									<hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select"
										disabled="#{pc_Xrh00501.propSelect.disabled}"
										rendered="#{pc_Xrh00501.propSelect.rendered}"
										style="#{pc_Xrh00501.propSelect.style}"
										action="#{pc_Xrh00501.doSelectAction}" tabindex="3">
									</hx:commandExButton> 
									<hx:commandExButton type="submit" value="解除"
										styleClass="commandExButton" id="release"
										disabled="#{pc_Xrh00501.propRelease.disabled}"
										rendered="#{pc_Xrh00501.propRelease.rendered}"
										style="#{pc_Xrh00501.propRelease.style}"
										action="#{pc_Xrh00501.doReleaseAction}" tabindex="4">
									</hx:commandExButton>
						  	   </TD>	
							</TR>
              			</TBODY>
              	</TABLE>		

				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
							<TD>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="sinki"
									disabled="#{pc_Xrh00501.propSinki.disabled}"
									value="新規登録"
									action="#{pc_Xrh00501.doEntryAction}"
									tabindex="5">
								</hx:commandExButton>
							</TD>
						</TR>
					</TBODY>
				</TABLE>								
					
				<TABLE border="0" cellpadding="5">
					<TBODY>
						<TR>
							<TD width="700" align="right">
								<h:outputText styleClass="outputText"
									id="lblListCount"
									value="#{pc_Xrh00501.propListCount.stringValue}"
									style="#{pc_Xrh00501.propListCount.style}">
								</h:outputText>
							</TD>
						</TR>
							
						<TR>
						<!-- ↓データテーブル部↓ -->
						<TD>
							<DIV id="listScroll" class="listScroll" style="height: 330px;">
							<h:dataTable
								columnClasses="columnClass" headerClass="headerClass"
								footerClass="footerClass"
								rowClasses="#{pc_Xrh00501.propKaijyoList.rowClasses}"
								styleClass="meisai_scroll" id="htmlKaijyoList"
								value="#{pc_Xrh00501.propKaijyoList.list}" var="varlist">
								
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText styleClass="outputText" 
											value="会場コード"
											id="lblListCodeColumn">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" 
										id="htmlListCode"
										value="#{varlist.kaijyoCd.stringValue}">
									</h:outputText>
									<f:attribute value="138" name="width" />
								</h:column>
											
								<h:column id="column2">
									<f:facet name="header">
										<h:outputText styleClass="outputText"	
											value="会場名"
											id="lblListNameColumn">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" 
										value="#{varlist.kaijyoName.displayValue}"
										title="#{varlist.kaijyoName.stringValue}"
										id="htmlListName"
										style="width: auto">
									</h:outputText>
									<f:attribute value="512" name="width" />
								</h:column>
								
								<h:column id="column3">
									<f:facet name="header">
									</f:facet>
									<hx:commandExButton type="submit"
										value="編集"
										styleClass="commandExButton" 
										id="edit"
										action="#{pc_Xrh00501.doEditAction}"
										tabindex="6">
									</hx:commandExButton>
									<f:attribute value="true" name="nowrap" />
									<f:attribute value="32" name="width" />
								</h:column>
							</h:dataTable>
							</DIV>
						</TD>
						</TR>
										
					</TBODY>
				</TABLE>	
				</DIV>
			</DIV>
			
		</DIV>
		<h:inputHidden
			value="#{pc_Xrh00501.propKaijyoList.scrollPosition}"
			id="scroll"></h:inputHidden>
		</h:form>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
