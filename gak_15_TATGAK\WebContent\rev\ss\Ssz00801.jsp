<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function confirmOk() {

	document.getElementById('form1:htmlConfirmVal').value = "1";

	 indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlConfirmVal').value = "0";
	return false;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz00801.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz00801.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz00801.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz00801.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="25%"></TD>
						<TD align="left" width="50%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso1.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso1" value="#{pc_Ssz00801.propJyugyoinKaiso1.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso1"
										value="#{pc_Ssz00801.propJyugyoinKaiso1.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso1.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso1.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text21" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso2.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso2" value="#{pc_Ssz00801.propJyugyoinKaiso2.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso2"
										value="#{pc_Ssz00801.propJyugyoinKaiso2.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso2.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso2.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text22" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso3.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso3" value="#{pc_Ssz00801.propJyugyoinKaiso3.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso3"
										value="#{pc_Ssz00801.propJyugyoinKaiso3.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso3.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso3.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text23" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso4.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso4" value="#{pc_Ssz00801.propJyugyoinKaiso4.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso4"
										value="#{pc_Ssz00801.propJyugyoinKaiso4.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso4.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso4.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text24" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_e" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso5.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso5" value="#{pc_Ssz00801.propJyugyoinKaiso5.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso5"
										value="#{pc_Ssz00801.propJyugyoinKaiso5.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso5.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso5.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text25" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_f" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso6.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso6" value="#{pc_Ssz00801.propJyugyoinKaiso6.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso6"
										value="#{pc_Ssz00801.propJyugyoinKaiso6.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso6.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso6.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text26" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso7.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso7" value="#{pc_Ssz00801.propJyugyoinKaiso7.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso7"
										value="#{pc_Ssz00801.propJyugyoinKaiso7.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso7.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso7.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text27" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso8.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso8" value="#{pc_Ssz00801.propJyugyoinKaiso8.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso8"
										value="#{pc_Ssz00801.propJyugyoinKaiso8.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso8.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso8.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text28" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso9.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso9" value="#{pc_Ssz00801.propJyugyoinKaiso9.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso9"
										value="#{pc_Ssz00801.propJyugyoinKaiso9.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso9.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso9.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text29" value="人未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="168"><h:outputText
										style="#{pc_Ssz00801.propJyugyoinKaiso10.labelStyle}"styleClass="outputText" id="lblJyugyoinKaiso10" value="#{pc_Ssz00801.propJyugyoinKaiso10.labelName}"></h:outputText></TH>
									<TD width="280"><h:inputText styleClass="inputText"
										id="htmlJyugyoinKaiso10"
										value="#{pc_Ssz00801.propJyugyoinKaiso10.stringValue}"
										maxlength="#{pc_Ssz00801.propJyugyoinKaiso10.maxLength}"
										style="#{pc_Ssz00801.propJyugyoinKaiso10.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text30" value="人未満"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="260"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Ssz00801.doRegisterAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ssz00801.propConfirmVal.stringValue}" id="htmlConfirmVal"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

