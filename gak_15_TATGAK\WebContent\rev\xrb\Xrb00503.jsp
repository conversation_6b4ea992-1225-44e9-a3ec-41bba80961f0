<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00503.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00503.jsp</TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	

<SCRIPT type="text/javascript">
function openKamokuSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;

	var reOpenSubWinFlg = "0";
	var ajaxServlet = "rev/xrx/XrxRemoveFromSessionAJAX";
	var args = new Array();
		args['pcClass'] = 'com.jast.gakuen.rev.km.PKmz0101';
		args['motoFuncId'] = '';
	var engine      = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) {
			var windowPointer = openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
			if(reOpenSubWinFlg=="0"){
				focus();
			}
		}
	);
	engine.send(ajaxServlet,null,args);
	return false;
}

function openSikakSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0201.jsp"
				+ "?retFieldName=" + field1;
	openModalWindow(url, "PKmz0201", "<%=com.jast.gakuen.rev.km.PKmz0201.getWindowOpenOption() %>");
	return false;
}

function doKamokuInfoAjax(thisObj, thisEvent) {
	var servlet = "rev/xrb/XrbRishuInfoAJAX";
    var args = new Array();
    args['kanriNo'] = document.getElementById("form1:htmlKanriNoHidden").value;
    args['kamokuCode'] = thisObj.value;
    args['henkoKbn'] = document.getElementById("form1:htmlHenkoKbn").value;
    args['rishuHoho'] = document.getElementById("form1:htmlRishuHoho").value;
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, "", args, "kamokuInfoAjaxCallBack");
}

function kamokuInfoAjaxCallBack(value) {
	if (value['kamokuName'] != "null" && value['kamokuName']) {
		document.getElementById("form1:htmlKamokuName").value = value['kamokuName'];
		document.getElementById("form1:htmlKamokuNameHidden").value = value['kamokuName'];
	} else {
		document.getElementById("form1:htmlKamokuName").value = "";
		document.getElementById("form1:htmlKamokuNameHidden").value = "";
	}
	if (value['taniSuDisp'] != "null" && value['taniSuDisp']) {
		document.getElementById("form1:htmlTaniSu").value = value['taniSuDisp'];
	} else {
		document.getElementById("form1:htmlTaniSu").value = "";
	}
	if (value['textTaniSu'] != "null" && value['textTaniSu']) {
		document.getElementById("form1:htmlTextTaniSuHidden").value = value['textTaniSu'];
	} else {
		document.getElementById("form1:htmlTextTaniSuHidden").value = "";
	}
	if (value['schoolingTaniSu'] != "null" && value['schoolingTaniSu']) {
		document.getElementById("form1:htmlSchoolingTaniSuHidden").value = value['schoolingTaniSu'];
	} else {
		document.getElementById("form1:htmlSchoolingTaniSuHidden").value = "";
	}
	if (value['utiwakeKingaku'] != "null" && value['utiwakeKingaku']) {
		document.getElementById("form1:htmlUtiwakeKingaku").value = value['utiwakeKingaku'];
	} else {
		document.getElementById("form1:htmlUtiwakeKingaku").value = "";
	}
}

function doSikakInfoAjax(thisObj, thisEvent, elementId1, elementId2) {
	var servlet = "rev/km/KmzSkkAJAX";
	var args = new Array();
	args['code'] = thisObj.value;
	var target = elementId1;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
	target = elementId2;
	ajaxUtil.getCodeName(servlet, target, args);
}

function confirmOk() {
	document.getElementById('form1:htmlWarningDialogFlag').value = 1;
	if (document.getElementById('form1:htmlWarningDialogType').value == 0) {
		indirectClick('htmlRegisterButton');
	} else if (document.getElementById('form1:htmlWarningDialogType').value == 1) {
		indirectClick('htmlKakuteiButton');
	} else if (document.getElementById('form1:htmlWarningDialogType').value == 2) {
		indirectClick('htmlDeleteButton');
	} else {
		indirectClick('htmlHenkoKbnSelectButton');
	}
}

function confirmCancel() {
	document.getElementById('form1:htmlWarningDialogFlag').value = 0;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00503.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
styleClass="commandExButton" id="closeDisp"
action="#{pc_Xrb00503.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00503.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00503.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
 <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
  <TBODY>
   <TR>
    <TD>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD align="right">
         <hx:commandExButton
          type="submit"
          value="戻る"
          styleClass="commandExButton"
          style="width: 80px"
          action="#{pc_Xrb00503.doReturnAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 学籍番号 -->
         <h:outputText
          styleClass="outputText"
          id="lblGakusekiCode"
          value="#{pc_Xrb00503.propGakusekiCode.labelName}"
          style="#{pc_Xrb00503.propGakusekiCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="1" style="border-right:none;">
         <h:inputText
          styleClass="inputText"
          id="htmlGakusekiCode" size="10"
          maxlength="#{pc_Xrb00503.propGakusekiCode.maxLength}"
          disabled="#{pc_Xrb00503.propGakusekiCode.disabled}"
          value="#{pc_Xrb00503.propGakusekiCode.stringValue}"
          style="#{pc_Xrb00503.propGakusekiCode.style}"
          readonly="#{pc_Xrb00503.propGakusekiCode.readonly}"
          onblur="return doGakuseiAjax(this, event)">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlGakusekiCdSearchButton"
          disabled="#{pc_Xrb00503.propGakusekiCode.disabled}"
          onclick="openSubWindow('form1:htmlGakusekiCode');">
         </hx:commandExButton>
        <h:inputText
          styleClass="likeOutput"
          id="htmlSearchName"
          size="40"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00503.propSearchName.stringValue}"/>
        </TD>
        <TD width="90" style="text-align: right;padding-right: 3px;border-left:none;">
         <hx:commandExButton
          type="submit"
          value="選択"
          styleClass="commandExButton"
          id="htmlGakuseiSelectButton"
          style="width: 40px"
          action="#{pc_Xrb00503.doGakuseiSelectAction}"
          disabled="#{pc_Xrb00503.propGakuseiSelectButton.disabled}"
          onclick="showInfoName(this)">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="解除"
          styleClass="commandExButton"
          id="htmlGakuseiCancelButton"
          style="width: 40px"
          action="#{pc_Xrb00503.doGakuseiCancelAction}"
          disabled="#{pc_Xrb00503.propGakuseiCancelButton.disabled}">
         </hx:commandExButton>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 申請NO -->
         <h:outputText styleClass="outputText" id="lblSinseiNoTitle"
          value="#{pc_Xrb00503.propSinseiNo.labelName}"
          style="#{pc_Xrb00503.propSinseiNo.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:outputText styleClass="outputText" id="lblSinseiNo"
          style="#{pc_Xrb00503.propSinseiNo.labelStyle}"
          value="#{pc_Xrb00503.propSinseiNo.stringValue}">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 変更区分 -->
         <h:outputText styleClass="outputText" id="lblHenkoKbn"
          value="#{pc_Xrb00503.propHenkoKbn.labelName}"
          style="#{pc_Xrb00503.propHenkoKbn.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:selectOneMenu
          id="htmlHenkoKbn"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00503.propHenkoKbn.value}"
          disabled="#{pc_Xrb00503.propHenkoKbn.disabled}"
          style="#{pc_Xrb00503.propHenkoKbn.style}"
          readonly="#{pc_Xrb00503.propHenkoKbn.readonly}">
          <f:selectItems value="#{pc_Xrb00503.propHenkoKbn.list}" />
         </h:selectOneMenu>
         <hx:commandExButton
          type="submit"
          value="選択"
          styleClass="commandExButton"
          id="htmlHenkoKbnSelectButton"
          style="width: 40px"
          disabled="#{pc_Xrb00503.propHenkoKbnSelectButton.disabled}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="解除"
          styleClass="commandExButton"
          id="htmlHenkoKbnCancelButton"
          style="width: 40px"
          action="#{pc_Xrb00503.doHenkoKbnCancelAction}"
          disabled="#{pc_Xrb00503.propHenkoKbnCancelButton.disabled}">
         </hx:commandExButton>
        </TD>
        <TH nowrap class="v_a" width="160">
         <!-- 申請状態 -->
         <h:outputText styleClass="outputText" id="lblSinseiJotai"
          value="#{pc_Xrb00503.propSinseiJotai.labelName}"
          style="#{pc_Xrb00503.propSinseiJotai.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:selectOneMenu
          id="htmlSinseiJotai"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00503.propSinseiJotai.value}"
          disabled="#{pc_Xrb00503.propSinseiJotai.disabled}"
          style="#{pc_Xrb00503.propSinseiJotai.style}"
          readonly="#{pc_Xrb00503.propSinseiJotai.readonly}">
          <f:selectItems value="#{pc_Xrb00503.propSinseiJotai.list}" />
         </h:selectOneMenu>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 申請日 -->
         <h:outputText styleClass="outputText" id="lblSinseiDate" 
          value="#{pc_Xrb00503.propSinseiDate.labelName}"
          style="#{pc_Xrb00503.propSinseiDate.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="inputText"
          id="htmlSinseiDate"
          size="10"
          disabled="#{pc_Xrb00503.propSinseiDate.disabled}"
          value="#{pc_Xrb00503.propSinseiDate.dateValue}"
          style="#{pc_Xrb00503.propSinseiDate.style}"
          readonly="#{pc_Xrb00503.propSinseiDate.readonly}">
          <f:convertDateTime />
          <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
          <hx:inputHelperDatePicker />
         </h:inputText>
        </TD>
        <TH nowrap class="v_a">
         <!-- 振込依頼人コード -->
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCodeTitle" 
          value="#{pc_Xrb00503.propHurikomiIraininCode.labelName}"
          style="#{pc_Xrb00503.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCode" 
          value="#{pc_Xrb00503.propHurikomiIraininCode.stringValue}"
          style="#{pc_Xrb00503.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 申請理由 -->
         <h:outputText styleClass="outputText" id="lblSinseiRiyu" 
          value="#{pc_Xrb00503.propSinseiRiyu.labelName}"
          style="#{pc_Xrb00503.propSinseiRiyu.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSinseiRiyu"
          value="#{pc_Xrb00503.propSinseiRiyu.stringValue}"
          style="#{pc_Xrb00503.propSinseiRiyu.style}"
          disabled="#{pc_Xrb00503.propSinseiRiyu.disabled}"
          cols="85"
          rows="6">
         </h:inputTextarea>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 職員コメント -->
         <h:outputText styleClass="outputText" id="lblSyokuinComment" 
          value="#{pc_Xrb00503.propSyokuinComment.labelName}"
          style="#{pc_Xrb00503.propSyokuinComment.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSyokuinComment"
          value="#{pc_Xrb00503.propSyokuinComment.stringValue}"
          style="#{pc_Xrb00503.propSyokuinComment.style}"
          disabled="#{pc_Xrb00503.propSyokuinComment.disabled}"
          cols="85"
          rows="6">
         </h:inputTextarea>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 副免資格コード -->
         <h:outputText styleClass="outputText" id="lblFukumenSikakCode" 
          value="#{pc_Xrb00503.propFukumenSikakCode.labelName}"
          style="#{pc_Xrb00503.propFukumenSikakCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="inputText"
          id="htmlFukumenSikakCode" size="10"
          maxlength="#{pc_Xrb00503.propFukumenSikakCode.maxLength}"
          disabled="#{pc_Xrb00503.propFukumenSikakCode.disabled}"
          value="#{pc_Xrb00503.propFukumenSikakCode.stringValue}"
          style="#{pc_Xrb00503.propFukumenSikakCode.style}"
          readonly="#{pc_Xrb00503.propFukumenSikakCode.readonly}"
          onblur="return doSikakInfoAjax(this, event, 'form1:htmlSikakName', 'form1:htmlSikakNameHidden');">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlSikakCdSearchButton"
          disabled="#{pc_Xrb00503.propFukumenSikakCode.disabled}"
          onclick="return openSikakSubWindow('form1:htmlFukumenSikakCode');">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="選択"
          styleClass="commandExButton"
          id="htmlSikakSelectButton"
          style="width: 40px"
          action="#{pc_Xrb00503.doSikakSelectAction}"
          disabled="#{pc_Xrb00503.propSikakSelectButton.disabled}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="解除"
          styleClass="commandExButton"
          id="htmlSikakCancelButton"
          style="width: 40px"
          action="#{pc_Xrb00503.doSikakCancelAction}"
          disabled="#{pc_Xrb00503.propSikakCancelButton.disabled}">
         </hx:commandExButton>
         <h:inputText
          styleClass="likeOutput"
          id="htmlSikakName"
          size="80"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00503.propSikakName.stringValue}">
         </h:inputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 合計金額 -->
         <h:outputText styleClass="outputText" id="lblGokeiKingakuTitle" 
          value="#{pc_Xrb00503.propGokeiKingaku.labelName}"
          style="#{pc_Xrb00503.propGokeiKingaku.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblGokeiKingaku" 
          value="#{pc_Xrb00503.propGokeiKingaku.integerValue}"
          style="#{pc_Xrb00503.propGokeiKingaku.labelStyle}">
          <f:convertNumber pattern="#,###,###,###"/>
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="874">
      <TBODY>
       <TR>
        <TD align="right" nowrap class="outputText" width="100%">
         <h:outputText
          styleClass="outputText"
          id="lblSinseiCount"
          value="#{pc_Xrb00503.propSinseiList.listCount}">
         </h:outputText>件
        </TD>
       </TR>
       <TR>
        <TD>
         <div class="listScroll" style="height: 300px">
          <h:dataTable
           border="0"
           cellpadding="0"
           cellspacing="0"
           headerClass="headerClass"
           footerClass="footerClass"
           columnClasses="columnClass1"
           rowClasses="#{pc_Xrb00503.propSinseiList.rowClasses}"
           styleClass="meisai_scroll"
           id="htmlSisneiList"
           var="varlist"
           value="#{pc_Xrb00503.propSinseiList.list}">
           <h:column id="column1">
            <f:facet name="header">
             <h:outputText id="lblKamokuCode_head"
              styleClass="outputText" value="科目コード">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblKamokuCode_list" value="#{varlist.propKamokuCode.stringValue}">
            </h:outputText>
            <f:attribute value="90" name="width" />
           </h:column>
           <h:column id="column2">
            <f:facet name="header">
             <h:outputText id="lblKamokuName_head"
              styleClass="outputText" value="科目名">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblKamokuName_list" value="#{varlist.propKamokuName.stringValue}">
            </h:outputText>
            <f:attribute value="280" name="width" />
           </h:column>
           <h:column id="column3">
            <f:facet name="header">
             <h:outputText id="lblRishuNenji_head"
              styleClass="outputText" value="履修年次">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblRishuNenji_list" value="#{varlist.propRishuNenji.stringValue}">
            </h:outputText>
            <f:attribute value="90" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column4">
            <f:facet name="header">
             <h:outputText id="lblRishuHoho_head"
              styleClass="outputText" value="履修方法">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblRishuHoho_list" value="#{varlist.propRishuHoho.stringValue}">
            </h:outputText>
            <f:attribute value="130" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column5">
            <f:facet name="header">
             <h:outputText id="lblTaniSu_head"
              styleClass="outputText" value="単位数">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblTaniSu_list" value="#{varlist.propTaniSu.doubleValue}">
            </h:outputText>
            <f:attribute value="100" name="width" />
            <f:attribute value="text-align: center" name="style" />
           </h:column>
           <h:column id="column6">
            <f:facet name="header">
             <h:outputText id="lblUtiwakeKingaku_head"
              styleClass="outputText" value="内訳金額">
             </h:outputText>
            </f:facet>
            <h:outputText styleClass="outputText"
             id="lblUtiwakeKingaku_list" value="#{varlist.propUtiwakeKingaku.integerValue}">
             <f:convertNumber pattern="###,###,###"/>
            </h:outputText>
            <f:attribute value="100" name="width" />
           </h:column>
           <h:column id="column7">
            <f:facet name="header">
             <h:outputText
              styleClass="outputText" value="<br>&nbsp;" escape="false">
             </h:outputText>
            </f:facet>
            <hx:commandExButton type="submit" value="選択"
             styleClass="commandExButton" id="htmlListSelectButton"
             rendered="#{varlist.rendered}"
             disabled="#{varlist.propListSelectButton.disabled}"
             style="width: 40px"
             action="#{pc_Xrb00503.doListSelectAction}">
            </hx:commandExButton>
            <hx:commandExButton type="submit" value="削除"
             styleClass="commandExButton" id="htmlListDeleteButton"
             rendered="#{varlist.rendered}"
             disabled="#{varlist.propListDeleteButton.disabled}"
             style="width: 40px"
             action="#{pc_Xrb00503.doListDeletetAction}">
            </hx:commandExButton>
            <f:attribute value="80" name="width" />
           </h:column>
          </h:dataTable>
         </div>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 科目コード -->
         <h:outputText styleClass="outputText" id="lblKamokuCode"
          value="#{pc_Xrb00503.propKamokuCode.labelName}"
          style="#{pc_Xrb00503.propKamokuCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:inputText
          styleClass="inputText"
          id="htmlKamokuCode" size="10"
          maxlength="#{pc_Xrb00503.propKamokuCode.maxLength}"
          disabled="#{pc_Xrb00503.propKamokuCode.disabled}"
          value="#{pc_Xrb00503.propKamokuCode.stringValue}"
          style="#{pc_Xrb00503.propKamokuCode.style}"
          readonly="#{pc_Xrb00503.propKamokuCode.readonly}"
          onblur="return doKamokuInfoAjax(this, event);">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlKamokuCodeSearchButton"
          disabled="#{pc_Xrb00503.propKamokuCode.disabled}"
          onclick="return openKamokuSubWindow('form1:htmlKamokuCode');">
         </hx:commandExButton>
         <h:inputText
          styleClass="likeOutput"
          id="htmlKamokuName"
          size="20"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00503.propKamokuName.stringValue}">
         </h:inputText>
        </TD>
        <TH nowrap class="v_a" width="160">
         <!-- 履修年次 -->
         <h:outputText styleClass="outputText" id="lblRishuNenji"
          value="#{pc_Xrb00503.propRishuNenji.labelName}"
          style="#{pc_Xrb00503.propRishuNenji.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:selectOneMenu
          id="htmlRishuNenji"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00503.propRishuNenji.value}"
          disabled="#{pc_Xrb00503.propRishuNenji.disabled}"
          style="#{pc_Xrb00503.propRishuNenji.style}"
          readonly="#{pc_Xrb00503.propRishuNenji.readonly}">
          <f:selectItems value="#{pc_Xrb00503.propRishuNenji.list}" />
         </h:selectOneMenu>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 履修方法 -->
         <h:outputText styleClass="outputText" id="lblRishuHoho"
          value="#{pc_Xrb00503.propRishuHoho.labelName}"
          style="#{pc_Xrb00503.propRishuHoho.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:selectOneMenu
          id="htmlRishuHoho"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00503.propRishuHoho.value}"
          disabled="#{pc_Xrb00503.propRishuHoho.disabled}"
          style="#{pc_Xrb00503.propRishuHoho.style}"
          readonly="#{pc_Xrb00503.propRishuHoho.readonly}"
          onchange="document.getElementById('form1:htmlKamokuCode').onblur()">
          <f:selectItems value="#{pc_Xrb00503.propRishuHoho.list}" />
         </h:selectOneMenu>
        </TD>
        <TH nowrap class="v_a">
         <!-- 単位数 -->
         <h:outputText styleClass="outputText" id="lblTaniSuTitle"
          value="#{pc_Xrb00503.propTaniSu.labelName}"
          style="#{pc_Xrb00503.propTaniSu.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="likeOutput"
          id="htmlTaniSu"
          size="20"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00503.propTaniSu.stringValue}">
         </h:inputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 内訳金額 -->
         <h:outputText styleClass="outputText" id="lblUtiwakeKingaku" 
          value="#{pc_Xrb00503.propUtiwakeKingaku.labelName}"
          style="#{pc_Xrb00503.propUtiwakeKingaku.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="710">
         <h:inputText
          styleClass="inputText"
          id="htmlUtiwakeKingaku" size="10"
          maxlength="#{pc_Xrb00503.propUtiwakeKingaku.maxLength}"
          disabled="#{pc_Xrb00503.propUtiwakeKingaku.disabled}"
          value="#{pc_Xrb00503.propUtiwakeKingaku.integerValue}"
          style="#{pc_Xrb00503.propUtiwakeKingaku.style}"
          readonly="#{pc_Xrb00503.propUtiwakeKingaku.readonly}">
          <f:convertNumber pattern="###,###,###"/>
         </h:inputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD colspan="2" align="right">
         <hx:commandExButton
          type="submit"
          value="登録"
          styleClass="commandExButton"
          id="htmlRegisterButton"
          style="width: 40px"
          action="#{pc_Xrb00503.doRegisterAction}"
          disabled="#{pc_Xrb00503.propRegisterButton.disabled}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD>
         <hx:commandExButton
          type="submit"
          value="確定"
          styleClass="commandExButton_dat"
          id="htmlKakuteiButton"
          disabled="#{pc_Xrb00503.propKakuteiButton.disabled}"
          confirm="#{msg.SY_MSG_0001W}"
          action="#{pc_Xrb00503.doKakuteiAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="削除"
          styleClass="commandExButton_dat"
          id="htmlDeleteButton"
          disabled="#{pc_Xrb00503.propDeleteButton.disabled}"
          confirm="#{msg.SY_MSG_0004W}"
          action="#{pc_Xrb00503.doDeleteAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="クリア"
          styleClass="commandExButton_dat"
          id="htmlClearButton"
          disabled="#{pc_Xrb00503.propClearButton.disabled}"
          action="#{pc_Xrb00503.doClearAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="CSV作成"
          styleClass="commandExButton_dat"
          id="htmlCsvButton"
          disabled="#{pc_Xrb00503.propCsvButton.disabled}"
          confirm="#{msg.SY_MSG_0020W}"
          action="#{pc_Xrb00503.doCsvAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
    </TD>
   </TR>
  </TBODY>
 </TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden
 id="htmlKanriNoHidden"
 value="#{pc_Xrb00503.propKanriNoHidden.longValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlWarningDialogFlag"
 value="#{pc_Xrb00503.propWarningDialogFlag.integerValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlWarningDialogType"
 value="#{pc_Xrb00503.propWarningDialogType.integerValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlKamokuNameHidden"
 value="#{pc_Xrb00503.propKamokuNameHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlTextTaniSuHidden"
 value="#{pc_Xrb00503.propTextTaniSuHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlSchoolingTaniSuHidden"
 value="#{pc_Xrb00503.propSchoolingTaniSuHidden.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlSikakNameHidden"
 value="#{pc_Xrb00503.propSikakNameHidden.stringValue}">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

