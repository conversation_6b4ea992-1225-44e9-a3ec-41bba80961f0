<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00402.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

	function confirmOk() {
		var prcMode;

		document.getElementById('form1:htmlButtonKind').value = 1;
		prcMode = parseInt(document.getElementById('form1:htmlProcMode').value);
		
		var barcodeRegisterFlg = document.getElementById('form1:htmlBarcodeRegisterFlg').value;
		if(barcodeRegisterFlg == 1){
			indirectClick('barcodeRegister');
			return;
		}
		
		if( prcMode == 1 ) {
			indirectClick('reg');
		}
		else{
			indirectClick('kousin');
		}
	}

	function confirmCancel() {
		var prcMode;

		document.getElementById('form1:htmlButtonKind').value = 9;
		prcMode = parseInt(document.getElementById('form1:htmlProcMode').value);

		var barcodeRegisterFlg = document.getElementById('form1:htmlBarcodeRegisterFlg').value;
		if(barcodeRegisterFlg == 1){
			indirectClick('barcodeRegister');
			return;
		}
		
		if( prcMode == 1 ) {
			indirectClick('reg');
		}
		else{
			indirectClick('kousin');
		}
	}

	 function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}

	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
	// 科目名称を取得する
		var servlet = "rev/km/KmzKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	// 学生検索画面（引数：①学籍番号）
	function openSubWindow(field1) {
	  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
	    + "?retFieldName=" + field1;

	  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	  return false;
	}

	// 学生氏名を取得する
	function doGakuseiAjax(thisObj, thisEven, targetLabel) {
	  var servlet = "rev/co/CobGakseiAJAX";
	  var args = new Array();
	  args['code1'] = thisObj.value;

	  var ajaxUtil = new AjaxUtil();
	  ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	
	// 戻るボタン押下時処理
	function onClickReturnDisp(id) {
		return true;
	}

	//初期表示時イベント
	function loadAction(event) {
	
		//画面ロード時の学生名称・科目名称の再取得
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiNo'), event, 'form1:lblName');
		doKamokuAjax(document.getElementById('form1:htmlKamokuCd'), event, 'form1:lblKamokuName');
		
		funcTorokuClick();
	}

	function funcTorokuClick() {
		var prcMode;
		document.getElementById('form1:htmlButtonKind').value = 0;
		prcMode = parseInt(document.getElementById('form1:htmlProcMode').value);
	// レポート登録ラジオボタンの処理
		var radioList = document.getElementsByName('form1:htmlReportTouroku');
		
		for(var i=0; i<radioList.length; i++){
			if (radioList[i].checked) {
				val = radioList[i].value
				break;
			}
		}
		
	if( prcMode == 1 ) {
			if( val == "1" ) {
				document.getElementById('form1:htmlMukouriyu').disabled = false;
				document.getElementById('form1:htmlHenkyakubi').disabled = false;
				//表示を退避から戻す
				document.getElementById('form1:htmlHenkyakubi').value = document.getElementById('form1:htmlBackDate').value;
			} 
			else {
				document.getElementById('form1:htmlMukouriyu').disabled = true;
				document.getElementById('form1:htmlHenkyakubi').disabled = true;
				//表示を退避する
				document.getElementById('form1:htmlBackDate').value = document.getElementById('form1:htmlHenkyakubi').value;
					//表示をクリアする
				document.getElementById('form1:htmlHenkyakubi').value = '';
			
			}
			}else{
						if( val == "1" ) {
				document.getElementById('form1:htmlMukouriyu').disabled = false;
				document.getElementById('form1:htmlHenkyakubi').disabled = false;
			} 
			else {
				document.getElementById('form1:htmlMukouriyu').disabled = true;
				document.getElementById('form1:htmlHenkyakubi').disabled = true;
			
			}
			}
		

	}
	
	// バーコード入力：KeyDown処理
	function fncKeyEvt() {
		if ( event.keyCode == 13 ) {
			
			indirectClick('barcodeRegister');
		}
	}	

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00402.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00402.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00402.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00402.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<hx:commandExButton type="submit"
					value="戻　る" styleClass="commandExButton" id="back"
					action="#{pc_Xrf00402.doReturnDispAction}"
					onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
				</hx:commandExButton>
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="600" >
						<TBODY>
							<TR>
								<TH class="v_a" width="150">
									<!--レポート登録 -->
									<h:outputText styleClass="outputText" id="lblReportTouroku"
										value="レポート登録">
									</h:outputText>
								</TH>
								<TD>
									<h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlReportTouroku"
										onclick="funcTorokuClick();"
										value="#{pc_Xrf00402.propRadioReportTouroku.stringValue}"
										disabled="#{pc_Xrf00402.propRadioReportTouroku.disabled}"
										style="#{pc_Xrf00402.propRadioReportTouroku.style}"
										>
										<f:selectItem itemValue="0" itemLabel="受付登録" />
										<f:selectItem itemValue="1" itemLabel="無効登録" />
									</h:selectOneRadio>
								</TD>
								<TD width="50"
									style="background-color: transparent"
									class="clear_border">
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					
					<BR>
					
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="600" >
						<TBODY>
							<TR>
								<TH class="v_a" width="150">
									<!--バーコード読取 -->
									<h:outputText styleClass="outputText" id="lblBarcode"
										value="バーコード読取">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText styleClass="inputText"
									id="htmlBarcode" size="22"
										value="#{pc_Xrf00402.propBarcode.stringValue}"
										readonly="#{pc_Xrf00402.propBarcode.readonly}"
										maxlength="#{pc_Xrf00402.propBarcode.maxLength}"
										style="#{pc_Xrf00402.propBarcode.style}" 
										onkeydown="fncKeyEvt();"
										>
									</h:inputText>
								</TD>
								<TD rowspan="4" width="50"
									style="background-color: transparent; text-align: center"
									class="clear_border">
									<hx:commandExButton type="submit" value="読取"
									styleClass="commandExButton" id="read"
										disabled="#{pc_Xrf00402.propRead.disabled}"
										action="#{pc_Xrf00402.doReadAction}">
									</hx:commandExButton>
									<hx:commandExButton type="submit" value="バーコード登録"
										styleClass="commandExButton" id="barcodeRegister"
										style="display:none;"
										action="#{pc_Xrf00402.doBarcodeRegisterAction}">
									</hx:commandExButton>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_a" width="150">
									<!--学籍番号 -->
									<h:outputText styleClass="outputText" id="lblGaksekiNo"
										style="#{pc_Xrf00402.propGakusekiNo.labelStyle}"
										value="#{pc_Xrf00402.propGakusekiNo.labelName}">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText styleClass="inputText"
										id="htmlGakusekiNo" size="10"
										readonly="#{pc_Xrf00402.propGakusekiNo.readonly}"
										value="#{pc_Xrf00402.propGakusekiNo.stringValue}"
										maxlength="#{pc_Xrf00402.propGakusekiNo.maxLength}"
										onblur="return doGakuseiAjax(this, event, 'form1:lblName');"
										style="#{pc_Xrf00402.propGakusekiNo.style}" 
										>
									</h:inputText>
									<hx:commandExButton type="button"
										styleClass="commandExButton_search" id="searchGakuseiNo"
										onclick="openSubWindow('form1:htmlGakusekiNo');"
										disabled="#{pc_Xrf00402.propGakusekiNo.disabled}"
										>
								 	</hx:commandExButton>
									<h:outputText styleClass="outputText"
										id="lblName"
										value="#{pc_Xrf00402.propName.stringValue}">
									</h:outputText>
								</TD>
							</TR>

							<TR>
								<TH class="v_a" width="150">
									<!--科目コード -->
									<h:outputText styleClass="outputText"
										id="lblKamokuCd"
										style="#{pc_Xrf00402.propKamokuCd.labelStyle}"
										value="#{pc_Xrf00402.propKamokuCd.labelName}">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText id="htmlKamokuCd" styleClass="inputText" 
										size="10"
										value="#{pc_Xrf00402.propKamokuCd.stringValue}"
										readonly="#{pc_Xrf00402.propKamokuCd.readonly}"
										maxlength="#{pc_Xrf00402.propKamokuCd.maxLength}"
										onblur="return doKamokuAjax(this, event, 'form1:lblKamokuName');"
										style="#{pc_Xrf00402.propKamokuCd.style}" 
										>
									</h:inputText>
									<hx:commandExButton type="button"
										styleClass="commandExButton_search" id="searchKamoku"
										onclick="return openKamokuSearchWindow(this, event);">
									</hx:commandExButton>
									<h:outputText
										styleClass="outputText" id="lblKamokuName"
										value="#{pc_Xrf00402.propKamokuName.stringValue}">
									</h:outputText>				
								</TD>
							</TR>

							<TR>
								<TH class="v_a" width="150">
									<!--分冊 -->
									<h:outputText styleClass="outputText"
										id="lblBunsatu"
										value="分冊"
										style="#{pc_Xrf00402.propBunsatu.labelStyle}"
										value="#{pc_Xrf00402.propBunsatu.labelName}">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText id="htmlBunsatu" styleClass="inputText" 
										size="10"
										value="#{pc_Xrf00402.propBunsatu.stringValue}"
										maxlength="#{pc_Xrf00402.propBunsatu.maxLength}"
										style="#{pc_Xrf00402.propBunsatu.style}" 
										>
									</h:inputText>
								</TD>	
							</TR>
							<TR>
								<TH class="v_a" width="150">
									<!--提出区分 -->
									<h:outputText styleClass="outputText"
										id="lblTeisyutuKbn"
										value="提出区分">
									</h:outputText>
								</TH>
								<TD>
									<h:outputText id="htmlTeisyutuKbn" styleClass="outputText" 
										value="#{pc_Xrf00402.propTeisyutuKbn.stringValue}">
									</h:outputText>
								</TD>	
							</TR>
						</TBODY>
					</TABLE>	
									
					<BR>
					
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="600" >
						<TBODY>					
							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblUketukebi"
										value="レポート受付日"
										>
									</h:outputText>
								</TH>
								<TD>
									<h:inputText id="htmlUketukebi"
										styleClass="inputText" size="12"
										value="#{pc_Xrf00402.propUketukebi.dateValue}">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText>
								</TD>
							</TR>
						
							<TR>
								<TH class="v_a" width="150" rowspan="2">
									<h:outputText styleClass="outputText"
										id="lblMukouriyu"
										value="無効理由">
									</h:outputText>
								</TH>
								<TD height="16">
								<h:outputText
									styleClass="outputText" id="lblSysMukou"
									value="#{pc_Xrf00402.propSysMukou.stringValue}" style="color: red">
								</h:outputText>
								</TD>
							</TR>
							
							<TR>
								<TD>
									<h:selectManyListbox
										styleClass="selectManyListbox" id="htmlMukouriyu"
										style="width:300px; height:150px;"
										disabled="#{pc_Xrf00402.propMukouriyu.disabled}"
										value="#{pc_Xrf00402.propMukouriyu.value}">
										<f:selectItems value="#{pc_Xrf00402.propMukouriyu.list}" />
									</h:selectManyListbox>
								</TD>
							</TR>

							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblHenkyakubi"
										value="レポート返却日">
									</h:outputText>
								</TH>
								<TD>
									<h:inputText id="htmlHenkyakubi"
										styleClass="inputText" size="12"
										value="#{pc_Xrf00402.propHenkyakubi.dateValue}">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText>
								</TD>
							</TR>

							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblMinasi"
										value="みなし合格">
									</h:outputText>
								</TH>
								<TD>
									<h:selectBooleanCheckbox 
										styleClass="selectBooleanCheckbox"
										id="checkboxMinasi"
										value="#{pc_Xrf00402.propCheckBoxMinasi.checked}"
										>
									</h:selectBooleanCheckbox>
									<h:outputText
										styleClass="outputText" 
										id="lblminasi" 
										value="みなし合格である">
									</h:outputText>
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					
					<BR>
					
					<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
						<TBODY>
							<TR>
						
								<TD>
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="reg"
										value="登録"
										rendered="#{pc_Xrf00402.propReg.rendered}"
										disabled="#{pc_Xrf00402.propReg.disabled}"
										action="#{pc_Xrf00402.doRegisterAction}"
										confirm="#{msg.SY_MSG_0002W}">
									</hx:commandExButton>
									
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="kousin"
										value="更新"
										rendered="#{pc_Xrf00402.propKousin.rendered}"
										action="#{pc_Xrf00402.doUpdateAction}"
										confirm="#{msg.SY_MSG_0003W}">
									</hx:commandExButton>
							
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="sakujyo"
										value="削除"
										rendered="#{pc_Xrf00402.propSakujyo.rendered}"
										disabled="#{pc_Xrf00402.propSakujyo.disabled}"
										action="#{pc_Xrf00402.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}">
									</hx:commandExButton>
									
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="clear"
										value="クリア"
										action="#{pc_Xrf00402.doClearAction}"
										rendered="#{pc_Xrf00402.propClear.rendered}">
									</hx:commandExButton>
								</TD>
								
							</TR>
						</TBODY>
					</TABLE>
				</DIV>
			</DIV>
			
		</DIV>

			<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrf00402.propBackPhase.integerValue}">
			</h:inputHidden>

			<h:inputHidden
				id="htmlProcMode"
				value="#{pc_Xrf00402.propProcMode.integerValue}">
			</h:inputHidden>

			<h:inputHidden
				id="htmlButtonKind"
				value="#{pc_Xrf00402.propButtonKind.integerValue}">
			</h:inputHidden>
			
			<h:inputHidden
				id="htmlOldBarcode"
				value="#{pc_Xrf00402.propOldBarcode.stringValue}">
			</h:inputHidden>
			
			<h:inputHidden
				id="htmlBarcodeRegisterFlg"
				value="#{pc_Xrf00402.propBarcodeRegisterFlg.integerValue}">
			</h:inputHidden>
			
			<h:inputHidden
				id="htmlSysMukouFlg"
				value="#{pc_Xrf00402.propSysMukouFlg.integerValue}">
			</h:inputHidden>
			
			<h:inputHidden
				id="htmlBackDate"
				value="#{pc_Xrf00402.propBackDate.stringValue}">
			</h:inputHidden>

		</h:form>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
