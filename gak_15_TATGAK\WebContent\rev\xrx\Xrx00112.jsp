<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00112.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<%@ page import="com.jast.gakuen.rev.xrx.Xrx00112"%>
<%@ page import="com.jast.gakuen.rev.xrx.action.bean.Xrx00112L01Bean"%>
<%@ page import="com.jast.gakuen.framework.util.UtilSystem"%>
<%@ page import="java.util.ArrayList"%>

<SCRIPT type="text/javascript" language="JavaScript">
<!--
-->
</SCRIPT>
<!--↓head↓-->
<f:subview id="Xrx00112">
  <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00112.onPageLoadBegin}">
    <%-- ↓ コンテンツ部 ↓ --%> 
    <hx:jspPanel>
      <DIV style="width:870px">

<%
Xrx00112 pc = (Xrx00112)UtilSystem.getManagedBean(Xrx00112.class);
ArrayList list = pc.getKaigoTaikenList();
int count = Integer.parseInt(pc.getPropCount().getStringValue()); 
%>
  
<%
if (count > 0) { 
  for(int i=0; i < list.size(); i++) { 
    Xrx00112L01Bean bean = (Xrx00112L01Bean)list.get(i);
%>
        <TABLE  class="table" width="100%" cellspacing="0" cellpadding="0" border="0">
          <TBODY>
            <TR>
              <!-- 体験年度 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propJissyuNendo.labelName}"
                  style="#{pc_Xrx00112.propJissyuNendo.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getTikenNendo()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 体験先種別 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propTaikenSbt.labelName}"
                  style="#{pc_Xrx00112.propTaikenSbt.labelStyle}"/>
              </TH>
              <TD  colspan="3">
                <span class="outputText"><%=bean.getkmkNama()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 体験開始日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propTaikenStrDate.labelName}"
                  style="#{pc_Xrx00112.propTaikenStrDate.labelStyle}"/>
              </TH>
              <TD width="40%">
                <span class="outputText"><%=bean.getTiknStrDate()%></span>
              </TD>
              <!-- 体験終了日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propTaikenEndDate.labelName}"
                  style="#{pc_Xrx00112.propTaikenEndDate.labelStyle}"/>
               </TH>
              <TD>
                <span class="outputText"><%=bean.getTiknEndDate()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 体験先都道府県 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propTodofuken.labelName}"
                  style="#{pc_Xrx00112.propTodofuken.labelStyle}"/>
               </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getsyussinName()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 取消・辞退日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propTorikeshiDate.labelName}"
                  style="#{pc_Xrx00112.propTorikeshiDate.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.gettorikesiJitaiDate()%></span>
              </TD>
            </TR>
                                  <TR>
              <!-- 日誌添削教員名 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propNisshi.labelName}"
                  style="#{pc_Xrx00112.propNisshi.labelStyle}"/>
              </TH>
              <TD width="40%">
                <span class="outputText"><%=bean.getNissiTnskKyoinCd()%>
                <% String p = bean.getNissiTnskKyoinCd();
                if(p!="") out.print(":"); %> 
                <%=bean.getshimeiName()%></span>
              </TD>
              <!-- 日誌添削依頼日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propNisshiIrai.labelName}"
                  style="#{pc_Xrx00112.propNisshiIrai.labelStyle}"/>
              </TH>
              <TD>
                <span class="outputText"><%=bean.getNissiTnskIraiDate()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 備考1 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propBikou1.labelName}"
                  style="#{pc_Xrx00112.propBikou1.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getSisetuNote1()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 備考2 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00112.propBikou2.labelName}"
                  style="#{pc_Xrx00112.propBikou2.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getSisetuNote2()%></span>
              </TD>
            </TR>
          </TBODY>
        </TABLE>
        <BR>
<% 
  } 
} 
%>
      </DIV>      
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
  </hx:scriptCollector>
</f:subview>
