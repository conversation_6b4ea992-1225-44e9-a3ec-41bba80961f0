<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrh00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

	function doSikentiAjax(thisObj, thisEvent) {
		var servlet = "rev/xrh/XrhMeiSikentiNmAJAX";
		var target = "form1:lblSikentiNm";
		var args = new Array();
		args['code'] = thisObj.value;
	
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, target, args);
	}


	function confirmOk(){
 			document.getElementById("form1:propExecutable").value = 1;
 			indirectClick("csvOut");
 	}
 	
 	function confirmCancel(){
			document.getElementById("form1:propExecutable").value = 2;
 			indirectClick("csvOut");
 	}
 	
	function funcSelKamokInfo() {

		// 出力ＣＳＶ形式の選択処理
		var radioList = document.getElementsByName('form1:htmlOutputCsvStyle');
		
		for(var i = 0; i < radioList.length; i++){
			if (radioList[i].checked) {
				val = radioList[i].value
				break;
			}
		}
		
		// 科目情報データCSVの場合、試験回数を非活性化
		if (val == "6") {
			document.getElementById('form1:htmlShikenCount').disabled = true;
		} else {
			document.getElementById('form1:htmlShikenCount').disabled = false;
		}
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="funcSelKamokInfo()">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00101.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00101.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"></TD>
				</TR>
			</TABLE>
			<!-- ↑ここに戻るボタンを配置 --></DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="500" valign="top">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Xrh00101.propNendo.labelName}"
										style="#{pc_Xrh00101.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD width="350"><h:inputText id="htmlNendo"
										styleClass="inputText"
										readonly="#{pc_Xrh00101.propNendo.readonly}"
										style="#{pc_Xrh00101.propNendo.style}"
										value="#{pc_Xrh00101.propNendo.dateValue}"
										disabled="#{pc_Xrh00101.propNendo.disabled}" size="4"
										tabindex="1">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblShikenCount"
										style="#{pc_Xrh00101.propShikenCount.labelStyle}"
										value="#{pc_Xrh00101.propShikenCount.labelName}">
									</h:outputText></TH>
									<TD width="350"><h:inputText id="htmlShikenCount"
										styleClass="inputText"
										readonly="#{pc_Xrh00101.propShikenCount.readonly}"
										style="#{pc_Xrh00101.propShikenCount.style}"
										value="#{pc_Xrh00101.propShikenCount.integerValue}"
										maxlength="#{pc_Xrh00101.propShikenCount.maxLength}"
										disabled="#{pc_Xrh00101.propShikenCount.disabled}" size="4"
										tabindex="2">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblOutputSeq"
										value="#{pc_Xrh00101.propOutputSeq.labelName}"
										style="#{pc_Xrh00101.propOutputSeq.labelStyle}">
									</h:outputText></TH>
									<TD width="350"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlOutputSeq"
										value="#{pc_Xrh00101.propOutputSeq.stringValue}" tabindex="5"
										style="#{pc_Xrh00101.propOutputSeq.style}">
										<f:selectItem itemValue="1" itemLabel="日程 - 科目コード" />
										<f:selectItem itemValue="2" itemLabel="日程 - 作問教員コード" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblOutputCsvStyle"
										value="#{pc_Xrh00101.propOutputCsvStyle.labelName}"
										style="#{pc_Xrh00101.propOutputCsvStyle.labelStyle}">
									</h:outputText></TH>
									<TD width="350" style="padding-left: 2px"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlOutputCsvStyle"
										layout="pageDirection"
										value="#{pc_Xrh00101.propOutputCsvStyle.stringValue}"
										onclick="funcSelKamokInfo();"
										tabindex="6" style="#{pc_Xrh00101.propOutputCsvStyle.style}">
										<f:selectItem itemValue="1" itemLabel="通常（出力項目指定にて指定した内容）" />
										<f:selectItem itemValue="6" itemLabel="科目情報データCSV（一括登録用）" />
										<f:selectItem itemValue="2" itemLabel="日程 - 時間割データCSV（一括登録用）" />
										<f:selectItem itemValue="3" itemLabel="場所情報データCSV（一括登録用）" />
										<f:selectItem itemValue="4" itemLabel="試験監督情報データCSV（一括登録用）" />
										<f:selectItem itemValue="5" itemLabel="作問情報データCSV（一括登録用）" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellspacing="0" class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD width="" nowrap><hx:commandExButton type="submit"
										value="ＣＳＶ作成" styleClass="commandExButton_dat" id="csvOut"
										tabindex="13"
										action="#{pc_Xrh00101.doCsvOutAction}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="出力項目指定" styleClass="commandExButton_dat" id="setOutput"
										tabindex="14" action="#{pc_Xrh00101.doSetOutputAction}">
									</hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>

		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		<h:inputHidden
				value="#{pc_Xrh00101.propExecutable.integerValue}"
				id="propExecutable">
		</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />

</f:view>

</HTML>
