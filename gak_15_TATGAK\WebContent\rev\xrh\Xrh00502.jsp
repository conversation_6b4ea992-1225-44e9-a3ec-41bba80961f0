<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">
function func_1(thisObj, thisEvent) {
	var zipNo = document.getElementById("form1:htmlPostNo").value;
	zipNo = encodeURIComponent(zipNo);
	var add = document.getElementById("form1:htmlAdd1").value;
	add = encodeURIComponent(add);
	var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp"
		   +"?"
  		   +"zipNo=form1:htmlPostNo"
  		   +"&"
  		   +"zipNoValue="+zipNo
  		   +"&"
  		   +"jyusyoKanji=form1:htmlAdd1";
	openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");
	return true;
}

 	function confirmOk(){
 		if(document.getElementById("form1:propExecutable").value == 1){
 			indirectClick("register");
 		}else if(document.getElementById("form1:propExecutable").value == 2){
 			indirectClick("delete");
 		}else if(document.getElementById("form1:propExecutable").value == 3){
 			indirectClick("clear");
 		}
 	}
 	
 	function confirmCancel(){
 	
 		document.getElementById("form1:propExecutable").value = 0;
 		
 	}
 	
	function onClickReturnDisp(id){
		return true;
	}

	function loadAction(event){
		setZipMenu('form1:htmlPostNo', 'form1:htmlAdd1', '');
	}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onLoad="loadAction(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00502.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00502.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00502.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00502.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<hx:commandExButton
					type="submit" value="戻る"
					styleClass="commandExButton" id="returnDisp"
					disabled="#{pc_Xrh00502.propReturnDisp.disabled}"
					action="#{pc_Xrh00502.doReturnDispAction}"
					onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');" tabindex="23">
				</hx:commandExButton>
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">
				 <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
				 	<TBODY>
				 		<TR>
				 			<TH nowrap class="v_a" width="150">
			              		<!--試験地コード -->
			                	<h:outputText styleClass="outputText"
			                		value="試験地コード">
			                	</h:outputText>
					        </TH>
					        <TD>
					        	<h:outputText styleClass="outputText" id="lblSikentiCd"
					                value="#{pc_Xrh00502.propSikentiCd.stringValue}"
					                style="#{pc_Xrh00502.propSikentiCd.labelStyle}">
					            </h:outputText>
								<f:attribute value="64" name="width" />
					        </TD>
					        <TH nowrap class="v_a" width="150">
			              		<!--試験地名 -->
			                	<h:outputText styleClass="outputText"
			                		value="試験地名">
			                	</h:outputText>
					        </TH>
					        <TD>
					        	<h:outputText styleClass="outputText" id="lblSikentiNm"
					                value="#{pc_Xrh00502.propSikentiNm.stringValue}">
					            </h:outputText>
					        </TD>
				 		</TR>
				 	</TBODY>	
				 </TABLE>

				 <BR>            
				 
				 <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700">
						<TBODY>
					           <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--会場コード -->
					                	<h:outputText styleClass="outputText" id="lblKaijyoCd"
					                		value="#{pc_Xrh00502.propKaijyoCd.labelName}"
					                		style="#{pc_Xrh00502.propKaijyoCd.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlKaijyoCd" size="8"
					                		maxlength="#{pc_Xrh00502.propKaijyoCd.maxLength}"
					                		disabled="#{pc_Xrh00502.propKaijyoCd.disabled}"
					                		readonly="#{pc_Xrh00502.propKaijyoCd.readonly}"
					                		value="#{pc_Xrh00502.propKaijyoCd.stringValue}"
					                		style="#{pc_Xrh00502.propKaijyoCd.style}" tabindex="1">
					                	</h:inputText>
					              </TD>
					            </TR>
					            <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--会場名称 -->
					                	<h:outputText styleClass="outputText" id="lblKaijyoNm"
					                		value="#{pc_Xrh00502.propKaijyoNm.labelName}"
					                		style="#{pc_Xrh00502.propKaijyoNm.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlKaijyoNm" size="40"
					                		maxlength="#{pc_Xrh00502.propKaijyoNm.maxLength}"
					                		readonly="#{pc_Xrh00502.propKaijyoNm.readonly}"
					                		value="#{pc_Xrh00502.propKaijyoNm.stringValue}"
					                		style="#{pc_Xrh00502.propKaijyoNm.style}" tabindex="2"
					                	>
					                	</h:inputText>
					              </TD>
					             </TR>
					            <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--会場名称カナ -->
					                	<h:outputText styleClass="outputText" id="lblKaijyoKa"
					                		value="#{pc_Xrh00502.propKaijyoKa.labelName}"
					                		style="#{pc_Xrh00502.propKaijyoKa.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlKaijyoKa" size="40"
					                		maxlength="#{pc_Xrh00502.propKaijyoKa.maxLength}"
					                		readonly="#{pc_Xrh00502.propKaijyoKa.readonly}"
					                		value="#{pc_Xrh00502.propKaijyoKa.stringValue}"
					                		style="#{pc_Xrh00502.propKaijyoKa.style}" tabindex="3"
					                	>
					                	</h:inputText>
					              </TD>
					             </TR>
					            <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--郵便番号 -->
					                	<h:outputText styleClass="outputText" id="lblPostNo"
					                		value="#{pc_Xrh00502.propPostNo.labelName}"
					                		style="#{pc_Xrh00502.propPostNo.labelStyle}"
					                		>
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlPostNo" size="8"
					                		maxlength="#{pc_Xrh00502.propPostNo.maxLength}"
					                		readonly="#{pc_Xrh00502.propPostNo.readonly}"
					                		value="#{pc_Xrh00502.propPostNo.stringValue}"
					                		style="#{pc_Xrh00502.propPostNo.style}" tabindex="4">
					                	</h:inputText>
					                	<hx:commandExButton type="button"
					                		styleClass="commandExButton_search" id="search"
					                		disabled="#{pc_Xrh00502.propSearch.disabled}"
					                		onclick="return func_1(this,event);" tabindex="5">
				                		</hx:commandExButton>
					              </TD>
					             </TR>
					             <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--住所１ -->
					                	<h:outputText styleClass="outputText" id="lblAdd1"
					                		value="#{pc_Xrh00502.propAdd1.labelName}"
					                		style="#{pc_Xrh00502.propAdd1.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlAdd1" size="40"
					                		maxlength="#{pc_Xrh00502.propAdd1.maxLength}"
					                		readonly="#{pc_Xrh00502.propAdd1.readonly}"
					                		value="#{pc_Xrh00502.propAdd1.stringValue}"
					                		style="#{pc_Xrh00502.propAdd1.style}" tabindex="6"
					                	>
					                	</h:inputText>(都道府県市区町村大字)
					              </TD>
					             </TR>
					             <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--住所２ -->
					                	<h:outputText styleClass="outputText" id="lblAdd2"
					                		value="#{pc_Xrh00502.propAdd2.labelName}"
					                		style="#{pc_Xrh00502.propAdd2.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlAdd2" size="40"
					                		maxlength="#{pc_Xrh00502.propAdd2.maxLength}"
					                		readonly="#{pc_Xrh00502.propAdd2.readonly}"
					                		value="#{pc_Xrh00502.propAdd2.stringValue}"
					                		style="#{pc_Xrh00502.propAdd2.style}" tabindex="7"
					                		>
					                	</h:inputText>(丁目・字以降)
					              </TD>
					             </TR>
					             <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--住所３ -->
					                	<h:outputText styleClass="outputText" id="lblAdd3"
					                		value="#{pc_Xrh00502.propAdd3.labelName}"
					                		style="#{pc_Xrh00502.propAdd3.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlAdd3" size="40"
					                		maxlength="#{pc_Xrh00502.propAdd3.maxLength}"
					                		readonly="#{pc_Xrh00502.propAdd3.readonly}"
					                		value="#{pc_Xrh00502.propAdd3.stringValue}"
					                		style="#{pc_Xrh00502.propAdd3.style}" tabindex="8"
					                		>
					                	</h:inputText>(マンション／ビル名　号室)
					              </TD>
					             </TR>					             
					             <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--電話番号 -->
					                	<h:outputText styleClass="outputText" id="lblTel"
					                		value="#{pc_Xrh00502.propTel.labelName}"
					                		style="#{pc_Xrh00502.propTel.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD>
					              		<h:inputText styleClass="inputText"
					                		id="htmlTel" size="20"
					                		maxlength="#{pc_Xrh00502.propTel.maxLength}"
					                		readonly="#{pc_Xrh00502.propTel.readonly}"
					                		value="#{pc_Xrh00502.propTel.stringValue}"
					                		style="#{pc_Xrh00502.propTel.style}" tabindex="9"
					                		>
					                	</h:inputText>
					              </TD>
					              <TH nowrap class="v_a" width="150">
					              		<!--FAX番号 -->
					                	<h:outputText styleClass="outputText" id="lblFax"
					                		value="#{pc_Xrh00502.propFax.labelName}"
					                		style="#{pc_Xrh00502.propFax.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD>
					              		<h:inputText styleClass="inputText"
					                		id="htmlFax" size="20"
					                		maxlength="#{pc_Xrh00502.propFax.maxLength}"
					                		readonly="#{pc_Xrh00502.propFax.readonly}"
					                		value="#{pc_Xrh00502.propFax.stringValue}"
					                		style="#{pc_Xrh00502.propFax.style}" tabindex="10"
					                	>
					                	</h:inputText>
					              </TD>
					             </TR>
					             <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--最寄駅 -->
					                	<h:outputText styleClass="outputText" id="lblStation"
					                		value="#{pc_Xrh00502.propStation.labelName}"
					                		style="#{pc_Xrh00502.propStation.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:inputText styleClass="inputText"
					                		id="htmlStation" size="20"
					                		maxlength="#{pc_Xrh00502.propStation.maxLength}"
					                		readonly="#{pc_Xrh00502.propStation.readonly}"
					                		value="#{pc_Xrh00502.propStation.stringValue}"
					                		style="#{pc_Xrh00502.propStation.style}" tabindex="11"
					                		>
					                	</h:inputText>
					              </TD>		
				          		 </TR>
				          		 <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--支払方法区分 -->
					                	<h:outputText styleClass="outputText" id="lblPayKbn"
					                		value="#{pc_Xrh00502.propPayKbn.labelName}"
					                		style="#{pc_Xrh00502.propPayKbn.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD>
					              		<h:selectOneMenu styleClass="selectOneMenu" id="htmlPayKbn"
											value="#{pc_Xrh00502.propPayKbn.stringValue}"
											style="#{pc_Xrh00502.propPayKbn.style};width:128px"
											disabled="#{pc_Xrh00502.propPayKbn.disabled}" tabindex="12">
											<f:selectItems value="#{pc_Xrh00502.propPayKbn.list}" />
										</h:selectOneMenu>
					              </TD>
					              <TH nowrap class="v_a" width="150">
					              		<!--申込方法区分 -->
					                	<h:outputText styleClass="outputText" id="lblOfferKbn"
					                		value="#{pc_Xrh00502.propOfferKbn.labelName}"
					                		style="#{pc_Xrh00502.propOfferKbn.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD>
					              		<h:selectOneMenu styleClass="selectOneMenu" id="htmlOfferKbn"
											value="#{pc_Xrh00502.propOfferKbn.stringValue}"
											style="#{pc_Xrh00502.propOfferKbn.style};width:128px"
											disabled="#{pc_Xrh00502.propOfferKbn.disabled}" tabindex="13">
											<f:selectItems value="#{pc_Xrh00502.propOfferKbn.list}" />
										</h:selectOneMenu>
					              </TD>	
				          		 </TR>
				          		 <TR>
					              <TH nowrap class="v_a" width="150">
					              		<!--予約可能前月数 -->
					                	<h:outputText styleClass="outputText" id="lblMonths"
					                		value="#{pc_Xrh00502.propMonths.labelName}"
					                		style="#{pc_Xrh00502.propMonths.labelStyle}">
					                	</h:outputText>
					              </TH>
					              <TD colspan="3">
					              		<h:selectOneMenu styleClass="selectOneMenu" id="htmlMonths"
											value="#{pc_Xrh00502.propMonths.stringValue}"
											style="#{pc_Xrh00502.propMonths.style};width:128px"
											disabled="#{pc_Xrh00502.propMonths.disabled}" tabindex="14">
											<f:selectItems value="#{pc_Xrh00502.propMonths.list}" />
										</h:selectOneMenu>
					              </TD>
					             </TR>
					             <TR>
					             	<TH nowrap class="v_a" width="150">
										<!--担当者名 -->
										<h:outputText styleClass="outputText" id="lblTantoNm"
											value="#{pc_Xrh00502.propTantoNm.labelName}"
											style="#{pc_Xrh00502.propTantoNm.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="3">
										<h:inputText styleClass="inputText"
											id="htmlTantoNm" size="40"
					                		maxlength="#{pc_Xrh00502.propTantoNm.maxLength}"
											readonly="#{pc_Xrh00502.propTantoNm.readonly}"
											value="#{pc_Xrh00502.propTantoNm.stringValue}"
											style="#{pc_Xrh00502.propTantoNm.style}" tabindex="15"
											>
										</h:inputText>
									</TD>
								  </TR>
								  <TR>
								  	<TH nowrap class="v_a" width="150">
										<!--担当者メモ -->
										<h:outputText styleClass="outputText" id="lblTantoMm"
											value="#{pc_Xrh00502.propTantoMm.labelName}"
											style="#{pc_Xrh00502.propTantoMm.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="3">
										<h:inputText styleClass="inputText"
											id="htmlTantoMm" size="80"
					                		maxlength="#{pc_Xrh00502.propTantoMm.maxLength}"
											readonly="#{pc_Xrh00502.propTantoMm.readonly}"
											value="#{pc_Xrh00502.propTantoMm.stringValue}"
											style="#{pc_Xrh00502.propTantoMm.style}" tabindex="16"
										>
										</h:inputText>
									</TD>
								   </TR>
								  <TR>
								  	<TH nowrap class="v_a" width="150">
										<!--URL -->
										<h:outputText styleClass="outputText" id="lblURL"
											value="#{pc_Xrh00502.propURL.labelName}"
											style="#{pc_Xrh00502.propURL.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="3">
										<h:inputText styleClass="inputText"
											id="htmlURL" size="80"
					                		maxlength="#{pc_Xrh00502.propURL.maxLength}"
											readonly="#{pc_Xrh00502.propURL.readonly}"
											value="#{pc_Xrh00502.propURL.stringValue}"
											style="#{pc_Xrh00502.propURL.style}" tabindex="17"
										>
										</h:inputText>
									</TD>
								  </TR>
								  <TR>
								    <TH nowrap class="v_a" width="150">
										<!--メモ -->
										<h:outputText styleClass="outputText" id="lblMemo"
											value="#{pc_Xrh00502.propMemo.labelName}"
											style="#{pc_Xrh00502.propMemo.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="3">
										<h:inputTextarea styleClass="inputTextarea"
											id="htmlMemo" cols="60" rows="4"
											readonly="#{pc_Xrh00502.propMemo.readonly}"
											value="#{pc_Xrh00502.propMemo.stringValue}"
											style="#{pc_Xrh00502.propMemo.style}" tabindex="18"
											>
										</h:inputTextarea>
									</TD>
								   </TR>
								   <TR>
								   	 <TH nowrap class="v_a" width="150">
										<!--注意事項 -->
										<h:outputText styleClass="outputText" id="lblNotice"
											value="#{pc_Xrh00502.propNotice.labelName}"
											style="#{pc_Xrh00502.propNotice.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="3">
										<h:inputTextarea styleClass="inputTextare"
											id="htmlNotice" cols="60" rows="4"
											readonly="#{pc_Xrh00502.propNotice.readonly}"
											value="#{pc_Xrh00502.propNotice.stringValue}"
											style="#{pc_Xrh00502.propNotice.style}" tabindex="19"
											>
										</h:inputTextarea>
									</TD>
								   </TR>
				         </TBODY>
				 </TABLE> 
				 
				 <BR>
				 
				 <TABLE  border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
							<TD>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="register"
									value="#{pc_Xrh00502.propRegister.labelName}"
									disabled="pc_Xrh00502.propRegister.disabled"
									action="#{pc_Xrh00502.doRegisterAction}"
									tabindex="20">
								</hx:commandExButton>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="delete"
									value="削除"
									disabled="pc_Xrh00502.propDelete.disabled"
									rendered="#{pc_Xrh00502.propDelete.rendered}"
									action="#{pc_Xrh00502.doDeleteAction}"
									tabindex="21">
								</hx:commandExButton>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="clear"
									value="クリア"
									disabled="pc_Xrh00502.propClear.disabled"
									action="#{pc_Xrh00502.doClearAction}"
									tabindex="22">
								</hx:commandExButton>
							</TD>	
						</TR>
					</TBODY>
				</TABLE>
				</DIV>
			</DIV>
			
		</DIV>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		
			<h:inputHidden
				value="#{pc_Xrh00502.propExecutable.integerValue}"
				id="propExecutable">
		</h:inputHidden>
		
		</h:form>
		
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
