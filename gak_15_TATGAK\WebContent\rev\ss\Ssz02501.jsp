<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz02501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz02501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz02501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz02501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz02501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz02501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" width="56%" class="">
				<TBODY>
					<TR>
						<TH
							width="100" class=""><h:outputText styleClass="outputText"
							id="text1" value="大分類（上" style="font-size: 8pt"></h:outputText></TH>
						<TH width="6"><h:inputText styleClass="inputText_dis"
							id="htmlDibnrui" size="1" maxlength="1"
							value="#{pc_Ssz02501.propDyibunruyi.stringValue}"
							style="font-size: 8pt; width: 12px" readonly="true"></h:inputText></TH>
						<TH width="32"><h:outputText styleClass="outputText" id="text2"
							style="font-size: 8pt" value="桁）"></h:outputText></TH>
						<TH width="128"></TH>
						<TH width="100"><h:outputText styleClass="outputText" id="text3"
							value="中分類（上" style="font-size: 8pt"></h:outputText></TH>
						<TH width="6"><h:inputText styleClass="inputText_dis"
							id="htmlTyubunruyi" size="1" maxlength="1"
							style="font-size: 8pt; width: 12px" readonly="true"
							value="#{pc_Ssz02501.propTyubunruyi.stringValue}"></h:inputText></TH>
						<TH width="32"><h:outputText styleClass="outputText" id="text4"
							style="font-size: 8pt" value="桁）"></h:outputText></TH>
						<TH width="128"></TH>
						<TH width="100"><h:outputText styleClass="outputText" id="text5"
							value="小分類（上" style="font-size: 8pt"></h:outputText></TH>
						<TH width="6"><h:inputText styleClass="inputText_dis"
							id="htmlSybnr" size="1" maxlength="1" disabled="true"
							style="font-size: 8pt; width: 12px"
							value="#{pc_Ssz02501.propSyobunruyi.stringValue}"></h:inputText></TH>
						<TH width="32"><h:outputText styleClass="outputText"
							style="font-size: 8pt" value="桁）" id="text7"></h:outputText></TH>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD></TD>
						<TD width="900"></TD>
						<TD width="410"><h:outputText styleClass="outputText" id="text10"
							style="font-size: 8pt"
							value="#{pc_Ssz02501.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text11" value="件"
							style="font-size: 8pt"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="14%"></TD>
						<TD width="56%" align="center">
						<div class="listScroll" style="height:296px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz02501.propJobList.rowClasses}"
							styleClass="meisai_scroll" id="table1" width="650"
							value="#{pc_Ssz02501.propJobList.list}" var="varlist"
							 style="listScroll">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text6" styleClass="outputText" value="コード"></h:outputText>
								</f:facet>
								<f:attribute value="80" name="width" />
								<h:outputText styleClass="outputText" id="htmlCode"
									value="#{varlist.sykusuCD}"></h:outputText>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="区分" id="text8"></h:outputText>
								</f:facet>
								<f:attribute value="80" name="width" />
								<h:outputText styleClass="outputText" id="htmlKubn"
									value="#{varlist.sukusuDif}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text9"></h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn3" 
											  value="#{varlist.sykusuName.displayValue}" 
											  title="#{varlist.sykusuName.value}"
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="245" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" 
												  value="職種分類" 
												  id="lblColumn4">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlColumn4" 
											  value="#{varlist.syoksyuBunruiName.displayValue}" 
											  title="#{varlist.syoksyuBunruiName.value}"
											  styleClass="outputText" 
											  style="white-space:nowrap;">
								</h:outputText>
								<f:attribute value="245" name="width" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="20" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz02501.doSelectAction}"></hx:commandExButton>
								<f:attribute value="24" name="width" />
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD width="14%"></TD>  
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="table" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TH class="v_a" width="147"><h:outputText
							styleClass="outputText" id="lblSyokusuCd"
							value="#{pc_Ssz02501.propSyokuyuCd.labelName}"
							style="#{pc_Ssz02501.propSyokuyuCd.labelStyle}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText"
							id="htmlSyokusuCD" size="5"
							value="#{pc_Ssz02501.propSyokuyuCd.stringValue}"
							style="#{pc_Ssz02501.propSyokuyuCd.style}"
							maxlength="#{pc_Ssz02501.propSyokuyuCd.maxLength}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="147"><h:outputText
							styleClass="outputText" id="lblSyokuyuName"
							value="#{pc_Ssz02501.propSyokuyuName.labelName}"
							style="#{pc_Ssz02501.propSyokuyuName.labelStyle}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText"
							id="htmlSyokuyuName" size="40"
							value="#{pc_Ssz02501.propSyokuyuName.stringValue}"
							maxlength="#{pc_Ssz02501.propSyokuyuName.maxLength}"
							style="#{pc_Ssz02501.propSyokuyuName.style}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="147"><h:outputText
							styleClass="outputText" id="lblSyokuyuknName"
							value="#{pc_Ssz02501.propSyokuyuknName.labelName}"
							style="#{pc_Ssz02501.propSyokuyuknName.labelStyle}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText"
							id="htmlSyokuyuknName" size="40"
							value="#{pc_Ssz02501.propSyokuyuknName.stringValue}"
							maxlength="#{pc_Ssz02501.propSyokuyuknName.maxLength}"
							style="#{pc_Ssz02501.propSyokuyuknName.style}">
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_d" width="147"><h:outputText styleClass="outputText"
							id="lblSyokuyunkyName"
							value="#{pc_Ssz02501.propSyokuyunkyName.labelName}"
							style="#{pc_Ssz02501.propSyokuyunkyName.labelStyle}"></h:outputText></TH>
						<TD width="368"><h:inputText styleClass="inputText" id="htmlSyokuyunkyName"
							size="20"
							value="#{pc_Ssz02501.propSyokuyunkyName.stringValue}"
							style="#{pc_Ssz02501.propSyokuyunkyName.style}"
							maxlength="#{pc_Ssz02501.propSyokuyunkyName.maxLength}">
						</h:inputText></TD>
					</TR>
					<!-- 職種分類 -->
					<TR>
						<TH style="" class="v_e" width="147">
							<h:outputText styleClass="outputText" 
										  id="lblShokumeiBunruiCd" 
										  style="#{pc_Ssz02501.propSyoksyuBunruiCd.style}" 
										  value="#{pc_Ssz02501.propSyoksyuBunruiCd.labelName}">
							</h:outputText>
						</TH>
						<TD width="368">
							<h:selectOneMenu
										styleClass="selectOneMenu" id="htmlShokumeiBunruiCd"
										value="#{pc_Ssz02501.propSyoksyuBunruiCd.value}"
										style="#{pc_Ssz02501.propSyoksyuBunruiCd.style}">
								<f:selectItems value="#{pc_Ssz02501.propSyoksyuBunruiCd.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
					<!-- 職種分類一括更新 -->
					<TR>
						<TH style="" class="v_f" width="147"><h:outputText
							styleClass="outputText" id="lblSyoksyuBatchUpdateFlg"
							value="#{pc_Ssz02501.propSyoksyuBatchUpdateFlg.labelName}"></h:outputText></TH>
						<TD width="520">
							<h:selectOneRadio
								disabledClass="selectOneRadio_Disabled"
								styleClass="selectOneRadio" id="htmlSyoksyuBatchUpdateFlg"
								value="#{pc_Ssz02501.propSyoksyuBatchUpdateFlg.value}">
								<f:selectItem itemValue="0" itemLabel="下位分類を更新する" />
								<f:selectItem itemValue="1" itemLabel="下位分類は更新しない" />
						</h:selectOneRadio></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">

				<TBODY>
					<TR>
						<TD align="center" width="100%"><hx:commandExButton
							type="submit" value="確定" styleClass="commandExButton_dat"
							id="register" action="#{pc_Ssz02501.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Ssz02501.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz02501.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz02501.propJobList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition("scroll","listScroll")
</SCRIPT>
</HTML>

