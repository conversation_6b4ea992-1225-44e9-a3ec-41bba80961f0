<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz03701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz03701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
uncheck('htmlJokenList','htmlCheckJoken');
}
function func_2(thisObj, thisEvent) {
check('htmlJokenList','htmlCheckJoken');
}
function confirmOk() {
	document.getElementById('form1:htmlExecutableGyos').value = "1";
	indirectClick('delete');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutableGyos').value = "0";
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz03701.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz03701.doCloseDispAction}"
></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz03701.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz03701.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0" width="700">
				<TBODY>
					<TR>

						<TD align="right"><h:outputText styleClass="outputText" id="textCnt1" value="#{pc_Ssz03701.propCount.stringValue}" style="font-size: 8pt"></h:outputText>
							<h:outputText styleClass="outputText" id="textCnt2" value="件" style="font-size: 8pt"></h:outputText>
						</TD>

					</TR>
				</TBODY>
			</TABLE>
			
			<TABLE border="0" cellpadding="0" cellspacing="0" width="700">
				<TBODY>
					<TR>
						<TD>
						
							<div class="listScroll" style="height:337px;" id="listScroll" onscroll="setScrollPosition('scroll',this);">
							<h:dataTable border="0" cellpadding="2" cellspacing="0"
								headerClass="headerClass" footerClass="footerClass"
								rowClasses="#{pc_Ssz03701.propGyosBunruiList.rowClasses}"
								styleClass="meisai_scroll" id="table1"
								value="#{pc_Ssz03701.propGyosBunruiList.list}" var="varlist">
								
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText id="text1" styleClass="outputText" value="並び順"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text2"
										value="#{varlist.orderNo}"
										style="text-align: left; vertical-align: baseline"></h:outputText>
									<f:attribute value="60" name="width" />
									<f:attribute value="text-align: right" name="style" />
								</h:column>

								<h:column id="column2">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="コード" id="text3"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text4"
										value="#{varlist.gyosyuBunruiCd}"></h:outputText>
									<f:attribute value="100" name="width" />
								</h:column>
								
								<h:column id="column3">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="名称" id="text5"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text6"
										value="#{varlist.gyosyuBunruiName}"></h:outputText>
									<f:attribute value="450" name="width" />
								</h:column>

								<h:column id="column4">
									<f:facet name="header">
										<h:outputText styleClass="outputText" value="出力対象" id="text7"></h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="text8"
										value="#{varlist.outPutName}"></h:outputText>
									<f:attribute value="60" name="width" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
								
								<h:column id="column5">
									<f:facet name="header">
									</f:facet>
									<hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select"
										action="#{pc_Ssz03701.doSelectAction}"></hx:commandExButton>
									<f:attribute value="30" name="width" />
								</h:column>
							</h:dataTable>
							</div>
						
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="700">
				<TBODY>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
							<TBODY>
								<TR>
									<TH width="30%" class="v_a"><h:outputText
										styleClass="outputText" id="lblGyosBunruiCd"
										value="#{pc_Ssz03701.propGyosBunruiCd.labelName}"
										style="#{pc_Ssz03701.propGyosBunruiCd.labelStyle}"></h:outputText></TH>
									<TD width="70%"><h:inputText styleClass="inputText"
										id="htmlGyosBunruiCd"
										value="#{pc_Ssz03701.propGyosBunruiCd.stringValue}"
										maxlength="#{pc_Ssz03701.propGyosBunruiCd.maxLength}"
										style="#{pc_Ssz03701.propGyosBunruiCd.style}" size="3">
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH width="30%" class="v_b"><h:outputText
										styleClass="outputText" id="lblGyosBunruiName"
										value="#{pc_Ssz03701.propGyosBunruiName.labelName}"
										style="#{pc_Ssz03701.propGyosBunruiName.labelStyle}"></h:outputText></TH>
									<TD width="70%"><h:inputText styleClass="inputText"
										id="htmlGyosBunruiName"
										value="#{pc_Ssz03701.propGyosBunruiName.stringValue}"
										style="#{pc_Ssz03701.propGyosBunruiName.style}"
										maxlength="#{pc_Ssz03701.propGyosBunruiName.maxLength}" size="50">
										</h:inputText>
									</TD>
								</TR>
								
								<TR>
									<TH width="30%" class="v_b"><h:outputText
										styleClass="outputText" id="lblGyosBunruiOrderNo"
										value="#{pc_Ssz03701.propGyosBunruiOrderNo.labelName}"
										style="#{pc_Ssz03701.propGyosBunruiOrderNo.labelStyle}"></h:outputText></TH>
									<TD width="70%"><h:inputText
										styleClass="inputText" id="htmlGyosBunruiOrderNo"
										value="#{pc_Ssz03701.propGyosBunruiOrderNo.integerValue}"
										maxlength="#{pc_Ssz03701.propGyosBunruiOrderNo.maxLength}"
										style="#{pc_Ssz03701.propGyosBunruiOrderNo.style}" size="3">
										<f:convertNumber type="number" pattern="##0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText>
									</TD>
								</TR>
								
								<TR>
									<TH width="30%" class="v_b"><h:outputText
										styleClass="outputText" id="lblGyosBunruiOutPutFlg"
										value="#{pc_Ssz03701.propGyosBunruiOutPutFlg.labelName}"></h:outputText></TH>
									<TD width="70%"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlGyosBunruiOutPutFlg"
										value="#{pc_Ssz03701.propGyosBunruiOutPutFlg.value}">
										<f:selectItem itemValue="1" itemLabel="出力対象にする" />
										<f:selectItem itemValue="0" itemLabel="出力対象にしない" />
									</h:selectOneRadio></TD>
								</TR>
							
							
							
							
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0"
							class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD align="center" height="16" width="100%"><hx:commandExButton
										type="submit" value="確定" styleClass="commandExButton_dat"
										id="register" action="#{pc_Ssz03701.doRegisterAction}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Ssz03701.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Ssz03701.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz03701.propGyosBunruiList.scrollPosition}" id="scroll"></h:inputHidden>
				
			<h:inputHidden
				value="#{pc_Ssz03701.propExecutableGyos.integerValue}"
				id="htmlExecutableGyos">
				<f:convertNumber />
			</h:inputHidden>
				
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

