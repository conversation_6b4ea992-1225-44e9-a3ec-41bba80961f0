<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz02701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz02701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>

	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz02701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz02701.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz02701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz02701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD align="right" width="562"><h:outputText
							styleClass="outputText" id="text1"
							value="#{pc_Ssz02701.propCount.stringValue}"
							style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="htmlCount" value="件"
							style="font-size: 8pt"></h:outputText></TD>
						<TD width="230"></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD width="551">
						<DIV class="listScroll" style="height:296px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz02701.propSikaku.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz02701.propSikaku.list}" var="varlist" width="532">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text11" styleClass="outputText" value="コード"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListSikakuCd"
									value="#{varlist.sikakuCd}"></h:outputText>
								<f:attribute value="84" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text12"></h:outputText>
								</f:facet>
								<h:outputText styleClass="likeOutput" id="htmlListSikakuName"
									value="#{varlist.sikakuNameOut.displayValue}"
									title="#{varlist.sikakuNameOut.value}"></h:outputText>
								<f:attribute value="492" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz02701.doSelectAction}"></hx:commandExButton>
								<f:attribute value="26" name="width" />


							</h:column>
						</h:dataTable></DIV>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD width="550">
						<TABLE border="0" class="table"
							cellspacing="0" cellpadding="0" width="550">
							<TBODY>
								<TR>

									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblSikakuCd"
										value="#{pc_Ssz02701.propSikakuCd.labelName}"
										style="#{pc_Ssz02701.propSikakuCd.labelStyle}"></h:outputText></TH>

									<TD width="400"><h:inputText styleClass="inputText"
										id="htmllSikakuCd"
										value="#{pc_Ssz02701.propSikakuCd.stringValue}" size="6"
										style="#{pc_Ssz02701.propSikakuCd.style}"
										maxlength="#{pc_Ssz02701.propSikakuCd.maxLength}">
									</h:inputText></TD>
								</TR>
								<TR>

									<TH class="v_c" width="150"><h:outputText
										
										styleClass="outputText" id="lblSikakuName"
										value="#{pc_Ssz02701.propSikakuName.labelName}"
										style="#{pc_Ssz02701.propSikakuName.labelStyle}"></h:outputText></TH>

									<TD width="400"><h:inputTextarea styleClass="inputTextarea"
										id="htmlSikakuName"
										style="#{pc_Ssz02701.propSikakuName.style}"
										value="#{pc_Ssz02701.propSikakuName.stringValue}"
										 cols="50">
									</h:inputTextarea></TD>

								</TR>
								<TR>

									<TH class="v_d" width="150"><h:outputText
										styleClass="outputText" id="lblSikakuNameRyak"
										value="#{pc_Ssz02701.propSikakuNameRyak.labelName}"></h:outputText></TH>

									<TD width="400"><h:inputText styleClass="inputText"
										id="htmlSikakuNameRyak"
										value="#{pc_Ssz02701.propSikakuNameRyak.stringValue}"
										size="20" style="#{pc_Ssz02701.propSikakuNameRyak.style}"
										maxlength="#{pc_Ssz02701.propSikakuNameRyak.maxLength}">
									</h:inputText></TD>

								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="register" value="確定"
							action="#{pc_Ssz02701.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" styleClass="commandExButton_dat" id="delete"
							value="削除" action="#{pc_Ssz02701.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz02701.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>



			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz02701.propSikaku.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>

</HTML>

