<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00108.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<%@ page import="com.jast.gakuen.rev.xrx.Xrx00108"%>
<%@ page import="com.jast.gakuen.rev.xrx.action.bean.Xrx00108L01Bean"%>
<%@ page import="com.jast.gakuen.framework.util.UtilSystem"%>
<%@ page import="java.util.ArrayList"%>

<SCRIPT type="text/javascript" language="JavaScript">
<!--
-->
</SCRIPT>
<!--↓head↓-->
<f:subview id="Xrx00108">
  <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00108.onPageLoadBegin}">
    <%-- ↓ コンテンツ部 ↓ --%>
    <hx:jspPanel>
      <DIV style="width:870px">

<%
Xrx00108 pc = (Xrx00108)UtilSystem.getManagedBean(Xrx00108.class);
ArrayList list = pc.getKyouikuJissyuList();
int count = Integer.parseInt(pc.getPropCount().getStringValue()); 
%>

<%
if (count > 0) { 
  for(int i=0; i < list.size(); i++) { 
    Xrx00108L01Bean bean = (Xrx00108L01Bean)list.get(i);
%>
        <TABLE  class="table" width="100%" cellspacing="0" cellpadding="0" border="0">
          <TBODY>
            <TR>
              <!-- 実習年度 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propNendo.labelName}"
                  style="#{pc_Xrx00108.propNendo.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getJissyuNendo()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 校種区分 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propKosyuKbn.labelName}"
                  style="#{pc_Xrx00108.propKosyuKbn.labelStyle}"/>
              </TH>
              <TD  colspan="3">
                <span class="outputText"><%=bean.getKosyuKbn()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 登録区分 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propTorokKbn.labelName}"
                  style="#{pc_Xrx00108.propTorokKbn.labelStyle}"/>
              </TH>
              <TD width="40%">
                <span class="outputText"><%=bean.getTorokKbn()%></span>
              </TD>
              <!-- 実習登録日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propJissyuTrkDate.labelName}"
                  style="#{pc_Xrx00108.propJissyuTrkDate.labelStyle}"/>
              </TH>
              <TD>
                <span class="outputText"><%=bean.getJissyuTrkDate()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 実習開始日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propJissyuStaDate.labelName}"
                  style="#{pc_Xrx00108.propJissyuStaDate.labelStyle}"/>
              </TH>
              <TD width="40%">
                <span class="outputText"><%=bean.getJissyuStaDate()%></span>
              </TD>
              <!-- 実習終了日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propJissyuEndDate.labelName}"
                  style="#{pc_Xrx00108.propJissyuEndDate.labelStyle}"/>
              </TH>
              <TD>
                <span class="outputText"><%=bean.getJissyuEndDate()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 実習先都道府県 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propTodofuken.labelName}"
                  style="#{pc_Xrx00108.propTodofuken.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getTodofuken()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 地域 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propChiki.labelName}"
                  style="#{pc_Xrx00108.propChiki.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getChiki()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 実習先名称 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propJissyusakiName.labelName}"
                  style="#{pc_Xrx00108.propJissyusakiName.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getJissyusakiName()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 許可日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propkyokaDate.labelName}"
                  style="#{pc_Xrx00108.propkyokaDate.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getKyokaDate()%></span>
              </TD>
            </TR>
            <TR>
              <!-- レポート添削教員名 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propReportKyoin.labelName}"
                  style="#{pc_Xrx00108.propReportKyoin.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getReportKyoin()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 辞退日 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propJitaiDate.labelName}"
                  style="#{pc_Xrx00108.propJitaiDate.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getJitaiDate()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 備考1 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propBiko1.labelName}"
                  style="#{pc_Xrx00108.propBiko1.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getBiko1()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 備考2 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propBiko2.labelName}"
                  style="#{pc_Xrx00108.propBiko2.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getBiko2()%></span>
              </TD>
            </TR>
            <TR>
              <!-- 備考3 -->
              <TH nowrap class="v_c" width="20%">
                <h:outputText 
                  styleClass="outputText"
                  value="#{pc_Xrx00108.propBiko3.labelName}"
                  style="#{pc_Xrx00108.propBiko3.labelStyle}"/>
              </TH>
              <TD colspan="3">
                <span class="outputText"><%=bean.getBiko3()%></span>
              </TD>
            </TR>
    
          </TBODY>
        </TABLE>
        <BR>
<% 
  } 
} 
%>
      </DIV>      
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
  </hx:scriptCollector>
</f:subview>
