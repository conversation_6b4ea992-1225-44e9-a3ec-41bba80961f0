<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00601.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function confirmOk() {

	document.getElementById('form1:htmlConfirmVal').value = "1";

	 indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlConfirmVal').value = "0";
	return false;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz00601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz00601.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz00601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz00601.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 --><BR>
			<TABLE border="0" width="400">
				<TBODY>
					<TR>
						<TD width="400">
						<TABLE border="0" width="500" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax1"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax1.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax1.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax1"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax1.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax1.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax1.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text1"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax2"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax2.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax2.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax2"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax2.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax2.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax2.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text2"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax3"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax3.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax3.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax3"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax3.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax3.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax3.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text3"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax4"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax4.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax4.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax4"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax4.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax4.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax4.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text4"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_e" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax5"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax5.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax5.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax5"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax5.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax5.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax5.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text5"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_f" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax6"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax6.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax6.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax6"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax6.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax6.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax6.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text6"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax7"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax7.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax7.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax7"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax7.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax7.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax7.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text7"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax8"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax8.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax8.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax8"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax8.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax8.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax8.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text8"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax9"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax9.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax9.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax9"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax9.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax9.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax9.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text9"
										value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblSyoninkyuKaisoMax10"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax10.labelName}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax10.labelStyle}"></h:outputText></TH>
									<TD width="350"><h:inputText styleClass="inputText"
										id="htmlSyoninkyuKaisoMax10"
										value="#{pc_Ssz00601.propSyoninkyuKaisoMax10.stringValue}"
										maxlength="#{pc_Ssz00601.propSyoninkyuKaisoMax10.maxLength}"
										style="#{pc_Ssz00601.propSyoninkyuKaisoMax10.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text10"
										value="千円未満"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確 定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz00601.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>

			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ssz00601.propConfirmVal.stringValue}" id="htmlConfirmVal"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

