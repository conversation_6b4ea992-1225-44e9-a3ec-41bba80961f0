<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00101T03.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">

<TITLE>Xrm00101T03.jsp</TITLE>
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript">


	//納付金配当一覧　全選択
	function func_check_on(thisObj, thisEvent) {
		check('htmlPaywList','htmlPayChecked');
	}

	//納付金配当一覧　全解除
	function func_check_off(thisObj, thisEvent) {
		uncheck('htmlPaywList','htmlPayChecked');
	}

	//学費学生検索画面へ遷移
	function openPGhz0301Window() {
		openPGhz0301("<%=com.jast.gakuen.rev.gh.PGhz0301.getWindowOpenOption() %>");
		return true;
	}

	function fncButtonActive(){

		var codeRegSearch = null;
		

		//選択ボタン
		codeRegSearch = document.getElementById('form1:htmlActiveControlSearch').value;
		if(codeRegSearch == 1){
			document.getElementById('form1:popGakSearch').disabled = true;
			document.getElementById('form1:GakSearch').disabled = true;
		} else {
			document.getElementById('form1:popGakSearch').disabled = false;
			document.getElementById('form1:GakSearch').disabled = false;
			document.getElementById('form1:unselect').disabled = true;
			document.getElementById('form1:pdfout').disabled = true;
		}

		//スクロールバーの位置を保持

		window.attachEvent('onload', endload);		

		//学籍氏名取得Ajax呼び出し
		funcAjaxGakusekiCd(document.getElementById('form1:htmlGakusekiCd'), '1' ,'form1:htmlGakseiName');
		
	}

	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}

	function onClickChkKigen(id) {
	// 201411納入期限チェックボックスをクリック時の処理追加
		indirectClick(id);
	}

	//メッセージ出力(OKボタン押下)
	function confirmOk() {
		indirectClick('GakSearch');
	}
	//メッセージ出力(キャンセルボタン押下)
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = 0;
	}

	//出力項目指定画面へ遷移
	function openPCos0401Window() {
		openPCos0401("<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption() %>");
		return true;
	}
	//チェックボックスクリック時処理	
	function onClickChk(id) {
		indirectClick(id);
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onload="fncButtonActive();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm00101T03.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm00101T03.doCloseDispAction}">
			</hx:commandExButton> <h:outputText styleClass="outputText"
				id="htmlFuncId" value="#{pc_Xrm00101T03.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm00101T03.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="900px">
				<TBODY>
					<TR>
						<td>
						<TABLE width="100%" border="0" class="table">
							<TBODY>

								<TR>
									<!-- 発行日付 -->
									<TH nowrap class="v_a" width="100px"><h:outputText
										styleClass="outputText" id="lblHakkouDate"
										value="#{pc_Xrm00101T03.propHakkouDate.labelName}"
										style="#{pc_Xrm00101T03.propHakkouDate.labelStyle}">
									</h:outputText></TH>
									<TD width="400px"><h:inputText styleClass="inputText"
										id="htmlHakkouDate"
										value="#{pc_Xrm00101T03.propHakkouDate.dateValue}"
										style="#{pc_Xrm00101T03.propHakkouDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<!-- 通信欄 -->
									<TD rowspan=3>
									<TABLE width="300" border="0" class="table">
										<TBODY>
											<TR>
												<TH align="center" nowrap class="v_a" width="150px"><h:outputText
													styleClass="outputText" id="lblTsushinText"
													value="#{pc_Xrm00101T03.propTsushinText.labelName}"
													style="#{pc_Xrm00101T03.propTsushinText.labelStyle}">
												</h:outputText></TH>
												<TD width="*"><h:inputTextarea styleClass="inputTextarea"
													id="htmlTsushinText" cols="50" rows="6"
													onchange="onCangeData();"
													disabled="#{pc_Xrm00101T03.propTsushinText.disabled}"
													value="#{pc_Xrm00101T03.propTsushinText.stringValue}"
													readonly="#{pc_Xrm00101T03.propTsushinText.readonly}"
													style="#{pc_Xrm00101T03.propTsushinText.style}">
												</h:inputTextarea></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<!-- 通信区分 -->
									<TH nowrap class="v_a"><h:outputText styleClass="outputText"
										id="lblTushinTxt"
										value="#{pc_Xrm00101T03.propTushinKbn.labelName}"
										style="#{pc_Xrm00101T03.propTushinKbn.labelStyle}">
									</h:outputText></TH>
									<TD valign="middle" width="*"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlTusinKbn"
										onclick="return onClickChk('linkKikanUseChkBox');"
										value="#{pc_Xrm00101T03.propTushinKbn.checked}"
										disabled="#{pc_Xrm00101T03.propTushinKbn.disabled}">
									</h:selectBooleanCheckbox>定型文を利用せず通信欄を直接入力する。</TD>
									<h:commandLink styleClass="commandLink" id="linkKikanUseChkBox"
										action="#{pc_Xrm00101T03.doChkAction}">
										<h:outputText id="htmlLinkKikanUseChkBox"
											styleClass="outputText">
										</h:outputText>
									</h:commandLink>

								</TR>
								<TR>
									<!-- 出力区分 -->
									<TH class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblOutPutTxt" value="出力区分" style="">
									</h:outputText></TH>
									<TD nowrap><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="radio1"
										value="#{pc_Xrm00101T03.propOutPutKbn.value}"
										layout="pageDirection" style="}">
										<f:selectItem itemValue="1" itemLabel="請求書を発行する。" />
										<f:selectItem itemValue="2" itemLabel="未出力の請求書を発行する。" />
										<f:selectItem itemValue="3" itemLabel="請求書を再発行する。" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
								<!-- 期限区分 -->
								<TH class="v_a" nowrap><h:outputText styleClass="outputText"
										id="lblNonyuTxt"
										value="#{pc_Xrm00101T03.propKigenkbn.labelName}"
										style="">
								</h:outputText></TH>
									<TD valign="middle" width="*"><h:selectBooleanCheckbox
											styleClass="selectBooleanCheckbox" id="htmlNonyuKbn"
											onclick="return onClickChkKigen('linkKigenUseChkBox');"
											value="#{pc_Xrm00101T03.propKigenkbn.checked}"
											disabled="#{pc_Xrm00101T03.propKigenkbn.disabled}">
										</h:selectBooleanCheckbox>納入期限を直接入力する。
										<h:commandLink
										styleClass="onClickChkKigen"
										id="linkKigenUseChkBox"
										action="#{pc_Xrm00101T03.doChkKigenAction}">
										<h:outputText
										id="htmlLinkKigenUseChkBox"
										styleClass="outputText">
										</h:outputText>
										</h:commandLink>
										
									</TD>
								<!-- 納入期限 -->	
								<TD>
									<TABLE width="600"  class="table">
									<TBODY>
								<TH width="150px><h:outputText
										styleClass="outputText" id="lblNonyukigen"
										value="#{pc_Xrm00101T03.propNonyuDate.labelName}"
										style="#{pc_Xrm00101T03.propNonyuDate.labelStyle}">
								</h:outputText></TH>
								<TD width="145px"><h:inputText styleClass="inputText"
										id="nonyuDate" disabled="#{pc_Xrm00101T03.propNonyuDate.disabled}" 
										value="#{pc_Xrm00101T03.propNonyuDate.dateValue}"
										style="#{pc_Xrm00101T03.propNonyuDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
								</h:inputText></TD>
								<!-- 有効期限-->
									
								<TH width="150px><h:outputText
										styleClass="outputText" id="lblYukokigen"
										value="#{pc_Xrm00101T03.propYukoDate.labelName}"
										style="#{pc_Xrm00101T03.propYukoDate.labelStyle}">
								</h:outputText></TH>
								<TD width="145px"><h:inputText styleClass="inputText"
										id="YukoDate"  disabled="#{pc_Xrm00101T03.propYukoDate.disabled}" 
										value="#{pc_Xrm00101T03.propYukoDate.dateValue}"
										style="#{pc_Xrm00101T03.propYukoDate.style}" size="11">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
								</h:inputText></TD>								
								</TBODY>
							</TABLE>
							</TD>
								</TR>

							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="5px"></TD>
					</TR>
					<TR>
						<TD>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR align="left">
									<TD><hx:commandExButton type="submit" value="納付金指定"
										styleClass="tab_head_off" id="htmlPayTab"
										action="#{pc_Xrm00101T03.doLinkPayTabAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="納付金・学生指定" styleClass="tab_head_off"
										id="htmlPayGakTab"
										action="#{pc_Xrm00101T03.doLinkPayGakTabAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="学生指定" styleClass="tab_head_on"
										id="htmlGakseiTab">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										class="tab_body">
										<TBODY>
											<TR align="center">
												<TD height="20px">

												<TABLE width="850px">
													<TR>
														<TD height="5px"></TD>
													</TR>
													<TR>
														<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0"
															class="table" width="100%">
															<TBODY>
																<TR>
																	<TH width="150px" nowrap class="v_a"><!-- 学籍番号 --> <h:outputText
																		styleClass="outputText" id="lblGakusekiCd"
																		style="#{pc_Xrm00101T03.propGakusekiCd.labelStyle}"
																		value="#{pc_Xrm00101T03.propGakusekiCd.labelName}">
																	</h:outputText></TH>
																	<TD width="*" nowrap valign="middle"><h:inputText
																		styleClass="inputText" id="htmlGakusekiCd"
																		onblur="return funcAjaxGakusekiCd(this, '1', 'form1:htmlGakseiName');"
																		value="#{pc_Xrm00101T03.propGakusekiCd.value}"
																		maxlength="#{pc_Xrm00101T03.propGakusekiCd.maxLength}"
																		style="#{pc_Xrm00101T03.propGakusekiCd.style}"
																		disabled="#{pc_Xrm00101T03.propGakusekiCd.disabled}"
																		size="10">
																	</h:inputText> <hx:commandExButton type="submit"
																		styleClass="commandExButton_search" id="popGakSearch"
																		onclick="return openPGhz0301Window();"
																		action="#{pc_Xrm00101T03.doPopGakSearchAction}">
																	</hx:commandExButton> </TD>
																	<TD rowspan=3><hx:commandExButton type="submit"
																		value="選択" styleClass="cmdBtn_dat_s" id="GakSearch"
																		action="#{pc_Xrm00101T03.doSearchAction}">
																	</hx:commandExButton> <hx:commandExButton type="submit"
																		value="解除" styleClass="cmdBtn_etc_s" id="unselect"
																		action="#{pc_Xrm00101T03.doUnselectAction}">
																	</hx:commandExButton></TD>
																</TR>
																<TR>
																	<TH width="150px" nowrap class="v_b"><h:outputText
																		styleClass="outputText" id="htmlpropHurikomininCd"
																		value="#{pc_Xrm00101T03.propHurikomininCd.labelName}"
																		style="#{pc_Xrm00101T03.propHurikomininCd.labelStyle}">
																	</h:outputText></TH>
																	<TD><h:inputText styleClass="inputText"
																		id='propHurikomininCd'
																		value="#{pc_Xrm00101T03.propHurikomininCd.value}"
																		style="#{pc_Xrm00101T03.propHurikomininCd.style}"
																		size="10"
																		maxlength="#{pc_Xrm00101T03.propHurikomininCd.max}"
																		disabled="#{pc_Xrm00101T03.propHurikomininCd.disabled}">
																	</h:inputText></TD>
																</TR>
																
																<TR>
																	<TH width="150px" nowrap class="v_b" height="20px"><!-- 学生氏名 -->
																	<h:outputText styleClass="outputText"
																		id="lblGakseiName"
																		value="#{pc_Xrm00101T03.propGakseiName.labelName}">
																	</h:outputText></TH>
																	<TD width="*" nowrap valign="middle"><h:outputText
																		styleClass="outputText" id="htmlGakseiName"
																		value="#{pc_Xrm00101T03.propGakseiName.value}"
																		style="#{pc_Xrm00101T03.propGakseiName.style}">
																	</h:outputText></TD>
																</TR>


															</TBODY>
														</TABLE>
														</TD>
													</TR>
													<TR>
														<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0"
															width="100%">
															<TBODY>
																<TR>
																	<TD align="right"><h:outputText styleClass="outputText"
																		id="htmlCount"
																		value="#{pc_Xrm00101T03.propPaywList.listCount}">
																	</h:outputText> <h:outputText styleClass="outputText"
																		id="lblCount" value="件">
																	</h:outputText></TD>
																</TR>
																<TR>
																	<TD align="center">
																	<DIV style="height: 222px; width=100%;" id="listScroll"
																		onscroll="setScrollPosition('htmlHidScroll',this);"
																		class="listScroll"><h:dataTable
																		footerClass="footerClass"
																		rows="#{pc_Xrm00101T03.propPaywList.rows}"
																		rowClasses="#{pc_Xrm00101T03.propPaywList.rowClasses}"
																		headerClass="headerClass" styleClass="meisai_scroll"
																		id="htmlPaywList"
																		value="#{pc_Xrm00101T03.propPaywList.list}"
																		var="varlist" width="850px">
																		<h:column id="column1">
																			<f:facet name="header">
																			</f:facet>
																			<h:selectBooleanCheckbox
																				styleClass="selectBooleanCheckbox"
																				id="htmlPayChecked" value="#{varlist.payChecked}"
																				rendered="#{varlist.rendered}">
																			</h:selectBooleanCheckbox>
																			<f:attribute value="30px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column2">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T03.propPaywListNendo.labelName}"
																					id="lblNendo">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPaywNendo"
																				value="#{varlist.nendo}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="65px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column3">
																			<f:facet name="header">
																				<hx:jspPanel>
																					<center><h:outputText styleClass="outputText"
																						value="納付金" id="lblPayCd1">
																					</h:outputText></center>
																					<center><h:outputText styleClass="outputText"
																						value="コード" id="lblPayCd2">
																					</h:outputText></center>
																				</hx:jspPanel>
																			</f:facet>
																			<h:outputText id="htmlPayCd" value="#{varlist.payCd}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="65px" name="width" />
																		</h:column>
																		<h:column id="column4">
																			<f:facet name="header">
																				<hx:jspPanel>
																					<center><h:outputText styleClass="outputText"
																						value="パターン" id="lblPatternCd1">
																					</h:outputText></center>
																					<center><h:outputText styleClass="outputText"
																						value="コード" id="lblPatternCd2">
																					</h:outputText></center>
																				</hx:jspPanel>
																			</f:facet>
																			<h:outputText id="htmlPatternCd"
																				value="#{varlist.patternCd}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="65px" name="width" />
																		</h:column>
																		<h:column id="column5">
																			<f:facet name="header">
																				<hx:jspPanel>
																					<center><h:outputText styleClass="outputText"
																						value="分納" id="lblBunnoKbnCd1">
																					</h:outputText></center>
																					<center><h:outputText styleClass="outputText"
																						value="区分" id="lblBunnoKbnCd2">
																					</h:outputText></center>
																				</hx:jspPanel>
																			</f:facet>
																			<h:outputText id="htmlBunnoKbnCd"
																				value="#{varlist.bunnoKbnCd}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="40px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column6">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T03.propPaywListPayName.labelName}"
																					id="lblPayName">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPayName"
																				value="#{varlist.payName}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="*" name="width" />
																		</h:column>
																		<h:column id="column7">
																			<f:facet name="header">
																				<hx:jspPanel>
																					<center><h:outputText styleClass="outputText"
																						value="分割" id="lblBunkatsuNo1">
																					</h:outputText></center>
																					<center><h:outputText styleClass="outputText"
																						value="ＮＯ" id="lblBunkatsuNo2">
																					</h:outputText></center>
																				</hx:jspPanel>
																			</f:facet>
																			<h:outputText id="htmlBunkatsuNo"
																				value="#{varlist.bunkatsuNo}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="40px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column8">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T03.propPaywListPayLimit.labelName}"
																					id="lblPayLimit">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlPayLimit"
																				value="#{varlist.payLimit}" styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="70px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																		<h:column id="column9">
																			<f:facet name="header">
																				<h:outputText styleClass="outputText"
																					value="#{pc_Xrm00101T03.propPaywListOutputDate.labelName}"
																					id="lblOutputDate">
																				</h:outputText>
																			</f:facet>
																			<h:outputText id="htmlOutputDate"
																				value="#{varlist.outputDate}"
																				styleClass="outputText">
																			</h:outputText>
																			<f:attribute value="70px" name="width" />
																			<f:attribute value="text-align: center" name="style" />
																		</h:column>
																	</h:dataTable></DIV>
																	</TD>
																</TR>
																<TR>
																	<TD>
																	<TABLE border="0" cellpadding="2" cellspacing="0"
																		class="meisai_scroll" width="100%">
																		<TBODY>
																			<TR>
																				<TD class="footerClass">
																				<TABLE class="panelBox">
																					<TBODY>
																						<TR>
																							<TD><%-- 全選択・全解除 --%> <hx:jspPanel id="jspPanel1">
																								<INPUT type="button" name="check" value="on"
																									onclick="return func_check_on(this, event);"
																									class="check">
																								<INPUT type="button" name="uncheck" value="off"
																									onclick="return func_check_off(this, event);"
																									class="uncheck">
																							</hx:jspPanel></TD>
																						</TR>
																					</TBODY>
																				</TABLE>
																				</TD>
																			</TR>
																		</TBODY>
																	</TABLE>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>
													</TR>
													<TR>
														<TD height="20px"></TD>
													</TR>
												</TABLE>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD>
						<TABLE width="100%" border="0" class="button_bar" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD width="100%"><font color="#FF0000">CSVは三菱UFJファクターに必ず送信する事。</font>
									</TD>
								</TR>
								<TR>
									<TD width="100%"><hx:commandExButton type="submit" value="実行"
										styleClass="commandExButton_out" id="pdfout"
										confirm="#{msg.SY_MSG_0019W}"
										action="#{pc_Xrm00101T03.doPdfoutAction}">
									</hx:commandExButton>&nbsp;</TD>
								</TR>
							</TBODY>
						</TABLE>

						</TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
			</DIV>
			<!--↑CONTENT↑--></DIV>
			<!--↑outer↑-->

			<h:inputHidden value="#{pc_Xrm00101T03.propPaywList.scrollPosition}"
				id="htmlHidScroll">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm00101T03.propActiveControlSearch.value}"
				id="htmlActiveControlSearch">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrm00101T03.propExecutableSearch.integerValue}"
				id="htmlExecutableSearch">
				<f:convertNumber />
			</h:inputHidden>

			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
