/* webee default style sheet */
/* ------------------------------------------------------------------- */
BODY {
	/*color : #333;*/
	background-color: #ffffff;
	cursor: default;
	margin: 0mm;
}

P#PAGE {
	margin: 0;
	padding: 0;
	border-width: 1px;
	border-color: black;
	border-style: solid;
	background-image: url('image/grid.gif');
	z-index: 0;
}

A {
	color: red;
}

.menu{
	position : absolute;
	background-color: #d3d3d3;
	z-index: 9;
	top: -100;
	left: -100;
	font-size: 10pt;
	border-width: 2px;
	border-style: outset;
	line-height: 1.2em;
}
.menuitem{
	border-bottom-width: 2px;
	border-bottom-style: groove;
} 
#menuItem4{
	border-bottom-width: 0px;
}

.picturefield {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	padding: 0px;
	letter-spacing: 0px;
	word-spacing: 0px;
	position : absolute;
	border-bottom-width: 0;
	border-top-width: 0;
	border-left-width: 0;
	border-right-width: 0;
	word-wrap: break-word;
	word-break: break-all;
	text-justify: distribute-all-lines;
	overflow-x : hidden;
	overflow-y : hidden;
	z-index: 5;
}
.field {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	padding: 0px;
	letter-spacing: 0px;
	word-spacing: 0px;
	position : absolute;
/*	border-bottom-width: 1;
	border-bottom-style: solid;*/
	border-bottom-width: 0;
	border-top-width: 0;
	border-left-width: 0;
	border-right-width: 0;
	word-wrap: break-word;
	word-break: break-all;
	text-justify: distribute-all-lines;
	overflow-x : hidden;
	overflow-y : hidden;
	z-index: 5;
}
.fieldBG {
	background-color: #ffffff;
	filter:alpha(opacity=35);
	position : absolute;
	width : 100%;
	height : 100%;
	z-index: 0;
}
.reserved {
	color: red;
	margin: 0;
	padding: 0;
	word-spacing: 0px;
/*	height: 1em;	*/
	border-bottom-width: 0;
	border-top-width: 0;
	border-left-width: 0;
	border-right-width: 0;
	word-wrap: break-word;
	word-break: break-all;
	text-justify: distribute-all-lines;
}


.line {
	color: black;
	position : absolute;
	z-index: 6;
}


.verticalrule{
	position : absolute;
	z-index: 6;
	width: 0px;
	background-color: black;
	border-bottom-width: 0;
	border-top-width: 0;
	border-left-width: 0;
	border-right-width: 0;
}

.rectangle{
	position : absolute;
	z-index: 4;
	border-style: solid;
	border-color: black;
}


/* -------------------------100dpi換算----------------------- */
.A3portrait {
/*
	width: 1129px;
	height: 1614px;
	width: 1150px;
	height: 1634px;
*/
	width: 1169px;
	height: 1654px;
}
.A3landscape  {
/*
	width: 1614px;
	height: 1129px;
	width: 1634px;
	height: 1150px;
*/
	width: 1654px;
	height: 1169px;
}
.A4portrait {
/*
	width: 787px;
	height: 1129px;
	width: 807px;
	height: 1150px;
*/
	width: 827px;
	height: 1169px;
}
.A4landscape  {
/*
	width: 1129px;
	height: 787px;
	width: 1150px;
	height: 807px;
*/
	width: 1169px;
	height: 827px;
}
.A5portrait {
/*
	width: 543px;
	height: 787px;
	width: 563px;
	height: 807px;
*/
	width: 583px;
	height: 827px;
}
.A5landscape  {
/*
	width: 787px;
	height: 543px;
	width: 807px;
	height: 563px;
*/
	width: 827px;
	height: 583px;
}
.B4portrait {
/*
	width: 972px;
	height: 1393px;
	width: 992px;
	height: 1413px;
*/
	width: 1012px;
	height: 1433px;
}
.B4landscape  {
/*
	width: 1393px;
	height: 972px;
	width: 1413px;
	height: 992px;
*/
	width: 1433px;
	height: 1012px;
}
.B5portrait {
/*
	width: 677px;
	height: 972px;
	width: 697px;
	height: 992px;
*/
	width: 717px;
	height: 1012px;
}
.B5landscape  {
/*
	width: 972px;
	height: 677px;
	width: 992px;
	height: 697px;
*/
	width: 1012px;
	height: 717px;
}
.LETTERportrait {
/*
	width: 810px;
	height: 1060px;
	width: 830px;
	height: 1080px;
*/
	width: 850px;
	height: 1100px;
}
.LETTERlandscape  {
/*
	width: 1060px;
	height: 810px;
	width: 1080px;
	height: 830px;
*/
	width: 1100px;
	height: 850px;
}
.POSTCARDportrait {
/*
	width: 354px;
	height: 543px;
	width: 374px;
	height: 563px;
*/
	width: 394px;
	height: 583px;
}
.POSTCARDlandscape  {
/*
	width: 543px;
	height: 354px;
	width: 563px;
	height: 374px;
*/
	width: 583px;
	height: 394px;
}
.subform {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	position : absolute;
	overflow : visible;
	background-color: #ffff33;
	filter:alpha(opacity=35);
	z-index: 1;
}
.recordArea {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	padding: 0px;
	letter-spacing: 0px;
	word-spacing: 0px;
	word-wrap: break-word;
	word-break: break-all;
	position : absolute;
	overflow : hidden;
	filter:alpha(opacity=35);
	z-index: 2;
}
.HEADER {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	padding: 0px;
	overflow : hidden;
	background-color: #00ff00;
}
.DETAIL {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	padding: 0px;
	overflow : hidden;
	background-color: #c0c0c0;
}
.SUM {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	padding: 0px;
	overflow : hidden;
	background-color: #00bfff;
}
.TOTAL {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	padding: 0px;
	overflow : hidden;
	background-color: #3399ff;
}
.fixed {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	position : absolute;
	background-color: #c0c0c0;
/*	filter:alpha(opacity=100);*/
	overflow : hidden;
	z-index: 2;
}
.barcode {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	position : absolute;
/*	background-color: Silver; */
	background-image: url('image/BARCODE.gif');
	overflow : hidden;
	z-index: 3;
}
.qrcode {
	position : absolute;
	z-index: 3;
}
.bitmap {
	position : absolute;
	z-index: 3;
}
.straightaway {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	overflow : hidden;
	position : absolute;
	z-index: 3;
}
.box {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	overflow : hidden;
	position : absolute;
	z-index: 3;
}
.text {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	position : absolute;
/*	text-justify: distribute-all-lines; */
	text-align: justify;
	word-wrap: normal ;
	word-break: keep-all;
	overflow : visible;
	color: blue;
	letter-spacing: -2; 
	z-index: 4;
}
.textfield {
	marginBottom : 0px;
	marginTop  : 0px;
	marginLeft : 0px;
	marginRight : 0px;
	position : absolute;
/*	text-justify: distribute-all-lines; */
	text-align: justify;
	word-wrap: normal ;
	word-break: keep-all;
	overflow : visible;
	color: red;
	letter-spacing: -2; 
	z-index: 4;
}

