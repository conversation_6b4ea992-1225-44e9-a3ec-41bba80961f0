<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00402.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }



function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xrb00401.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xrb00401.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xrb00401.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xrb00401.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->
				<hx:commandExButton type="submit" value="戻る"
					styleClass="commandExButton" id="returnDisp"
					action="#{pc_Xrb00402.doReturnDispAction}">
				</hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
            <TABLE width="850">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblNyugakNendoName"
	                                	value="入学年度">
	                                </h:outputText>
	                            </TH>
	                            <TD width="260">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlNyugakNendoName"
	                                	value="#{pc_Xrb00402.propNyugakNendoName.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                           	<TH class="v_b" width="140">
	                           		<h:outputText styleClass="outputText"
	                                	id="lblNyugakGakkiNoName"
	                                	value="入学学期">
	                                </h:outputText>
	                            </TH>
	                            <TD width="260">
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlNyugakGakkiNoName"
	                                	value="#{pc_Xrb00402.propNyugakGakkiNoName.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                        </TR>
	                        <TR>
	                            <TH class="v_a">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblNyugakNenjiName"
	                                	value="入学年次">
	                                </h:outputText>
	                            </TH>
	                            <TD>
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlNyugakNenjiName"
	                                	value="#{pc_Xrb00402.propNyugakNenjiName.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                           	<TH class="v_b">
	                           		<h:outputText styleClass="outputText"
	                                	id="lblNyugakSbtName"
	                                	value="入学種別">
	                                </h:outputText>
	                            </TH>
	                            <TD>
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlNyugakSbtName"
	                                	value="#{pc_Xrb00402.propNyugakSbtName.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                        </TR>
	                        <TR>
	                            <TH class="v_a">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblCurGakkaCdName"
	                                	value="カリキュラム学科組織">
	                                </h:outputText>
	                            </TH>
	                            <TD colspan=3>
	                            	<h:outputText styleClass="outputText"
	                                	id="htmlCurGakkaCdName"
	                                	value="#{pc_Xrb00402.propCurGakkaCdName.stringValue}">
	                            	</h:outputText>
	                            </TD>
	                        </TR>
	                    </TABLE>
                    </TD>
                </TR>
            </TABLE>
            <BR>
            <TABLE width="850">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
										id="lblKamokuFurikaeCd"
										style="#{pc_Xrb00402.propKamokuFurikaeCd.labelStyle}"
	                                	value="#{pc_Xrb00402.propKamokuFurikaeCd.labelName}">
									</h:outputText>
								</TH>
		                        <TD colspan="3">
	                            	<h:inputText styleClass="inputText"
	                                	id="htmlKamokuFurikaeCd"
										disabled="#{pc_Xrb00402.propKamokuFurikaeCd.disabled}"
	                                	value="#{pc_Xrb00402.propKamokuFurikaeCd.stringValue}"
	                                	style="#{pc_Xrb00402.propKamokuFurikaeCd.style}"
	                                	maxlength="#{pc_Xrb00402.propKamokuFurikaeCd.maxLength}"
	                                	size="10" tabindex="1">
	                            	</h:inputText>
								</TD>
	                        </TR>
	                        <TR>
	                            <TH class="v_a">
	                            	<h:outputText styleClass="outputText"
										id="lblKamokuFurikaeKbn"
										style="#{pc_Xrb00402.propKamokuFurikaeKbn.labelStyle}"
	                                	value="#{pc_Xrb00402.propKamokuFurikaeKbn.labelName}">
									</h:outputText>
								</TH>
	                            <TD width="260">
		                        	<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlKamokuFurikaeKbn" tabindex="2"
										value="#{pc_Xrb00402.propKamokuFurikaeKbn.value}">
										<f:selectItems value="#{pc_Xrb00402.propKamokuFurikaeKbn.list}" />
									</h:selectOneMenu>
								</TD>
	                            <TH class="v_b" width="140">
	                            	<h:outputText styleClass="outputText"
										id="lblKisyutokFlg"
										style="#{pc_Xrb00402.propKisyutokFlg.labelStyle}"
	                                	value="#{pc_Xrb00402.propKisyutokFlg.labelName}">
									</h:outputText>
								</TH>
	                            <TD width="260">
		                        	<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlKisyutokFlg" tabindex="3"
										value="#{pc_Xrb00402.propKisyutokFlg.value}">
										<f:selectItems value="#{pc_Xrb00402.propKisyutokFlg.list}" />
									</h:selectOneMenu>
								</TD>
	                        </TR>
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
										id="lblTitle"
										style="#{pc_Xrb00402.propTitle.labelStyle}"
	                                	value="#{pc_Xrb00402.propTitle.labelName}">
									</h:outputText>
								</TH>
		                        <TD colspan="3">
	                            	<h:inputText styleClass="inputText"
	                                	id="htmlTitle"
	                                	value="#{pc_Xrb00402.propTitle.stringValue}"
	                                	style="#{pc_Xrb00402.propTitle.style}"
	                                	maxlength="#{pc_Xrb00402.propTitle.maxLength}"
	                                	size="60" tabindex="4">
	                            	</h:inputText>
								</TD>
	                        </TR>
	                    </TABLE>
                    </TD>
                </TR>
            </TABLE>
            <BR>
            <TABLE width="850">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
										id="lblFurikaeSettei"
	                                	value="振替設定">
									</h:outputText>
								</TH>
		                        <TD width="670">
			                    	<hx:commandExButton type="submit" value="振替元"
										styleClass="commandExButton_dat" id="furikaeMoto"
										disabled="#{pc_Xrb00402.propFurikaeMoto.disabled}"
										style="#{pc_Xrb00402.propFurikaeMoto.style}"
										action="#{pc_Xrb00402.doFurikaeMotoAction}" tabindex="5">
									</hx:commandExButton>
			                    	<hx:commandExButton type="submit" value="振替先"
										styleClass="commandExButton_dat" id="furikaeSaki"
										disabled="#{pc_Xrb00402.propFurikaeSaki.disabled}"
										style="#{pc_Xrb00402.propFurikaeSaki.style}"
										action="#{pc_Xrb00402.doFurikaeSakiAction}" tabindex="6">
									</hx:commandExButton>
								</TD>
	                        </TR>
	                    </TABLE>
                    </TD>
                </TR>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE width="850" class="button_bar" border="0" cellpadding="0"
                cellspacing="0">
                <TR>
                    <TD >
                    	<hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							style="#{pc_Xrb00402.propRegister.style}"
							action="#{pc_Xrb00402.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}" tabindex="7">
						</hx:commandExButton>
                    	<hx:commandExButton type="submit" value="クリア"
							styleClass="commandExButton_dat" id="clear"
							style="#{pc_Xrb00402.propClear.style}"
							action="#{pc_Xrb00402.doClearAction}" tabindex="8">
						</hx:commandExButton>
                    </TD>
                </TR>
            </TABLE>
			<h:inputHidden value="#{pc_Xrb00402.propHidNyugakNendo.stringValue}" id="htmlHidNyugakNendo"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00402.propHidNyugakGakkiNo.stringValue}" id="htmlHidNyugakGakkiNo"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00402.propHidNyugakNenji.stringValue}" id="htmlHidNyugakNenji"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00402.propHidNyugakSbt.stringValue}" id="htmlHidNyugakSbt"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00402.propHidCurGakkaCd.stringValue}" id="htmlHidCurGakkaCd"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00402.propHidNewFlg.stringValue}" id="htmlHidNewFlg"></h:inputHidden>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

