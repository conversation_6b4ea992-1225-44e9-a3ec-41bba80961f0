	<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xri/Xri00301.java" --%><%-- /jsf:pagecode --%>
	
	<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
	<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
	<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
	
	<HTML>
	<HEAD>
	<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
	<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
	
	<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<META name="GENERATOR" content="IBM Software Development Platform">
	<META http-equiv="Content-Style-Type" content="text/css">
	
	<LINK href="../inc/gakuen.css" rel="stylesheet" type="text/css">
	<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
	<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
	
	<TITLE>Xri00301.jsp</TITLE>
	
	<SCRIPT type="text/javascript">
	
		
		//メッセージ出力(OKボタン押下)
		function confirmOk() {
		
			var procPaywFind = document.getElementById('form1:htmlPaywListFind').value;
			var procPayhFind = document.getElementById('form1:htmlPayhListFind').value;
	
			//選択処理実行
			if(procPaywFind == "1" || 
				procPayhFind == "1"){
				indirectClick('search');
			}
	
		}			
		//メッセージ出力(キャンセルボタン押下)
		function confirmCancel() {
			document.getElementById('form1:htmlPaywListFind').value = 0;
			document.getElementById('form1:htmlPayhListFind').value = 0;
		}
		

	
	</SCRIPT>
	</HEAD>
	<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/>
		<BODY>
		<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xri00301.onPageLoadBegin}">
			<h:form styleClass="form" id="form1">
			
				<!-- ヘッダーインクルード -->
				<jsp:include page ="../inc/header.jsp" />
				
				<!-- ヘッダーへのデータセット領域 -->
				<div style="display:none;">
					<hx:commandExButton type="submit" value="閉じる"
						styleClass="commandExButton" id="closeDisp"
						action="#{pc_Xri00301.doCloseDispAction}">
					</hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xri00301.funcId}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xri00301.screenName}"></h:outputText>
				</div>			
	
				<!--↓OUTER↓-->
				<DIV class="outer">
					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
						<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
							styleClass="outputText" escape="false">
						</h:outputText>
					</FIELDSET>
	                <!--↓CONTENT↓-->
	
	                <DIV class="head_button_area" >
					</DIV>
	
	                
	                <DIV id="content">          
	
	                    <DIV class="column" align="center">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
								<TBODY>
									<TR>
										<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
												<TBODY>
		
													<TR >
														<TH nowrap class="v_a" width="150px">
														<!-- 学費年度 -->
															<h:outputText styleClass="outputText" id="lblGhYear"
															value="#{pc_Xri00301.propGhYear.labelName}"
															style="#{pc_Xri00301.propGhYear.labelStyle}"></h:outputText></TH>
														<TD valign="middle">
															<h:inputText styleClass="inputText" id="htmlGhYear"
																size="4" value="#{pc_Xri00301.propGhYear.dateValue}"
																style="#{pc_Xri00301.propGhYear.style}"
																disabled="#{pc_Xri00301.propGhYear.disabled}"
																>
																<hx:inputHelperAssist imeMode="inactive"
																	errorClass="inputText_Error" promptCharacter="_" />
																<f:convertDateTime pattern="yyyy" />
															</h:inputText>
														</TD>
	
														<!-- 学期(前期、後期) -->
														<TD nowrap width="430px">
															<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlpropZenkiKouki"
																value="#{pc_Xri00301.propZenkiKouki.value}"
																disabled="#{pc_Xri00301.propZenkiKouki.disabled}"
																tabindex="22">
																<f:selectItem itemValue="1" itemLabel="前期" />
																<f:selectItem itemValue="2" itemLabel="後期" />
															</h:selectOneRadio>
														</TD>
													</TR>
	
												</TBODY>
											</TABLE>
										</TD>
									</TR>
	
									<TR>
										<TD height="5px">
										</TD>
									</TR>
								
								</TBODY>
							</TABLE>


							<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
								<TBODY>
									<TR>
										<TD>
											<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
												<TBODY>
	
													<TR>
														<TH nowrap class="v_e" width="150px">
														<!-- 異動種別 -->
															<h:outputText styleClass="outputText" id="lblIdoSbt"
															value="#{pc_Xri00301.propIdoSbt.labelName}"
															style="#{pc_Xri00301.propIdoSbt.style}"></h:outputText></TH>
														<TD colspan="2"; nowrap width="430px"><h:outputText styleClass="outputText"
															id="propIdoSbtLabel"
															value="#{pc_Xri00301.propIdoSbtLabel.stringValue}"
															style="#{pc_Xri00301.propIdoSbtLabel.style}">
															</h:outputText>
														</TD>
													</TR>
													<TR>
														<TH width="150px" class="v_e" >
															<!-- 異動日付 -->
															<h:outputText styleClass="outputText" 
																id="htmlIdoDate1" 
																value="#{pc_Xri00301.propIdoDate.name}"
																style="#{pc_Xri00301.propIdoDate.style}">
															</h:outputText>
														</TH>
														<TD style="border-right-style:none;">
														<TABLE border="0" cellpadding="0" cellspacing="0">
															<TBODY>
																<TR>
																	<TD class="clear_border" width="115px">
																		<h:inputText styleClass="inputText" 
																			id="htmlIdoDateStart" 
																			value="#{pc_Xri00301.propIdoDateStart.dateValue}" 
																			style="#{pc_Xri00301.propIdoDateStart.style}"
																			disabled="#{pc_Xri00301.propIdoDateStart.disabled}"
																			size="11" tabindex="6">
																			<f:convertDateTime />
																			<hx:inputHelperDatePicker />
																			<hx:inputHelperAssist 
																				errorClass="inputText_Error" promptCharacter="_" />
																		</h:inputText>
																	</TD>
																	<TD class="clear_border" width="25px">
																		<h:outputText 
																			styleClass="outputText" 
																			id="textKara" value="～">
																		</h:outputText>
																	</TD>
																	<TD class="clear_border" width="*">
																		<h:inputText styleClass="inputText" 
																			id="htmlIdoDateEnd" 
																			value="#{pc_Xri00301.propIdoDateEnd.dateValue}" 
																			style="#{pc_Xri00301.propIdoDateEnd.style}"
																			disabled="#{pc_Xri00301.propIdoDateEnd.disabled}"
																			size="11" tabindex="7">
																			<f:convertDateTime />
																			<hx:inputHelperDatePicker />
																			<hx:inputHelperAssist 
																				errorClass="inputText_Error" promptCharacter="_" />
																		</h:inputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
														</TD>						
	
														<TD colspan="2"; style="border-left-style:none;">
														</TD>
	
													</TR>
	
													<TR align="center">
														<TH nowrap class="v_d" width="150px"><h:outputText styleClass="outputText" id="lblSyoriKbn" value="#{pc_Xri00301.propSyoriKbn.name}" style="#{pc_Xri00301.propSyoriKbn.style}"></h:outputText></TH>
														<TD align="left" colspan= "2"><h:selectBooleanCheckbox
															styleClass="selectBooleanCheckbox" id="htmlCheckOnly" value="#{pc_Xri00301.propCheckOnly.checked}" style="#{pc_Xri00301.propCheckOnly.style}" tabindex="29"></h:selectBooleanCheckbox><h:outputText
															styleClass="outputText" id="lblCheckOnly"
															value="#{pc_Xri00301.propCheckOnly.name}"
															style="#{pc_Xri00301.propCheckOnly.style}"></h:outputText></TD>
													</TR>
	
													<TR>
														<TH width="150px" nowrap class="v_c">
															<h:outputText styleClass="outputText" 
																id="lblChkList"
																value="#{pc_Xri00301.propChkList.name}">
															</h:outputText>
														</TH>
														<TD style="border-right-style:none;">
															<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
																<TBODY>
																	<TR>
																		<TD width="300" nowrap class="clear_border">
																			<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																				id="htmlChkListNormal"
																				value="#{pc_Xri00301.propChkListNormal.checked}">
																			</h:selectBooleanCheckbox>
																			<h:outputText styleClass="outputText" 
																				id="lblChkListNormal"
																				value="#{pc_Xri00301.propChkListNormal.name}"
																				style="#{pc_Xri00301.propChkListNormal.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TD width="300" nowrap class="clear_border">
																			<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																				id="htmlChkListError"
																				value="#{pc_Xri00301.propChkListError.checked}">
																			</h:selectBooleanCheckbox>
																			<h:outputText styleClass="outputText"
																				id="lblChkListError"
																				value="#{pc_Xri00301.propChkListError.name}"
																				style="#{pc_Xri00301.propChkListError.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																	<TR>
																		<TD width="300" nowrap class="clear_border">
																			<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																				id="htmlChkListWarning"
																				value="#{pc_Xri00301.propChkListWarning.checked}">
																			</h:selectBooleanCheckbox>
																			<h:outputText styleClass="outputText"
																				id="lblChkListWarning"
																				value="#{pc_Xri00301.propChkListWarning.name}"
																				style="#{pc_Xri00301.propChkListWarning.style}">
																			</h:outputText>
																		</TD>
																	</TR>
																</TBODY>
															</TABLE>
														</TD>
				
														<TD colspan="2"; style="border-left-style:none;">
														</TD>
				
													</TR>
	
												</TBODY>
											</TABLE>
										</TD>
									<TR>
										<TD>
										</TD>
									</TR>
	
									<TR>
										<TD>
	
											<TABLE cellspacing="0" cellpadding="0" class="button_bar" width="100%">
												<TBODY>
													<TR align="right">
														<TD align="center"><hx:commandExButton type="submit"
															value="実行" styleClass="commandExButton_dat" id="regist"
															confirm="#{msg.SY_MSG_0002W}"
															action="#{pc_Xri00301.doRegistAction}"
															disabled="#{pc_Xri00301.propHidDisabled.disabled}" tabindex="33"></hx:commandExButton></TD>
													</TR>
												</TBODY>
											</TABLE>
	
										</TD>
									</TR>
								</TBODY>
							</TABLE>

							<h:inputHidden id="htmlHidKanriNo" value="#{pc_Xri00301.propHidKanriNo.value}"></h:inputHidden>
						</DIV>
	                </DIV>
	                <!--↑CONTENT↑-->
	            </DIV>
				<!--↑outer↑-->
				
				<!-- フッダーインクルード -->
				<jsp:include page ="../inc/footer.jsp" />
				
			</h:form>
		</hx:scriptCollector>
		</BODY>
		
		<jsp:include page ="../inc/common.jsp" />
	</f:view>
	</HTML>
