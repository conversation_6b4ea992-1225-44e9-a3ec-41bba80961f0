<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx04201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrx04201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
// 入力項目指定画面を開く
function openPCos0401() {
	setTarget("xxxx");
	openModalWindow("", "xxxx", "<%=com.jast.gakuen.rev.co.PCos0401.getWindowOpenOption()%>");
	return true;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrx04201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrx04201.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrx04201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrx04201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<CENTER>
			<TABLE border="0" width="650" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblInputFile"
							value="#{pc_Xrx04201.propInputFile.labelName}"
							style="#{pc_Xrx04201.propInputFile.labelStyle}"></h:outputText><BR>
						<h:outputText styleClass="outputText" id="lblInputFileOld"
							value="#{pc_Xrx04201.propInputFileOld.labelName}"
							style="#{pc_Xrx04201.propInputFileOld.labelStyle}"></h:outputText></TH>
						<TD nowrap width="500"><hx:fileupload styleClass="fileupload"
							id="htmlInputFile" value="#{pc_Xrx04201.propInputFile.value}"
							style="#{pc_Xrx04201.propInputFile.style};width:485px">
							<hx:fileProp name="fileName"
								value="#{pc_Xrx04201.propInputFile.fileName}" />
							<hx:fileProp name="contentType"
								value="#{pc_Xrx04201.propInputFile.contentType}" />
						</hx:fileupload><BR><h:outputText styleClass="outputText" id="htmlInputFileOld"
							value="#{pc_Xrx04201.propInputFileOld.stringValue}"
							style="#{pc_Xrx04201.propInputFileOld.style}"></h:outputText><BR>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_c"><h:outputText styleClass="outputText"
							id="lblSyoriKbn" value="#{pc_Xrx04201.propSyoriKbn.labelName}"
							style="#{pc_Xrx04201.propSyoriKbn.labelStyle}"></h:outputText></TH>
						<TD nowrap><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
							value="#{pc_Xrx04201.propSyoriKbn.checked}"></h:selectBooleanCheckbox><h:outputText
							styleClass="outputText" id="text9"
							value="チェックのみ（データの登録/更新は行いません）"
							style="#{pc_Xrx04201.propSyoriKbn.style}"></h:outputText></TD>
					</TR>
					<TR>
						<TH nowrap width="30%" class="v_d"><h:outputText
							styleClass="outputText" id="text4" value="チェックリスト出力指定"
							style="#{pc_Xrx04201.propChkListNormal.labelStyle}"></h:outputText></TH>
						<TD nowrap width="70%">
						<TABLE border="0" cellpadding="0" cellspacing="0"
							class="clear_border">
							<TBODY>
								<TR>
									<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="htmlChkListNormal"
										value="#{pc_Xrx04201.propChkListNormal.checked}"
										style="#{pc_Xrx04201.propChkListNormal.style}"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="text5" value="正常データ"></h:outputText></TD>
								</TR>
								<TR>
									<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
										id="htmlChkListError"
										value="#{pc_Xrx04201.propChkListError.checked}"
										style="#{pc_Xrx04201.propChkListError.style}"></h:selectBooleanCheckbox><h:outputText
										styleClass="outputText" id="text7" value="エラーデータ"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR noshade class="hr">
			<TABLE border="0" cellpadding="0" cellspacing="0" width="650"
				class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="実行"
							styleClass="commandExButton_dat" id="exec"
							action="#{pc_Xrx04201.doExecAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

