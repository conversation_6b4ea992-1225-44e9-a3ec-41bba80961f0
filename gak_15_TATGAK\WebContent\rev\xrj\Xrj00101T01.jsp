<%@page language="java" contentType="text/html; charset=UTF-8"	pageEncoding="UTF-8"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrj/Xrj00101T01.java" --%><%-- /jsf:pagecode --%>
<HTML>
<HEAD>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrj00101T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrj00101T01.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrj00101T01.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrj00101T01.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrj00101T01.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				styleClass="outputText" id="htmlMessage"
				value="#{requestScope.DISPLAY_INFO.displayMessage}" escape="false"></h:outputText>
			</FIELDSET>
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　</DIV>
			<!--↓content↓-->
			<DIV id="content"><!-- ↓ここにコンポーネントを配置 -->
			<DIV class="column">
			<TABLE border="0" cellpadding="0" width="650">
				<TBODY>
					<TR>
						<TD width="650">

						<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="180">
										<h:outputText styleClass="outputText" id="lblStudentKbn"
										value="#{pc_Xrj00101T01.propStudentKbn.labelName}"
										style="#{pc_Xrj00101T01.propStudentKbn.labelStyle}"></h:outputText></TH>
									<TD width="469">
										<h:selectOneMenu styleClass="selectOneMenu" id="htmlStudentKbn"
										value="#{pc_Xrj00101T01.propStudentKbn.stringValue}"
										disabled="#{pc_Xrj00101T01.propSelect.disabled}">
											<f:selectItems value="#{pc_Xrj00101T01.propStudentKbn.list}" />
										</h:selectOneMenu><hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select"
										action="#{pc_Xrj00101T01.doSelectAction}"
										disabled="#{pc_Xrj00101T01.propSelect.disabled}">
										</hx:commandExButton><hx:commandExButton type="submit"
										value="解除" styleClass="commandExButton" id="unselect"
										action="#{pc_Xrj00101T01.doUnselectAction}"
										disabled="#{!pc_Xrj00101T01.propSelect.disabled}">
										</hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH class="v_b" width="180"><h:outputText
										styleClass="outputText" id="lblRegistRangeNumber"
										value="#{pc_Xrj00101T01.propRegistRangeNumber.labelName}"
										style="#{pc_Xrj00101T01.propRegistRangeNumber.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:outputText 
										styleClass="outputText" id="htmlRegistRangeNumberFirst" value="小数点第"></h:outputText>
										<h:inputText styleClass="inputText" id="txtRegistRangeNumber"
										value="#{pc_Xrj00101T01.propRegistRangeNumber.integerValue}"
										style="#{pc_Xrj00101T01.propRegistRangeNumber.style}"
										size="3"
										maxlength="#{pc_Xrj00101T01.propRegistRangeNumber.maxLength}">
											<f:convertNumber type="number" pattern="0" />
											<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText><h:outputText styleClass="outputText"
										id="htmlRegistRangeNumberLast" value="位までを計算します"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_c" width="180"><h:outputText
										styleClass="outputText" id="lblMarumeKbn"
										value="#{pc_Xrj00101T01.propMarumeKbn.labelName}"
										style="#{pc_Xrj00101T01.propMarumeKbn.labelStyle}"></h:outputText></TH>
									<TD width="469"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="rdbMarumeKbn"
										value="#{pc_Xrj00101T01.propMarumeKbn.stringValue}"
										style="#{pc_Xrj00101T01.propMarumeKbn.style}">
										<f:selectItems value="#{pc_Xrj00101T01.propMarumeKbn.list}" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_d" width="180">
										<h:outputText styleClass="outputText" id="lblGpaKbn"
										value="#{pc_Xrj00101T01.propGpaKbn.labelName}"
										style="#{pc_Xrj00101T01.propGpaKbn.labelStyle}"></h:outputText></TH>
									<TD width="469">
										<h:selectManyCheckbox id="chkGpaKbn" layout="pageDirection" styleClass="selectManyCheckbox"
										value="#{pc_Xrj00101T01.propGpaKbn.stringValue}">
											<f:selectItems value="#{pc_Xrj00101T01.propGpaKbn.list}" />
										</h:selectManyCheckbox>
									</TD>
								</TR>
							</TBODY>
						</TABLE>

						<BR>
						</TD>
					</TR>
					<c:if test="${pc_Xrj00101T01.propSelect.disabled}">
					<TR>
						<TD align="left"><br>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="140">
							<TBODY>
								<TR>
									<TD width="70" nowrap align="center" valign="middle"><hx:commandExButton
										type="button" value="一括指定" styleClass="tab_head_on"
										id="ikkatsuSitei">
									</hx:commandExButton></TD>
									<TD width="70" nowrap align="center" valign="middle"
										class="tab_head_off"><hx:commandExButton type="submit"
										value="学生指定" styleClass="tab_head_off" id="btnSwitch"
										action="#{pc_Xrj00101T01.doSwitchAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD width="650" align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="tab_body"
							width="100%" height="330px">
							<TBODY>
								<TR>
									<TD valign="top">
									<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
										class="table" style="margin-top:10px">
										<TBODY>
											<TR>
												<TH class="v_a" width="180"><h:outputText
													styleClass="outputText"
													value="#{pc_Xrj00101T01.propSzkGakkaCd.labelName}"
													style="#{pc_Xrj00101T01.propSzkGakkaCd.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneMenu
													styleClass="selectOneMenu" id="cmbSzkGakkaCd" 
													value="#{pc_Xrj00101T01.propSzkGakkaCd.stringValue}"
													style="#{pc_Xrj00101T01.propSzkGakkaCd.style}">
													<f:selectItems
														value="#{pc_Xrj00101T01.propSzkGakkaCd.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_b" width="180"><h:outputText
													styleClass="outputText"
													value="#{pc_Xrj00101T01.propCurGakkaCd.labelName}"
													style="#{pc_Xrj00101T01.propCurGakkaCd.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneMenu
													styleClass="selectOneMenu" id="cmbCurGakkaCd"
													value="#{pc_Xrj00101T01.propCurGakkaCd.stringValue}"
													style="#{pc_Xrj00101T01.propCurGakkaCd.style}">
													<f:selectItems
														value="#{pc_Xrj00101T01.propCurGakkaCd.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_c" width="180"><h:outputText
													styleClass="outputText"
													value="#{pc_Xrj00101T01.propGakunen.labelName}"
													style="#{pc_Xrj00101T01.propGakunen.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneMenu
													styleClass="selectOneMenu" id="cmbGakunen"
													value="#{pc_Xrj00101T01.propGakunen.stringValue}"
													style="#{pc_Xrj00101T01.propGakunen.style}">
													<f:selectItems
														value="#{pc_Xrj00101T01.propGakunen.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_d" width="180"><h:outputText
													styleClass="outputText"
													value="#{pc_Xrj00101T01.propSinsei.labelName}"
													style="#{pc_Xrj00101T01.propSinsei.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectOneMenu
													styleClass="selectOneMenu" id="cmbSinsei"
													value="#{pc_Xrj00101T01.propSinsei.stringValue}"
													style="#{pc_Xrj00101T01.propSinsei.style}">
													<f:selectItems value="#{pc_Xrj00101T01.propSinsei.list}" />
												</h:selectOneMenu></TD>
											</TR>
											<TR>
												<TH class="v_e" width="180"><h:outputText
													styleClass="outputText"
													value="#{pc_Xrj00101T01.propTargetStudent.labelName}"
													style="#{pc_Xrj00101T01.propTargetStudent.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectManyCheckbox
													styleClass="selectManyCheckbox" id="chkTargetStudent"
													layout="pageDirection"
													value="#{pc_Xrj00101T01.propTargetStudent.stringValue}"
													style="#{pc_Xrj00101T01.propTargetStudent.style}">
													<f:selectItems value="#{pc_Xrj00101T01.propTargetStudent.list}" />
												</h:selectManyCheckbox></TD>
											</TR>
											<TR>
												<TH class="v_f" width="180"><h:outputText
													styleClass="outputText"
													value="#{pc_Xrj00101T01.propKyugaku.labelName}"
													style="#{pc_Xrj00101T01.propKyugaku.labelStyle}"></h:outputText></TH>
												<TD width="469"><h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" id="chkKyugaku"
													value="#{pc_Xrj00101T01.propKyugaku.checked}"
													style="#{pc_Xrj00101T01.propKyugaku.style}" />休学中を含む</TD>
											</TR>
										</TBODY>
									</TABLE>

									<BR>
									<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
										class="button_bar">
										<TBODY>
											<TR>
												<TD align="center" height="8"><hx:commandExButton
													type="submit" value="実行" styleClass="commandExButton_dat"
													id="btnExec"
													action="#{pc_Xrj00101T01.doExecAction}"
													confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>

						</TD>
					</TR>
					</c:if>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑--></DIV>
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
