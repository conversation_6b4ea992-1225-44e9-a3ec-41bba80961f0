<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

}

function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}

// 画面ロード時の学生氏名の再取得
function loadAction(event){
  doGakseiAjax(document.getElementById('form1:htmlGaksekiCd'), event, 'form1:lblGakseiName');
}

// 学生検索画面（引数：①学籍番号）
function openSubWindow(field1) {
  var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
    + "?retFieldName="  + field1;
  openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
  return false;
}

// 学生氏名取得
function doGakseiAjax(thisObj, thisEven, targetLabel){
  var servlet = "rev/co/CobGakseiAJAX";
  var args = new Array();

  args['code1'] = thisObj.value;

  var ajaxUtil = new AjaxUtil();
  ajaxUtil.getCodeName(servlet, targetLabel, args);
}

// 全て除外ボタン押下
function confirmMultiDelete(id){
  var length = document.getElementById("form1:htmlGakseiList").length;
  if (length > 0) {
    return confirm(messageCreate(id, new Array("リスト情報")));
  }
  return false;
}

// 除外ボタン押下
function confirmDelete(){
  var length = document.getElementById("form1:htmlGakseiList").length;
  if (length > 0) {
    return true;
  }
  return false;
}

// 確認ダイアログで「ＯＫ」の場合
function confirmOk() {
  addListWarnOK("htmlExecutableBtnAdd1", "register");
}

// 確認ダイアログで「キャンセル」の場合
function confirmCancel() {
  addListWarnCancel("htmlExecutableBtnAdd1");
}

// リストボックス警告データ追加確認ダイアログにて「ＯＫ」押下時の処理
// 追加(個別指定)ボタン押下回数をインクリメントして追加(個別指定)ボタンを押下する。
// 引数1 : hiddenId 追加(個別指定)ボタン押下回数を格納している隠しフィールドＩＤ
// 引数2 : buttonId 追加(個別指定)ボタンＩＤ
function addListWarnOK(hiddenId, buttonId) {

	// 追加(個別指定)ボタン押下回数をインクリメント
	var targetId = "form1:" + hiddenId; 
	var nowReq = document.getElementById(targetId).value;
	var nextReq = eval(nowReq) + 1;
	document.getElementById(targetId).value = nextReq;

	// 追加(個別指定)ボタン押下
	indirectClick(buttonId);
}

// リストボックス警告データ追加確認ダイアログにて「キャンセル」押下時の処理
// 追加(個別指定)ボタン押下回数をインクリメントして追加(個別指定)ボタンを押下する。
// 引数1 : hiddenId 追加(個別指定)ボタン押下回数を格納している隠しフィールドＩＤ
function addListWarnCancel(hiddenId) {

	// 追加(個別指定)ボタン押下回数を初期化(0に設定)
	var targetId = "form1:" + hiddenId; 
	document.getElementById(targetId).value = 0;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xra00401.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xra00401.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xra00401.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xra00401.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="900">
				<TR>
					<TD width="810" align="left">
						<TABLE class="table">
							<TR>
								<TH class="v_a" width="140">
									<h:outputText styleClass="outputText"
										id="lblNyugakNendo"
										style="#{pc_Xra00401.propNyugakNendo.labelStyle}"
										value="#{pc_Xra00401.propNyugakNendo.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:inputText styleClass="inputText"
										id="htmlNyugakNendo"
										disabled="#{pc_Xra00401.propNyugakNendo.disabled}"
										value="#{pc_Xra00401.propNyugakNendo.dateValue}"
										style="#{pc_Xra00401.propNyugakNendo.style}"
										size="10" tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
								</TD>
								<TH class="v_b" width="140">
									<h:outputText styleClass="outputText"
										id="lblNyugakGakkiNo"
										style="#{pc_Xra00401.propNyugakGakkiNo.labelStyle}"
										value="#{pc_Xra00401.propNyugakGakkiNo.labelName}">
									</h:outputText>
								</TH>
								<TD width="260">
									<h:inputText styleClass="inputText"
										id="htmlNyugakGakkiNo"
										disabled="#{pc_Xra00401.propNyugakGakkiNo.disabled}"
										value="#{pc_Xra00401.propNyugakGakkiNo.integerValue}"
										style="#{pc_Xra00401.propNyugakGakkiNo.style}"
										size="5" tabindex="2">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
									</h:inputText>
								</TD>
							</TR>
							<TR>
								<TH nowrap class="v_c">
									<h:outputText styleClass="outputText"
										id="lblHakkoDate"
										value="#{pc_Xra00401.propHakkoDate.labelName}"
										style="#{pc_Xra00401.propHakkoDate.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan=3>
									<h:inputText styleClass="inputText"
										id="htmlHakkoDate"
										disabled="#{pc_Xra00401.propHakkoDate.disabled}"
										value="#{pc_Xra00401.propHakkoDate.dateValue}"
										style="#{pc_Xra00401.propHakkoDate.style}"
										size="12" tabindex="3">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
										<hx:inputHelperDatePicker />
									</h:inputText>
								</TD>
							</TR>
						</TABLE>
					</TD>
					<TD align="left">
						<hx:commandExButton type="submit"
							value="選択" styleClass="commandExButton" id="search"
							action="#{pc_Xra00401.doSearchAction}"
							disabled="#{pc_Xra00401.propSearch.disabled}" tabindex="4">
						</hx:commandExButton>
						<hx:commandExButton type="submit"
							value="解除" styleClass="commandExButton" id="cancel"
							action="#{pc_Xra00401.doCancelAction}"
							disabled="#{pc_Xra00401.propCancel.disabled}" tabindex="5">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<BR>
			<TABLE width="900">
				<TR>
					<TD width="900" align="left">
						<TABLE class="table">
							<TR>
								<TH nowrap class="v_c" width="140">
									<h:outputText styleClass="outputText"
										id="lblSelectInput"
										value="#{pc_Xra00401.propSelectInput.labelName}"
										style="#{pc_Xra00401.propSelectInput.labelStyle}">
									</h:outputText>
								</TH>
								<TD width="665">
									<h:selectOneRadio
										onchange="onCangeData();"
										id="htmlselectInput" disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" tabindex="6"
										disabled="#{pc_Xra00401.propSelectInput.disabled}"
										value="#{pc_Xra00401.propSelectInput.stringValue}">
										<f:selectItem itemValue="1"
											itemLabel="未出力の学籍番号全て" />
										<f:selectItem itemValue="2"
											itemLabel="指定した学生のみ" />
									</h:selectOneRadio>
								</TD>
							</TR>
							<TR>
								<!-- 入力ファイル（ラベル） -->
								<TH class="v_a" nowrap width="140">
									<h:outputText
										styleClass="outputText" id="lblInputFile"
										value="#{pc_Xra00401.propInputFile.name}">
									</h:outputText>
									<BR>
									<!-- 前回ファイル（ラベル） -->
									<h:outputText
										styleClass="outputText" id="lblInputOldFile"
										value="#{pc_Xra00401.propInputFileOld.name}">
									</h:outputText>
								</TH>
								<TD nowrap width="600">
									<!-- 入力ファイル -->
									<hx:fileupload
										styleClass="fileupload" id="htmlInputFile"
										disabled="#{pc_Xra00401.propInputFile.disabled}"
										value="#{pc_Xra00401.propInputFile.value}" size="60"
										style="width: 520px" tabindex="7">
										<hx:fileProp name="fileName"
											value="#{pc_Xra00401.propInputFile.fileName}" />
										<hx:fileProp name="contentType"
											value="#{pc_Xra00401.propInputFile.contentType}" />
									</hx:fileupload>
									<hx:commandExButton type="submit"
										value="取込" styleClass="commandExButton" id="multiRegister"
										disabled="#{pc_Xra00401.propMultiRegister.disabled}"
										action="#{pc_Xra00401.doMultiRegisterAction}" tabindex="8">
									</hx:commandExButton>
									<!-- 前回ファイル -->
									<BR>
									<h:outputText styleClass="outputText" id="htmlInputFileOld"
										value="#{pc_Xra00401.propInputFileOld.value}">
									</h:outputText>
									<BR>
								</TD>
							</TR>
							<TR>
								<TH class="v_b" width="140">
									<!-- 学籍番号（個別） -->
									<h:outputText styleClass="outputText" id="lblGaksekiCd"
										value="#{pc_Xra00401.propGaksekiCd.labelName}">
									</h:outputText>
								</TH>
								<TD nowrap>
									<h:inputText styleClass="inputText"
										id="htmlGaksekiCd" size="18" tabindex="9"
										disabled="#{pc_Xra00401.propGaksekiCd.disabled}"
										value="#{pc_Xra00401.propGaksekiCd.value}"
										maxlength="#{pc_Xra00401.propGaksekiCd.maxLength}"
										onblur="return doGakseiAjax(this, event, 'form1:lblGakseiName');">
										<hx:inputHelperAssist errorClass="inputText_Error" imeMode="disabled" />
									</h:inputText>
									<hx:commandExButton type="button"
										value="検索" styleClass="commandExButton_search"
										id="btnGakusekiCd" tabindex="10"
										disabled="#{pc_Xra00401.propGaksekiCd.disabled}"
										onclick="openSubWindow('form1:htmlGaksekiCd');">
									</hx:commandExButton>
									<hx:commandExButton styleClass="commandExButton" type="submit"
										id="register" value="追加" tabindex="11"
										disabled="#{pc_Xra00401.propRegister.disabled}"
										action="#{pc_Xra00401.doRegisterAction}">
									</hx:commandExButton>
									<h:inputText styleClass="likeOutput" id="lblGakseiName"
										size="40" tabindex="-1"
										value="#{pc_Xra00401.propGakseiName.value}"
										readonly="#{pc_Xra00401.propGakseiName.readonly}">
									</h:inputText>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD width="800" align="right">
					</TD>
				</TR>
			</TABLE>
			<!-- 対象学生一覧 -->
			<TABLE border="0" padding="0" cellspacing="0" width="600">
				<TBODY>
					<TR>
						<TD width="410" align="left">
							<h:outputText styleClass="outputText" id="lblGakseiList"
								value="#{pc_Xra00401.propGakseiList.labelName}">
							</h:outputText>
						</TD>
						<TD width="90">
						</TD>
					</TR>
					<TR>
						<TD>
							<h:selectManyListbox styleClass="selectManyListbox"
								style="width:100%" id="htmlGakseiList" size="16"
								value="#{pc_Xra00401.propGakseiList.integerValue}"
								tabindex="12">
								<f:selectItems value="#{pc_Xra00401.propGakseiList.list}" />
							</h:selectManyListbox>
						</TD>
						<TD align="center" valign="top">
							<hx:commandExButton type="submit" value="除外" 
								styleClass="commandExButton"
								id="delete"
								onclick="return confirmDelete();"
								disabled="#{pc_Xra00401.propDelete.disabled}"
								action="#{pc_Xra00401.doDeleteAction}"
								style="width:60px" tabindex="13">
							</hx:commandExButton>
							<BR>
							<h:outputText styleClass="outputText" id="text6"
								value="(複数選択可)">
							</h:outputText>
							<BR>
							<hx:commandExButton type="submit" value="全て除外"
								styleClass="commandExButton" id="multiDelete"
								disabled="#{pc_Xra00401.propMultiDelete.disabled}"
								action="#{pc_Xra00401.doMultiDeleteAction}"
								style="width:60px" tabindex="14"
								onclick="return confirmMultiDelete('#{msg.SY_MSG_0006W}');">
							</hx:commandExButton>
						</TD>
					</TR>
					<TR>
						<TD align="right">
							<h:outputFormat styleClass="outputFormat" id="format1"
								value="合計件数：{0}件　正常件数：{1}件　エラー件数：{2}件">
								<f:param name="maxCount"
									value="#{pc_Xra00401.propGakseiList.listCount}">
								</f:param>
								<f:param name="nomalCount"
									value="#{pc_Xra00401.propGakseiList.listCount - pc_Xra00401.propErrorCount.value}">
								</f:param>
								<f:param name="errCount"
									value="#{pc_Xra00401.propErrorCount.value}">
								</f:param>
							</h:outputFormat>
						</TD>
						<TD align="center">
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="900" class="button_bar" border="0" cellpadding="0"
				cellspacing="0">
				<TR>
					<TD >
						<hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfout"
							disabled="#{pc_Xra00401.propPdfout.disabled}"
							style="#{pc_Xra00401.propPdfout.style}"
							action="#{pc_Xra00401.doPdfoutAction}"
							confirm="#{msg.SY_MSG_0019W}" tabindex="15">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="印刷"
							styleClass="commandExButton_out" id="print"
							disabled="#{pc_Xra00401.propPrint.disabled}" 
							style="#{pc_Xra00401.propPrint.style}" 
							action="#{pc_Xra00401.doPrintAction}"
							confirm="#{msg.SY_MSG_0022W}" tabindex="16">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<h:inputHidden id="htmlExecutableBtnAdd1" value="#{pc_Xra00401.propExecutableBtnAdd1.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

