<%-- 
	入出庫先種別登録
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Smp00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1";
	indirectClick('delete');
	return true;	
}
function confirmCancel() {
	return false;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrc00301.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrc00301.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrc00301.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrc00301.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<BR>
			<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 -->
			<TABLE border="0" width="780">
				<TBODY>
					<TR>
						<TD  align="right"><h:outputText styleClass="outputText" id="text8"
							style="font-size: 8pt"
							value="#{pc_Xrc00301.propNskSbtList.listCount == null ? 0 : pc_Xrc00301.propNskSbtList.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text21" style="font-size: 8pt"
							value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="780">
				<TBODY>
					<TR>
						<TD>
						<DIV class="listScroll" style="height:229px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							styleClass="meisai_scroll" id="htmlNskSbtList"
							value="#{pc_Xrc00301.propNskSbtList.list}" var="varlist"
							rowClasses="#{pc_Xrc00301.propNskSbtList.rowClasses}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="入出庫先種別コード" id="text1"></h:outputText>
								</f:facet>
								<f:attribute value="140" name="width" />
								<h:outputText styleClass="outputText" id="text2"
									value="#{varlist.nskSbtCd}"></h:outputText>
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="入出庫先種別名称" id="text3"></h:outputText>
								</f:facet>
								<f:attribute value="360" name="width" />
								<h:outputText styleClass="outputText" id="text4"
									value="#{varlist.nskSbtName}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								  <h:outputText styleClass="outputText" value="入出庫先種別区分" id="text5"></h:outputText>
								</f:facet>
								<f:attribute value="250" name="width" />
								<hx:jspPanel id="jspPanel2">
									<Table border="0">
										<TBODY>
										<TR>										
										<TD style="border-style:none; text-align:right;" width="20"><h:outputText 
										   styleClass="outputText" id="text6" value="#{varlist.nskSbtKbn}"></h:outputText></TD>
									<TD style="border-top-style:none; border-bottom-style: none; border-right-style: none;">
									<h:outputText styleClass="outputText" id="text7" value="#{varlist.nskSbtKbnName}"></h:outputText></TD>
										</TR>
										<TBODY>
									</Table>	
								</hx:jspPanel>
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="select" value="選択" action="#{pc_Xrc00301.doSelectAction}"></hx:commandExButton>
								<f:attribute value="30" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			<TABLE border="0" class="table" cellspacing="0" cellpadding="0" width="780px">
				<TBODY>
					<TR>
						<TH class="v_b" width="200"><h:outputText
							styleClass="outputText" id="lblNskSbtCd"
							value="#{pc_Xrc00301.propNskSbtCd.labelName}"
							style="#{pc_Xrc00301.propNskSbtCd.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlNskSbtCd" size="18"
							maxlength="#{pc_Xrc00301.propNskSbtCd.maxLength}"
							style="#{pc_Xrc00301.propNskSbtCd.style}"
							value="#{pc_Xrc00301.propNskSbtCd.stringValue}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="200"><h:outputText
							styleClass="outputText" id="lblNskSbtName"
							value="#{pc_Xrc00301.propNskSbtName.labelName}"
							style="#{pc_Xrc00301.propNskSbtName.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlNskSbtName" size="80"
							maxlength="#{pc_Xrc00301.propNskSbtName.maxLength}"
							style="#{pc_Xrc00301.propNskSbtName.style}"
							value="#{pc_Xrc00301.propNskSbtName.stringValue}">
							<hx:inputHelperAssist errorClass="inputText_Error" />
						</h:inputText></TD>
					</TR>
					<TR>
						<TH width="200" class="v_a"><h:outputText
							styleClass="outputText" id="lblNskSbtKbn"
							value="#{pc_Xrc00301.propNskSbtKbn.labelName}"
							style="#{pc_Xrc00301.propNskSbtKbn.labelStyle}"></h:outputText></TH>
						<TD>
						<h:selectOneMenu styleClass="selectOneMenu"
							id="htmlNskSbtKbnList" 
							value="#{pc_Xrc00301.propNskSbtKbn.stringValue}"
							style="#{pc_Xrc00301.propNskSbtKbn.style};width:220px">
							<f:selectItems value="#{pc_Xrc00301.propNskSbtKbn.list}" />
						</h:selectOneMenu>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>
			<HR noshade width="100%" class="hr">
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center">
							<hx:commandExButton type="submit" value="確定"
								styleClass="commandExButton_dat" id="register" 
								action="#{pc_Xrc00301.doRegisterAction}" 
								confirm="#{msg.SY_MSG_0001W}">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除"
								styleClass="commandExButton_dat" id="delete"
								action="#{pc_Xrc00301.doDeleteAction1}"
								confirm="#{msg.SY_MSG_0004W}">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア"
								styleClass="commandExButton_etc" id="clear"
								action="#{pc_Xrc00301.doClearAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden
				value="#{pc_Xrc00301.propExecutable.integerValue}"
				id="htmlExecutable">
				<f:convertNumber />
			</h:inputHidden>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
<h:inputHidden value="#{pc_Xrc00301.propNskSbtList.scrollPosition}"
				id="scroll"></h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

