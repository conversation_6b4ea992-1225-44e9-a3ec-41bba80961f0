<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00402T03.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">
 	//	画面ロード時の処理
	function formLoad(thisEvent) {
		//	試験地名称再表示
		doSikentiAjax(document.getElementById('form1:htmlSikentiCd'),
			thisEvent, 'form1:lblSikentiName');
	    setZipMenu('form1:htmlYubinNo','form1:htmlAddress1','');
	}
	
	//	試験地名称表示（Ajax）
	function doSikentiAjax( thisObj, thisEvent, target ) {
  		var servlet = "rev/xrh/XrhMeiSikentiNmAJAX";
  		getCodeName(servlet, target, thisObj.value);
	}

	//	試験地名称検索画面
	function openSikentiSearchWindow(thisObj, thisEvent) {
		var url="${pageContext.request.contextPath}/faces/rev/xrh/pXrh0101.jsp?retFieldName=form1:htmlSikentiCd";
		openModalWindow(url, "pXrh0101", "<%= com.jast.gakuen.rev.xrh.PXrh0101.getWindowOpenOption() %>");
		return true;
	}

	// 戻るボタン押下時処理
	function onClickReturnDisp(id) {
		var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
		if(changeDataFlg == "1"){
			return confirm(id);
		}
		
		return true;
	}
	
	// データ変更時
	function onChangeData() {
		document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
	}

	//	郵便番号検索	
	function func_1(thisObj, thisEvent) {
		var zipNo = document.getElementById("form1:htmlYubinNo").value;
		zipNo = encodeURIComponent(zipNo);
		var add = document.getElementById("form1:htmlAddress1").value;
		add = encodeURIComponent(add);
		var url="${pageContext.request.contextPath}/faces/rev/co/pCoz0801.jsp"
			   +"?"
	  		   +"zipNo=form1:htmlYubinNo"
	  		   +"&"
	  		   +"zipNoValue="+zipNo
	  		   +"&"
	  		   +"jyusyoKanji=form1:htmlAddress1";
		openModalWindow(url, "PCoz0801", "<%= com.jast.gakuen.rev.co.PCoz0801.getWindowOpenOption() %>");
		return true;
	}
	
	// ポップアップメッセージを表示
	function doPopupMsg(id, msg) {
		var args = new Array();
		args[0] = msg;
		if (confirm(messageCreate(id, args))) {
			onChangeData();
			return true;
		}
	  
		return false;
	}

	// 登録ボタンクリック：OK選択時の処理
	function confirmOk() {
		document.getElementById('form1:propExecutableRegister').value = 1;
		indirectClick('register');
	}

	// 登録ボタンクリック：キャンセル選択時の処理
	function confirmCancel() {
		return;
	}
	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="formLoad(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00402T03.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00402T03.xrh00402.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00402T03.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00402T03.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"><hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="53"
						action="#{pc_Xrh00402T03.xrh00402.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
					</hx:commandExButton></TD>
				</TR>
			</TABLE>
			<!-- ↑ここにボタンを配置 --></DIV>

			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="800" valign="top"><!-- ↓タブ間共有テーブル↓ -->
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="190"><!--年度 --> <h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Xrh00402T03.xrh00402.propNendo.labelName}"
										style="#{pc_Xrh00402T03.xrh00402.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNendo" size="4"
										value="#{pc_Xrh00402T03.xrh00402.propNendo.dateValue}"
										disabled="#{pc_Xrh00402T03.xrh00402.propNendo.disabled}"
										style="#{pc_Xrh00402T03.xrh00402.propNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TD width="100" align="right" rowspan="2"
										style="background-color: transparent; text-align: right"
										class="clear_border"><hx:commandExButton type="submit"
										value="#{pc_Xrh00402T03.xrh00402.propSelect.caption}"
										styleClass="commandExButton" id="select"
										disabled="#{pc_Xrh00402T03.xrh00402.propSelect.disabled}"
										rendered="#{pc_Xrh00402T03.xrh00402.propSelect.rendered}"
										style="#{pc_Xrh00402T03.xrh00402.propSelect.style}"
										action="#{pc_Xrh00402T03.xrh00402.doSelectAction}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="#{pc_Xrh00402T03.xrh00402.propUnSelect.caption}"
										styleClass="commandExButton" id="unselect"
										disabled="#{pc_Xrh00402T03.xrh00402.propUnSelect.disabled}"
										rendered="#{pc_Xrh00402T03.xrh00402.propUnSelect.rendered}"
										style="#{pc_Xrh00402T03.xrh00402.propUnSelect.style}"
										action="#{pc_Xrh00402T03.xrh00402.doUnSelectAction}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenKaisu"
										style="#{pc_Xrh00402T03.xrh00402.propSikenKaisu.labelStyle}"
										value="#{pc_Xrh00402T03.xrh00402.propSikenKaisu.labelName}">
									</h:outputText></TH>
									<TD width="200"><h:inputText id="htmlSikenKaisu"
										styleClass="inputText" size="4"
										value="#{pc_Xrh00402T03.xrh00402.propSikenKaisu.integerValue}"
										disabled="#{pc_Xrh00402T03.xrh00402.propSikenKaisu.disabled}"
										disabled="#{pc_Xrh00402T03.xrh00402.propSikenKaisu.disabled}"
										style="#{pc_Xrh00402T03.xrh00402.propSikenKaisu.style}">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenbiYobi"
										style="#{pc_Xrh00402T03.xrh00402.propSikenbiYoubi.labelStyle}"
										value="#{pc_Xrh00402T03.xrh00402.propSikenbiYoubi.labelName}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSikenbiYoubi"
										value="#{pc_Xrh00402T03.xrh00402.propSikenbiYoubi.stringValue}"
										style="#{pc_Xrh00402T03.xrh00402.propSikenbiYoubi.style};width:128px"
										disabled="#{pc_Xrh00402T03.xrh00402.propSikenbiYoubi.disabled}">
										<f:selectItems
											value="#{pc_Xrh00402T03.xrh00402.propSikenbiYoubi.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						<!-- ↑タブ間共有テーブル↑ --> <BR>
						<TABLE border="0" cellpadding="20" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="800" align="left">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="日程・時間割" id="moveNiteiJikanTab"
													disabled="#{pc_Xrh00402T03.xrh00402.propMoveNiteiJikanTab.disabled}"
													styleClass="tab_head_off" action="Xrh00402T01">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="場所情報" styleClass="tab_head_off" id="moveLocateTab"
													disabled="#{pc_Xrh00402T03.xrh00402.propMoveLocateTab.disabled}"
													action="Xrh00402T02">
												</hx:commandExButton></TD>
												<TD class="tab_head_on"><hx:commandExButton type="button"
													value="試験監督" styleClass="tab_head_on"
													disabled="#{pc_Xrh00402T03.xrh00402.propMoveSikenKantokuTab.disabled}"
													id="moveSikenKantokuTab">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="作問情報" styleClass="tab_head_off" id="moveSakumonTab"
													disabled="#{pc_Xrh00402T03.xrh00402.propMoveSakumonTab.disabled}"
													action="Xrh00402T04">
												</hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD valign="top">
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="100%">
										<TBODY>
											<TR>
												<TD>
												<CENTER><BR>
												<TABLE border="0" cellpadding="5">
													<TBODY>
														<TR>
															<TD>
															<DIV id="listScroll" class="listScroll"
																style="height: 128px;"
																onscroll="setScrollPosition('scroll',this);"><h:dataTable
																columnClasses="columnClass" headerClass="headerClass"
																footerClass="footerClass"
																rowClasses="#{pc_Xrh00402T03.propSikenKantokuList.rowClasses}"
																styleClass="meisai_scroll" id="htmlSikenKantokuList"
																value="#{pc_Xrh00402T03.propSikenKantokuList.list}"
																var="varlist">

																<h:column id="column1">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="試験地"
																			id="lblListSikentiColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.sikentiName.displayValue}"
																		title="#{varlist.sikentiName.stringValue}"
																		id="htmlListSikenti">
																	</h:outputText>
																	<f:attribute value="80" name="width" />
																	<f:attribute value="true" name="nowrap" />
																</h:column>


																<h:column id="column2">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="監督業務委託先"
																			id="lblListItakusakiColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.itakuKantoku.displayValue}"
																		title="#{varlist.itakuKantoku.stringValue}"
																		id="htmlListItakusaki">
																	</h:outputText>
																	<f:attribute value="150" name="width" />
																	<f:attribute value="true" name="nowrap" />
																</h:column>


																<h:column id="column3">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="住所"
																			id="lblListAddressColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.itakuAddrAll.displayValue}"
																		title="#{varlist.itakuAddrAll.stringValue}"
																		id="htmlListAddress">
																	</h:outputText>
																	<f:attribute value="400" name="width" />
																	<f:attribute value="true" name="nowrap" />
																</h:column>

																<h:column id="column4">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="電話番号１"
																			id="lblListTelCol1umn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.itakuTel1.stringValue}"
																		id="htmlListTel">
																	</h:outputText>
																	<f:attribute value="100" name="width" />
																	<f:attribute value="true" name="nowrap" />
																</h:column>

																<h:column id="column5">
																	<f:facet name="header">
																	</f:facet>
																	<hx:commandExButton type="submit"
																		value="#{varlist.btnEdit.caption}"
																		action="#{pc_Xrh00402T03.doEditAction}"
																		styleClass="commandExButton" id="edit">
																	</hx:commandExButton>
																	<f:attribute value="32" name="width" />
																	<f:attribute value="true" name="nowrap" />
																</h:column>
															</h:dataTable></DIV>
															</TD>
														</TR>
													</TBODY>
												</TABLE>

												<BR>
												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="table">
													<TBODY>
														<TR>
															<TH nowrap class="v_a" width="150"><!--試験地 --> <h:outputText
																styleClass="outputText" id="lblSikentiCd"
																value="#{pc_Xrh00402T03.propSikentiCd.labelName}"
																style="#{pc_Xrh00402T03.propSikentiCd.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSikentiCd" size="4" onchange="onChangeData();"
																value="#{pc_Xrh00402T03.propSikentiCd.stringValue}"
																onblur="return doSikentiAjax(this, event, 'form1:lblSikentiName');"
																disabled="#{pc_Xrh00402T03.propSikentiCd.disabled}"
																maxlength="#{pc_Xrh00402T03.propSikentiCd.maxLength}"
																style="#{pc_Xrh00402T03.propSikentiCd.style}">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText> <hx:commandExButton type="button"
																styleClass="commandExButton_search" id="searchSikenti"
																disabled="#{pc_Xrh00402T03.propSearchSikenti.disabled}"
																onclick="return openSikentiSearchWindow(this, event);">
															</hx:commandExButton> <h:outputText
																styleClass="outputText" id="lblSikentiName"
																value="#{pc_Xrh00402T03.propSikentiName.labelName}"
																style="#{pc_Xrh00402T03.propSikentiName.labelStyle}">
															</h:outputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="150"><!--監督業務委託先 --> <h:outputText
																styleClass="outputText" id="lblItakusaki"
																value="#{pc_Xrh00402T03.propItakusaki.labelName}"
																style="#{pc_Xrh00402T03.propItakusaki.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlItakusaki" size="40" onchange="onChangeData();"
																value="#{pc_Xrh00402T03.propItakusaki.stringValue}"
																disabled="#{pc_Xrh00402T03.propItakusaki.disabled}"
																maxlength="#{pc_Xrh00402T03.propItakusaki.maxLength}"
																style="#{pc_Xrh00402T03.propItakusaki.style}">
															</h:inputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="150"><!--郵便番号 --> <h:outputText
																styleClass="outputText" id="lblYubinNo"
																value="#{pc_Xrh00402T03.propYubinNo.labelName}"
																style="#{pc_Xrh00402T03.propYubinNo.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlYubinNo" size="10" onchange="onChangeData();"
																value="#{pc_Xrh00402T03.propYubinNo.stringValue}"
																disabled="#{pc_Xrh00402T03.propYubinNo.disabled}"
																maxlength="#{pc_Xrh00402T03.propYubinNo.maxLength}"
																style="#{pc_Xrh00402T03.propYubinNo.style}">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText> <hx:commandExButton type="button"
																styleClass="commandExButton_search" id="searchYubinNo"
																disabled="#{pc_Xrh00402T03.propSearchYubinNo.disabled}"
																onclick="return func_1(this,event);">
															</hx:commandExButton></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="150"><!--住所１ --> <h:outputText
																styleClass="outputText" id="lblAddress1"
																value="#{pc_Xrh00402T03.propAddress1.labelName}"
																style="#{pc_Xrh00402T03.propAddress1.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlAddress1" size="40" onchange="onChangeData();"
																maxlength="#{pc_Xrh00402T03.propAddress1.maxLength}"
																value="#{pc_Xrh00402T03.propAddress1.stringValue}"
																disabled="#{pc_Xrh00402T03.propAddress1.disabled}"
																style="#{pc_Xrh00402T03.propAddress1.style}">
															</h:inputText> <h:outputText styleClass="outputText"
																value="(都道府県市町村大字)" /></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="150"><!--住所２ --> <h:outputText
																styleClass="outputText" id="lblAddress2"
																value="#{pc_Xrh00402T03.propAddress2.labelName}"
																style="#{pc_Xrh00402T03.propAddress2.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlAddress2" size="40" onchange="onChangeData();"
																maxlength="#{pc_Xrh00402T03.propAddress2.maxLength}"
																value="#{pc_Xrh00402T03.propAddress2.stringValue}"
																disabled="#{pc_Xrh00402T03.propAddress2.disabled}"
																style="#{pc_Xrh00402T03.propAddress2.style}">
															</h:inputText> <h:outputText styleClass="outputText"
																value="(丁目・字以降)" /></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="150"><!--住所３ --> <h:outputText
																styleClass="outputText" id="lblAddress3"
																value="#{pc_Xrh00402T03.propAddress3.labelName}"
																style="#{pc_Xrh00402T03.propAddress3.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlAddress3" size="40" onchange="onChangeData();"
																maxlength="#{pc_Xrh00402T03.propAddress3.maxLength}"
																value="#{pc_Xrh00402T03.propAddress3.stringValue}"
																disabled="#{pc_Xrh00402T03.propAddress3.disabled}"
																style="#{pc_Xrh00402T03.propAddress3.style}">
															</h:inputText> <h:outputText styleClass="outputText"
																value="(マンション／ビル名　号室)" /></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="150"><!--電話番号１ --> <h:outputText
																styleClass="outputText" id="lblTel1"
																value="#{pc_Xrh00402T03.propTel1.labelName}"
																style="#{pc_Xrh00402T03.propTel1.labelStyle}">
															</h:outputText></TH>
															<TD><h:inputText styleClass="inputText" id="htmlTel1"
																size="10" value="#{pc_Xrh00402T03.propTel1.stringValue}"
																onchange="onChangeData();"
																maxlength="#{pc_Xrh00402T03.propTel1.maxLength}"
																disabled="#{pc_Xrh00402T03.propTel1.disabled}"
																style="#{pc_Xrh00402T03.propTel1.style}">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText></TD>
															<TH nowrap class="v_a" width="110"><!--電話番号２ --> <h:outputText
																styleClass="outputText" id="lblTel2"
																value="#{pc_Xrh00402T03.propTel2.labelName}"
																style="#{pc_Xrh00402T03.propTel2.labelStyle}">
															</h:outputText></TH>
															<TD><h:inputText styleClass="inputText" id="htmlTel2"
																size="10" value="#{pc_Xrh00402T03.propTel2.stringValue}"
																onchange="onChangeData();"
																maxlength="#{pc_Xrh00402T03.propTel2.maxLength}"
																disabled="#{pc_Xrh00402T03.propTel2.disabled}"
																style="#{pc_Xrh00402T03.propTel2.style}">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText></TD>
														</TR>
													</TBODY>
												</TABLE>

												<BR>

												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="button_bar">
													<TBODY>
														<TR>
															<TD><hx:commandExButton type="submit"
																action="#{pc_Xrh00402T03.doRegisterAction}"
																confirm="#{msg.SY_MSG_0002W}"
																styleClass="commandExButton_dat" id="register"
																value="確定"
																disabled="#{pc_Xrh00402T03.propRegister.disabled}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																action="#{pc_Xrh00402T03.doDeleteAction}"
																onclick="return confirm('#{msg.SY_MSG_0004W}');"
																styleClass="commandExButton_dat" id="delete" value="削除"
																disabled="#{pc_Xrh00402T03.propDelete.disabled}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																action="#{pc_Xrh00402T03.doClearAction}"
																onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '表示内容');"
																styleClass="commandExButton_dat" id="clear" value="クリア"
																disabled="#{pc_Xrh00402T03.propClear.disabled}">
															</hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>



												</CENTER>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<h:inputHidden id="htmlHidChangeDataFlg"
				value="#{pc_Xrh00402T03.propHidChangeDataFlg.stringValue}"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrf00601.propExecutableRegister.integerValue}"
				id="propExecutableRegister"></h:inputHidden> <h:inputHidden
				value="#{pc_Xrh00402T03.propSikenKantokuList.scrollPosition}"
				id="scroll"></h:inputHidden></DIV>

			</DIV>

		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
	changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
