<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function confirmOk() {

	document.getElementById('form1:htmlConfirmVal').value = "1";

	 indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlConfirmVal').value = "0";
	return false;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz00401.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz00401.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz00401.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz00401.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><BR>
			<TABLE border="0" width="512" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxOne"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxOne.labelName}"
							 style="#{pc_Ssz00401.propSihonkinKaisoMaxOne.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxOne"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxOne.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxOne.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxOne.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text3" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxTwo"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxTwo.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxTwo.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxTwo"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxTwo.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxTwo.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxTwo.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text10" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxThree"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxThree.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxThree.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxThree"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxThree.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxThree.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxThree.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text11" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_d" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxFour"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxFour.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxFour.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxFour"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxFour.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxFour.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxFour.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text12" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_e" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxFive"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxFive.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxFive.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxFive"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxFive.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxFive.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxFive.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text25" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_f" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxSix"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxSix.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxSix.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxSix"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxSix.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxSix.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxSix.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text26" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_g" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxSeven"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxSeven.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxSeven.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxSeven"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxSeven.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxSeven.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxSeven.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text27" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_a" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxEight"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxEight.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxEight.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxEight"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxEight.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxEight.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxEight.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text28" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxNine"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxNine.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxNine.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxNine"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxNine.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxNine.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxNine.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text29" value="百万円未満"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="223"><h:outputText styleClass="outputText"
							id="lblSihonkinKaisoMaxTen"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxTen.labelName}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxTen.labelStyle}"></h:outputText></TH>
						<TD width="281"><h:inputText styleClass="inputText"
							id="htmlSihonkinKaisoMaxTen"
							value="#{pc_Ssz00401.propSihonkinKaisoMaxTen.stringValue}"
							maxlength="#{pc_Ssz00401.propSihonkinKaisoMaxTen.maxLength}"
							style="#{pc_Ssz00401.propSihonkinKaisoMaxTen.style}"></h:inputText><h:outputText
							styleClass="outputText" id="text30" value="百万円未満"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
						<TABLE border="0" class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit"
										value="確定" styleClass="commandExButton_dat" id="register"
										action="#{pc_Ssz00401.doRegisterAction}" confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ssz00401.propConfirmVal.stringValue}" id="htmlConfirmVal"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

