<%-- 
	証明書発行番号パターン設定
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrk/Xrk00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrk00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrk00101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrk00101.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrk00101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrk00101.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
<!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="700">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText
							styleClass="outputText" value="#{pc_Xrk00101.propXrkSyomPtn.listCount == null ? 0 : pc_Xrk00101.propXrkSyomPtn.listCount}" style="font-size: 8pt"></h:outputText><h:outputText
							styleClass="outputText" id="htmlCountlbl" value="件" style="font-size: 8pt"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" width="700">
				<TBODY>
					<TR>
						<TD>

						<div class="listScroll" style="height:254px;" id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrk00101.propXrkSyomPtn.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xrk00101.propXrkSyomPtn.list}" var="varlist">
							
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblNo_head" styleClass="outputText"
										value="パターンＮＯ">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblNo"
									value="#{varlist.patternNo}"></h:outputText>
								<f:attribute value="90" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="lblName_head" styleClass="outputText"
										value="パターン名称">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblName"
									value="#{varlist.propPatternName.stringValue}"></h:outputText>
								<f:attribute value="360" name="width" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="パターン区分"
										id="lblKbn_head"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblKbn"
									value="#{varlist.patternKbn}"></h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="最終発行番号"
										id="lblNoLst_head"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="lblNoLst"
									value="#{varlist.hakkoNoLst}"></h:outputText>
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>

							<h:column id="column5">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="30" name="width">
								</f:attribute>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style">
								</f:attribute>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Xrk00101.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" class="table" width="700" cellpadding="0"
				cellspacing="0">
				<TBODY>
					<TR>
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPatternNo"
							value="#{pc_Xrk00101.propPatternNo.labelName}"
							style="#{pc_Xrk00101.propPatternNo.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlPatternNo"
							value="#{pc_Xrk00101.propPatternNo.integerValue}"
							maxlength="#{pc_Xrk00101.propPatternNo.maxLength}" size="2"
							style="#{pc_Xrk00101.propPatternNo.style}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					
					<TR>
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPatternName" 
							value="#{pc_Xrk00101.propPatternName.labelName}"
							style="#{pc_Xrk00101.propPatternName.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlPatternName"
							value="#{pc_Xrk00101.propPatternName.stringValue}"
							maxlength="#{pc_Xrk00101.propPatternName.maxLength}" size="40"
							style="#{pc_Xrk00101.propPatternName.style}">
							<hx:inputHelperAssist errorClass="inputText_Error"/>
						</h:inputText></TD>
					</TR>
					
					<TR>
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblHakkoNoMin" 
							value="#{pc_Xrk00101.propHakkoNoMin.labelName}"
							style="#{pc_Xrk00101.propHakkoNoMin.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlHakkoNoMin"
							value="#{pc_Xrk00101.propHakkoNoMin.integerValue}"
							maxlength="#{pc_Xrk00101.propHakkoNoMin.maxLength}" size="7"
							style="#{pc_Xrk00101.propHakkoNoMin.style}">
							<f:convertNumber type="number" pattern="######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>

					<TR>
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblHakkoNoMax" 
							value="#{pc_Xrk00101.propHakkoNoMax.labelName}"
							style="#{pc_Xrk00101.propHakkoNoMax.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlHakkoNoMax"
							value="#{pc_Xrk00101.propHakkoNoMax.integerValue}"
							maxlength="#{pc_Xrk00101.propHakkoNoMax.maxLength}" size="7"
							style="#{pc_Xrk00101.propHakkoNoMax.style}">
							<f:convertNumber type="number" pattern="######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					
					<TR>
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblHakkoNoLst" 
							value="#{pc_Xrk00101.propHakkoNoLst.labelName}"
							style="#{pc_Xrk00101.propHakkoNoLst.labelStyle}"></h:outputText></TH>
						<TD width="500"><h:inputText styleClass="inputText"
							id="htmlHakkoNoLst"
							value="#{pc_Xrk00101.propHakkoNoLst.integerValue}"
							maxlength="#{pc_Xrk00101.propHakkoNoLst.maxLength}" size="7"
							style="#{pc_Xrk00101.propHakkoNoLst.style}">
							<f:convertNumber type="number" pattern="######0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
					</TR>
					
					<TR>
						<TH class="v_e" width="200"><h:outputText
				styleClass="outputText" id="lblNendoKanri" value="年度管理"></h:outputText></TH>
						<TD width="500"><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlNendoKanriFlg"
							value="#{pc_Xrk00101.propNendoKanriFlg.checked}"></h:selectBooleanCheckbox>
						<h:outputText styleClass="outputText" id="lblNendoKanriFlg" value="実施する"></h:outputText></TD>
					</TR>

					<TR>
						<TH class="v_d" width="200"><h:outputText
							styleClass="outputText" id="lblPatternKbn" 
							value="#{pc_Xrk00101.propPatternKbn.labelName}"
							style="#{pc_Xrk00101.propPatternKbn.labelStyle}"></h:outputText></TH>
						<TD  colspan="2"><h:selectOneRadio
							disabledClass="selectOneRadio_Disabled"
							styleClass="selectOneRadio" id="htmlPatternKbn"
							value="#{pc_Xrk00101.propPatternKbn.value}">
							<f:selectItem itemValue="1" itemLabel="証明書" />
							<f:selectItem itemValue="2" itemLabel="学割" />
						</h:selectOneRadio></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register" 
							action="#{pc_Xrk00101.doRegisterAction}" 
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}" 
							action="#{pc_Xrk00101.doDeleteAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrk00101.doClearAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrk00101.propXrkSyomPtn.scrollPosition}"
				id="scroll"></h:inputHidden>
			<hx:inputHelperSetFocus target="htmlPatternNo"></hx:inputHelperSetFocus>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

