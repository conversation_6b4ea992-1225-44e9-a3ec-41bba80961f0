<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrk/Xrk00601.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>

<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrk00601.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrk00601.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrk00601.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrk00601.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrk00601.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<CENTER>
			<TABLE border="0" width="650" cellpadding="0" cellspacing="0"
				class="table">
				<TBODY>
					<TR>
						<TH nowrap class="v_a" width="200"><h:outputText
							styleClass="outputText" id="lblOutputKikan"
							value="抽出対象期間"></h:outputText></TH>
						<TD nowrap width="450"><h:inputText styleClass="inputText"
								id="htmlKikanFrom" size="12"
								value="#{pc_Xrk00601.propKikanFrom.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" />
							</h:inputText>&nbsp;～
							<h:inputText styleClass="inputText"
								id="htmlKikanTo" size="12"
								value="#{pc_Xrk00601.propKikanTo.dateValue}">
								<f:convertDateTime />
								<hx:inputHelperDatePicker />
								<hx:inputHelperAssist errorClass="inputText_Error"promptCharacter="_" />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH nowrap class="v_c"><h:outputText styleClass="outputText"
							id="lblSyoriKbn" value="証明書発行記録出力済み"></h:outputText></TH>
						<TD nowrap><h:selectBooleanCheckbox
							styleClass="selectBooleanCheckbox" id="htmlSyoriKbn"
							value="#{pc_Xrk00601.propSyoriKbn.checked}"></h:selectBooleanCheckbox>
						</TD>
					</TR>
					<TR>
						<TH nowrap width="30%" class="v_d"><h:outputText styleClass="outputText"
							id="lblPtnKbn" value="パターン区分"
							style="#{pc_Xrk00601.propPtnKbn.labelStyle}"></h:outputText></TH>
						<TD nowrap>
							<h:selectOneRadio styleClass="selectOneRadio"
								id="htmlPtnKbn"
								value="#{pc_Xrk00601.propPtnKbn.stringValue}">
								<f:selectItem itemValue="1" itemLabel="証明書" />
								<f:selectItem itemValue="2" itemLabel="学割" />
							</h:selectOneRadio>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR noshade class="hr">
			<TABLE border="0" cellpadding="0" cellspacing="0" width="650"
				class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="CSV出力"
							styleClass="commandExButton_dat" id="exec"
							action="#{pc_Xrk00601.doExecAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</CENTER>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

