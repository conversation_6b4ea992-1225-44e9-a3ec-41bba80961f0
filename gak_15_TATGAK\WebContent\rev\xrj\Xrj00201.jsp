<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrj/Xrj00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@page import="com.jast.gakuen.rev.xrj.Xrj00201"; %>
<%@page import="com.jast.gakuen.framework.util.UtilSystem";%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrl00701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	return true;
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrj00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrj00201.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrj00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrj00201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
				<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="table">
					<TBODY>				
						<TR>
							<TH class="v_a" style="table" width="152"><h:outputText
								styleClass="outputText" id="lblSotyoTeiYM"
								value="#{pc_Xrj00201.propSotyoTeiYM.labelName}"
								style="#{pc_Xrj00201.propSotyoTeiYM.labelStyle}"></h:outputText></TH>
							<TD width="450"><h:inputText id="htmlSotyoTeiYM"
								styleClass="inputText"
								style="#{pc_Xrj00201.propSotyoTeiYM.style}"
								value="#{pc_Xrj00201.propSotyoTeiYM.dateValue}"
								maxlength="#{pc_Xrj00201.propSotyoTeiYM.maxLength}"
								size="9" tabindex="1">
								<hx:inputHelperAssist errorClass="inputText_Error"
									imeMode="inactive" promptCharacter="_" />
								<f:convertDateTime pattern="yyyy/MM" />
							</h:inputText></TD>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE width="600" border="0" cellpadding="0" cellspacing="0" class="table" style="margin-top:10px">
					<TBODY>	
						<TR>
							<TH class="v_b" width="150"><h:outputText styleClass="outputText"
								id="lblGpaValFrom" value="#{pc_Xrj00201.propGpaValFrom.labelName}"
								></h:outputText></TH>
							<TD align="left"><h:inputText id="htmlGpaValFrom"
								styleClass="inputText"
								tabindex="2"
								style="#{pc_Xrj00201.propGpaValFrom.style}"
								value="#{pc_Xrj00201.propGpaValFrom.doubleValue}"
								maxlength="9" size="9">
								<f:convertNumber type="number" pattern="##0.0####"/>
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" /></h:inputText>
								<h:outputText styleClass="outputText" value="以上～"></h:outputText>
								<h:inputText id="htmlGpaValTo"
								styleClass="inputText"
								tabindex="3"
								style="#{pc_Xrj00201.propGpaValTo.style}"
								value="#{pc_Xrj00201.propGpaValTo.doubleValue}"
								maxlength="9" size="9">
								<f:convertNumber type="number" pattern="##0.0####"/>
								<hx:inputHelperAssist errorClass="inputText_Error"
								promptCharacter="_" /></h:inputText>
								<h:outputText styleClass="outputText" value="以下"></h:outputText>
							</TD>
						</TR>
						<TR height="60">
							<TH class="v_c" width="150"><h:outputText styleClass="outputText"
								value="出力対象指定"></h:outputText></TH>
							<TD align="left"><h:selectBooleanCheckbox tabindex="4"
								styleClass="selectBooleanCheckbox" id="htmlOutputTarget1" value="#{pc_Xrj00201.propOutputTarget1.checked}"></h:selectBooleanCheckbox>
								<h:outputText styleClass="outputText" value="再入学生を含む"></h:outputText><BR>
								<h:selectBooleanCheckbox tabindex="5"
								styleClass="selectBooleanCheckbox" id="htmlOutputTarget2" value="#{pc_Xrj00201.propOutputTarget2.checked}"></h:selectBooleanCheckbox>
								<h:outputText styleClass="outputText" value="編入生を含む"></h:outputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="650" style="margin-top:50px">
					<TBODY>
						<TR><TD><HR></TD></TR>
						<TR height="80">
							<TD align="center" valign="middle"><hx:commandExButton
								tabindex="6"
								type="submit" value="PDF作成" styleClass="commandExButton_out"
								id="pdfout" action="#{pc_Xrj00201.doPdfoutAction}"
								confirm="#{msg.SY_MSG_0019W}"
								style="margin-right:6px"></hx:commandExButton><hx:commandExButton
								tabindex="7"
								type="submit" value="CSV作成" styleClass="commandExButton_out"
								id="csvout" action="#{pc_Xrj00201.doCsvoutAction}"
								confirm="#{msg.SY_MSG_0020W}"></hx:commandExButton><hx:commandExButton
								tabindex="8"
								type="submit" value="出力項目指定" styleClass="commandExButton_out"
								id="setoutput" action="#{pc_Xrj00201.doSetoutputAction}"
								onclick="return func_1(this, event);"
								style="margin-left:6px"></hx:commandExButton>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

