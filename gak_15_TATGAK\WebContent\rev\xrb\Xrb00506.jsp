<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00506.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00506.jsp</TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	

<SCRIPT type="text/javascript">
function openKamokuSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;

	var reOpenSubWinFlg = "0";
	var ajaxServlet = "rev/xrx/XrxRemoveFromSessionAJAX";
	var args = new Array();
		args['pcClass'] = 'com.jast.gakuen.rev.km.PKmz0101';
		args['motoFuncId'] = '';
	var engine      = new AjaxEngine();
	engine.setCallbackMethod(
		function(value) {
			var windowPointer = openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
			if(reOpenSubWinFlg=="0"){
				focus();
			}
		}
	);
	engine.send(ajaxServlet,null,args);
	return false;
}

function openSikakSubWindow(field1) {
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0201.jsp"
				+ "?retFieldName=" + field1;
	openModalWindow(url, "PKmz0201", "<%=com.jast.gakuen.rev.km.PKmz0201.getWindowOpenOption() %>");
	return false;
}

function doKamokuInfoAjax(thisObj, thisEvent) {
	var servlet = "rev/xrb/XrbRishuInfoAJAX";
    var args = new Array();
    args['kanriNo'] = document.getElementById("form1:htmlKanriNoHidden").value;
    args['kamokuCode'] = thisObj.value;
    args['henkoKbn'] = document.getElementById("form1:htmlHenkoKbn").value;
    args['rishuHoho'] = document.getElementById("form1:htmlRishuHoho").value;
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValueSetMethod(servlet, "", args, "kamokuInfoAjaxCallBack");
}

function kamokuInfoAjaxCallBack(value) {
	if (value['kamokuName'] != "null" && value['kamokuName']) {
		document.getElementById("form1:htmlKamokuName").value = value['kamokuName'];
		document.getElementById("form1:htmlKamokuNameHidden").value = value['kamokuName'];
	} else {
		document.getElementById("form1:htmlKamokuName").value = "";
		document.getElementById("form1:htmlKamokuNameHidden").value = "";
	}
	if (value['taniSuDisp'] != "null" && value['taniSuDisp']) {
		document.getElementById("form1:htmlTaniSu").value = value['taniSuDisp'];
	} else {
		document.getElementById("form1:htmlTaniSu").value = "";
	}
	if (value['textTaniSu'] != "null" && value['textTaniSu']) {
		document.getElementById("form1:htmlTextTaniSuHidden").value = value['textTaniSu'];
	} else {
		document.getElementById("form1:htmlTextTaniSuHidden").value = "";
	}
	if (value['schoolingTaniSu'] != "null" && value['schoolingTaniSu']) {
		document.getElementById("form1:htmlSchoolingTaniSuHidden").value = value['schoolingTaniSu'];
	} else {
		document.getElementById("form1:htmlSchoolingTaniSuHidden").value = "";
	}
	if (value['utiwakeKingaku'] != "null" && value['utiwakeKingaku']) {
		document.getElementById("form1:htmlUtiwakeKingaku").value = value['utiwakeKingaku'];
	} else {
		document.getElementById("form1:htmlUtiwakeKingaku").value = "";
	}
}

function doSikakInfoAjax(thisObj, thisEvent, elementId1, elementId2) {
	var servlet = "rev/km/KmzSkkAJAX";
	var args = new Array();
	args['code'] = thisObj.value;
	var target = elementId1;
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, target, args);
	target = elementId2;
	ajaxUtil.getCodeName(servlet, target, args);
}

function confirmOk() {
	document.getElementById('form1:htmlWarningDialogFlag').value = 1;
	if (document.getElementById('form1:htmlWarningDialogType').value == 0) {
		indirectClick('htmlKakuteiButton');
	} else {
		indirectClick('htmlDeleteButton');
	}
}

function confirmCancel() {
	document.getElementById('form1:htmlWarningDialogFlag').value = 0;
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00506.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
styleClass="commandExButton" id="closeDisp"
action="#{pc_Xrb00506.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00506.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00506.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
 <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
  <TBODY>
   <TR>
    <TD>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD align="right">
         <hx:commandExButton
          type="submit"
          value="戻る"
          styleClass="commandExButton"
          style="width: 80px"
          action="#{pc_Xrb00506.doReturnAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 学籍番号 -->
         <h:outputText
          styleClass="outputText"
          id="lblGakusekiCode"
          value="#{pc_Xrb00506.propGakusekiCode.labelName}"
          style="#{pc_Xrb00506.propGakusekiCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="1" style="border-right:none;">
         <h:inputText
          styleClass="inputText"
          id="htmlGakusekiCode" size="10"
          maxlength="#{pc_Xrb00506.propGakusekiCode.maxLength}"
          disabled="#{pc_Xrb00506.propGakusekiCode.disabled}"
          value="#{pc_Xrb00506.propGakusekiCode.stringValue}"
          style="#{pc_Xrb00506.propGakusekiCode.style}"
          readonly="#{pc_Xrb00506.propGakusekiCode.readonly}"
          onblur="return doGakuseiAjax(this, event)">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlGakusekiCdSearchButton"
          disabled="#{pc_Xrb00506.propGakusekiCode.disabled}"
          onclick="openSubWindow('form1:htmlGakusekiCode');">
         </hx:commandExButton>
        <h:inputText
          styleClass="likeOutput"
          id="htmlSearchName"
          size="40"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00506.propSearchName.stringValue}"/>
        </TD>
        <TD width="90" style="text-align: right;padding-right: 3px;border-left:none;">
         <hx:commandExButton
          type="submit"
          value="選択"
          styleClass="commandExButton"
          id="htmlGakuseiSelectButton"
          style="width: 40px"
          action="#{pc_Xrb00506.doGakuseiSelectAction}"
          disabled="#{pc_Xrb00506.propGakuseiSelectButton.disabled}"
          onclick="showInfoName(this)">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="解除"
          styleClass="commandExButton"
          id="htmlGakuseiCancelButton"
          style="width: 40px"
          action="#{pc_Xrb00506.doGakuseiCancelAction}"
          disabled="#{pc_Xrb00506.propGakuseiCancelButton.disabled}">
         </hx:commandExButton>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 申請NO -->
         <h:outputText styleClass="outputText" id="lblSinseiNoTitle"
          value="#{pc_Xrb00506.propSinseiNo.labelName}"
          style="#{pc_Xrb00506.propSinseiNo.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:outputText styleClass="outputText" id="lblSinseiNo"
          style="#{pc_Xrb00506.propSinseiNo.labelStyle}"
          value="#{pc_Xrb00506.propSinseiNo.stringValue}">
         </h:outputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 変更区分 -->
         <h:outputText styleClass="outputText" id="lblHenkoKbn"
          value="#{pc_Xrb00506.propHenkoKbn.labelName}"
          style="#{pc_Xrb00506.propHenkoKbn.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:selectOneMenu
          id="htmlHenkoKbn"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00506.propHenkoKbn.value}"
          disabled="#{pc_Xrb00506.propHenkoKbn.disabled}"
          style="#{pc_Xrb00506.propHenkoKbn.style}"
          readonly="#{pc_Xrb00506.propHenkoKbn.readonly}">
          <f:selectItems value="#{pc_Xrb00506.propHenkoKbn.list}" />
         </h:selectOneMenu>
         <hx:commandExButton
          type="submit"
          value="選択"
          styleClass="commandExButton"
          id="htmlHenkoKbnSelectButton"
          style="width: 40px"
          disabled="#{pc_Xrb00506.propHenkoKbnSelectButton.disabled}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="解除"
          styleClass="commandExButton"
          id="htmlHenkoKbnCancelButton"
          style="width: 40px"
          action="#{pc_Xrb00506.doHenkoKbnCancelAction}"
          disabled="#{pc_Xrb00506.propHenkoKbnCancelButton.disabled}">
         </hx:commandExButton>
        </TD>
        <TH nowrap class="v_a" width="160">
         <!-- 申請状態 -->
         <h:outputText styleClass="outputText" id="lblSinseiJotai"
          value="#{pc_Xrb00506.propSinseiJotai.labelName}"
          style="#{pc_Xrb00506.propSinseiJotai.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="275">
         <h:selectOneMenu
          id="htmlSinseiJotai"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00506.propSinseiJotai.value}"
          disabled="#{pc_Xrb00506.propSinseiJotai.disabled}"
          style="#{pc_Xrb00506.propSinseiJotai.style}"
          readonly="#{pc_Xrb00506.propSinseiJotai.readonly}">
          <f:selectItems value="#{pc_Xrb00506.propSinseiJotai.list}" />
         </h:selectOneMenu>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 申請日 -->
         <h:outputText styleClass="outputText" id="lblSinseiDate" 
          value="#{pc_Xrb00506.propSinseiDate.labelName}"
          style="#{pc_Xrb00506.propSinseiDate.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="inputText"
          id="htmlSinseiDate"
          size="10"
          disabled="#{pc_Xrb00506.propSinseiDate.disabled}"
          value="#{pc_Xrb00506.propSinseiDate.dateValue}"
          style="#{pc_Xrb00506.propSinseiDate.style}"
          readonly="#{pc_Xrb00506.propSinseiDate.readonly}">
          <f:convertDateTime />
          <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
          <hx:inputHelperDatePicker />
         </h:inputText>
        </TD>
        <TH nowrap class="v_a">
         <!-- 振込依頼人コード -->
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCodeTitle" 
          value="#{pc_Xrb00506.propHurikomiIraininCode.labelName}"
          style="#{pc_Xrb00506.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblHurikomiIraininCode" 
          value="#{pc_Xrb00506.propHurikomiIraininCode.stringValue}"
          style="#{pc_Xrb00506.propHurikomiIraininCode.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 申請理由 -->
         <h:outputText styleClass="outputText" id="lblSinseiRiyu" 
          value="#{pc_Xrb00506.propSinseiRiyu.labelName}"
          style="#{pc_Xrb00506.propSinseiRiyu.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSinseiRiyu"
          value="#{pc_Xrb00506.propSinseiRiyu.stringValue}"
          style="#{pc_Xrb00506.propSinseiRiyu.style}"
          disabled="#{pc_Xrb00506.propSinseiRiyu.disabled}"
          cols="85"
          rows="6">
         </h:inputTextarea>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 職員コメント -->
         <h:outputText styleClass="outputText" id="lblSyokuinComment" 
          value="#{pc_Xrb00506.propSyokuinComment.labelName}"
          style="#{pc_Xrb00506.propSyokuinComment.labelStyle}">
         </h:outputText>
        </TH>
        <TD colspan="3">
         <h:inputTextarea styleClass="inputTextarea"
          id="htmlSyokuinComment"
          value="#{pc_Xrb00506.propSyokuinComment.stringValue}"
          style="#{pc_Xrb00506.propSyokuinComment.style}"
          disabled="#{pc_Xrb00506.propSyokuinComment.disabled}"
          cols="85"
          rows="6">
         </h:inputTextarea>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 変更前学科組織 -->
         <h:outputText styleClass="outputText" id="lblBeforeCurGakkaCdTitle"
          value="#{pc_Xrb00506.propBeforeCurGakkaCd.labelName}"
          style="#{pc_Xrb00506.propBeforeCurGakkaCd.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:outputText styleClass="outputText" id="lblBeforeCurGakkaCd"
          value="#{pc_Xrb00506.propBeforeCurGakkaCd.stringValue}"
          style="#{pc_Xrb00506.propBeforeCurGakkaCd.labelStyle}">
         </h:outputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 変更後学科組織 -->
         <h:outputText styleClass="outputText" id="lblAfterCurGakkaCd"
          value="#{pc_Xrb00506.propAfterCurGakkaCd.labelName}"
          style="#{pc_Xrb00506.propAfterCurGakkaCd.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:selectOneMenu
          id="htmlAfterCurGakkaCd"
          styleClass="selectOneMenu"
          value="#{pc_Xrb00506.propAfterCurGakkaCd.value}"
          disabled="#{pc_Xrb00506.propAfterCurGakkaCd.disabled}"
          style="#{pc_Xrb00506.propAfterCurGakkaCd.style}"
          readonly="#{pc_Xrb00506.propAfterCurGakkaCd.readonly}">
          <f:selectItems value="#{pc_Xrb00506.propAfterCurGakkaCd.list}" />
         </h:selectOneMenu>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 変更後資格コード -->
         <h:outputText styleClass="outputText" id="lblAfterSikakCd1"
          value="#{pc_Xrb00506.propAfterSikakCd1.labelName}"
          style="#{pc_Xrb00506.propAfterSikakCd1.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="inputText"
          id="htmlAfterSikakCode1" size="10"
          maxlength="#{pc_Xrb00506.propAfterSikakCd1.maxLength}"
          disabled="#{pc_Xrb00506.propAfterSikakCd1.disabled}"
          value="#{pc_Xrb00506.propAfterSikakCd1.stringValue}"
          style="#{pc_Xrb00506.propAfterSikakCd1.style}"
          readonly="#{pc_Xrb00506.propAfterSikakCd1.readonly}"
          onblur="return doSikakInfoAjax(this, event, 'form1:htmlAfterSikakName1', 'form1:htmlAfterSikakNameHidden1');">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlAfterSikakCd1SearchButton1"
          disabled="#{pc_Xrb00506.propAfterSikakCd1.disabled}"
          onclick="return openSikakSubWindow('form1:htmlAfterSikakCode1');">
         </hx:commandExButton>
         <h:inputText
          styleClass="likeOutput"
          id="htmlAfterSikakName1"
          size="90"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00506.propAfterSikakName1.stringValue}">
         </h:inputText>
        </TD>
       </TR>
       <TR>
        <TH nowrap class="v_a">
         <!-- 変更後資格コード -->
         <h:outputText styleClass="outputText" id="lblAfterSikakCd2"
          value="#{pc_Xrb00506.propAfterSikakCd2.labelName}"
          style="#{pc_Xrb00506.propAfterSikakCd2.labelStyle}">
         </h:outputText>
        </TH>
        <TD>
         <h:inputText
          styleClass="inputText"
          id="htmlAfterSikakCode2" size="10"
          maxlength="#{pc_Xrb00506.propAfterSikakCd2.maxLength}"
          disabled="#{pc_Xrb00506.propAfterSikakCd2.disabled}"
          value="#{pc_Xrb00506.propAfterSikakCd2.stringValue}"
          style="#{pc_Xrb00506.propAfterSikakCd2.style}"
          readonly="#{pc_Xrb00506.propAfterSikakCd2.readonly}"
          onblur="return doSikakInfoAjax(this, event, 'form1:htmlAfterSikakName2', 'form1:htmlAfterSikakNameHidden2');">
         </h:inputText>
         <hx:commandExButton
          type="button"
          value="検"
          styleClass="commandExButton_search"
          id="htmlAfterSikakCd1SearchButton2"
          disabled="#{pc_Xrb00506.propAfterSikakCd2.disabled}"
          onclick="return openSikakSubWindow('form1:htmlAfterSikakCode2');">
         </hx:commandExButton>
         <h:inputText
          styleClass="likeOutput"
          id="htmlAfterSikakName2"
          size="90"
          tabindex="-1"
          readonly="true"
          value="#{pc_Xrb00506.propAfterSikakName2.stringValue}">
         </h:inputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE class="table" width="870">
      <TBODY>
       <TR>
        <TH nowrap class="v_a" width="160">
         <!-- 内訳金額 -->
         <h:outputText styleClass="outputText" id="lblUtiwakeKingaku" 
          value="#{pc_Xrb00506.propUtiwakeKingaku.labelName}"
          style="#{pc_Xrb00506.propUtiwakeKingaku.labelStyle}">
         </h:outputText>
        </TH>
        <TD width="710">
         <h:inputText
          styleClass="inputText"
          id="htmlUtiwakeKingaku" size="10"
          maxlength="#{pc_Xrb00506.propUtiwakeKingaku.maxLength}"
          disabled="#{pc_Xrb00506.propUtiwakeKingaku.disabled}"
          value="#{pc_Xrb00506.propUtiwakeKingaku.integerValue}"
          style="#{pc_Xrb00506.propUtiwakeKingaku.style}"
          readonly="#{pc_Xrb00506.propUtiwakeKingaku.readonly}">
          <f:convertNumber pattern="###,###,###"/>
         </h:inputText>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
     <BR>
     <TABLE width="870">
      <TBODY>
       <TR>
        <TD>
         <hx:commandExButton
          type="submit"
          value="確定"
          styleClass="commandExButton_dat"
          id="htmlKakuteiButton"
          disabled="#{pc_Xrb00506.propKakuteiButton.disabled}"
          confirm="#{msg.SY_MSG_0001W}"
          action="#{pc_Xrb00506.doKakuteiAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="削除"
          styleClass="commandExButton_dat"
          id="htmlDeleteButton"
          disabled="#{pc_Xrb00506.propDeleteButton.disabled}"
          confirm="#{msg.SY_MSG_0004W}"
          action="#{pc_Xrb00506.doDeleteAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="クリア"
          styleClass="commandExButton_dat"
          id="htmlClearButton"
          disabled="#{pc_Xrb00506.propClearButton.disabled}"
          action="#{pc_Xrb00506.doClearAction}">
         </hx:commandExButton>
         <hx:commandExButton
          type="submit"
          value="CSV作成"
          styleClass="commandExButton_dat"
          id="htmlCsvButton"
          disabled="#{pc_Xrb00506.propCsvButton.disabled}"
          confirm="#{msg.SY_MSG_0020W}"
          action="#{pc_Xrb00506.doCsvAction}">
         </hx:commandExButton>
        </TD>
       </TR>
      </TBODY>
     </TABLE>
    </TD>
   </TR>
  </TBODY>
 </TABLE>
<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
<h:inputHidden
 id="htmlKanriNoHidden"
 value="#{pc_Xrb00506.propKanriNoHidden.longValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlWarningDialogFlag"
 value="#{pc_Xrb00506.propWarningDialogFlag.integerValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlWarningDialogType"
 value="#{pc_Xrb00506.propWarningDialogType.integerValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlAfterSikakNameHidden1"
 value="#{pc_Xrb00506.propAfterSikakNameHidden1.stringValue}">
</h:inputHidden>
<h:inputHidden
 id="htmlAfterSikakNameHidden2"
 value="#{pc_Xrb00506.propAfterSikakNameHidden2.stringValue}">
</h:inputHidden>
</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

