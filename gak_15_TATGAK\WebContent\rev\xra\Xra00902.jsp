<%-- 
	学籍情報登録（資格登録）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xra/Xra00902.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ taglib uri="/gakuen" prefix="gakuen"%>

<%@page import="com.jast.gakuen.rev.xra.Xra00902"%>
<%@page import="java.util.ArrayList"%>
<%@page import="com.jast.gakuen.framework.util.UtilSystem"%>


<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xra00902.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xra00902.onPageLoadBegin}">
<gakuen:itemStateCtrl managedbean="pc_Xra00902">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xra00902.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xra00902.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xra00902.screenName}"></h:outputText>
</div>			

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>

<!--↓content↓-->
<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
<hx:commandExButton type="submit" value="戻る"
	styleClass="commandExButton" id="returnDisp"
	action="#{pc_Xra00902.doReturnAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

	<TABLE width="100%" border="0" cellpadding="5">
		<TBODY>
			<TR>
				<TD>
				<TABLE class="table" width="830">
					<TBODY>
						<TR align="center" valign="middle">
							<TH nowrap class="v_a" width="180">
							<!--学籍番号 -->
								<h:outputText styleClass="outputText" id="lblGaksekiNoOut"
								value="#{pc_Xra00902.propGakusekiNo.name}"></h:outputText></TH>
							<TD width="235"><h:outputText styleClass="outputText"
								id="lblGaksekiNo"
								value="#{pc_Xra00902.propGakusekiNo.stringValue}"></h:outputText>
							</TD>
							<TH nowrap class="v_a" width="180">
							<!--学生氏名 -->
								<h:outputText styleClass="outputText" id="lblGakseiName" 
								value="#{pc_Xra00902.propName.name}"></h:outputText></TH>
							<TD width="235"><h:outputText styleClass="outputText"
										id="htmlName" value="#{pc_Xra00902.propName.stringValue}"></h:outputText>
							</TD>
						</TR>
						<TR>
							<TH nowrap class="v_b">
							<!-- 学籍状況 -->
								<h:outputText styleClass="outputText" id="lblGakJokyo" 
								value="#{pc_Cob00101T01.cob00101.propGakJokyo.labelName}"
								style="#{pc_Cob00101T01.cob00101.propGakJokyo.labelStyle}"></h:outputText></TH>
							<TD width="225" colspan="3"><h:outputText styleClass="outputText" id="htmlGakJokyoOut" 
								value="#{pc_Cob00101T01.cob00101.propGakJokyo.stringValue}"></h:outputText></TD>
						</TR>
					</TBODY>
				</TABLE>
				<BR>
				<TABLE border="0" cellpadding="0" cellspacing="0" width="830">
					<TBODY>
						<TR>
							<TD align="right" nowrap class="outputText" width="100%"><h:outputText
								styleClass="outputText" id="lblHsyListCnt" value="#{pc_Xra00902.propShikakuList.listCount}"></h:outputText>件</TD>
						</TR>
						<TR>
							<TD>
									<div class="listScroll" id="listScroll" style="height: 250px"
										onscroll="setScrollPosition('htmlHidScroll', this);"><h:dataTable
										border="1" cellpadding="2" cellspacing="0"
										headerClass="headerClass" footerClass="footerClass"
										columnClasses="columnClass1"
										rowClasses="#{pc_Xra00902.propShikakuList.rowClasses}"
										styleClass="meisai_scroll" id="htmlShikakuList" var="varlist"
										value="#{pc_Xra00902.propShikakuList.list}">
										<h:column id="column1">
											<f:facet name="header">
												<h:outputText id="lblSikakKiboNendo_head"
													styleClass="outputText"
													value="#{pc_Xra00902.propSikakKiboNendo.labelName}"
													style="#{pc_Xra00902.propSikakKiboNendo.labelStyle}"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSikakKiboNendo_list"
										        value="#{varlist.propSikakKiboNendo.integerValue}"
												></h:outputText>
											<f:attribute value="60" name="width" />
											<f:attribute value="text-align: left" name="style" />
										</h:column>
										<h:column id="column2">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblSikakKiboGakkiNo_head"
													value="#{pc_Xra00902.propSikakKiboGakkiNo.labelName}"
													style="#{pc_Xra00902.propSikakKiboGakkiNo.labelStyle}"
													></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSikakKiboGakkiNo_list"
										        value="#{varlist.propSikakKiboGakkiNo.integerValue}"
												></h:outputText>
											<f:attribute value="60" name="width" />
											<f:attribute value="text-align: right" name="style" />
										</h:column>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblSikakName_head"
													value="#{pc_Xra00902.propSikakName.labelName}"
													style="#{pc_Xra00902.propSikakName.labelStyle}"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSikakName_list"
												value="#{varlist.propSikakName.displayValue}"
												title="#{varlist.propSikakName.stringValue}"></h:outputText>
											<f:attribute value="200" name="width" />
											<f:attribute value="text-align: left" name="style" />
											<f:attribute value="true" name="nowrap" />
										</h:column>
										
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblSyutokMkmKbn_head"
													value="#{pc_Xra00902.propSyutokMkmKbn.labelName}"
													style="#{pc_Xra00902.propSyutokMkmKbn.labelStyle}"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSyutokMkmKbn_list"
										        value="#{varlist.propSyutokMkmKbn.displayValue}"></h:outputText>
											<f:attribute value="120" name="width" />
											<f:attribute value="text-align: left" name="style" />
										</h:column>
										
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblSyutokMkmDate_head"
													value="#{pc_Xra00902.propSyutokMkmDate.labelName}"
													style="#{pc_Xra00902.propSyutokMkmDate.labelStyle}"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSyutokMkmDate_list"
										        value="#{varlist.propSyutokMkmDate.displayValue}"></h:outputText>
											<f:attribute value="90" name="width" />
											<f:attribute value="text-align: left" name="style" />
										</h:column>
										
										<h:column id="column6">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblSyutokKbn_head"
													value="#{pc_Xra00902.propSyutokKbn.labelName}"
													style="#{pc_Xra00902.propSyutokKbn.labelStyle}"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSyutokKbn_list"
										        value="#{varlist.propSyutokKbn.displayValue}"></h:outputText>
											<f:attribute value="85" name="width" />
											<f:attribute value="text-align: left" name="style" />
										</h:column>
										
										<h:column id="column7">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblSyutokDate_head"
													value="#{pc_Xra00902.propSyutokDate.labelName}"
													style="#{pc_Xra00902.propSyutokDate.labelStyle}"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSyutokDate_list"
										        value="#{varlist.propSyutokDate.displayValue}"></h:outputText>
											<f:attribute value="70" name="width" />
											<f:attribute value="text-align: left" name="style" />
										</h:column>
										
										<h:column id="column8">
											<f:facet name="header">
												<h:outputText styleClass="outputText"
													id="lblSyufukKbn_head"
													value="#{pc_Xra00902.propSyufukKbn.labelName}"
													style="#{pc_Xra00902.propSyufukKbn.labelStyle}">
											    </h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="lblSyufukKbn_list"
										        value="#{varlist.propSyufukKbn.displayValue}"></h:outputText>
											<f:attribute value="100" name="width" />
										</h:column>
										<h:column id="column9">
											<f:facet name="header">
											</f:facet>
											<hx:commandExButton type="submit" value="選択"
												styleClass="commandExButton" id="button_select"
												action="#{pc_Xra00902.doBtnSelectAction}"></hx:commandExButton>
											<f:attribute value="35" name="width" />
										</h:column>
									</h:dataTable><BR>
									</div>
									</TD>
						</TR>
					</TBODY>
				</TABLE>
				<BR>
				<TABLE class="table" width="830">
					<TBODY>
					 <TR>
					  <TH nowrap class="v_a" width="180">
					  <!-- 資格希望年度 -->
					   <h:outputText styleClass="outputText" id="lblSelectSikakKiboNen" 
					    value="#{pc_Xra00902.propSelectSikakKiboNen.labelName}"
						style="#{pc_Xra00902.propSelectSikakKiboNen.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD width="235">
					   <h:inputText styleClass="inputText"
					    id="htmlSelectSikakKiboNen" size="10"
						maxlength="#{pc_Xra00902.propSelectSikakKiboNen.maxLength}"
						disabled="#{pc_Xra00902.propSelectSikakKiboNen.disabled}"
						value="#{pc_Xra00902.propSelectSikakKiboNen.dateValue}"
						style="#{pc_Xra00902.propSelectSikakKiboNen.style}"
						readonly="#{pc_Xra00902.propSelectSikakKiboNen.readonly}">
						<hx:inputHelperAssist errorClass="inputText_Error"
						 imeMode="inactive" promptCharacter="_" />
					    <f:convertDateTime pattern="yyyy" />
					   </h:inputText>
					  </TD>
					  <TH nowrap class="v_a" width="180">
					  <!-- 資格希望学期NO -->
					   <h:outputText styleClass="outputText" id="lblSelectSikakKiboGakkiNo"
					    value="#{pc_Xra00902.propSelectSikakKiboGakkiNo.labelName}"
					    style="#{pc_Xra00902.propSelectSikakKiboGakkiNo.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD width="235">
					   <h:inputText styleClass="inputText"
					    id="htmlSelectSikakKiboGakkiNo" size="10"
						maxlength="#{pc_Xra00902.propSelectSikakKiboGakkiNo.maxLength}"
						disabled="#{pc_Xra00902.propSelectSikakKiboGakkiNo.disabled}"
						value="#{pc_Xra00902.propSelectSikakKiboGakkiNo.integerValue}"
						style="#{pc_Xra00902.propSelectSikakKiboGakkiNo.style}"
						readonly="#{pc_Xra00902.propSelectSikakKiboGakkiNo.readonly}">
						 <f:convertNumber type="number" pattern="##"/>
						 <hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
					   </h:inputText>
					  </TD>
					 </TR>
					 <TR>
					  <TH nowrap class="v_a" width="180">
					  <!-- 資格 -->
					   <h:outputText styleClass="outputText" id="lblSelectSikakName" 
					    value="#{pc_Xra00902.propSelectSikakName.labelName}"
						style="#{pc_Xra00902.propSelectSikakName.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD colspan="3">
					   <h:selectOneMenu styleClass="selectOneMenu"
			            id="htmlSelectSikakName"
						disabled="#{pc_Xra00902.propSelectSikakName.disabled}"
						value="#{pc_Xra00902.propSelectSikakName.value}"
						readonly="#{pc_Xra00902.propSelectSikakName.readonly}"
						style="width:508px;">
						<f:selectItems
						 value="#{pc_Xra00902.propSelectSikakName.list}" />
					   </h:selectOneMenu>
					  </TD>
					 </TR>
					 <TR>
					  <TH nowrap class="v_a">
					  <!-- 取得見込区分 -->
					   <h:outputText styleClass="outputText" id="lblSelectSyutokMikomiKbn" 
					    value="#{pc_Xra00902.propSelectSyutokMikomiKbn.labelName}"
						style="#{pc_Xra00902.propSelectSyutokMikomiKbn.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD>
					   <h:selectOneMenu styleClass="selectOneMenu"
			            id="htmlSelectSyutokMikomiKbn"
						disabled="#{pc_Xra00902.propSelectSyutokMikomiKbn.disabled}"
						value="#{pc_Xra00902.propSelectSyutokMikomiKbn.value}"
						readonly="#{pc_Xra00902.propSelectSyutokMikomiKbn.readonly}"
						style="width:150px;">
						<f:selectItems
						 value="#{pc_Xra00902.propSelectSyutokMikomiKbn.list}" />
					   </h:selectOneMenu>
					  </TD>
					  <TH nowrap class="v_a">
					  <!-- 取得見込日付 -->
					   <h:outputText styleClass="outputText" id="lblSelectSyutokMikomiDate"
					    value="#{pc_Xra00902.propSelectSyutokMikomiDate.labelName}"
					    style="#{pc_Xra00902.propSelectSyutokMikomiDate.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD>
					   <h:inputText
						styleClass="inputText"
						id="htmlSelectSyutokMikomiDate"
						size="10"
						disabled="#{pc_Xra00902.propSelectSyutokMikomiDate.disabled}"
						value="#{pc_Xra00902.propSelectSyutokMikomiDate.dateValue}"
						style="#{pc_Xra00902.propSelectSyutokMikomiDate.style}"
						readonly="#{pc_Xra00902.propSelectSyutokMikomiDate.readonly}">
						<f:convertDateTime />
						<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						<hx:inputHelperDatePicker />
					   </h:inputText>
					  </TD>
					 </TR>
					 <TR>
					  <TH nowrap class="v_a">
					  <!-- 取得区分 -->
					   <h:outputText styleClass="outputText" id="lblSelectSyutokKbn" 
					    value="#{pc_Xra00902.propSelectSyutokKbn.labelName}"
						style="#{pc_Xra00902.propSelectSyutokKbn.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD>
					   <h:selectOneMenu styleClass="selectOneMenu"
			            id="htmlSelectSyutokKbn"
						disabled="#{pc_Xra00902.propSelectSyutokKbn.disabled}"
						value="#{pc_Xra00902.propSelectSyutokKbn.value}"
						readonly="#{pc_Xra00902.propSelectSyutokKbn.readonly}"
						style="width:150px;">
						<f:selectItems
						 value="#{pc_Xra00902.propSelectSyutokKbn.list}" />
					   </h:selectOneMenu>
					  </TD>
					  <TH nowrap class="v_a">
					  <!-- 取得日付 -->
					   <h:outputText styleClass="outputText" id="lblSelectSyutokDate"
					    value="#{pc_Xra00902.propSelectSyutokDate.labelName}"
					    style="#{pc_Xra00902.propSelectSyutokDate.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD>
					   <h:inputText
						styleClass="inputText"
						id="htmlSelectSyutokDate"
						size="10"
						disabled="#{pc_Xra00902.propSelectSyutokDate.disabled}"
						value="#{pc_Xra00902.propSelectSyutokDate.dateValue}"
						style="#{pc_Xra00902.propSelectSyutokDate.style}"
						readonly="#{pc_Xra00902.propSelectSyutokDate.readonly}">
						<f:convertDateTime />
						<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						<hx:inputHelperDatePicker />
					   </h:inputText>
					  </TD>
					 </TR>
					 <TR>
					  <TH nowrap class="v_a" width="180">
					  <!-- 主副区分 -->
					   <h:outputText styleClass="outputText" id="lblSelectSyufukKbn" 
					    value="#{pc_Xra00902.propSelectSyufukKbn.labelName}"
						style="#{pc_Xra00902.propSelectSyufukKbn.labelStyle}">
					   </h:outputText>
					  </TH>
					  <TD colspan="3">
					   <h:selectOneMenu styleClass="selectOneMenu"
			            id="htmlSelectSyufukKbn"
						disabled="#{pc_Xra00902.propSelectSyufukKbn.disabled}"
						value="#{pc_Xra00902.propSelectSyufukKbn.value}"
						readonly="#{pc_Xra00902.propSelectSyufukKbn.readonly}"
						style="width:150px;">
						<f:selectItems
						 value="#{pc_Xra00902.propSelectSyufukKbn.list}" />
					   </h:selectOneMenu>
					  </TD>
					 </TR>
					</TBODY>
				</TABLE>
				<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="830">
					<TBODY>
						<TR align="right">
							<TD align="center"><hx:commandExButton
								type="submit" value="確定" styleClass="commandExButton_dat"
								id="update" action="#{pc_Xra00902.doUpdateAction}"></hx:commandExButton>
								<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
								id="delete" action="#{pc_Xra00902.doDeleteAction}"></hx:commandExButton>
								<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
								id="clear" action="#{pc_Xra00902.doClearAction}"></hx:commandExButton></TD>
						</TR>
					</TBODY>
				</TABLE>
				<HR width="100%" class ="hr" noshade>
				<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="830">
					<TBODY>
						<TR align="right">
							<TD align="center"><hx:commandExButton
								type="submit" value="一覧確定" styleClass="commandExButton_dat"
								id="listKakutei" action="#{pc_Xra00902.doListKakuteiAction}"></hx:commandExButton></TD>
						</TR>
					</TBODY>
				</TABLE>
				</TD>
			</TR>
		</TBODY>
	</TABLE>
	<h:inputHidden id="htmlHidScroll" value="#{pc_Xra00902.propShikakuList.scrollPosition}"></h:inputHidden>
	<h:inputHidden id="htmlChihoCd" value=""></h:inputHidden>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			
		</h:form>
</gakuen:itemStateCtrl>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	
	function endload() {
		changeScrollPosition('htmlHidScroll', 'listScroll');
	}
</SCRIPT>

</HTML>
