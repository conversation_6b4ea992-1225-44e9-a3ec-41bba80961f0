<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm00901T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Cob01001T02.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<SCRIPT language="JavaScript" src="${pageContext.request.contextPath}/rev/co/inc/gakuenCO.js"></SCRIPT>
<SCRIPT type="text/javascript">
function confirmAllRemove(id){
	var length = document.getElementById("form1:htmlGakseiList").length;
	if (length > 0) {
		return doPopupMsg(id, "リスト情報");
	}
	return false;
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
	var args = new Array();
	args[0] = msg;
	return confirm(messageCreate(id, args));
}

// 確認ダイアログで「ＯＫ」の場合
function confirmOk() {
	addListWarnOK("htmlExecutableBtnAdd", "btnAdd1");
}
// 確認ダイアログで「キャンセル」の場合
function confirmCancel() {	
	addListWarnCancel("htmlExecutableBtnAdd");
}

function loadAction(event){
// 画面ロード時の学生名称再取得
	doGakuseiAjax(document.getElementById('form1:htmlGakusekiNo'), event, 'form1:lblGakuseiNm');
}

function openSubWindow(field1) {
// 学生検索画面
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp"
		+ "?retFieldName=" + field1;
		
	openModalWindow(url, "PCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
	return false;
}

function doGakuseiAjax(thisObj, thisEven, targetLabel){
// 学生名称を取得する
	var servlet = "rev/co/CobGakseiAJAX";
	var args = new Array();
	args['code1'] = thisObj.value;

	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>

<BODY onLoad="loadAction(event)">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrm00901T02.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrm00901T02.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrm00901T02.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrm00901T02.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" ><!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->

<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
	<TBODY>
		<TR>
			<TD>
			<TABLE border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="637">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="637">
								<TBODY>
									<TR height="50">
										<TD valign="top"  align="left">
										<br>
										<TABLE class="table" width="600">
											<TBODY>
												<TR>
													<TH nowrap class="v_a" width="146"><h:outputText styleClass="outputText"
													 id="lblNendo"
													 value="#{pc_Xrm00901T02.propNendo.labelName}"
													 style="#{pc_Xrm00901T02.propNendo.labelStyle}"></h:outputText></TH>
													<TD width="454"><h:inputText styleClass="inputText"
													 id="htmlNendo" style="#{pc_Xrm00901T02.propNendo.style}"
													 disabled="#{pc_Xrm00901T02.propNendo.disabled}"
													 value="#{pc_Xrm00901T02.propNendo.dateValue}"
													 size="5">
													<hx:inputHelperAssist errorClass="inputText_Error"
							    					imeMode="inactive" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
													</h:inputText></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR><TD><BR></TD></TR>
					<TR align="left">
						<TD width="637">
						<hx:commandExButton type="submit" value="#{pc_Xrm00901T02.proplblAllSitei.name}"
							styleClass="tab_head_off" id="htmlAllSitei"
							action="#{pc_Xrm00901T02.doLink1Action}"></hx:commandExButton><hx:commandExButton
							type="submit" value="#{pc_Xrm00901T02.proplblSelectSitei.name}" styleClass="tab_head_on"
							id="link1"></hx:commandExButton></TD>
					</TR>
					<TR>
						<TD width="637">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="637"
							class="tab_body">
								<TBODY>
									<TR height="300">
										<TD valign="top"  align="center">
										<BR>
										<TABLE class="table" width="600">
											<TBODY>
												<TR>
													<TH nowrap class="v_a" width="149"><h:outputText
														styleClass="outputText" id="htmlFile"
														value="#{pc_Xrm00901T02.proplblInputFile.name}"></h:outputText><BR>
													<h:outputText styleClass="outputText" id="htmlOldFile"
														value="#{pc_Xrm00901T02.proplblOldFile.name}"></h:outputText></TH>
													<TD nowrap width="451"><hx:fileupload styleClass="fileupload"
																	id="htmlInputFile"
																	value="#{pc_Xrm00901T02.propInputFile.value}" size="60"
																	style="width:400px">
																	<hx:fileProp name="fileName"
																		value="#{pc_Xrm00901T02.propInputFile.fileName}" />
																	<hx:fileProp name="contentType"
																		value="#{pc_Xrm00901T02.propInputFile.contentType}" />
																</hx:fileupload> <hx:commandExButton type="submit"
														value="取込" styleClass="commandExButton" id="takeIn"
														action="#{pc_Xrm00901T02.doFileInputAction}"></hx:commandExButton><BR>
													<h:outputText styleClass="outputText"
														id="htmlInputFileOld"
														value="#{pc_Xrm00901T02.propInputFileOld.value}"></h:outputText><BR></TD>
												</TR>
												<TR>
													<TH nowrap class="v_b" width="149"><h:outputText
														styleClass="outputText" id="htmlKobetu"
														value="#{pc_Xrm00901T02.proplblKobetu.name}"></h:outputText></TH>
													<TD nowrap width="451"><h:inputText styleClass="inputText"
														id="htmlGakusekiNo"
														style="#{pc_Xrm00901T02.propGakusekiNo.style}"
														value="#{pc_Xrm00901T02.propGakusekiNo.stringValue}"
														maxlength="#{pc_Xrm00901T02.propGakusekiNo.maxLength}"
														onblur="return doGakuseiAjax(this, event, 'form1:lblGakuseiNm');"
														size="18">
													</h:inputText> <hx:commandExButton type="button"
														value="検索" styleClass="commandExButton_search" id="btnGakuseki1"
														onclick="openSubWindow('form1:htmlGakusekiNo');"></hx:commandExButton>
													<hx:commandExButton type="submit" value="追加"
														styleClass="commandExButton"
														id="btnAdd1" action="#{pc_Xrm00901T02.doBtnAddKobetsuAction}"></hx:commandExButton>
													<h:inputText styleClass="likeOutput" id="lblGakuseiNm"
														size="30" tabindex="-1"
														value="#{pc_Xrm00901T02.propGakuseiNm.value}"
														readonly="#{pc_Xrm00901T02.propGakuseiNm.readonly}">
													</h:inputText></TD>
												</TR>
											</TBODY>
										</TABLE>
										<TABLE border="0" cellpadding="0" cellspacing="0" width="600">
											<TR>
												<TD align="right"></TD>
											</TR>
										</TABLE>
										<!-- 対象学生一覧 -->
										<TABLE border="0" cellpadding="0" cellspacing="0" width="500">
											<TBODY>
												<TR>
													<TD width="410" align="left">
														<h:outputText
															styleClass="outputText" id="lblGakseiList"
															value="#{pc_Xrm00901T02.propGakseiList.labelName}">
														</h:outputText>
													</TD>
													<TD width="90"></TD>
												</TR>
												<TR>
													<TD>
														<h:selectManyListbox styleClass="selectManyListbox"
															style="width:100%" id="htmlGakseiList" size="10"
															value="#{pc_Xrm00901T02.propGakseiList.integerValue}">
																<f:selectItems
																	value="#{pc_Xrm00901T02.propGakseiList.list}" />
														</h:selectManyListbox>
													</TD>
													<TD align="center" valign="top">
														<hx:commandExButton
															type="submit" value="除外" styleClass="commandExButton"
															id="remove" action="#{pc_Xrm00901T02.doRemoveAction}"
															style="width:60px">
														</hx:commandExButton>
														<BR>
														<h:outputText styleClass="outputText" id="text6"
															value="(複数選択可)">
														</h:outputText>
														<BR>
														<hx:commandExButton type="submit" value="全て除外"
																styleClass="commandExButton" id="removeAll"
																action="#{pc_Xrm00901T02.doRemoveAllAction}"
																style="width:60px" onclick="return confirmAllRemove('#{msg.SY_MSG_0006W}');">
															</hx:commandExButton>
													</TD>
												</TR>
												<TR>
													<TD align="right">
														<h:outputFormat
																styleClass="outputFormat" id="format1"
																value="合計件数：{0}件　正常件数：{1}件　エラー件数：{2}件">
																<f:param name="maxCount"
																	value="#{pc_Xrm00901T02.propGakseiList.listCount}"></f:param>
																<f:param name="nomalCount"
																	value="#{pc_Xrm00901T02.propGakseiList.listCount - pc_Xrm00901T02.propErrorCount.value}"></f:param>
																<f:param name="errCount"
																	value="#{pc_Xrm00901T02.propErrorCount.value}"></f:param>
															</h:outputFormat>
													</TD>
													<TD align="center"></TD>
												</TR>
											</TBODY>
										</TABLE>
										</TD>
									</TR>
								   <TR height="50">
								 		<TD  valign="top">
											<TABLE  cellspacing="1" cellpadding="1" class="button_bar" width="600">
												<TBODY>
													<TR align="right">
														<TD align="center">
															<hx:commandExButton type="submit" value="PDF作成"
																styleClass="commandExButton_out" id="PdfOut"
																confirm="#{msg.SY_MSG_0019W}"
																action="#{pc_Xrm00901T02.doPdfOutAction}">
															</hx:commandExButton>
														</TD>
													</TR>
												</TBODY>
											</TABLE>
								     </TD>
								   </TR>
								</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</TD>
		</TR>
	</TBODY>
</TABLE>

<!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrm00901T02.propExecutableBtnAdd.integerValue}" id="htmlExecutableBtnAdd"><f:convertNumber /></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
