<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:htmlExecutable').value = "1"
	indirectClick('register');
}
function confirmCancel() {
	document.getElementById('form1:htmlExecutable').value = "0";
	return false;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz01001.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz01001.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz01001.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz01001.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><TABLE border="0" width="600" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="214"><h:outputText styleClass="outputText"
							id="text1" value="大分類での集計は、地域コードの上"
							style="#{pc_Ssz01001.propDai.labelStyle}"></h:outputText></TH>
						<TD width="380" valign="middle" nowrap><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlDai" value="#{pc_Ssz01001.propDai.value}"
							>
							<f:selectItems value="#{pc_Ssz01001.propDai.list}" />
						</h:selectOneMenu>
						<h:outputText styleClass="outputText" id="text4" value="桁で行います。"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="214"><h:outputText styleClass="outputText"
							style="#{pc_Ssz01001.propChu.labelStyle}"id="text2" value="中分類での集計は、地域コードの上"></h:outputText></TH>
						<TD width="380"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlChu" value="#{pc_Ssz01001.propChu.value}">
							<f:selectItems value="#{pc_Ssz01001.propChu.list}" />
						</h:selectOneMenu>
						<h:outputText styleClass="outputText" id="text5" value="桁で行います。"></h:outputText></TD>
					</TR>
					<TR>
						<TH class="v_c" width="214"><h:outputText styleClass="outputText"
							style="#{pc_Ssz01001.propSho.labelStyle}"id="text3" value="小分類での集計は、地域コードの上"></h:outputText></TH>
						<TD width="380"><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlSho" value="#{pc_Ssz01001.propSho.value}">
							<f:selectItems value="#{pc_Ssz01001.propSho.list}" />
						</h:selectOneMenu>
						<h:outputText styleClass="outputText" id="text6" value="桁で行います。"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="600" class="">
				<TBODY>
					<TR>
						<TD height="82" align="left"><h:outputText styleClass="note" id="text7"
							value=" （注）"></h:outputText><BR>
						<h:outputText styleClass="note" id="text8"
							value="　　　　中分類は大分類より大きい数字を、小分類は中分類より大きい数字を設定してください。"></h:outputText><BR>
						<h:outputText styleClass="note" id="text9"
							value="　　　　大分類だけの設定または大分類と中分類だけの設定でも構いません。"></h:outputText>
						<BR>
						<h:outputText styleClass="note" id="text10"
							value="　　　　また全てに何も設定しなくても構いません。"></h:outputText><BR>
						<h:outputText styleClass="outputText" id="text11"></h:outputText>
						<BR></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit"
							value="確 定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz01001.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<h:inputHidden
				value="#{pc_Ssz01001.propExecutable.stringValue}"
				id="htmlExecutable"></h:inputHidden>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>

</HTML>

