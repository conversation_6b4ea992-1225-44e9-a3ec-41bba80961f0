<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00201.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz00201.doCloseDispAction}"
			></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz00201.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="30%" align="left"><h:outputText styleClass="outputText"
							id="text1" value="共通学期設定内容"></h:outputText></TD>
						<TD width="30%" align="right"><h:outputText
							styleClass="outputText" id="text2"
							value="#{pc_Ssz00201.propGakNenCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text5" value="件"></h:outputText></TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD >
						<div class="listScroll" style="height:118px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz00201.propGakNen.rowClasses}"
							styleClass="meisai_scroll" id="table2" width="540;"
							value="#{pc_Ssz00201.propGakNen.list}" var="varlist">
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText id="text4" styleClass="outputText" value="年度"></h:outputText>
								</f:facet>
								<f:attribute value="250" name="width" />
								<h:outputText styleClass="outputText" id="text6"
									value="#{varlist.nendo}"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学期" id="text3"></h:outputText>
								</f:facet>
								<f:attribute value="250" name="width" />
								<h:outputText styleClass="outputText" id="text11"
									value="#{varlist.gakki}"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%" align="right"><h:outputText
							styleClass="outputText" id="text15"
							value="#{pc_Ssz00201.propNendCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text16" value="件"></h:outputText></TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%">
						<div class="listScroll" style="height:128px;"
							id="listScroll2" onscroll="setScrollPosition('scroll2',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz00201.propNend.rowClasses}"
							styleClass="meisai_scroll" id="table1" width="540"
							value="#{pc_Ssz00201.propNend.list}" var="varlist">
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText id="text7" styleClass="outputText" value="年度"></h:outputText>
								</f:facet>
								<f:attribute value="128" name="width" />
								<h:outputText styleClass="outputText" id="text12"
									value="#{varlist.kjnNendo}"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学期" id="text8"></h:outputText>
								</f:facet>
								<f:attribute value="142" name="width" />
								<h:outputText styleClass="outputText" id="text13"
									value="#{varlist.kjnGakkiNo}"></h:outputText>
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text9"></h:outputText>
								</f:facet>
								<f:attribute value="210" name="width" />
								<h:outputText styleClass="outputText" id="text14"
									value="#{varlist.kjnGakkiName}"></h:outputText>
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="24" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz00201.doSelectAction}"></hx:commandExButton>
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD>
						<TABLE width="100%" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="220"><h:outputText
										styleClass="outputText" id="lblKjnNendo"
										value="#{pc_Ssz00201.propKjnNendo.labelName}"
										style="#{pc_Ssz00201.propKjnNendo.labelStyle}"></h:outputText></TH>
									<TD width="327"><h:inputText styleClass="inputText"
										id="htmlKjnNendo"
										value="#{pc_Ssz00201.propKjnNendo.dateValue}"
										size="4"
										style="#{pc_Ssz00201.propKjnNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
										imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" /></h:inputText>
										<h:outputText styleClass="outputText" id="lblNen"
										value="年度"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="220"><h:outputText
										styleClass="outputText" id="lblKjnGyomCycle"
										value="#{pc_Ssz00201.propKjnGyomCycle.labelName}"></h:outputText></TH>
									<TD width="327"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlKjnGyomCycle"
										layout="pageDirection"
										value="#{pc_Ssz00201.propKjnGyomCycle.stringValue}">
										<f:selectItem itemValue="0" itemLabel="通年" />
										<f:selectItem itemValue="1" itemLabel="学期毎" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_c" width="220"><h:outputText
										styleClass="outputText" id="lblKjnGakkiNo"
										value="#{pc_Ssz00201.propKjnGakkiNo.labelName}"
										style="#{pc_Ssz00201.propKjnGakkiNo.labelStyle}"></h:outputText></TH>
									<TD width="327"><h:inputText styleClass="inputText"
										id="htmlKjnGakkiNo"
										maxlength="#{pc_Ssz00201.propKjnGakkiNo.maxLength}" size="2"
										value="#{pc_Ssz00201.propKjnGakkiNo.stringValue}"
										style="#{pc_Ssz00201.propKjnGakkiNo.style}"></h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="220"><h:outputText
										styleClass="outputText" id="lblKjnGakkiName"
										style="#{pc_Ssz00201.propKjnGakkiName.labelStyle}"
										value="#{pc_Ssz00201.propKjnGakkiName.labelName}"></h:outputText></TH>
									<TD width="327"><h:inputText styleClass="inputText"
										id="htmlKjnGakkiName"
										maxlength="#{pc_Ssz00201.propKjnGakkiName.maxLength}" size="6"
										value="#{pc_Ssz00201.propKjnGakkiName.stringValue}"
										style="#{pc_Ssz00201.propKjnGakkiName.style}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="60%" align="left"><h:outputText styleClass="note"
							id="text24" value="＊ 求人業務サイクルが通年の場合、求人学期ＮＯは１しか設定できません。"></h:outputText></TD>
						<TD width="20%">
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz00201.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Ssz00201.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz00201.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz00201.propGakNen.scrollPosition}" id='scroll'></h:inputHidden>
			<h:inputHidden value="#{pc_Ssz00201.propNend.scrollPosition}" id='scroll2'></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
	window.attachEvent('onload', endload);
	function endload() {
		changeScrollPosition('scroll','listScroll');
		changeScrollPosition('scroll2','listScroll2');
	}
</SCRIPT>
</HTML>

