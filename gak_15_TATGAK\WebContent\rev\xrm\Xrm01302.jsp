<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrm/Xrm01302.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrm01302.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/gh/inc/gakuenGH.js"></SCRIPT>
<SCRIPT type="text/javascript"
	src="${pageContext.request.contextPath}/rev/co/inc/gakuenCOG.js"></SCRIPT>
<SCRIPT language="JavaScript"
	src="${pageContext.request.contextPath}/rev/xrm/inc/gakuenXRM.js"></SCRIPT>
<SCRIPT type="text/javascript">
	

//新規登録可能な振込依頼人コード取得Ajax呼び出し
function ajaxNewFurikomiIraiCd() {

	var nyugakuShohiFlg = document.getElementById('form1:htmlNyugakuShohiFlg').value;
	//入学検定日のみajax連携を行う。
	if(nyugakuShohiFlg == "1" || nyugakuShohiFlg == "2"){
		//振込依頼人コード
		var furikomiIraiCd = document.getElementById('form1:htmlSashikaeIraiCd').value;
		//新規登録可能な振込依頼人コード項目id
		var newFurikomiIraiCdId = "form1:htmlNewFurikomiIraiCd";
		
		//新規登録可能な振込依頼人コード取得Ajax呼び出し
		funcAjaxSetNewFurikomiIraiCd(furikomiIraiCd, newFurikomiIraiCdId)
	}
	return true;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrm01302.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrm01302.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrm01302.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrm01302.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			
            <DIV class="head_button_area" >
				<hx:commandExButton type="submit" 
					value="戻る" 
					styleClass="commandExButton_etc"
					id="returnDisp" 
					action="#{pc_Xrm01302.doReturnDispAction}">
				</hx:commandExButton>
            </DIV>
			
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->

			<hx:jspPanel id="jspPanel2">
				<TABLE width="800px" border="0" cellpadding="0" cellspacing="0"
					class="table">
					<TBODY>
						<TR>
							<!-- 入金区分 -->
	 						<TH width="300px" align="left" nowrap class="v_d">
		 						<h:outputText
									styleClass="outputText" id="lblComment"
									value="#{pc_Xrm01302.propComment.labelName}"
									style="#{pc_Xrm01302.propComment.labelStyle}">
								</h:outputText>
							</TH>
							<TD align="left">
								<h:inputText styleClass="likeOutput"
									id="htmlComment"
									value="#{pc_Xrm01302.propComment.stringValue}"
									style="#{pc_Xrm01302.propComment.style}"
									readonly="#{pc_Xrm01302.propComment.readonly}"
									size="50"
									tabindex="-1">
								</h:inputText>
							</TD>
						</TR>
						<TR>
							<!-- 振込依頼人コード -->
	 						<TH align="left" nowrap class="v_d">
		 						<h:outputText
									styleClass="outputText" id="lblFurikomiIraiCd"
									value="#{pc_Xrm01302.propFurikomiIraiCd.labelName}"
									style="#{pc_Xrm01302.propFurikomiIraiCd.labelStyle}">
								</h:outputText>
							</TH>
							<TD align="left">
								<h:inputText styleClass="likeOutput"
									id="htmlFurikomiIraiCd"
									value="#{pc_Xrm01302.propFurikomiIraiCd.stringValue}"
									style="#{pc_Xrm01302.propFurikomiIraiCd.style}"
									readonly="#{pc_Xrm01302.propFurikomiIraiCd.readonly}"
									tabindex="-1">
								</h:inputText>
							</TD>
						</TR>
						<TR>
							<!-- 振込依頼人氏名 -->
	 						<TH align="left" nowrap class="v_d">
		 						<h:outputText
									styleClass="outputText" id="lblFurikomiIraiName"
									value="#{pc_Xrm01302.propFurikomiIraiName.labelName}"
									style="#{pc_Xrm01302.propFurikomiIraiName.labelStyle}">
								</h:outputText>
							</TH>
							<TD align="left">
								<h:inputText 
									styleClass="likeOutput" id="htmlFurikomiIraiName"
									value="#{pc_Xrm01302.propFurikomiIraiName.stringValue}"
									style="#{pc_Xrm01302.propFurikomiIraiName.style}"
									readonly="#{pc_Xrm01302.propFurikomiIraiName.readonly}"
									tabindex="-1">
								</h:inputText>
							</TD>
						</TR>
						<TR>
							<!-- 入金額 -->
							<TH align="left" nowrap class="v_d">
								<h:outputText
									styleClass="outputText" id="lblNyukinGaku"
									value="#{pc_Xrm01302.propNyukinGaku.labelName}"
									style="#{pc_Xrm01302.propNyukinGaku.labelStyle}">
								</h:outputText>
							</TH>
							<TD align="left">
								<h:inputText 
									styleClass="likeOutput" id="htmlNyukinGaku"
									value="#{pc_Xrm01302.propNyukinGaku.stringValue}"
									style="#{pc_Xrm01302.propNyukinGaku.style}"
									readonly="#{pc_Xrm01302.propNyukinGaku.readonly}"
									tabindex="-1">
								</h:inputText>
							</TD>
						</TR>
						<TR>
							<!-- 勘定日 -->
							<TH align="left" nowrap class="v_c">
								<h:outputText
									styleClass="outputText" id="lblNyukinDate"
									value="#{pc_Xrm01302.propNyukinDate.labelName}"
									style="#{pc_Xrm01302.propNyukinDate.labelStyle}">
								</h:outputText>
							</TH>
							<TD align="left">
								<h:inputText
									styleClass="likeOutput" id="htmlNyukinDate"
									value="#{pc_Xrm01302.propNyukinDate.dateValue}"
									style="#{pc_Xrm01302.propNyukinDate.style}"
									readonly="#{pc_Xrm01302.propNyukinDate.readonly}"
									tabindex="-1">
									<f:convertDateTime />
								</h:inputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>


			<BR>

			<hx:jspPanel id="jspPanel3" rendered="#{!pc_Xrm01302.strHenkan}">
				<TABLE border="0" width="800">
					<TBODY>
						<TR>
							<TD>
	
							<div class="listScroll" style="height:254px;" id="listScroll"
								><h:dataTable
								border="0" cellpadding="2" cellspacing="0"
								headerClass="headerClass" footerClass="footerClass"
								rowClasses="#{pc_Xrm01302.propXrkSyomBmn.rowClasses}"
								styleClass="meisai_scroll" id="table1"
								value="#{pc_Xrm01302.propXrkSyomBmn.list}" var="varlist">
	
								<h:column id="column1">
									<f:facet name="header">
										<h:outputText id="lblgakuhiNendo_head" styleClass="outputText"
											value="学費年度">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="gakuhiNendo"
										value="#{varlist.gakuhiNendo}"></h:outputText>
									<f:attribute value="70" name="width" />
									<f:attribute value="middle" name="valign" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
	
								<h:column id="column2">
									<f:facet name="header">
										<h:outputText id="lblpayCd_head" styleClass="outputText"
											value="納付金コード">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="payCd"
										value="#{varlist.payCd}"></h:outputText>
									<f:attribute value="60" name="width" />
									<f:attribute value="middle" name="valign" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
	
	
								<h:column id="column3">
									<f:facet name="header">
										<h:outputText id="lblpatternName_head"
											styleClass="outputText" value="パターン名称">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="patternName"
										value="#{varlist.patternName}"></h:outputText>
									<f:attribute value="330" name="width" />
									<f:attribute value="middle" name="valign" />
									<f:attribute value="text-align: left" name="style" />
								</h:column>
	
	
								<h:column id="column4">
									<f:facet name="header">
										<h:outputText id="lblbunnoKbn_head"
											styleClass="outputText" value="分納区分">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="bunnoKbn"
										value="#{varlist.bunnoKbn}"></h:outputText>
									<f:attribute value="40" name="width" />
									<f:attribute value="middle" name="valign" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
	
	
								<h:column id="column5">
									<f:facet name="header">
										<h:outputText id="lblbunkatsuNo_head"
											styleClass="outputText" value="分割ＮＯ">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="bunkatsuNo"
										value="#{varlist.bunkatsuNo}"></h:outputText>
									<f:attribute value="40" name="width" />
									<f:attribute value="middle" name="valign" />
									<f:attribute value="text-align: center" name="style" />
								</h:column>
	
								<h:column id="column6">
									<f:facet name="header">
										<h:outputText id="lblpayGaku_head" styleClass="outputText"
											value="請求額">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="payGaku"
										value="#{varlist.payGakuComma}"></h:outputText>
									<f:attribute value="60" name="width" />
									<f:attribute value="middle" name="valign" />
									<f:attribute value="text-align: right" name="style" />
								</h:column>
	
								<h:column id="column7">
									<f:facet name="header">
										<h:outputText id="lblbikou_head" styleClass="outputText"
											value="備考">
										</h:outputText>
									</f:facet>
									<h:outputText styleClass="outputText" id="bikou"
										value="#{varlist.bikou}" escape="false"></h:outputText>
									<f:attribute value="180" name="width" />
									<f:attribute value="middle" name="valign" />
									<f:attribute value="text-align: left" name="style" />
								</h:column>
	
								<h:column id="column8">
									<f:facet name="header">
									</f:facet>
									<f:attribute value="50" name="width">
									</f:attribute>
									<f:attribute value="text-align: center; vertical-align: middle"
										name="style">
									</f:attribute>
									<hx:commandExButton type="submit" value="入金"
										styleClass="commandExButton" id="select"
										action="#{pc_Xrm01302.doNyukinAction}"
										onclick="return confirm('入金処理を実行します。よろしいですか？')">
									</hx:commandExButton>
								</h:column>
							</h:dataTable></div>
							</TD>
							<TD></TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			
			
			<hx:jspPanel id="jspPanel4" rendered="#{pc_Xrm01302.strHenkan}">
				<TABLE width="800px" border="0" cellpadding="0" cellspacing="0"
					class="table">
					<TBODY>
						<TR><!-- 差替先振込依頼人コード・入替先振込依頼人コード -->
	 						<TH width="300px" align="left" nowrap class="v_d">
		 						<h:outputText
									styleClass="outputText" id="lblSashikaeIraiCd"
									value="#{pc_Xrm01302.propSashikaeIraiCd.labelName}"
									style="#{pc_Xrm01302.propSashikaeIraiCd.labelStyle}">
								</h:outputText>
							</TH>
							<TD align="left">
								<h:inputText styleClass="inputText"
									id="htmlSashikaeIraiCd"
									value="#{pc_Xrm01302.propSashikaeIraiCd.stringValue}"
									style="#{pc_Xrm01302.propSashikaeIraiCd.style}"
									disabled="#{pc_Xrm01302.propSashikaeIraiCd.disabled}" 
									onblur="return ajaxNewFurikomiIraiCd();"
									maxlength="10"
									style="padding-right: 3px; " tabindex="12"
									size="10">
								</h:inputText>
								<!-- 新規登録可能な振込依頼人コード -->
								<h:inputText styleClass="likeOutput"
									id="htmlNewFurikomiIraiCd" tabindex="-1"
									value="#{pc_Xrm01302.propNewFurikomiIraiCd.stringValue}"
									style="#{pc_Xrm01302.propNewFurikomiIraiCd.style}"
									readonly="#{pc_Xrm01302.propNewFurikomiIraiCd.readonly}"
									size="10">
								</h:inputText>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
				<TABLE width="800px" border="0" cellpadding="0" cellspacing="0">
					<TBODY>
						<TR>
							<TD align="left">
								<font size="2"><B>※既に存在する振込依頼人コードを変換します。</B></font>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			
			<hx:jspPanel id="jspPanel5" rendered="#{pc_Xrm01302.strHenkan}">
				<TABLE border="0" width="100%" class="button_bar">
					<TBODY>
						<TR>
							<TD align="center">
								<hx:commandExButton type="submit" value="確定"
									styleClass="commandExButton_dat" id="register"
									action="#{pc_Xrm01302.doRegisterAction}" confirm="#{msg.SY_MSG_0001W}">
								</hx:commandExButton>
							</TD>
						</TR>
					</TBODY>
				</TABLE>
			</hx:jspPanel>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			
			<h:inputHidden value="#{pc_Xrm01302.nyugakuShohiFlg}"
				id="htmlNyugakuShohiFlg">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
</HTML>




