<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01902.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01902.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz01902.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz01902.doCloseDispAction}"
				c></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz01902.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz01902.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton" id="moveBack"
				action="#{pc_Ssz01902.doReturnDispAction}"></hx:commandExButton> <!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="100%">
				<tr>
					<td width="18%"></td>
					<td width="560">
					<TABLE border="0" class="table" width="560">
						<TBODY>
							<TR>
								<TH class="v_a" width="223"><B><h:outputText
									styleClass="outputText" id="lblFreTsyCd"
									value="#{pc_Ssz01902.propFreTsyCd.labelName}"></h:outputText></B></TH>
								<TD width="329"><B><h:outputText styleClass="outputText"
									id="htmlFreTsyCd"
									value="#{pc_Ssz01902.propFreTsyCd.stringValue}"></h:outputText></B></TD>
							</TR>
							<TR>
								<TH class="v_b" width="223"><B><h:outputText
									styleClass="outputText" id="lblFreTsyName"
									value="#{pc_Ssz01902.propFreTsyName.name}"></h:outputText></B></TH>
								<TD width="329"><B><h:outputText styleClass="outputText"
									id="htmlFreTsyName"
									value="#{pc_Ssz01902.propFreTsyName.stringValue}"></h:outputText></B></TD>
							</TR>
						</TBODY>
					</TABLE>
					</td>
					<td></td>
				</tr>
			</TABLE>

			<TABLE width="560" border="0"
				style="margin-left:-30px;margin-top:10px">
				<TBODY>
					<TR>
						<TD></TD>
						<TD align="right"><h:outputText styleClass="outputText"
							id="htmlCount" value="#{pc_Ssz01902.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text4" value="件"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE width="100%">
				<tr>
					<td width="18%"></td>
					<td width="560">
					<div id="listScroll" class="listScroll"
						style="height:191px;width: 560px;"
						onscroll="setScrollPosition('scroll',this);"><h:dataTable
						border="0" cellpadding="2" cellspacing="0"
						headerClass="headerClass" footerClass="footerClass"
						rowClasses="#{pc_Ssz01902.propKomokList.rowClasses}"
						styleClass="meisai_scroll" id="table1" width="543"
						value="#{pc_Ssz01902.propKomokList.list}" var="varlist"
						first="#{pc_Ssz01902.propKomokList.first}"
						rows="#{pc_Ssz01902.propKomokList.rows}">
						<h:column id="column1">
							<f:facet name="header">
								<h:outputText id="text1" styleClass="outputText" value="ＮＯ"></h:outputText>
							</f:facet>
							<f:attribute value="110" name="width" />
							<h:outputText styleClass="outputText" id="text9"
								value="#{varlist.freKomokNo}"></h:outputText>
						</h:column>
						<h:column id="column2">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="代表" id="text2"></h:outputText>
							</f:facet>
							<f:attribute value="86" name="width" />
							<h:outputText styleClass="outputText" id="text10"
								value="#{varlist.titleFlg}"></h:outputText>
							<f:attribute value="text-align: center" name="style" />
						</h:column>
						<h:column id="column3">
							<f:facet name="header">
								<h:outputText styleClass="outputText" value="名称" id="text3"></h:outputText>
							</f:facet>
							<f:attribute value="354" name="width" />
							<h:outputText styleClass="outputText" id="text11"
								value="#{varlist.freKomokName}"></h:outputText>
						</h:column>
						<h:column id="column4">
							<f:facet name="header">
							</f:facet>
							<f:attribute value="24" name="width" />
							<hx:commandExButton type="submit" value="選択"
								styleClass="commandExButton" id="select"
								action="#{pc_Ssz01902.doSelectAction}"></hx:commandExButton>
						</h:column>
					</h:dataTable></div>
					</td>
					<td></td>
				</tr>
			</TABLE>
			<br>
			<TABLE width="100%">
				<tr>
					<td width="18%"></td>
					<td width="560">
					<TABLE width="560" border="0" class="table">
						<TBODY>
							<TR>
								<TH class="v_a"><h:outputText styleClass="outputText"
									id="lblKomokNo" value="#{pc_Ssz01902.propKomokNo.labelName}"
									style="#{pc_Ssz01902.propKomokNo.labelStyle}"></h:outputText></TH>
								<TD><h:inputText styleClass="inputText" id="htmlKomokNo"
									value="#{pc_Ssz01902.propKomokNo.stringValue}"
									style="#{pc_Ssz01902.propKomokNo.style}"
									maxlength="#{pc_Ssz01902.propKomokNo.maxLength}" size="4"></h:inputText></TD>
							</TR>
							<TR>
								<TH class="v_b"><h:outputText styleClass="outputText"
									id="lblKomokName"
									value="#{pc_Ssz01902.propKomokName.labelName}"
									style="#{pc_Ssz01902.propKomokName.labelStyle}"></h:outputText></TH>
								<TD><h:inputText styleClass="inputText" id="htmlKomokName"
									value="#{pc_Ssz01902.propKomokName.stringValue}"
									style="#{pc_Ssz01902.propKomokName.style}"
									maxlength="#{pc_Ssz01902.propKomokName.maxLength}" size="21"></h:inputText></TD>
							</TR>
							<TR>
								<TH class="v_c"><h:outputText styleClass="outputText"
									id="lblKomokDyho" value="#{pc_Ssz01902.propKomokDyho.name}"></h:outputText></TH>
								<TD><h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
									id="htmlKomokDyho" value="#{pc_Ssz01902.propKomokDyho.checked}"></h:selectBooleanCheckbox></TD>
							</TR>
						</TBODY>
					</TABLE>
					</td>
					<td></td>
				</tr>
			</TABLE>
			<br>
			<DIV align="right">
			<TABLE width="100%" border="0" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Ssz01902.doRegisterAction}"></hx:commandExButton>
							 <hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Ssz01902.doDeleteAction}"></hx:commandExButton> <hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz01902.doClearAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz01902.propKomokList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

