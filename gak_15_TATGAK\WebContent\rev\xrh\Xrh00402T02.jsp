<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00402T02.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
 	//	画面ロード時の処理
	function formLoad(thisEvent) {
		//	試験地名称再表示
		doSikentiAjax(document.getElementById('form1:htmlSikentiCd'),
			thisEvent, 'form1:lblSikentiName');
		//	会場情報再表示
		doKaijyoAjax(document.getElementById('form1:htmlKaijyo'),
			thisEvent, 'form1:htmlYubinNo');
	}
	
	//	試験地名称表示（Ajax）
	function doSikentiAjax( thisObj, thisEvent, target ) {
  		var servlet = "rev/xrh/XrhMeiSikentiNmAJAX";
  		getCodeName(servlet, target, thisObj.value);
	}

	//	試験地名称検索画面
	function openSikentiSearchWindow(thisObj, thisEvent) {
		var url="${pageContext.request.contextPath}/faces/rev/xrh/pXrh0101.jsp?retFieldName=form1:htmlSikentiCd";
		openModalWindow(url, "pXrh0101", "<%= com.jast.gakuen.rev.xrh.PXrh0101.getWindowOpenOption() %>");
		return true;
	}

	// 戻るボタン押下時処理
	function onClickReturnDisp(id) {
		var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
		if(changeDataFlg == "1"){
			return confirm(id);
		}
		
		return true;
	}
	
	// データ変更時
	function onChangeData() {
		document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
	}

	//	会場変更時の処理
	function onChangeKaijyo(thisObj, thisEvent) {
		//	* 会場変更した際、郵便番号・住所・電話番号をAjaxにてセットする
		doKaijyoAjax(thisObj, thisEvent, 'form1:htmlYubinNo');
	}
	
	//	会場情報表示（Ajax）
	function doKaijyoAjax( thisObj, thisEvent, targetLabel ) {
		var servlet = "rev/xrh/XrhKmkKjoDetailAJAX";
	    var args = new Array();
	    args['kaijoCd'] = thisObj.value;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getPluralValueSetMethod(servlet, targetLabel, args, "setKaijyoInfo");
	}
	
	// 会場情報を取得する（CallBack関数）
	function setKaijyoInfo(value){
		document.getElementById('form1:htmlYubinNo').innerHTML = value['postCd'];
		document.getElementById('form1:htmlAddress').innerHTML = value['addrAll'];
		document.getElementById('form1:htmlTel').innerHTML = value['tel'];
	}

	// ポップアップメッセージを表示
	function doPopupMsg(id, msg) {
		var args = new Array();
		args[0] = msg;
		if (confirm(messageCreate(id, args))) {
			onChangeData();
			return true;
		}
	  
		return false;
	}
	
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="formLoad(event);">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00402T02.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00402T02.xrh00402.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00402T02.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00402T02.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここにボタンを配置 -->
			<TABLE>
				<TR>
					<TD nowrap align="right"><hx:commandExButton type="submit"
						value="戻　る" styleClass="commandExButton" id="returnDisp"
						tabindex="53"
						action="#{pc_Xrh00402T02.xrh00402.doReturnDispAction}"
						onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
					</hx:commandExButton></TD>
				</TR>
			</TABLE>
			<!-- ↑ここにボタンを配置 --></DIV>

			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE border="0" cellpadding="5">
				<TBODY>
					<TR>
						<TD width="800" valign="top"><!-- ↓タブ間共有テーブル↓ -->
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="190"><!--年度 --> <h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Xrh00402T02.xrh00402.propNendo.labelName}"
										style="#{pc_Xrh00402T02.xrh00402.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD colspan="3"><h:inputText styleClass="inputText"
										id="htmlNendo" size="4"
										value="#{pc_Xrh00402T02.xrh00402.propNendo.dateValue}"
										readonly="#{pc_Xrh00402T02.xrh00402.propNendo.readonly}"
										disabled="#{pc_Xrh00402T02.xrh00402.propNendo.disabled}"
										style="#{pc_Xrh00402T02.xrh00402.propNendo.style}">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
									<TD width="100" align="right" rowspan="2"
										style="background-color: transparent; text-align: right"
										class="clear_border"><hx:commandExButton type="submit"
										value="#{pc_Xrh00402T02.xrh00402.propSelect.caption}"
										styleClass="commandExButton" id="select"
										disabled="#{pc_Xrh00402T02.xrh00402.propSelect.disabled}"
										rendered="#{pc_Xrh00402T02.xrh00402.propSelect.rendered}"
										style="#{pc_Xrh00402T02.xrh00402.propSelect.style}"
										action="#{pc_Xrh00402T02.xrh00402.doSelectAction}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="#{pc_Xrh00402T02.xrh00402.propUnSelect.caption}"
										styleClass="commandExButton" id="unselect"
										disabled="#{pc_Xrh00402T02.xrh00402.propUnSelect.disabled}"
										rendered="#{pc_Xrh00402T02.xrh00402.propUnSelect.rendered}"
										style="#{pc_Xrh00402T02.xrh00402.propUnSelect.style}"
										action="#{pc_Xrh00402T02.xrh00402.doUnSelectAction}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenKaisu"
										style="#{pc_Xrh00402T02.xrh00402.propSikenKaisu.labelStyle}"
										value="#{pc_Xrh00402T02.xrh00402.propSikenKaisu.labelName}">
									</h:outputText></TH>
									<TD width="200"><h:inputText id="htmlSikenKaisu"
										styleClass="inputText" size="4"
										value="#{pc_Xrh00402T02.xrh00402.propSikenKaisu.integerValue}"
										readonly="#{pc_Xrh00402T02.xrh00402.propSikenKaisu.readonly}"
										disabled="#{pc_Xrh00402T02.xrh00402.propSikenKaisu.disabled}"
										style="#{pc_Xrh00402T02.xrh00402.propSikenKaisu.style}">
										<f:convertNumber type="number" pattern="#0" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
									<TH class="v_a" width="190"><h:outputText
										styleClass="outputText" id="lblSikenbiYobi"
										style="#{pc_Xrh00402T02.xrh00402.propSikenbiYoubi.labelStyle}"
										value="#{pc_Xrh00402T02.xrh00402.propSikenbiYoubi.labelName}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSikenbiYoubi"
										value="#{pc_Xrh00402T02.xrh00402.propSikenbiYoubi.stringValue}"
										style="#{pc_Xrh00402T02.xrh00402.propSikenbiYoubi.style};width:128px"
										disabled="#{pc_Xrh00402T02.xrh00402.propSikenbiYoubi.disabled}">
										<f:selectItems
											value="#{pc_Xrh00402T02.xrh00402.propSikenbiYoubi.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						<!-- ↑タブ間共有テーブル↑ --> <BR>
						<TABLE border="0" cellpadding="20" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="800" align="left">
									<TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="日程・時間割" id="moveNiteiJikanTab"
													disabled="#{pc_Xrh00402T02.xrh00402.propMoveNiteiJikanTab.disabled}"
													styleClass="tab_head_off" action="Xrh00402T01">
												</hx:commandExButton></TD>
												<TD class="tab_head_on"><hx:commandExButton type="button"
													disabled="#{pc_Xrh00402T02.xrh00402.propMoveLocateTab.disabled}"
													value="場所情報" styleClass="tab_head_on" id="moveLocateTab">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="試験監督" styleClass="tab_head_off"
													disabled="#{pc_Xrh00402T02.xrh00402.propMoveSikenKantokuTab.disabled}"
													id="moveSikenKantokuTab" action="Xrh00402T03">
												</hx:commandExButton></TD>
												<TD class="tab_head_off"><hx:commandExButton type="submit"
													value="作問情報" styleClass="tab_head_off" id="moveSakumonTab"
													disabled="#{pc_Xrh00402T02.xrh00402.propMoveSakumonTab.disabled}"
													action="Xrh00402T04">
												</hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD valign="top">
									<TABLE border="0" cellpadding="0" cellspacing="0"
										class="tab_body" width="100%">
										<TBODY>
											<TR>
												<TD>
												<CENTER><BR>
												<TABLE border="0" cellpadding="5">
													<TBODY>
														<TR>
															<TD>
															<DIV id="listScroll" class="listScroll"
																style="height: 216px;"
																onscroll="setScrollPosition('scroll',this);"><h:dataTable
																columnClasses="columnClass" headerClass="headerClass"
																footerClass="footerClass"
																rowClasses="#{pc_Xrh00402T02.propBasyoList.rowClasses}"
																styleClass="meisai_scroll" id="htmlBasyoList"
																value="#{pc_Xrh00402T02.propBasyoList.list}"
																var="varlist">

																<h:column id="column1">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="予約"
																			id="lblListYoyakuColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.yoyakuDisp.stringValue}"
																		id="htmlListYoyaku">
																	</h:outputText>
																	<f:attribute value="32" name="width" />
																	<f:attribute value="text-align: center" name="style" />
																</h:column>


																<h:column id="column2">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="試験地"
																			id="lblListSikentiColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.sikentiName.displayValue}"
																		title="#{varlist.sikentiName.stringValue}"
																		id="htmlListSikenti">
																	</h:outputText>
																	<f:attribute value="80" name="width" />
																</h:column>


																<h:column id="column3">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="会場名"
																			id="lblListKaijyoNameColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.kaijoName.displayValue}"
																		title="#{varlist.kaijoName.stringValue}"
																		id="htmlListKaijyoName">
																	</h:outputText>
																	<f:attribute value="256" name="width" />
																</h:column>

																<h:column id="column4">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="教室名"
																			id="lblListKyousituNameColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.kyosituName.displayValue}"
																		title="#{varlist.kyosituName.stringValue}"
																		id="htmlListKyousituName">
																	</h:outputText>
																	<f:attribute value="128" name="width" />
																</h:column>

																<h:column id="column5">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText" value="控室名"
																			id="lblListHikaeNameColumn">
																		</h:outputText>
																	</f:facet>
																	<h:outputText styleClass="outputText"
																		value="#{varlist.hikaesituName.displayValue}"
																		title="#{varlist.hikaesituName.stringValue}"
																		id="htmlListHikaeName">
																	</h:outputText>
																	<f:attribute value="128" name="width" />
																</h:column>

																<h:column id="column6">
																	<f:facet name="header">
																	</f:facet>
																	<hx:commandExButton type="submit"
																		value="#{varlist.btnEdit.caption}"
																		action="#{pc_Xrh00402T02.doEditAction}"
																		styleClass="commandExButton" id="edit">
																	</hx:commandExButton>
																	<f:attribute value="32" name="width" />
																	<f:attribute value="true" name="nowrap" />
																</h:column>
															</h:dataTable></DIV>
															</TD>
														</TR>
													</TBODY>
												</TABLE>

												<BR>

												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="table">
													<TBODY>
														<TR>
															<TH nowrap class="v_a" width="100"><!--試験地 --> <h:outputText
																styleClass="outputText" id="lblSikenti"
																value="#{pc_Xrh00402T02.propSikentiCd.labelName}"
																style="#{pc_Xrh00402T02.propSikentiCd.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:inputText styleClass="inputText"
																id="htmlSikentiCd" size="4" onchange="onChangeData();"
																value="#{pc_Xrh00402T02.propSikentiCd.stringValue}"
																onblur="return doSikentiAjax(this, event, 'form1:lblSikentiName');"
																disabled="#{pc_Xrh00402T02.propSikentiCd.disabled}"
																maxlength="#{pc_Xrh00402T02.propSikentiCd.maxLength}"
																style="#{pc_Xrh00402T02.propSikentiCd.style}">
																<hx:inputHelperAssist imeMode="disabled"
																	errorClass="inputText_Error" />
															</h:inputText> <hx:commandExButton type="button"
																styleClass="commandExButton_search" id="searchSikenti"
																disabled="#{pc_Xrh00402T02.propSearchSikenti.disabled}"
																onclick="return openSikentiSearchWindow(this, event);">
															</hx:commandExButton> <hx:commandExButton type="submit"
																value="#{pc_Xrh00402T02.propSelectSikenti.caption}"
																styleClass="commandExButton" id="selectSikenti"
																disabled="#{pc_Xrh00402T02.propSelectSikenti.disabled}"
																rendered="#{pc_Xrh00402T02.propSelectSikenti.rendered}"
																style="#{pc_Xrh00402T02.propSelectSikenti.style}"
																action="#{pc_Xrh00402T02.doSelectAction}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																value="#{pc_Xrh00402T02.propUnSelectSikenti.caption}"
																styleClass="commandExButton" id="unselectSikenti"
																disabled="#{pc_Xrh00402T02.propUnSelectSikenti.disabled}"
																rendered="#{pc_Xrh00402T02.propUnSelectSikenti.rendered}"
																style="#{pc_Xrh00402T02.propUnSelectSikenti.style}"
																action="#{pc_Xrh00402T02.doUnSelectAction}">
															</hx:commandExButton> <h:outputText
																styleClass="outputText" id="lblSikentiName"
																value="#{pc_Xrh00402T02.propSikentiName.labelName}"
																style="#{pc_Xrh00402T02.propSikentiName.labelStyle}">
															</h:outputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="100"><!--会場 --> <h:outputText
																styleClass="outputText" id="lblKaijyo"
																value="#{pc_Xrh00402T02.propKaijyo.labelName}"
																style="#{pc_Xrh00402T02.propKaijyo.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:selectOneMenu
																styleClass="selectOneMenu" id="htmlKaijyo"
																value="#{pc_Xrh00402T02.propKaijyo.stringValue}"
																style="#{pc_Xrh00402T02.propKaijyo.style};width:100%"
																onchange="onChangeData(); return onChangeKaijyo(this, event);"
																disabled="#{pc_Xrh00402T02.propKaijyo.disabled}">
																<f:selectItems value="#{pc_Xrh00402T02.propKaijyo.list}" />
															</h:selectOneMenu></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="100"><!--教室名 --> <h:outputText
																styleClass="outputText" id="lblKyousituName"
																value="#{pc_Xrh00402T02.propKyousituName.labelName}"
																style="#{pc_Xrh00402T02.propKyousituName.labelStyle}">
															</h:outputText></TH>
															<TD colspan="2"><h:inputText styleClass="inputText"
																id="htmlKyousituName" size="40"
																onchange="onChangeData();"
																maxlength="#{pc_Xrh00402T02.propKyousituName.maxLength}"
																value="#{pc_Xrh00402T02.propKyousituName.stringValue}"
																disabled="#{pc_Xrh00402T02.propKyousituName.disabled}"
																style="#{pc_Xrh00402T02.propKyousituName.style}">
															</h:inputText></TD>
															<TD nowrap><h:outputText styleClass="outputText"
																value="椅子">
															</h:outputText> <h:inputText styleClass="inputText"
																id="htmlKyousituChair" size="4"
																onchange="onChangeData();"
																value="#{pc_Xrh00402T02.propKyousituChair.integerValue}"
																disabled="#{pc_Xrh00402T02.propKyousituChair.disabled}"
																maxlength="#{pc_Xrh00402T02.propKyousituChair.maxLength}"
																style="#{pc_Xrh00402T02.propKyousituChair.style}">
																<f:convertNumber type="number" pattern="##0" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
															</h:inputText> <h:outputText styleClass="outputText"
																value="人　定員">
															</h:outputText> <h:inputText styleClass="inputText"
																id="htmlKyousituTein" size="4"
																onchange="onChangeData();"
																value="#{pc_Xrh00402T02.propKyousituTein.integerValue}"
																disabled="#{pc_Xrh00402T02.propKyousituTein.disabled}"
																maxlength="#{pc_Xrh00402T02.propKyousituTein.maxLength}"
																style="#{pc_Xrh00402T02.propKyousituTein.style}">
																<f:convertNumber type="number" pattern="##0" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
															</h:inputText> <h:outputText styleClass="outputText"
																value="人">
															</h:outputText></TD>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="100"><!--控室名 --> <h:outputText
																styleClass="outputText" id="lblHikaeName"
																value="#{pc_Xrh00402T02.propHikaeName.labelName}"
																style="#{pc_Xrh00402T02.propHikaeName.labelStyle}">
															</h:outputText></TH>
															<TD colspan="2"><h:inputText styleClass="inputText"
																id="htmlHikaeName" size="40" onchange="onChangeData();"
																maxlength="#{pc_Xrh00402T02.propHikaeName.maxLength}"
																value="#{pc_Xrh00402T02.propHikaeName.stringValue}"
																disabled="#{pc_Xrh00402T02.propHikaeName.disabled}"
																style="#{pc_Xrh00402T02.propHikaeName.style}">
															</h:inputText></TD>
															<TD nowrap><h:outputText styleClass="outputText"
																value="椅子">
															</h:outputText> <h:inputText styleClass="inputText"
																id="htmlHikaeChair" size="4" onchange="onChangeData();"
																value="#{pc_Xrh00402T02.propHikaeChair.integerValue}"
																disabled="#{pc_Xrh00402T02.propHikaeChair.disabled}"
																maxlength="#{pc_Xrh00402T02.propHikaeChair.maxLength}"
																style="#{pc_Xrh00402T02.propHikaeChair.style}">
																<f:convertNumber type="number" pattern="##0" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
															</h:inputText> <h:outputText styleClass="outputText"
																value="人　定員">
															</h:outputText> <h:inputText styleClass="inputText"
																id="htmlHikaeTein" size="4" onchange="onChangeData();"
																value="#{pc_Xrh00402T02.propHikaeTein.integerValue}"
																disabled="#{pc_Xrh00402T02.propHikaeTein.disabled}"
																maxlength="#{pc_Xrh00402T02.propHikaeTein.maxLength}"
																style="#{pc_Xrh00402T02.propHikaeTein.style}">
																<f:convertNumber type="number" pattern="##0" />
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
															</h:inputText> <h:outputText styleClass="outputText"
																value="人">
															</h:outputText>
														</TR>
														<TR>
															<TH nowrap class="v_a" width="100"><!--予約済みフラグ --> <h:outputText
																styleClass="outputText" id="lblRadioYoyaku"
																value="#{pc_Xrh00402T02.propRadioYoyaku.labelName}"
																style="#{pc_Xrh00402T02.propRadioYoyaku.labelStyle}">
															</h:outputText></TH>
															<TD width="200"><h:selectOneRadio
																disabledClass="selectOneRadio_Disabled"
																styleClass="selectOneRadio" id="htmlYoyaku"
																onchange="onChangeData();"
																disabled="#{pc_Xrh00402T02.propRadioYoyaku.disabled}"
																value="#{pc_Xrh00402T02.propRadioYoyaku.stringValue}">

																<f:selectItem itemValue="1" itemLabel="済" />
																<f:selectItem itemValue="0" itemLabel="未" />

															</h:selectOneRadio></TD>
															<TH nowrap class="v_a" width="100"><!--借用時間 --> <h:outputText
																styleClass="outputText" id="lblSyakuyo" value="借用時間">
															</h:outputText></TH>
															<TD nowrap><h:inputText styleClass="inputText"
																id="htmlSyakuyoFrom" size="4" onchange="onChangeData();"
																value="#{pc_Xrh00402T02.propSyakuyoFrom.dateValue}"
																disabled="#{pc_Xrh00402T02.propSyakuyoFrom.disabled}"
																style="#{pc_Xrh00402T02.propSyakuyoFrom.style}">
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
																<f:convertDateTime type="time" timeStyle="short" />
																<hx:inputHelperDatePicker delta="60" />
															</h:inputText> <h:outputText styleClass="outputText"
																value="～">
															</h:outputText> <h:inputText styleClass="inputText"
																id="htmlSyakuyoTo" size="4" onchange="onChangeData();"
																value="#{pc_Xrh00402T02.propSyakuyoTo.dateValue}"
																disabled="#{pc_Xrh00402T02.propSyakuyoTo.disabled}"
																style="#{pc_Xrh00402T02.propSyakuyoTo.style}">
																<hx:inputHelperAssist errorClass="inputText_Error"
																	promptCharacter="_" />
																<f:convertDateTime type="time" timeStyle="short" />
																<hx:inputHelperDatePicker delta="60" />
															</h:inputText></TD>
														</TR>

														<TR>
															<TH nowrap class="v_a" width="100"><!--郵便番号 --> <h:outputText
																styleClass="outputText" id="lblYubinNo"
																value="#{pc_Xrh00402T02.propYubinNo.labelName}"
																style="#{pc_Xrh00402T02.propYubinNo.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:outputText styleClass="outputText"
																id="htmlYubinNo"
																style="#{pc_Xrh00402T02.propYubinNo.style}"
																value="#{pc_Xrh00402T02.propYubinNo.stringValue}">
															</h:outputText></TD>
														</TR>

														<TR>
															<TH nowrap class="v_a" width="100"><!--住所 --> <h:outputText
																styleClass="outputText" id="lblAddres"
																value="#{pc_Xrh00402T02.propAddress.labelName}"
																style="#{pc_Xrh00402T02.propAddress.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:outputText styleClass="outputText"
																id="htmlAddress"
																value="#{pc_Xrh00402T02.propAddress.stringValue}">
															</h:outputText></TD>
														</TR>

														<TR>
															<TH nowrap class="v_a" width="100"><!--電話番号 --> <h:outputText
																styleClass="outputText" id="lblTel"
																value="#{pc_Xrh00402T02.propTel.labelName}"
																style="#{pc_Xrh00402T02.propTel.labelStyle}">
															</h:outputText></TH>
															<TD colspan="3"><h:outputText styleClass="outputText"
																id="htmlTel"
																value="#{pc_Xrh00402T02.propTel.stringValue}">
															</h:outputText></TD>
														</TR>
													</TBODY>
												</TABLE>

												<BR>

												<TABLE border="0" cellpadding="0" cellspacing="0"
													class="button_bar">
													<TBODY>
														<TR>
															<TD><hx:commandExButton type="submit"
																styleClass="commandExButton_dat" id="register"
																onclick="return confirm('#{msg.SY_MSG_0002W}');"
																value="確定" action="#{pc_Xrh00402T02.doRegisterAction}"
																disabled="#{pc_Xrh00402T02.propRegister.disabled}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																onclick="return confirm('#{msg.SY_MSG_0004W}');"
																action="#{pc_Xrh00402T02.doDeleteAction}"
																styleClass="commandExButton_dat" id="delete" value="削除"
																disabled="#{pc_Xrh00402T02.propDelete.disabled}">
															</hx:commandExButton> <hx:commandExButton type="submit"
																action="#{pc_Xrh00402T02.doClearAction}"
																onclick="return doPopupMsg('#{msg.SY_MSG_0006W}', '表示内容');"
																styleClass="commandExButton_dat" id="clear" value="クリア"
																disabled="#{pc_Xrh00402T02.propClear.disabled}">
															</hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE>



												</CENTER>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			<h:inputHidden id="htmlHidChangeDataFlg"
				value="#{pc_Xrh00402T02.propHidChangeDataFlg.stringValue}"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrh00402T02.propBasyoList.scrollPosition}"
				id="scroll"></h:inputHidden></DIV>

			</DIV>

		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
	changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
