<%-- 
	レポート科目分冊登録
	
	<AUTHOR>
--%>



<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrf00801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<SCRIPT type="text/javascript">

function confirmOk() {
	var phase;
	phase = parseInt(document.getElementById('form1:htmlBackPhase').value);
	if (phase == 0) {
		document.getElementById('form1:htmlBackPhase').value = 1;
	}
	
	indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlBackPhase').value = 0;
}

function openKamokuSubWindow(field1) {
// 科目検索画面
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;
	openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	return false;
}

function openKamokuSubWindow2(field1) {
// 科目検索画面
	var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
		+ "?retFieldName=" + field1;
	openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	return false;
}

function doKamokuAjax(thisObj, thisEvent, targetLabel, targetLabel2) {
// 科目名称,単位数を取得する
	var servlet = "rev/xrf/XrfKmkAJAX";
    var args = new Array();
    args['code'] = thisObj.value;

    args['func'] = "doKamokuAjax-1";
    
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);


	// 単位数
	if ( targetLabel2 != "" ) {
	    var args2 = new Array();
	    args2['code'] = thisObj.value;
	    args2['tanisu'] = "GET";
	    args2['addString'] = " 単位";

	    args['func'] = "doKamokuAjax-2";

		ajaxUtil.getCodeName(servlet, targetLabel2, args2);
	}
}


function doKamokuAjax2(thisObj, thisEvent, targetLabel) {
// 科目名称を取得する
	var servlet = "rev/xrf/XrfKmkAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    
    args['func'] = "doKamokuAjax2-1";

    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

function loadAction(event) {
// 画面ロード時の学生名称・教員名称の再取得
	doKamokuAjax(document.getElementById('form1:htmlKamokCd'), event, 'form1:lblPreKamokName', 'form1:lblPreKamokTani');
	doKamokuAjax2(document.getElementById('form1:htmlKamokCd2'), event, 'form1:lblPreKamokName2');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00801.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00801.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>
			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
				<TBODY>
					<TR>
						<TD width="718">
						</TD>
 						<TD align="center" valign="top" rowspan="2" colspan="4" width="119" >
							<hx:commandExButton type="submit" value="選択"
								styleClass="commandExButton" id="selectNendo"
								action="#{pc_Xrf00801.doSelectAction}"
								disabled="#{pc_Xrf00801.propSelectNendo.disabled}" tabindex="2">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="解除" id="unSelectNendo"
								styleClass="commandExButton"
								action="#{pc_Xrf00801.doUnSelectAction}"
								disabled="#{pc_Xrf00801.propUnSelectNendo.disabled}" tabindex="3">
							</hx:commandExButton>
						</TD>
						<TR>
							<TD valign="top" height="34" >
								<TABLE border="0" cellpadding="0" cellspacing="0"  class="table" height="30">
									<TBODY>
										<TR>
											<TH class="v_a" width="200">
												<h:outputText
													styleClass="outputText" id="lblNyugkNendo"
													value="#{pc_Xrf00801.propNyugkNendo.labelName}"
													style="#{pc_Xrf00801.propNyugkNendo.labelStyle}">
												</h:outputText>
											</TH>
											<TD colspan="3">
												<h:inputText styleClass="inputText"
													id="htmlNyugkNendo"
													value="#{pc_Xrf00801.propNyugkNendo.dateValue}"
													style="#{pc_Xrf00801.propNyugkNendo.style}"
													disabled="#{pc_Xrf00801.propNyugkNendo.disabled}" size="4"
													tabindex="1">
													<hx:inputHelperAssist errorClass="inputText_Error"
														imeMode="inactive" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
												</h:inputText>
											</TD>
										</TR>
										<TR>
											<TH class="v_b" width="200">
												<h:outputText
													styleClass="outputText" id="lblKamokCd"
													style="#{pc_Xrf00801.propKamokCd.labelStyle}"
													value="#{pc_Xrf00801.propKamokCd.labelName}">
												</h:outputText>
											</TH>
											<TD width="150">
												<h:inputText styleClass="inputText" id="htmlKamokCd"
													value="#{pc_Xrf00801.propKamokCd.stringValue}"
													maxlength="#{pc_Xrf00801.propKamokCd.maxLength}" size="10"
													style="#{pc_Xrf00801.propKamokCd.style}"
													onblur="return doKamokuAjax(this, event, 'form1:lblPreKamokName', 'form1:lblPreKamokTani');"
													disabled="#{pc_Xrf00801.propKamokCd.disabled}" tabindex="4">
													<hx:inputHelperAssist imeMode="disabled" errorClass="inputText_Error" />
												</h:inputText>
												<hx:commandExButton type="button" value=""
													styleClass="commandExButton_search" id="search"
													onclick="return openKamokuSubWindow('form1:htmlKamokCd');"
													disabled="#{pc_Xrf00801.propSearchButton.disabled}"
													tabindex="5">
												</hx:commandExButton>
												<TD width="540">
													<h:outputText styleClass="outputText" id="lblPreKamokName">
													</h:outputText>
												</TD>
												<TD width="100">
													<h:outputText styleClass="outputText" id="lblPreKamokTani">
													</h:outputText>
												</TD>
										</TR>
										<TR>
											<TH class="v_b" width="200">
												<h:outputText
													styleClass="outputText" id="lblTorokuJyokyo"
													value="登録状況">
												</h:outputText>
											</TH>
											<TD colspan="3">
												<h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" 
													id="htmlKadaiKyoinMiToroku" tabindex="6" 
													value="#{pc_Xrf00801.propKadaiKyoinMiToroku.checked}"
													disabled="#{pc_Xrf00801.propKadaiKyoinMiToroku.disabled}">
												</h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText"
													id="lblKadaiKyoinMiToroku" value="課題教員未登録のみ">
												</h:outputText>
												<h:selectBooleanCheckbox
													styleClass="selectBooleanCheckbox" 
													id="htmlTensakuKyoinMiToroku" tabindex="7" 
													value="#{pc_Xrf00801.propTensakuKyoinMiToroku.checked}"
													disabled="#{pc_Xrf00801.propTensakuKyoinMiToroku.disabled}">
												</h:selectBooleanCheckbox>
												<h:outputText styleClass="outputText"
													id="lblTensakuKyoinMiToroku" value="添削教員未登録のみ">
												</h:outputText>
											</TD>
										</TR>
									</TBODY>
								</TABLE>
							</TD>
						</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="800px">
				<TBODY>
					<TR>
						<TD align="right">
							<h:outputText styleClass="outputText" id="htmlCount"
								value="#{pc_Xrf00801.propRptKamokuBnsatuList.listCount}">
							</h:outputText>
							<h:outputText
								styleClass="outputText" id="lblCount" value="件">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>				

			<TABLE border="0" cellpadding="0" cellspacing="0"  width="800px">
				<TBODY>					
					<TR>
						<TD>
							<div id="listScroll" class="listScroll"
								onscroll="setScrollPosition('scroll',this);"
								style="height:270px;">
								<h:dataTable border="0"
									cellpadding="2" cellspacing="0" headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Xrf00801.propRptKamokuBnsatuList.rowClasses}"
									styleClass="meisai_scroll" id="tableRptKamokuBnsatu"
									value="#{pc_Xrf00801.propRptKamokuBnsatuList.list}" var="varlist">

									<h:column id="column1">
										<f:facet name="header">
											<h:outputText id="ListText01" styleClass="outputText" value="科目コード">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKmokuCd"
											value="#{varlist.listKamokCd}">
										</h:outputText>
										<f:attribute value="80" name="width" />
									</h:column>

									<h:column id="column2">
										<f:facet name="header">
											<h:outputText id="ListText02" styleClass="outputText" value="科目名称">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKamokuName"
											value="#{varlist.listKamokName.displayValue}"
											title="#{varlist.listKamokName.stringValue}">
										</h:outputText>
										<f:attribute value="200" name="width" />
									</h:column>

									<h:column id="column3">
										<f:facet name="header">
											<h:outputText id="ListText03" styleClass="outputText" value="分冊">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListBunSatu"
											value="#{varlist.listBunSatu}">
										</h:outputText>
										<f:attribute value="30" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column4">
										<f:facet name="header">
											<h:outputText id="ListText04" styleClass="outputText" value="新旧刊">
											</h:outputText>
										</f:facet>
											<h:outputText styleClass="outputText" id="htmlListSinKyuKan"
												value="#{varlist.listSinKyuKan}">
											</h:outputText>
										<f:attribute value="50" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column5">
										<f:facet name="header">
											<h:outputText id="ListText05" styleClass="outputText" value="提出区分">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListTeisyutuKbn"
											value="#{varlist.listTeisyutu}">
										</h:outputText>
										<f:attribute value="60" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column6">
										<f:facet name="header">
											<h:outputText id="ListText06" styleClass="outputText" value="形式">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKeisiki"
											value="#{varlist.listKeisiki}">
										</h:outputText>
										<f:attribute value="35" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>
									
									<h:column id="column7">
										<f:facet name="header">
											<h:outputText id="ListText07" styleClass="outputText" value="提出">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListTeisyutu"
											value="#{varlist.listTeisyutuFuka}">
										</h:outputText>
										<f:attribute value="40" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>
									
									<h:column id="column8">
										<f:facet name="header">
											<h:outputText id="ListText08" styleClass="outputText" value="レポート文字数">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListRptMojisu"
											value="#{varlist.listRptMojisu}">
										</h:outputText>
										<f:attribute value="130" name="width" />
									</h:column>

									<h:column id="column9">
										<f:facet name="header">
											<h:outputText id="ListText09" styleClass="outputText" value="課題">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKadai"
											value="#{varlist.listKadai}"
											style="text-align: center; vertical-align: middle">
										</h:outputText>
										<f:attribute value="40" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									
									<h:column id="column10">
										<f:facet name="header">
											<h:outputText id="ListText10" styleClass="outputText" value="添削">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListTensaku"
											value="#{varlist.listTensaku}"
											style="text-align: center; vertical-align: middle">
										</h:outputText>
										<f:attribute value="40" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									
									<h:column id="column11">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit" value="編集"
											styleClass="commandExButton" id="changeKamoku"
											action="#{pc_Xrf00801.doEditAction}" tabindex="8">
										</hx:commandExButton>
										<f:attribute value="40" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column12">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit" value="選択"
											styleClass="commandExButton" id="selectKamoku"
											action="#{pc_Xrf00801.doReferAction}" tabindex="9">
										</hx:commandExButton>
										<f:attribute value="40" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>
								</h:dataTable>
							</div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit" value="CSV作成"
								styleClass="commandExButton_dat" id="csvout"
								action="#{pc_Xrf00801.doCsvoutAction}"
								disabled="#{pc_Xrf00801.propCsvout.disabled}"
								confirm="#{msg.SY_MSG_0020W}" tabindex="10">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR noshade class="hr">
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="800" class="table">
				<TBODY>
					<TR>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblKamokCd2"
								style="#{pc_Xrf00801.propKamokCd2.labelStyle}"
								value="#{pc_Xrf00801.propKamokCd2.labelName}">
							</h:outputText>
						</TH>
						<TD width="120">
							<h:inputText styleClass="inputText" id="htmlKamokCd2"
								value="#{pc_Xrf00801.propKamokCd2.stringValue}"
								maxlength="#{pc_Xrf00801.propKamokCd2.maxLength}" size="10"
								style="#{pc_Xrf00801.propKamokCd2.style}"
								onblur="return doKamokuAjax2(this, event, 'form1:lblPreKamokName2');"
								disabled="#{pc_Xrf00801.propKamokCd2.disabled}" tabindex="11">
							<hx:inputHelperAssist imeMode="disabled"
								errorClass="inputText_Error" />
							</h:inputText>
								<hx:commandExButton type="button" value=""
								styleClass="commandExButton_search" id="search2"
								onclick="return openKamokuSubWindow2('form1:htmlKamokCd2');"
								disabled="#{pc_Xrf00801.propSearchButton2.disabled}" tabindex="12">
							</hx:commandExButton>
						</TD>
						<TD colspan="4">
							<h:outputText styleClass="outputText" id="lblPreKamokName2">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblBunsatu"
								value="#{pc_Xrf00801.propBunsatu.labelName}"
								style="#{pc_Xrf00801.propBunsatu.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="5">
							<h:inputText styleClass="inputText" id="htmlBunsatu"
							value="#{pc_Xrf00801.propBunsatu.stringValue}"
							maxlength="#{pc_Xrf00801.propBunsatu.maxLength}"
							style="#{pc_Xrf00801.propBunsatu.style}"
							disabled="#{pc_Xrf00801.propBunsatu.disabled}" size="2"
							tabindex="13">
							<hx:inputHelperAssist imeMode="disabled"
								errorClass="inputText_Error" />
						</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblSinKyuKanKbn"
								value="#{pc_Xrf00801.propSinKyuKanKbn.labelName}"
								style="#{pc_Xrf00801.propSinKyuKanKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="1" width="120">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlSinKyuKanKbn"
								value="#{pc_Xrf00801.propSinKyuKanKbn.stringValue}"
								style="#{pc_Xrf00801.propSinKyuKanKbn.style}"
								disabled="#{pc_Xrf00801.propSinKyuKanKbn.disabled}" tabindex="14">
								<f:selectItems value="#{pc_Xrf00801.propSinKyuKanKbn.list}" />
							</h:selectOneMenu>
						</TD>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblTeisyutuKbn"
								value="提出区分"
								style="#{pc_Xrf00801.propTeisyutuKbn.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="3">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlTeisyutuKbn"
								value="#{pc_Xrf00801.propTeisyutuKbn.stringValue}"
								style="#{pc_Xrf00801.propTeisyutuKbn.style}"
								disabled="#{pc_Xrf00801.propTeisyutuKbn.disabled}" tabindex="15">
								<f:selectItems value="#{pc_Xrf00801.propTeisyutuKbn.list}" />
							</h:selectOneMenu>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblRptKeisiki"
								style="#{pc_Xrf00801.propRptKeisiki.labelStyle}"
								value="レポート形式">
							</h:outputText>
						</TH>
						<TD width="120">
							<h:selectOneMenu styleClass="selectOneMenu" id="htmlKadaiFlag"
								value="#{pc_Xrf00801.propRptKeisiki.stringValue}"
								disabled="#{pc_Xrf00801.propRptKeisiki.disabled}"
								style="#{pc_Xrf00801.propRptKeisiki.style}">
								<f:selectItems value="#{pc_Xrf00801.propRptKeisiki.list}" />
							</h:selectOneMenu>
						</TD>

						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblRptMojisu"
								value="レポート文字数">
							</h:outputText>
						</TH>
						<TD width="200">
							<h:inputText styleClass="inputText"
								id="htmlRptMojisuFrom"
								value="#{pc_Xrf00801.propRptMojisuFrom.stringValue}"
								disabled="#{pc_Xrf00801.propRptMojisuFrom.disabled}" size="4"
								maxlength="#{pc_Xrf00801.propRptMojisuFrom.maxLength}"
								tabindex="16">
							</h:inputText>以上&nbsp;～&nbsp;
							<h:inputText styleClass="inputText"
								id="htmlRptMojisuTo"
								value="#{pc_Xrf00801.propRptMojisuTo.stringValue}"
								disabled="#{pc_Xrf00801.propRptMojisuTo.disabled}" size="4"
								maxlength="#{pc_Xrf00801.propRptMojisuTo.maxLength}"
								tabindex="17">
							</h:inputText>以内
						</TD>
						<TH class="v_b" width="110">
							<h:outputText
								styleClass="outputText" id="lblTeisyutuFuka"
								value="提出不可">
							</h:outputText>
						</TH>
						<TD width="130">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlTeisyutuFuka"
								value="#{pc_Xrf00801.propTeisyutuFuka.checked}"
								disabled="#{pc_Xrf00801.propTeisyutuFuka.disabled}" tabindex="18">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" id="lblWebTeisyutuFuka" value="WEB提出停止">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit" value="確定"
								styleClass="commandExButton_dat" id="register"
								action="#{pc_Xrf00801.doRegisterAction}"
								disabled="#{pc_Xrf00801.propRegister.disabled}"
								confirm="#{msg.SY_MSG_0002W}" tabindex="19">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
								id="delete" action="#{pc_Xrf00801.doDeleteAction}"
								disabled="#{pc_Xrf00801.propDelete.disabled}"
								confirm="#{msg.SY_MSG_0004W}" tabindex="20">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
								id="clear" action="#{pc_Xrf00801.doClearAction}"
								disabled="#{pc_Xrf00801.propClear.disabled}" 
								confirm="#{msg.SY_MSG_0014W}" tabindex="21">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrf00801.propRptKamokuBnsatuList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
			<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrf00801.propBackPhase.integerValue}">
			</h:inputHidden>

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

