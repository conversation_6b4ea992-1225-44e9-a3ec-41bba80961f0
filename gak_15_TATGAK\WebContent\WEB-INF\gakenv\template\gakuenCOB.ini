

#------------------------------------------------
# gakuenCOB.ini
#------------------------------------------------

[PATH]
GAKIMGVIEW=../co/image/gak/
GAKIMGPATH=${WAR_PATH}/rev/co/image/gak/
GAKUPLOADWORKPATH=${WAR_PATH}/rev/co/upload_work/gak/
GAKIMGTEMPPATH=${WAR_PATH}/rev/co/image/gak/temp/
GAKIMGSAMPLE=${WAR_PATH}/rev/image/ja/gak_image_sample.gif

JINJIMGVIEW=../co/image/jinj/
JINJIMGPATH=${WAR_PATH}/rev/co/image/jinj/
JINJIMGTEMPPATH=${WAR_PATH}/rev/co/image/jinj/temp/

SGKSIMGVIEW=../co/image/sgks/
SGKSIMGPATH=${WAR_PATH}/rev/co/image/sgks/
SGKSIMGTEMPPATH=${WAR_PATH}/rev/co/image/sgks/temp/
SGKSIMGSAMPLE=${WAR_PATH}/rev/image/ja/sgks_image_sample.gif

[FILE]
KAOPICTNOCHKFILE=Thumbs.db

[GKWR_SEIGEN_FUNCTION]
GKWR_SEIGEN=1

[AUTH_CNTL]
GAKUSEI=0

[STATS_TABLE]
AGE_BASE_DATE=0501

[GKWR_DATE]
CALENDAR=0