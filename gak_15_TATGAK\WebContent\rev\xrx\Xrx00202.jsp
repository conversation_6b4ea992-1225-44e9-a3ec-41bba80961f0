<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00202.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>

<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xrx00202.jsp</TITLE>

<SCRIPT type="text/javascript">
	function init(){
		changeScrollPosition('htmlScroll','listScroll');
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="init();">
 <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00202.onPageLoadBegin}">
  <h:form styleClass="form" id="form1">
   
   <!-- ヘッダーインクルード -->
   <jsp:include page ="../inc/header.jsp" />

   <!--↓OUTER↓-->
   <DIV class="outer">
    
    <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
     <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	 	styleClass="outputText" escape="false"></h:outputText>
	</FIELDSET>
				
    <DIV id="content">
	 <DIV class="column">
	 
      <TABLE width="860px" cellspacing="0" cellpadding="0" border="0">
       <TBODY>
        
        <TR>
        
         <TD align="right">
          <hx:commandExButton type="submit"
              value="戻る" styleClass="commandExButton" id="htmlBack"
              style="width: 80px; margin-bottom:10px"
			  action="#{pc_Xrx00202.doCloseDispAction}">
          </hx:commandExButton>
         </TD>
        
        </TR>
        
        <TR>
        
         <TD>
          <TABLE width="100%" class="table" cellspacing="0" cellpadding="0" border="0">
           <TBODY>
            <TR>
             <TH class="v_a" width="20%">
              <h:outputText styleClass="outputText" id="lblWorkTypeTitle"
			      value="#{pc_Xrx00202.propWorkType.labelName}"
			      style="#{pc_Xrx00202.propWorkType.labelStyle}">
			  </h:outputText>
			 </TH>
			 <TD width="30%">
              <h:outputText styleClass="outputText" id="lblWorkType"
			      value="#{pc_Xrx00202.propWorkType.stringValue}"
			      style="#{pc_Xrx00202.propWorkType.labelStyle}">
			  </h:outputText>
             </TD>
             <TH class="v_a" width="20%">
              <h:outputText styleClass="outputText" id="lblNameTypeCodeTitle"
			      value="#{pc_Xrx00202.propNameTypeCode.labelName}"
			      style="#{pc_Xrx00202.propNameTypeCode.labelStyle}">
			  </h:outputText>
			 </TH>
			 <TD width="30%">
              <h:outputText styleClass="outputText" id="lblNameTypeCode"
			      value="#{pc_Xrx00202.propNameTypeCode.stringValue}"
			      style="#{pc_Xrx00202.propNameTypeCode.labelStyle}">
			  </h:outputText>
             </TD>
            </TR>
            <TR>
             <TH class="v_a" width="20%">
              <h:outputText styleClass="outputText" id="lblTypeNameTitle"
			      value="#{pc_Xrx00202.propTypeName.labelName}"
			      style="#{pc_Xrx00202.propTypeName.labelStyle}">
			  </h:outputText>
			 </TH>
			 <TD width="80%" colspan="3">
              <h:outputText styleClass="outputText" id="lblTypeName"
			      value="#{pc_Xrx00202.propTypeName.stringValue}"
			      style="#{pc_Xrx00202.propTypeName.labelStyle}">
			  </h:outputText>
             </TD>
            </TR>
           </TBODY>
          </TABLE>
		 </TD>

        </TR>
	    
	    <TR>
	    
         <TD align="right" colspan="4">
          <h:outputText styleClass="outputText"
              id="htmlTotal" value="#{pc_Xrx00202.propItemNameList.listCount}"></h:outputText>
          <h:outputText styleClass="outputText"
              id="labelDataCount" value="件"></h:outputText>
         </TD>

        </TR>
        
        <TR>
        
         <TD colspan="4">
          <DIV style="height: 260px;" id="listScroll" onscroll="setScrollPosition('htmlScroll',this);" class="listScroll">
           <h:dataTable border="0" cellpadding="2" cellspacing="0"
               columnClasses="columnClass1" headerClass="headerClass"
               styleClass="meisai_scroll" footerClass="footerClass"
               rowClasses="#{pc_Xrx00202.propItemNameList.rowClasses}"
               id="htmlItemNameList" value="#{pc_Xrx00202.propItemNameList.list}"
               first="#{pc_Xrx00202.propItemNameList.first}"
               rows="#{pc_Xrx00202.propItemNameList.rows}"
               var="varlist">
          
            <h:column id="column1">
             <h:outputText styleClass="outputText" id="htmlItemNum"
                 title="#{varlist.itemNumOut.value}"
                 value="#{varlist.itemNumOut.displayValue}"></h:outputText>
             <f:facet name="header">
              <h:outputText
                  id="lblItemNum"
                  styleClass="outputText"
                  value="#{pc_Xrx00202.propItemNum.labelName}"
                  style="#{pc_Xrx00202.propItemNum.labelStyle}"></h:outputText>
             </f:facet>
             <f:attribute value="60" name="width" />
            </h:column>
          
            <h:column id="column2">
             <h:outputText styleClass="outputText" id="htmlItemName1"
                 title="#{varlist.itemName1Out.value}"
                 value="#{varlist.itemName1Out.displayValue}"></h:outputText>
             <f:facet name="header">
              <h:outputText
                  id="lblItemName1"
                  styleClass="outputText"
                  value="#{pc_Xrx00202.propItemName1.labelName}"
                  style="#{pc_Xrx00202.propItemName1.labelStyle}"></h:outputText>
             </f:facet>
			 <f:attribute value="380" name="width" />
			</h:column>
          
            <h:column id="column3">
             <h:outputText styleClass="outputText" id="htmlItemName2"
                 title="#{varlist.itemName2Out.value}"
                 value="#{varlist.itemName2Out.displayValue}">
             </h:outputText>
             <f:facet name="header">
              <h:outputText
                  id="lblItemName2"
                  styleClass="outputText"
                  value="#{pc_Xrx00202.propItemName2.labelName}"
                  style="#{pc_Xrx00202.propItemName2.labelStyle}"></h:outputText>
             </f:facet>
             <f:attribute value="380" name="width" />
            </h:column>
          
            <h:column id="column4">
             <hx:commandExButton type="submit" value="選択"
                 styleClass="commandExButton" id="htmlSelectButton"
                 rendered="#{varlist.rendered}"
                 style="width: 40px"
                 action="#{pc_Xrx00202.doSelectAction}">
             </hx:commandExButton>
             <f:facet name="header"></f:facet>
             <f:attribute value="40" name="width" />
            </h:column>
          
           </h:dataTable>
          </DIV>
         </TD>
        
        </TR>
        
       </TBODY>
      </TABLE>
      
      <BR/>
      
      <TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="860px">
       <TBODY>
        <TR>
         <TH class="v_a" width="18%">
          <h:outputText
              styleClass="outputText" id="lblSelectItemNum"
              value="#{pc_Xrx00202.propSelectItemNum.labelName}"
              style="#{pc_Xrx00202.propSelectItemNum.labelStyle}">
          </h:outputText>
         </TH>
         <TD colspan="2">
          <h:inputText styleClass="inputText"
              id="htmlSelectItemNum"
              value="#{pc_Xrx00202.propSelectItemNum.stringValue}"
              style="#{pc_Xrx00202.propSelectItemNum.style}"
              maxlength="#{pc_Xrx00202.propSelectItemNum.maxLength}"
              disabled="#{pc_Xrx00202.propSelectItemNum.disabled}"
              size="10">
           <hx:inputHelperAssist errorClass="inputText_Error" />
          </h:inputText></TD>
        </TR>
        <TR>
         <TH class="v_b">
          <h:outputText
              styleClass="outputText" id="lblSelectItemName1"
              value="#{pc_Xrx00202.propSelectItemName1.labelName}"
              style="#{pc_Xrx00202.propSelectItemName1.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputTextarea styleClass="inputTextarea"
              id="htmlSelectItemName1"
              value="#{pc_Xrx00202.propSelectItemName1.stringValue}"
              disabled="#{pc_Xrx00202.propSelectItemName1.disabled}"
              style="overflow:hidden;"
              cols="33"
              rows="3">
          </h:inputTextarea>
         </TD>
         <TH class="v_c" width="18%">
          <h:outputText
              styleClass="outputText" id="lblSelectItemDescription1"
              value="#{pc_Xrx00202.propSelectItemDescription1.labelName}"
              style="#{pc_Xrx00202.propSelectItemDescription1.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputTextarea styleClass="inputTextarea"
              id="htmlSelectItemDescription1"
              value="#{pc_Xrx00202.propSelectItemDescription1.stringValue}"
              disabled="#{pc_Xrx00202.propSelectItemDescription1.disabled}"
              style="overflow:hidden;"
              cols="33"
              rows="3">
          </h:inputTextarea>
         </TD>
        </TR>
        <TR>
         <TH class="v_b">
          <h:outputText
              styleClass="outputText" id="lblSelectItemName2"
              value="#{pc_Xrx00202.propSelectItemName2.labelName}"
              style="#{pc_Xrx00202.propSelectItemName2.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputTextarea styleClass="inputTextarea"
              id="htmlSelectItemName2"
              value="#{pc_Xrx00202.propSelectItemName2.stringValue}"
              disabled="#{pc_Xrx00202.propSelectItemName2.disabled}"
              style="overflow:hidden;"
              cols="33"
              rows="3">
          </h:inputTextarea>
         </TD>
         <TH class="v_c">
          <h:outputText
              styleClass="outputText" id="lblSelectItemDescription2"
              value="#{pc_Xrx00202.propSelectItemDescription2.labelName}"
              style="#{pc_Xrx00202.propSelectItemDescription2.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputTextarea styleClass="inputTextarea"
              id="htmlSelectItemDescription2"
              value="#{pc_Xrx00202.propSelectItemDescription2.stringValue}"
              disabled="#{pc_Xrx00202.propSelectItemDescription2.disabled}"
              style="overflow:hidden;"
              cols="33"
              rows="3">
          </h:inputTextarea>
         </TD>
        </TR>
       </TBODY>
      </TABLE>
      
      <BR/>
      
      <TABLE border="0" class="button_bar" width="80%">
       <TBODY>
        <TR>
         <TD>
          <SPAN>
          <hx:commandExButton type="submit"
              value="確定" styleClass="commandExButton_dat" id="htmlDetermine"
              confirm="#{msg.SY_MSG_0001W}"
              action="#{pc_Xrx00202.doDetermineAction}"
              disabled="#{pc_Xrx00202.propDetermine.disabled}">
          </hx:commandExButton>
          <SPAN style="margin-left: 20px">
          <hx:commandExButton type="submit" value="クリア" 
              styleClass="commandExButton_etc" id="htmlClear" 
              action="#{pc_Xrx00202.doClearAction}"
              disabled="#{pc_Xrx00202.propClear.disabled}">
          </hx:commandExButton>
          </SPAN>
          </SPAN>
         </TD>
        </TR>
       </TBODY>
      </TABLE>
      
     </DIV>
    </DIV>
   </DIV>
   <!--↑OUTER↑-->
   
   <h:inputHidden id="htmlScroll" value="#{pc_Xrx00202.propItemNameList.scrollPosition}"></h:inputHidden>
			
   <!-- フッダーインクルード -->
   <jsp:include page ="../inc/footer.jsp" />

  </h:form>
 </hx:scriptCollector>
</BODY>

<jsp:include page ="../inc/common.jsp" />

</f:view>
</HTML>
