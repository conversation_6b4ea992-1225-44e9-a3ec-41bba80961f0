<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/gh/Ghe01101.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xri00401.jsp</TITLE>
<SCRIPT type="text/javascript"></SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
    <BODY>
    <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xri00401.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">   

            <!-- ヘッダーインクルード -->
            <jsp:include page ="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;">
                <hx:commandExButton type="submit" value="閉じる"
                    styleClass="commandExButton" id="closeDisp"
                    action="#{pc_Xri00401.doCloseDispAction}">
                </hx:commandExButton>
                <h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xri00401.funcId}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
                <h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xri00401.screenName}"></h:outputText>
            </div>          

            <!--↓outer↓-->
            <DIV class="outer">
            
                <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
                    <h:outputText id="message" 
                    	value="#{requestScope.DISPLAY_INFO.displayMessage}"
                        styleClass="outputText" escape="false">
                    </h:outputText>
                </FIELDSET>

                <!--↓content↓-->
                <DIV class="head_button_area" >　
                <!-- ↓ここに戻る／閉じるボタンを配置 -->

                <!-- ↑ここに戻る／閉じるボタンを配置 -->
                </DIV>
                <DIV id="content">          
                    <DIV class="column" align="center">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="900px">
							<TBODY>
								<TR>
									<TD>
										<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
											<TBODY>
												<TR>
													<!-- 出学種別 -->
													<TH nowrap class="v_a" width="220">
														<h:outputText
															styleClass="outputText" id="lblStgkSbt" value="出学種別"
															style="#{pc_Xri00401.propStgkSbt.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="450">
														<h:selectOneMenu styleClass="selectOneMenu"
															id="htmlStgkSbt"
															value="#{pc_Xri00401.propStgkSbt.value}"
															style="#{pc_Xri00401.propStgkSbt.style};width:150px">
															<f:selectItems value="#{pc_Xri00401.propStgkSbt.list}" />
														</h:selectOneMenu>
													</TD>
												</TR>
 
												<TR>
													<TH nowrap class="v_a" width="180">
													    <!-- 除籍日 -->
														<h:outputText styleClass="outputText" id="lblJosekiDay" 
														    value="#{pc_Xri00401.propJosekiDay.name}"
														    style="#{pc_Xri00401.propJosekiDay.labelStyle}">
														</h:outputText>
													</TH>
													<TD valign="middle" width="250">
														<h:inputText styleClass="inputText"
															id="htmlJosekiDay" size="2"
															disabled="#{pc_Xri00401.propJosekiDay.disabled}"
															value="#{pc_Xri00401.propJosekiDay.value}"
															readonly="#{pc_Xri00401.propJosekiDay.readonly}"
															size="11"
															style="#{pc_Xri00401.propJosekiDay.style}">
															<f:convertDateTime pattern="yyyy/MM/dd" />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
															<hx:inputHelperDatePicker />
														</h:inputText>
													</TD>
												</TR>

												<TR>
													<TH width="150px" nowrap class="v_c">
														<h:outputText styleClass="outputText"
															id="lblSyoriKbnLabel"
															style="#{pc_Xri00401.propSyoriKbn.style}"
															value="#{pc_Xri00401.propSyoriKbnLabel.name}">
														</h:outputText>
													</TH>
													<TD width="*" nowrap>
														<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
															id="htmlSyoriKbn"
															value="#{pc_Xri00401.propSyoriKbn.checked}">
														</h:selectBooleanCheckbox>
														<h:outputText styleClass="outputText"
															id="lblSyoriKbn" 
															value="#{pc_Xri00401.propSyoriKbn.name}"
															style="#{pc_Xri00401.propSyoriKbn.style}">
														</h:outputText>
													</TD>
												</TR>
												<TR>
													<TH width="150px" nowrap class="v_d">
														<h:outputText styleClass="outputText" 
															id="lblChkList"
															value="#{pc_Xri00401.propChkListLabel.name}">
														</h:outputText>
													</TH>
													<TD>
														<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
															<TBODY>
																<TR>
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListNormal"
																			value="#{pc_Xri00401.propChkListNormal.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText" 
																			id="lblChkListNormal"
																			value="#{pc_Xri00401.propChkListNormal.name}"
																			style="#{pc_Xri00401.propChkListNormal.style}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListError"
																			value="#{pc_Xri00401.propChkListError.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText"
																			id="lblChkListError"
																			value="#{pc_Xri00401.propChkListError.name}"
																			style="#{pc_Xri00401.propChkListError.style}">
																		</h:outputText>
																	</TD>
																</TR>
																<TR>													
																	<TD width="550" nowrap class="clear_border">
																		<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" 
																			id="htmlChkListWarning"
																			value="#{pc_Xri00401.propChkListWarning.checked}">
																		</h:selectBooleanCheckbox>
																		<h:outputText styleClass="outputText" 
																			id="lblChkListWarning"
																			value="#{pc_Xri00401.propChkListWarning.name}"
																			style="#{pc_Xri00401.propChkListWarning.style}">
																		</h:outputText>
																	</TD>
																</TR>
															</TBODY>
														</TABLE>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								<TR>
									<TD>
									</TD>
								</TR>
								<TR>
									<TD>
										<TABLE width="100%" border="0" class="button_bar" cellpadding="0" cellspacing="0">
											<TBODY>
												<TR>
													<TD>
														<hx:commandExButton type="submit" 
															value="実行"
															styleClass="commandExButton_dat" id="exec"
															confirm="#{msg.SY_MSG_0001W}" 
															action="#{pc_Xri00401.doExecAction}">
														</hx:commandExButton>
													</TD>
												</TR>
											</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</DIV>
				</DIV>
            	<!--↑CONTENT↑-->
            
			</DIV>
	        <!--↑outer↑-->

	        <!-- フッダーインクルード -->
	        <jsp:include page ="../inc/footer.jsp" />

		</h:form>
    </hx:scriptCollector>
    </BODY>
    
    <jsp:include page ="../inc/common.jsp" />
</f:view>
</HTML>
