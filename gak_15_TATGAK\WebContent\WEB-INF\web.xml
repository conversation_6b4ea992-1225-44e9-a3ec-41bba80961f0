<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID" version="2.4" xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
	<display-name>gakuen</display-name>
	<context-param>
		<description>
		Monitors Faces JSP files for modifications and synchronizes a running server with
the changes without restarting the server.  If this parameter is set to false or
removed from the deployment descriptor, any changes made to Faces JSP files may
not be seen by the server until it is restarted.  This parameter is usually set
to true while the Faces JSP files are being developed and debugged in order to
improve the performance of the development environment.</description>
		<param-name>com.ibm.ws.jsf.JSP_UPDATE_CHECK</param-name>
		<param-value>true</param-value>
	</context-param>
	<context-param>
		<description>
		</description>
		<param-name>com.ibm.ws.jsf.LOAD_FACES_CONFIG_AT_STARTUP</param-name>
		<param-value>true</param-value>
	</context-param>
	<context-param>
    	<param-name>javax.faces.CONFIG_FILES</param-name>
	    <param-value>/WEB-INF/faces-config_euc.xml</param-value>
	</context-param>
	<listener>
		<listener-class>com.sun.faces.config.ConfigureListener</listener-class>
	</listener>
	<servlet id="Servlet_1116397835733">
		<servlet-name>JS Resource Servlet</servlet-name>
		<servlet-class>com.ibm.faces.webapp.JSResourceServlet</servlet-class>
		<load-on-startup>-1</load-on-startup>
	</servlet>
	<servlet id="Servlet_1116397836575">
		<servlet-name>Faces Servlet</servlet-name>
		<servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
		<load-on-startup>-1</load-on-startup>
	</servlet>
	<servlet>
		<display-name>errorpage</display-name>
		<servlet-name>errorpage</servlet-name>
		<jsp-file>/err/errorpage.jsp</jsp-file>
	</servlet>
	<servlet>
		<display-name>AjaxServlet</display-name>
		<servlet-name>AjaxServlet</servlet-name>
		<servlet-class>com.jast.gakuen.framework.AjaxServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>HttpFileDownloadServlet</servlet-name>
		<servlet-class>com.jast.gakuen.framework.util.filetrans.HttpFileDownloadServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>HttpFileUploadServlet</servlet-name>
		<servlet-class>com.jast.gakuen.framework.util.filetrans.HttpFileUploadServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>ExecAppServlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.co.servlet.ExecAppServlet</servlet-class>
	</servlet>
	<servlet>
		<servlet-name>KjnCmtServlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.ss.servlet.KjnCmtServlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		CoiGetImage</display-name>
		<servlet-name>CoiGetImage</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.co.servlet.CoiGetImageServlet</servlet-class>
	</servlet>

	<servlet>
		<description>
		</description>
		<display-name>
		GetImage</display-name>
		<servlet-name>GetImage</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.co.servlet.GetImageServlet</servlet-class>
	</servlet>

	<!-- 証明書発行機 -->
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00101Servlet</display-name>
		<servlet-name>Hka00101Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00101Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00201Servlet</display-name>
		<servlet-name>Hka00201Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00201Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00202Servlet</display-name>
		<servlet-name>Hka00202Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00202Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00203Servlet</display-name>
		<servlet-name>Hka00203Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00203Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00204Servlet</display-name>
		<servlet-name>Hka00204Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00204Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00301Servlet</display-name>
		<servlet-name>Hka00301Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00301Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00302Servlet</display-name>
		<servlet-name>Hka00302Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00302Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00401Servlet</display-name>
		<servlet-name>Hka00401Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00401Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00102Servlet</display-name>
		<servlet-name>Hka00102Servlet</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.hk.servlet.Hka00102Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hko00101Servlet</display-name>
		<servlet-name>Hko00101Servlet</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.hk.servlet.Hko00101Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00103Servlet</display-name>
		<servlet-name>Hka00103Servlet</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.hk.servlet.Hka00103Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hkm00101Servlet</display-name>
		<servlet-name>Hkm00101Servlet</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.hk.servlet.Hkm00101Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hkm00102Servlet</display-name>
		<servlet-name>Hkm00102Servlet</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.hk.servlet.Hkm00102Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hkd00101Servlet</display-name>
		<servlet-name>Hkd00101Servlet</servlet-name>
		<servlet-class>
		com.jast.gakuen.rev.hk.servlet.Hkd00101Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00501Servlet</display-name>
		<servlet-name>Hka00501Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00501Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00502Servlet</display-name>
		<servlet-name>Hka00502Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00502Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00503Servlet</display-name>
		<servlet-name>Hka00503Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00503Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00504Servlet</display-name>
		<servlet-name>Hka00504Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00504Servlet</servlet-class>
	</servlet>
	<servlet>
		<description>
		</description>
		<display-name>
		Hka00505Servlet</display-name>
		<servlet-name>Hka00505Servlet</servlet-name>
		<servlet-class>com.jast.gakuen.rev.hk.servlet.Hka00505Servlet</servlet-class>
	</servlet>
	
	
	<servlet-mapping>
		<servlet-name>errorpage</servlet-name>
		<url-pattern>/errorpage</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Faces Servlet</servlet-name>
		<url-pattern>*.faces</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Faces Servlet</servlet-name>
		<url-pattern>/faces/*</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>JS Resource Servlet</servlet-name>
		<url-pattern>/.ibmjsfres/*</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>AjaxServlet</servlet-name>
		<url-pattern>/ajax/*</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>HttpFileDownloadServlet</servlet-name>
		<url-pattern>/HttpFileDownloadServlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>HttpFileUploadServlet</servlet-name>
		<url-pattern>/HttpFileUploadServlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>ExecAppServlet</servlet-name>
		<url-pattern>/ExecAppServlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>KjnCmtServlet</servlet-name>
		<url-pattern>/KjnCmtServlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>CoiGetImage</servlet-name>
		<url-pattern>/CoiGetImage</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>GetImage</servlet-name>
		<url-pattern>/GetImage</url-pattern>
	</servlet-mapping>

	<!-- 証明書発行機 -->
	<servlet-mapping>
		<servlet-name>Hka00101Servlet</servlet-name>
		<url-pattern>/Hka00101Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00201Servlet</servlet-name>
		<url-pattern>/Hka00201Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00202Servlet</servlet-name>
		<url-pattern>/Hka00202Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00203Servlet</servlet-name>
		<url-pattern>/Hka00203Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00204Servlet</servlet-name>
		<url-pattern>/Hka00204Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00301Servlet</servlet-name>
		<url-pattern>/Hka00301Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00302Servlet</servlet-name>
		<url-pattern>/Hka00302Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00401Servlet</servlet-name>
		<url-pattern>/Hka00401Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00102Servlet</servlet-name>
		<url-pattern>/Hka00102Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hko00101Servlet</servlet-name>
		<url-pattern>/Hko00101Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00103Servlet</servlet-name>
		<url-pattern>/Hka00103Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hkm00101Servlet</servlet-name>
		<url-pattern>/Hkm00101Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hkm00102Servlet</servlet-name>
		<url-pattern>/Hkm00102Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hkd00101Servlet</servlet-name>
		<url-pattern>/Hkd00101Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00103Servlet</servlet-name>
		<url-pattern>/Hka00103Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00501Servlet</servlet-name>
		<url-pattern>/Hka00501Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00502Servlet</servlet-name>
		<url-pattern>/Hka00502Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00503Servlet</servlet-name>
		<url-pattern>/Hka00503Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00504Servlet</servlet-name>
		<url-pattern>/Hka00504Servlet</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>Hka00505Servlet</servlet-name>
		<url-pattern>/Hka00505Servlet</url-pattern>
	</servlet-mapping>
	<!-- 証明書発行機 -->

	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
		<welcome-file>index.htm</welcome-file>
		<welcome-file>index.jsp</welcome-file>
		<welcome-file>default.html</welcome-file>
		<welcome-file>default.htm</welcome-file>
		<welcome-file>default.jsp</welcome-file>
	</welcome-file-list>
	<error-page>
		<exception-type>java.lang.Exception</exception-type>
		<location>/faces/err/errorpage.jsp</location>
	</error-page>

	<!-- 共通ServletFilter -->
	<filter>
		<description></description>
		<display-name>RequestFilter</display-name>
		<filter-name>RequestFilter</filter-name>
		<filter-class>com.jast.gakuen.framework.util.RequestFilter</filter-class>
	</filter>

	<filter-mapping>
		<filter-name>RequestFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!-- 認証チェックServletFilter -->
	<filter>
		<description></description>
		<display-name>LoginCheckFilter</display-name>
		<filter-name>LoginCheckFilter</filter-name>
		<filter-class>com.jast.gakuen.framework.util.CheckLoginFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>LoginCheckFilter</filter-name>
		<url-pattern>/faces/sample/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
	</filter-mapping>
	<filter-mapping>
		<filter-name>LoginCheckFilter</filter-name>
		<url-pattern>/faces/rev/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
	</filter-mapping>
	<filter-mapping>
		<filter-name>LoginCheckFilter</filter-name>
		<url-pattern>/faces/up/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
	</filter-mapping>

	<filter-mapping>
		<filter-name>LoginCheckFilter</filter-name>
		<url-pattern>/ajax/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
	</filter-mapping>

	<!-- ダブルクリック防止ServletFilter -->
<!--
	<filter>
		<filter-name>CheckDuplicateFilter</filter-name>
		<filter-class>com.jast.gakuen.framework.util.CheckDuplicateFilter</filter-class>
		<init-param>
			<param-name>clientCounterName</param-name>
			<param-value>counter</param-value>
		</init-param>
		<init-param>
			<param-name>serverCounterName</param-name>
			<param-value>counter</param-value>
		</init-param>
		<init-param>
			<param-name>errorPage</param-name>
			<param-value>/faces/err/dup_errorpage.jsp</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>CheckDuplicateFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
-->
	<jsp-config>
		<taglib>
			<taglib-uri>/gakuen</taglib-uri>
			<taglib-location>/WEB-INF/gakuen.tld</taglib-location>
		</taglib>
	</jsp-config>

</web-app>
