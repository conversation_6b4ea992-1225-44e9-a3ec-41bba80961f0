<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">

function confirmOk() {
	document.getElementById('form1:propExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:propExecutableSearch').value = "0";
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
}</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page ="../../rev/inc/header.jsp" />
	        <!-- ヘッダーへのデータセット領域 -->
				<div style="display:none;"><hx:commandExButton type="submit"
					value="閉じる" styleClass="commandExButton" id="closeDisp"
					action="#{pc_Xrg00101.doCloseDispAction}"></hx:commandExButton> <h:outputText
					styleClass="outputText" id="htmlFuncId"
					value="#{pc_Xrg00101.funcId}"></h:outputText> <h:outputText
					styleClass="outputText" id="htmlLoginId"
					value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
					styleClass="outputText" id="htmlScrnName"
					value="#{pc_Xrg00101.screenName}"></h:outputText></div>
				<!--↓outer↓-->
				<DIV class="outer">
					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
						id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
						styleClass="outputText" escape="false">
					</h:outputText></FIELDSET>
	
				<DIV class="head_button_area">
					<!-- ↓ここに戻るボタンを配置 -->
					<TABLE>
						<TR>
							<TD></TD>
						</TR>
					</TABLE>
					<!-- ↑ここに戻るボタンを配置 -->
				</DIV>
	
				<!--↓content↓-->
				<DIV id="content">
					<DIV class="column" align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="730">
							<TBODY>
								<TR>
									<TH class="v_a" width="140">
										<h:outputText styleClass="outputText" id="lblBunrui"
											value="#{pc_Xrg00101.propBunrui.labelName}"
											style="#{pc_Xrg00101.propBunrui.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="460">
										<h:selectOneRadio
											disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlBunrui"
											value="#{pc_Xrg00101.propBunrui.stringValue}"
											disabled="#{pc_Xrg00101.propBunrui.disabled}" tabindex="1"
											style="#{pc_Xrg00101.propBunrui.style}">
											<f:selectItem itemValue="1" itemLabel="スクーリング" />
											<f:selectItem itemValue="2" itemLabel="教育実習" />
											<f:selectItem itemValue="3" itemLabel="介護等体験" />
											<f:selectItem itemValue="4" itemLabel="テキスト" />
										</h:selectOneRadio></TD>
									<TD rowspan="3" width="130" style="background-color: transparent;border-bottom-style: none; border-top-style: none; border-right-style: none; text-align: right">
										<hx:commandExButton type="submit" value="選択"
											styleClass="commandExButton" id="select"  tabindex="3"
											action="#{pc_Xrg00101.doSelectAction}"
											disabled="#{pc_Xrg00101.propSelect.disabled}"
											style="#{pc_Xrg00101.propSelect.style}">
										</hx:commandExButton>
										<hx:commandExButton type="submit" value="解除" tabindex="4"
											styleClass="commandExButton" id="release" 
											action="#{pc_Xrg00101.doReleaseAction}"
											disabled="#{pc_Xrg00101.propRelease.disabled}"
											style="#{pc_Xrg00101.propRelease.style}">
										</hx:commandExButton>
									</TD>
								</TR>
								<TR>
									<TH class="v_b">
										<h:outputText styleClass="outputText"
											id="lblTargetNendo"
											value="#{pc_Xrg00101.propTargetNendo.labelName}"
											style="#{pc_Xrg00101.propTargetNendo.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="450"><h:inputText styleClass="inputText"
										id="htmlTargetNendo" tabindex="2"
										value="#{pc_Xrg00101.propTargetNendo.dateValue}"
										disabled="#{pc_Xrg00101.propTargetNendo.disabled}"
										style="#{pc_Xrg00101.propTargetNendo.style}" size="10">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar">
							<TBODY>
								<TR>
									<TD width="700">
										<hx:commandExButton type="submit"
											styleClass="commandExButton_dat" id="entry" tabindex="5" value="新規登録"
											action="#{pc_Xrg00101.doEntryAction}" 
											disabled="#{pc_Xrg00101.propEntry.disabled}"
											style="#{pc_Xrg00101.propEntry.style}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<!-- ↓データテーブル部↓ -->
						<TABLE border="0" cellpadding="5" width="600">
							<TBODY>
			                    <TR>
			                        <TD align="right">
			                        	<h:outputText styleClass="outputText"
											id="htmlSchoolingListCount"
											value="#{pc_Xrg00101.propSchoolingListCount.stringValue}"
											style="#{pc_Xrg00101.propSchoolingListCount.style}">
										</h:outputText>
			                        </TD>
			                    </TR>
								<TR>
									<TD>
										<DIV style="height:336px" class="listScroll" 
											id="listScroll">
											<h:dataTable border="0" cellpadding="0" cellspacing="0"
												columnClasses="columnClass" headerClass="headerClass"
												footerClass="footerClass"
												rowClasses="#{pc_Xrg00101.propSchoolingList.rowClasses}"
												styleClass="meisai_scroll" id="htmlSchoolingList" width="720"
												value="#{pc_Xrg00101.propSchoolingList.list}" var="varlist">
												<h:column id="column1">
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="コード"
															id="lblSchoolingSbtCd">
														</h:outputText>
													</f:facet>
													<h:outputText id="htmlSchoolingSbtCd" styleClass="outputText"
														value="#{varlist.schoolingSbtCd}" style="width: 70px">
													</h:outputText>
													<f:attribute value="true" name="nowrap" />
													<f:attribute value="40" name="width" />
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column2">
													<f:facet name="header">
														<h:outputText styleClass="outputText" id="lblSchoolingSbtName"
															value="スクーリング種別">
														</h:outputText>
													</f:facet>
													<h:outputText id="htmlSchoolingSbtName" styleClass="outputText"
														value="#{varlist.propSchoolingSbtName.displayValue}"
														title="#{varlist.propSchoolingSbtName.stringValue}"
														style="width: 240px">
													</h:outputText>
													<f:attribute value="true" name="nowrap" />
													<f:attribute value="240" name="width" />
												</h:column>
												<h:column id="column3">
													<f:facet name="header">
														<h:outputText styleClass="outputText"
															id="lblGakuseiMibun" value="対象学生身分">
														</h:outputText>
													</f:facet>
													<h:outputText id="htmlGakuseiMibun" styleClass="outputText"
														value="#{varlist.propGakuseiMibun.displayValue}"
														title="#{varlist.propGakuseiMibun.stringValue}"
														style="width: auto">
													</h:outputText>
													<f:attribute value="true" name="nowrap" />
													<f:attribute value="130" name="width" />
												</h:column>
												<h:column id="column4">
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="遅刻数" id="lblChikokusu"></h:outputText>
													</f:facet>
													<f:attribute value="60" name="width" />
													<h:outputText id="htmlChikokusu" styleClass="outputText"
														value="#{varlist.chikokusu}"
														style="width: 60px">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
												</h:column>
												<h:column id="column5">
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="集中・出張" id="lblShuchuKogiFlg"></h:outputText>
													</f:facet>
													<h:outputText id="htmlShuchuKogiFlg" styleClass="outputText"
														value="#{varlist.shuchuKogiFlg}"
														style="width: 90px">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
													<f:attribute value="90" name="width" />
												</h:column>
												<h:column id="column6">
													<f:facet name="header">
														<h:outputText styleClass="outputText" value="受講料請求" id="lblJukoSekyuFlg"></h:outputText>
													</f:facet>
													<h:outputText id="htmlJukoSekyuFlg" styleClass="outputText"
														value="#{varlist.jukoSekyuFlg}"
														style="width: 90px">
													</h:outputText>
													<f:attribute value="text-align: center" name="style" />
													<f:attribute value="90" name="width" />
												</h:column>
												<h:column id="column7">
													<f:facet name="header">
													</f:facet>
													<hx:commandExButton type="submit" value="編集"
														styleClass="commandExButton" tabindex="6"
														action="#{pc_Xrg00101.doEditAction}"
														disabled="#{pc_Xrg00101.propEdit.disabled}"
														style="#{pc_Xrg00101.propEdit.style}">
													</hx:commandExButton>
													<hx:commandExButton type="submit" value="削除"
														styleClass="commandExButton" tabindex="7"
														onclick="return doPopupMsg('#{msg.SY_MSG_0004W}');"
														action="#{pc_Xrg00101.doDeleteAction}"
														disabled="#{pc_Xrg00101.propDelete.disabled}"
														style="#{pc_Xrg00101.propDelete.style}">
													</hx:commandExButton>
													<f:attribute value="true" name="nowrap" />
													<f:attribute value="70" name="width" />
												</h:column>
											</h:dataTable>
										</DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<!-- ↑データテーブル部↑ -->
					</DIV>
				</DIV>
				<h:inputHidden
					value="#{pc_Xrg00101.propExecutableSearch.integerValue}"
					id="propExecutableSearch">
					<f:convertNumber />
				</h:inputHidden>
			</DIV>
			<h:inputHidden
				value="#{pc_Xrg00101.propSchoolingList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>