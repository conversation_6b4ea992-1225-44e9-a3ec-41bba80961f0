<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrk/Xrk00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
  pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrk00501</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
  title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">
<SCRIPT type="text/javascript">
	function init() {
		func_1(document.getElementById("form1:htmlGakusekiCd"),"");
		// フォーカス設定を行う。
		if(document.getElementById("form1:htmlFocus").value != ""){
			document.getElementById("form1:" + document.getElementById("form1:htmlFocus").value ).focus();
			document.getElementById("form1:htmlFocus").value = "";
		}
	}
	function func_1(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		//var servlet = "rev/km/GakseiAJAX";
		//var target = "form1:htmlGakuSekiName";
		//var code = thisObj.value;
		//getCodeName(servlet, target, code );
		//return true;
		var servlet = "rev/co/CobGakseiAJAX";
		var target = "form1:htmlGakusekiName";
		var args = new Array();
		args['code1'] = thisObj.value;
		args['code2'] = "";
		args['code3'] = "";
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getPluralValue(servlet,target,args);
		return true;
	}
	function func_2(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakuSekiCd";
 		openModalWindow(url, "pCob0101", " <%= com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %> ");
	}
	function func_3(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		openModalWindow("", "PCos0402", "<%=com.jast.gakuen.rev.co.PCos0402.getWindowOpenOption() %>");
		setTarget("PCos0402"); return true;
	}
	function callBackMethod(value){
		try{
			document.getElementById("form1:htmlGakusekiName").innerHTML = value["name"];
			document.getElementById("form1:htmlAjaxName").value = value["name"];
		} catch (e) {
		}
	}
	function confirmCancel(){
		try{
			document.getElementById("form1:htmlConfilm").value = "0";
		} catch (e) {
		}
	}
	function confirmOk(){
		try{
			document.getElementById("form1:htmlConfilm").value = "1";
			indirectClick(document.getElementById("form1:htmlAction").value);
		} catch (e) {
		}
	}
	function confirmCancel(){
		addListWarnCancel("htmlExecutableBtnAdd");
	}
	function confirmOk(){
		addListWarnOK("htmlExecutableBtnAdd", "add");
	}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
  <f:loadBundle basename="properties.message" var="msg" />
  <BODY onload="init();">
  <hx:scriptCollector id="scriptCollector1"
    preRender="#{pc_Xrk00501.onPageLoadBegin}">
    <h:form styleClass="form" id="form1">

      <!-- ヘッダーインクルード -->
      <jsp:include page="../../rev/inc/header.jsp" />

      <!-- ヘッダーへのデータセット領域 -->
      <div style="display:none;"><hx:commandExButton type="submit"
        value="閉じる" styleClass="commandExButton" id="closeDisp"
        action="#{pc_Xrk00501.doCloseDispAction}"></hx:commandExButton> <h:outputText
        styleClass="outputText" id="htmlFuncId"
        value="#{pc_Xrk00501.funcId}"></h:outputText> <h:outputText
        styleClass="outputText" id="htmlLoginId"
        value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
        styleClass="outputText" id="htmlScrnName"
        value="#{pc_Xrk00501.screenName}"></h:outputText></div>

      <!--↓outer↓-->
      <DIV class="outer">

      <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
        id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
        styleClass="outputText" escape="false">
      </h:outputText></FIELDSET>

      <!--↓content↓-->
      <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
      <DIV id="content">
      <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
      <TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
        <TBODY>
          <TR>
            <TD width="5%"></TD>
            <TD width="90%">
            <TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
              class="table">
              <TBODY>
                <TR>
                  <TH width="30%" class="v_a"><h:outputText
                    styleClass="outputText" id="lblHakoPtn"
                    value="#{pc_Xrk00501.propCobHakoPtn.labelName}"
                    style="#{pc_Xrk00501.propCobHakoPtn.labelStyle}"></h:outputText></TH>
                  <TD width="70%"><h:selectOneMenu styleClass="selectOneMenu"
                    id="htmlHakoPtn"
                    value="#{pc_Xrk00501.propCobHakoPtn.stringValue}"
                    style="width:270px;">
                    <f:selectItems value="#{pc_Xrk00501.propCobHakoPtn.list}" />
                  </h:selectOneMenu></TD>
                </TR>
                <TR>
                  <TH class="v_b" width="30%"><h:outputText
                    styleClass="outputText" id="lblHakoBangu"
                    value="#{pc_Xrk00501.propCkbHakoBangu.labelName}"
                    style="#{pc_Xrk00501.propCkbHakoBangu.labelStyle}"></h:outputText></TH>
                  <TD width="70%"><h:selectOneRadio
                    disabledClass="selectOneRadio_Disabled"
                    styleClass="selectOneRadio" id="htmlHakoBangu"
                    value="#{pc_Xrk00501.propCkbHakoBangu.stringValue}">
                    <f:selectItem itemValue="0" itemLabel="出力する" />
                    <f:selectItem itemValue="1" itemLabel="出力しない" />
                    <f:selectItem itemValue="2" itemLabel="発行のみ" />
                  </h:selectOneRadio></TD>

                </TR>
                <TR>
                  <TH class="v_d" width="30%"><h:outputText
                    styleClass="outputText"
                    value="#{pc_Xrk00501.propHakoDate.name}" id="lblHakoDate"
                    style="#{pc_Xrk00501.propHakoDate.labelStyle}"></h:outputText></TH>
                  <TD width="70%"><h:inputText styleClass="inputText"
                    id="htmlHakoDate"
                    value="#{pc_Xrk00501.propHakoDate.dateValue}">
                    <f:convertDateTime />
                    <hx:inputHelperDatePicker />
                    <hx:inputHelperAssist errorClass="inputText_Error"
                      promptCharacter="_"></hx:inputHelperAssist>
                  </h:inputText></TD>
                </TR>
                <TR>
                  <TH class="v_e" width="30%"><h:outputText
                    styleClass="outputText"
                    value="有効期限" id="lblYukuKigen"
                    style="#{pc_Xrk00501.propYukuKigenFrom.labelStyle}"></h:outputText></TH>
                  <TD width="70%"><h:inputText styleClass="inputText"
                    id="htmlYukuKigenFrom"
                    value="#{pc_Xrk00501.propYukuKigenFrom.dateValue}"
                    style="#{pc_Xrk00501.propHakoDate.style}">
                    <f:convertDateTime />
                    <hx:inputHelperDatePicker />
                    <hx:inputHelperAssist errorClass="inputText_Error"
                      promptCharacter="_"></hx:inputHelperAssist>
                  </h:inputText>　～　
                  <h:inputText styleClass="inputText"
                    id="htmlYukuKigenTo"
                    value="#{pc_Xrk00501.propYukuKigenTo.dateValue}"
                    style="#{pc_Xrk00501.propHakoDate.style}">
                    <f:convertDateTime />
                    <hx:inputHelperDatePicker />
                    <hx:inputHelperAssist errorClass="inputText_Error"
                      promptCharacter="_"></hx:inputHelperAssist>
                  </h:inputText></TD>
                </TR>
                <TR>
                  <TH class="v_c"><h:outputText styleClass="outputText"
                    id="lblTray" value="#{pc_Xrk00501.propPrinter.labelName}"
                    style="#{pc_Xrk00501.propPrinter.labelStyle}"></h:outputText></TH>
                  <TD colspan="3">
                  <h:selectOneMenu styleClass="selectOneMenu"
                    id="htmlPrinter" value="#{pc_Xrk00501.propPrinter.value}"
                    disabled="#{pc_Xrk00501.propPrinter.disabled}"
                    readonly="#{pc_Xrk00501.propPrinter.readonly}">
                    <f:selectItems value="#{pc_Xrk00501.propPrinter.list}" />
                  </h:selectOneMenu></TD>
                </TR>
                <TR>
                  <TH class="v_f" width="30%"><h:outputText
                    styleClass="outputText"
                    value="#{pc_Xrk00501.propCobSiyoMokuteki.labelName}"
                    id="lblMuko"
                    style="#{pc_Xrk00501.propCobSiyoMokuteki.labelStyle}"></h:outputText></TH>
                  <TD width="70%">
                  	<h:selectOneMenu styleClass="selectOneMenu" id="htmlCobSiyoMokuteki"
                    value="#{pc_Xrk00501.propCobSiyoMokuteki.value}"
                    style="width:280px;">
                    <f:selectItems value="#{pc_Xrk00501.propCobSiyoMokuteki.list}" />
                  </h:selectOneMenu></TD>
                </TR>

              </TBODY>
            </TABLE>
            </TD>
            <TD width="5%"></TD>
          </TR>
          <TR>
            <TD width="5%"></TD>
            <TD width="90%" height="20"></TD>
            <TD width="5%"></TD>
          </TR>
          <TR>
            <TD width="5%"></TD>
            <TD width="90%">
            <TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
              class="table">
              <TBODY>
                <TR>
                  <TH width="30%" class="v_g"><h:outputText
                    styleClass="outputText" id="lblFileSet" value="ファイル指定"></h:outputText><BR>
                  <h:outputText styleClass="outputText" id="text6"
                    value="　(前回ファイル)"></h:outputText></TH>
                  <TD colspan="3" width="70%"><hx:fileupload styleClass="fileupload"
                    id="htmlfileupload"
                    value="#{pc_Xrk00501.propFileupload.value}"
                    style="width:540px;">
                    <hx:fileProp name="fileName"
                      value="#{pc_Xrk00501.propFileupload.fileName}" />
                    <hx:fileProp name="contentType" />
                  </hx:fileupload> <hx:commandExButton type="submit" value="取込"
                    styleClass="commandExButton" id="read"
                    action="#{pc_Xrk00501.doReadAction}"></hx:commandExButton><BR>
                  <h:outputText styleClass="outputText" id="htmlFileSave"
                    value="#{pc_Xrk00501.propFileSave.stringValue}"></h:outputText><BR>
                  </TD>
                </TR>
                <TR>
                  <TH width="30%" class="v_a"><h:outputText
                    styleClass="outputText" id="lblGakusekiCd"
                    value="#{pc_Xrk00501.propGakuSekiCd.labelName}"></h:outputText></TH>
                  <TD align="right" width="70%" colspan="3"><h:inputText
                    styleClass="inputText" id="htmlGakusekiCd"
                    value="#{pc_Xrk00501.propGakuSekiCd.stringValue}"
                   	style="ime-mode:disabled"
                    maxlength="#{pc_Xrk00501.propGakuSekiCd.maxLength}"
                    onblur="return func_1(this, event);" size="10"></h:inputText>
                    <hx:commandExButton
                    type="button" styleClass="commandExButton_search" id="search"
                    onclick="return func_2(this, event);"></hx:commandExButton>
                    <hx:commandExButton
                    type="submit" value="追加" styleClass="commandExButton"
                    id="simpleadd" action="#{pc_Xrk00501.doSimpleaddAction}"></hx:commandExButton>
					<h:outputText
					styleClass="outputText" id="lblGakusekiNameBlank" value="　"></h:outputText>
					<h:outputText
                    styleClass="outputText" id="htmlGakuSekiName"
                    value="#{pc_Xrk00501.propGakuSekiName.stringValue}"></h:outputText>
                  </TD>
                </TR>
              </TBODY>
            </TABLE>
            </TD>
            <TD width="5%"></TD>
          </TR>
          <TR>
            <TD width="5%"></TD>
            <TD width="90%" align="right"><h:outputText
              styleClass="outputText" id="text12" value="(重複指定可能)"></h:outputText></TD>
            <TD width="5%"></TD>
          </TR>
          <TR>
            <TD width="5%"></TD>
            <TD width="90%">
              <TABLE border="0" cellspacing="0" width="600" align="center">
                <TBODY>
                  <TR>
                    <TD width="510" align="left">
                      <h:outputText styleClass="outputText"
                        id="text1" value="対象学生">
                      </h:outputText></TD>
                    <TD valign="top" align="center" width="90"></TD>
                  </TR>
                  <TR>
                    <TD width="510"><h:selectManyListbox styleClass="selectManyListbox"
                    id="htmlStudentList" size="15" style="width: 100%"
                    value="#{pc_Xrk00501.propObjectStudent.integerValue}">
                    <f:selectItems value="#{pc_Xrk00501.propObjectStudent.list}" />
                  </h:selectManyListbox>
                    </TD>
                    <TD valign="top" align="center">
                      <hx:commandExButton
                        type="submit" value="除外" styleClass="commandExButton"
                        id="remove" action="#{pc_Xrk00501.doRemoveAction}"
                        style="width:60px">
                      </hx:commandExButton><BR>
                      <h:outputText styleClass="outputText" id="text20"
                        value="（複数選択可）">
                      </h:outputText><BR>
                      <hx:commandExButton type="submit" value="全て除外"
                        styleClass="commandExButton" id="removeall"
                        action="#{pc_Xrk00501.doRemoveallAction}" style="width:60px">
                      </hx:commandExButton>
                    </TD>
                  </TR>
                  <TR>
                    <TD align="right">
                      <h:outputFormat styleClass="outputFormat"
                        id="htmlTotalCount" value="合計件数：　{0}件">
                          <f:param name="normalCount"
                            value="#{pc_Xrk00501.propObjectStudent.listCount}"></f:param>
                      </h:outputFormat><h:outputFormat styleClass="outputFormat"
                        id="htmlNormalCount" value="正常件数：　{0}件">
                          <f:param name="normalCount"
                            value="#{pc_Xrk00501.propObjectStudent.listCount - pc_Xrk00501.propErrorCount.integerValue}"></f:param>
                      </h:outputFormat>
                      <h:outputFormat styleClass="outputFormat"
                        id="htmlErrorCount" value="エラー件数：　{0}件">
                          <f:param name="errorCount"
                            value="#{pc_Xrk00501.propErrorCount.integerValue}"></f:param>
                      </h:outputFormat></TD>
                    <TD valign="top" align="center"></TD>
                  </TR>
                </TBODY>
              </TABLE>
            </TD>
            <TD width="5%"></TD>
          </TR>
          <TR>
            <TD width="5%" colspan="3"><BR>
            </TD>
          </TR>
          <TR>
            <TD width="5%" colspan="3">
            <TABLE border="0" class="button_bar" height="" width="100%"
              bordercolor="" cellpadding="0" cellspacing="0">
              <TBODY>
                <TR>
                  <TD align="center" valign="middle" height="100%">
                    <hx:commandExButton
                       type="submit" value="PDF作成" styleClass="commandExButton_out"
                       id="pdfOut" action="#{pc_Xrk00501.doPdfOutAction}"
                       confirm="#{msg.SY_MSG_0019W}"></hx:commandExButton>
                    <hx:commandExButton
                       type="submit" value="印刷" styleClass="commandExButton_out"
                       id="print" confirm="#{msg.SY_MSG_0022W}"
                       action="#{pc_Xrk00501.doPrintAction}"></hx:commandExButton></TD>
                </TR>
              </TBODY>
            </TABLE>
            </TD>
          </TR>
        </TBODY>
      </TABLE>
      </DIV>
      </DIV>
      <!--↑content↑--></DIV>
      <!--↑outer↑-->
      <!-- フッダーインクルード -->
      <jsp:include page="../../rev/inc/footer.jsp" />

			<h:inputHidden
				value="#{pc_Xrk00501.propConfilm.stringValue}"
				id="htmlConfilm">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrk00501.propAction.stringValue}"
				id="htmlAction">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrk00501.propAjaxName.stringValue}"
				id="htmlAjaxName">
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrk00501.propFocus.stringValue}"
				id="htmlFocus">
			</h:inputHidden>
			<h:inputHidden id="htmlExecutableBtnAdd"
				value="#{pc_Xrk00501.propExecutableBtnAdd.integerValue}">
				<f:convertNumber />
			</h:inputHidden>
    </h:form>
  </hx:scriptCollector>
  </BODY>
  <jsp:include page="../../rev/inc/common.jsp" />
</f:view>
</HTML>

