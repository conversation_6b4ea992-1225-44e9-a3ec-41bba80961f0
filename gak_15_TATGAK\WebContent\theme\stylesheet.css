/********************************
 *	Faces Components Stylesheet *
 ********************************/

.form {
}

.commandExButton {
}

.commandExButton_Label {
	color: windowtext;
	text-align: center;
	vertical-align: middle;
}

.commandLink {
}

.outputLinkEx {
}

.graphicImageEx {
}

.outputLabel {
}

.inputText {
}

.inputText_Error {
	border-style: solid;
	border-color: #DE5C5C;
}

.inputTextarea {
}

.inputSecret {
}

.outputText {
}

.outputFormat {
}

.outputSeparator {
}

.message {
}

.messages {
}

.selectBooleanCheckbox {
}

.selectOneRadio {
}

.selectOneRadio_Disabled {
	color: GrayText;
}

.selectManyCheckbox {
}

.selectManyCheckbox_Disabled {
	color: GrayText;
}

.selectOneListbox {
}

.selectManyListbox {
}

.selectOneMenu {
}

.fileupload {
}

.panelBox {
}

.panelLayout {
}

.panelGrid {
}

.jspPanel {
}

.playerGenericPlayer {
}

.playerFlash {
}

.playerShockwave{
}

.playerRealPlayer {
}

.playerMediaPlayer {
}

/*************************************
 *	Compound Component:  Action Bar  *
 *************************************/

.panelActionbar {
}

.panelActionbar  A:link {
	text-decoration:none;
}

.panelActionbar  A:visited {
	text-decoration:none;
}

.panelActionbar  A:hover {
	text-decoration:underline;
}

.panelActionbar IMG {
	border:0;
}

.panelActionbar TD {
	padding:1;
}

/******************************************
 *	Compound Component:  Slider Dropdown  *
 ******************************************/

.inputText_Slider {
	background-color:buttonface;
	border:1px;
	border-style:solid;
	border-color:windowframe;
	font-family: sans-serif;
	font-decoration: none;
	color: windowtext;
}
.inputText_Slider_Body {
	background-color:buttonface;
	color: threedshadow;
	border-style:solid;
	border-color:threedhighlight;
	border-right-color:threedShadow;
}

/**********************************************
 *	Compound Component:  DatePicker Dropdown  *
 **********************************************/

.inputText_DatePicker {
	background-color: #94B9DD;
	border: 1px;
	border-style: solid;
	border-color: ButtonShadow;
	font-family: Arial, sans-serif;
	font-size: 8pt;
	text-decoration: none;
	font-weight: normal;
	color: white;
	padding: 2px;
	margin: 0px;
}

.inputText_DatePicker-Size {
}

.inputText_DatePicker-Header {
	border-width: 0px;
	border-bottom-width: 1px;
	border-style: solid;
	border-color: ButtonShadow;
}

.inputText_DatePicker-Body {
	border-width: 0px;
	border-style: none;
	background-color: window;
}

.inputText_DatePicker-HeaderLine1,
.inputText_DatePicker-HeaderLine2,
.inputText_DatePicker-HeaderWeekday, 
.inputText_DatePicker-HeaderYear,
.inputText_DatePicker-HeaderMonth {
	font-family: Arial, sans-serif;
	font-size: 8pt;
	text-decoration: none;
	font-weight: bold;
	vertical-align: middle;
}

.inputText_DatePicker-HeaderLine1 {
	width: 12px;
	padding-left: 2px;
	padding-right: 2px;
	padding-top: 2px;
	padding-bottom: 1px;
}

.inputText_DatePicker-HeaderYear {
	color: white;
	padding-top:2px;
	padding-bottom:1px; 
}

.inputText_DatePicker-HeaderLine2 {
	width: 12px;
	padding-left: 2px;
	padding-right: 2px;
	padding-bottom: 3px;
}

.inputText_DatePicker-HeaderMonth {
	color: white;
	padding-bottom: 3px;
}

.inputText_DatePicker-Button {
	border: 0px;
	width:  12px;
	height: 12px;
        color:red;
}

.inputText_DatePicker-HeaderWeekday {
	color: WindowText;
	background-color: ButtonFace;
	padding-top: 1px;
	padding-bottom: 2px;
        background-color:#D0DDE8;
}

.inputText_DatePicker-OtherMonth,
.inputText_DatePicker-CurrentMonth,
.inputText_DatePicker-CurrentDay, 
.inputText_DatePicker-CurrentOtherDay, 
.inputText_DatePicker-InvalidDay, 
.inputText_DatePicker-Today,
.inputText_DatePicker-CurrentToday {
	font-family: Arial, sans-serif;
	font-size: 9pt;
	text-decoration: none;
	font-weight: normal;
	text-align: center;
	vertical-align: middle;
	border-width: 1px;
	border-style: solid;
	padding: 1px;
	padding-left: 2px;
	padding-right: 2px;
}

.inputText_DatePicker-OtherMonth {
	color: GrayText;
	border-color: Window;
}
.inputText_DatePicker-CurrentMonth {
	color: WindowText;
	border-color: Window;
}

.inputText_DatePicker-CurrentDay {
	color: HighlightText;
	background-color: Highlight;
	border-color: Window;
}

.inputText_DatePicker-CurrentOtherDay {
	color: HighlightText;
	background-color: Highlight;
	border-color: Window;
}

.inputText_DatePicker-InvalidDay {
	color: GrayText;
	background-color: buttonface;
	border-color: buttonface;
}

.inputText_DatePicker-Today {
	color: WindowText;
	font-weight: bold;
	background-color: Window;
	border-color: ButtonShadow;
}

.inputText_DatePicker-CurrentToday {
	color: HighlightText;
	font-weight: bold;
	background-color: Highlight;
	border-color: Window;
}

/*************************************
 *	Compound Component:  Data Grid   *
 *************************************/

.dataTable {
}

.headerClass {
	background-color: ThreeDFace;
	color: WindowText;
	border-width: 1px;
	border-style: solid;
	border-color: ThreeDShadow;
	margin:2px;
/*
	padding:0px;
	padding-left:4pt;
	padding-right:4pt;
	padding-bottom:2px;
*/
	font-weight: 400;
}

.footerClass {
        /*
	background-color: ThreeDFace;
        */
        background-color: #A4B2BF;
	color: WindowText;
	border-width: 0px;
	border-style: none;
	padding:0px;
	padding-left:4pt;
	padding-right:4pt;
	font-weight: 400;
        
}

.rowClass1 {
	background-color: window;
}

.rowClass2 {
	background-color: ThreeDFace;
}

.columnClass1 {
/*	background-color: window;*/
}

.columnClass2 {
	/*background-color: ThreeDFace;*/
}

.panelRowCategory {
}

.inputRowSelect {
	vertical-align: middle;;
	margin: 0px;
	margin-top: 1px;
	margin-left: 3px;
}

.commandExRowEdit {
}

.editStyleClass {
	background-color: Window;
	border-color: WindowFrame;
	border-style: solid;
	border-width: 1px;
	padding-bottom:4px;
	text-align:right;
}

.editStyleClass A {
	margin: 0px;
	padding: 0px;
	border-width: 0px;
	border-style: none;
	width:1px;
	height:1px;
}

.pagerSimple {
}

.pagerGoto {
}

.pagerWeb {
}

.outputStatistics {
}

.pagerDeluxe {
	/*background-color: ThreeDFace;*/
        background-color: #A4B2BF;
	/*border-color: ThreeDFace;*/
	border-width: 1px;
	border-style: solid;
}

.pagerDeluxe TD {
	padding: 0px;
	margin: 0px;
	border-width: 1px;
	/*border-color: ThreeDShadow;
        border-color: ThreeDShadow;
        */
	border-style: solid;
	/*background-color: transparent;*/
        background-color: #A4B2BF;
}

.pagerDeluxe_button {
	padding: 0px;
	margin: 0px;
	width:  22px; 
	height: 19px; 
        /*
	background-color: ThreeDFace;
        */
        /*background-color: #FCFFFF;*/


	border-style: solid;
	border-width: 1px; 
	/*border-color: Window; */
	font-size: 0px;
}

.pagerDeluxe_text {
	vertical-align: middle;
	text-align: center;
	padding: 0px;
	padding-left:  8px;
	padding-right: 8px;
	margin: 0px;
	/*background-color: ThreeDFace;*/
        background-color: #A4B2BF;
	border-width: 0px; 
	border-style: solid;
	/*border-color: ThreeDFace;*/
	font-size: 13px;
	color: windowtext;
}

.pagerDeluxe_button_twistie {
	color:  windowtext;
	/*border-color: windowtext; */
        background-color: #A4B2BF;
	list-style-type: none;
}
