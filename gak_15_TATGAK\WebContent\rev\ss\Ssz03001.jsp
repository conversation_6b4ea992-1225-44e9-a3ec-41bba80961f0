<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz03001.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz03001.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz03001.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz03001.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz03001.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz03001.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 --><BR>
			<TABLE border="0" width="100%" height="12">
				<TBODY>
					<TR>
						<TD width="25%"></TD>
						<TD align="right" width="548"><h:outputText
							styleClass="outputText" id="text5"
							value="#{pc_Ssz03001.propCount.stringValue}"></h:outputText> <h:outputText
							styleClass="outputText" id="text6" value="件"></h:outputText></TD>
						<TD align="left" width="168"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="580">
						<div class="listScroll" style="height:296px;width: 100%;" id="listScroll"  onscroll="setScrollPosition('scroll',this);">
                        <h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz03001.propSszKinmuKeitaiKbn.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz03001.propSszKinmuKeitaiKbn.list}" var="varlist"
							width="563">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="lblListOboKbn" styleClass="outputText"
										value="区分"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlLblOboKbn"
									value="#{varlist.kinmuKeitaiKbn}"></h:outputText>
								<f:attribute value="84" name="width" />
								<f:attribute value="text-align: left; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称"
										id="lblListOboKbnName"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlLblOboKbnName"
									value="#{varlist.kinmuKeitaiKbnName}"></h:outputText>
								<f:attribute value="470" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz03001.doSelectAction}"></hx:commandExButton>
								<f:attribute value="24" name="width" />
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD width="226"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD align="left" width="620">
						<TABLE border="0" class="table">
							<TBODY>
								<TR>
									<TH width="165" class="v_a"><h:outputText
										styleClass="outputText" id="lblKinmuKeitaiKbn"
										value="#{pc_Ssz03001.propKinmuKeitaiKbn.labelName}"
										style="#{pc_Ssz03001.propKinmuKeitaiKbn.labelStyle}"></h:outputText></TH>
									<TD width="410"><h:inputText styleClass="inputText"
										id="htmlKinmuKeitaiKbn" size="1"
										value="#{pc_Ssz03001.propKinmuKeitaiKbn.stringValue}"
										style="#{pc_Ssz03001.propKinmuKeitaiKbn.style}"
										maxlength="#{pc_Ssz03001.propKinmuKeitaiKbn.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="165" class="v_b"><h:outputText
										styleClass="outputText" id="lblKinmuKeitaiKbnName"
										value="#{pc_Ssz03001.propKinmuKeitaiKbnName.labelName}"
										style="#{pc_Ssz03001.propKinmuKeitaiKbnName.labelStyle}"></h:outputText></TH>
									<TD width="410"><h:inputText styleClass="inputText"
										id="htmlKinmuKeitaiKbnName" size="54"
										value="#{pc_Ssz03001.propKinmuKeitaiKbnName.stringValue}"
										style="#{pc_Ssz03001.propKinmuKeitaiKbnName.style}"
										maxlength="#{pc_Ssz03001.propKinmuKeitaiKbnName.maxLength}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Ssz03001.doRegisterAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Ssz03001.doDeleteAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz03001.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ssz03001.propSszKinmuKeitaiKbn.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

