
<!--
 Copyright 2003 Sun Microsystems, Inc. All rights reserved.
 SUN PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
-->

<!DOCTYPE faces-config PUBLIC
  "-//Sun Microsystems, Inc.//DTD JavaServer Faces Config 1.0//EN"
  "http://java.sun.com/dtd/web-facesconfig_1_0.dtd">

<!-- =========== FULL CONFIGURATION FILE ================================== -->


<faces-config>
<!-- 学籍異動 managed-bean-->
     <managed-bean>
        <managed-bean-name>pc_Xri00101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xri.Xri00101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
     <managed-bean>
        <managed-bean-name>pc_Xri00201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xri.Xri00201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xri00501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xri.Xri00501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xri00301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xri.Xri00301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
<!-- 学費未納除籍 managed-bean-->       
    <managed-bean>
        <managed-bean-name>pc_Xri00401</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xri.Xri00401</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

<!-- 証明 managed-bean-->
    <managed-bean>
        <managed-bean-name>pc_Xrk00101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrk.Xrk00101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrk00201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrk.Xrk00201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrk00301</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrk.Xrk00301</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrk00401</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrk.Xrk00401</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrk00402</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrk.Xrk00402</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrk00501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrk.Xrk00501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrk00601</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrk.Xrk00601</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

<!-- 手当・学務 managed-bean-->
	<managed-bean>
		<managed-bean-name>pc_Xrl00101</managed-bean-name>
		<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00101</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	<managed-bean>
		<managed-bean-name>pc_Xrl00201</managed-bean-name>
		<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00201</managed-bean-class>
		<managed-bean-scope>session</managed-bean-scope>
	</managed-bean>
	<managed-bean>
    	<managed-bean-name>pc_Xrl00301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrl00401</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00401</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
	
	<managed-bean>
    	<managed-bean-name>pc_Xrl00501T01</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T01</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T02</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T02</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T03</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T03</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T04</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T04</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T05</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T05</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T06</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T06</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T07</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T07</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T08</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T08</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00501T09</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00501T09</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
	
	<managed-bean>
    	<managed-bean-name>pc_Xrl00601</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00601</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00701</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00701</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <managed-bean>
    	<managed-bean-name>pc_Xrl00801</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00801</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrl00802</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00802</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
        <managed-bean>
    	<managed-bean-name>pc_Xrl00901</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrl.Xrl00901</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
<!-- 卒業 managed-bean -->
    <managed-bean>
    	<managed-bean-name>pc_Xrj00101T01</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrj.Xrj00101T01</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrj00101T02</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrj.Xrj00101T02</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_PXrj0101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrj.PXrj0101</managed-bean-class>
    	<managed-bean-scope>request</managed-bean-scope>
    </managed-bean>	
    <managed-bean>
    	<managed-bean-name>pc_Xrj00201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrj.Xrj00201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学費 managed-bean-->
    <managed-bean>
        <managed-bean-name>pc_Xrm00101T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00101T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrm00101T02</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00101T02</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrm00101T03</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00101T03</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
	
    <managed-bean>
        <managed-bean-name>pc_Xrm00201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrm00301T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00301T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrm00301T02</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00301T02</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm00401</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00401</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm00501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm00601</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00601</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm00701</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00701</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm00801</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00801</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm00901T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00901T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm00901T02</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm00901T02</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        
        <managed-bean>
        <managed-bean-name>pc_Xrm01001</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01001</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm01101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm01201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm01301</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01301</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrm01302</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01302</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean> 
    <managed-bean>
        <managed-bean-name>pc_Xrm01401</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01401</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrm01402</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01402</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm01501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
        <managed-bean-name>pc_Xrm01601</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrm.Xrm01601</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>    

<!-- レポート managed-bean-->	 
    <managed-bean>
    	<managed-bean-name>pc_Xrf00101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00401</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00401</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00402</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00402</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00501</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00501</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00502</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00502</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00601</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00601</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00701</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00701</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00801</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00801</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrf00802</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrf.Xrf00802</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>


<!-- スクーリング managed-bean-->	 
    <managed-bean>
    	<managed-bean-name>pc_Xrg00101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00102</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00102</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00202</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00202</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00202T01</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00202T01</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00202T02</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00202T02</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00202T03</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00202T03</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00202T04</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00202T04</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrg00301</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00301</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrg00401</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00401</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrg00501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrg00601</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00601</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00701</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00701</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00702</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00702</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00703</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00703</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00801</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00801</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg00901</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg00901</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01001</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01001</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01401</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01401</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01501</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01501</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01601</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01601</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01701</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01701</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrg01801</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrg.Xrg01801</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_PXrg0101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrg.PXrg0101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
<!-- 科目試験 managed-bean-->	 
    <managed-bean>
    	<managed-bean-name>pc_Xrh00101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00401</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00401</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh00402</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00402</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh00402T01</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00402T01</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh00402T02</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00402T02</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh00402T03</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00402T03</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh00402T04</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00402T04</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh00403</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00403</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00501</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00501</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh00502</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00502</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00601</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00601</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00701</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00701</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00801</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00801</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh00901</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh00901</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01001</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01001</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh01302</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01302</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh01302T01</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01302T01</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh01302T02</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01302T02</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01401</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01401</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01501</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01501</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01601</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01601</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrh01602</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01602</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

    <managed-bean>
    	<managed-bean-name>pc_Xrh01701</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01701</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_PXrh0101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrh.PXrh0101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
     <managed-bean>
    	<managed-bean-name>pc_Xrh01201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrh.Xrh01201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
<!-- 教育実習登録 managed-bean-->	
	<managed-bean>
        <managed-bean-name>pc_Xrd00101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrd.Xrd00101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrd00102</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrd.Xrd00102</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrd00102T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrd.Xrd00102T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrd00102T02</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrd.Xrd00102T02</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrd00102T03</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrd.Xrd00102T03</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrd00301</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrd.Xrd00301</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 介護等体験登録 managed-bean-->	
     <managed-bean>
        <managed-bean-name>pc_Xre00101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xre.Xre00101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xre00102</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xre.Xre00102</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xre00102T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xre.Xre00102T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xre00102T02</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xre.Xre00102T02</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xre00102T03</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xre.Xre00102T03</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xre00201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xre.Xre00201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
<!-- 学生関連情報照会 managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00100</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00100</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(学籍基本情報) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(身分異動情報) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00102</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00102</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(資格情報) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00103</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00103</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(履修登録) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00104</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00104</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(科目試験) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00105</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00105</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(スクーリング) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00106</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00106</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(スクーリング単位認定) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00107</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00107</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(教育実習情報) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00108</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00108</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(学費納入) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00109</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00109</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(学費記録) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00110</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00110</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(証明書発行履歴) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00111</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00111</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(介護等体験情報) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00112</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00112</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(レポート・試験情報) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00113</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00113</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(自由設定) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00114</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00114</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 学生関連情報照会(卒業関連) managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00115</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00115</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- end managed-bean-->
<!-- 名称種別設定 managed-bean-->
	<managed-bean>
    	<managed-bean-name>pc_Xrx00201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- end managed-bean-->
<!-- 名称種別項目設定 managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx00202</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx00202</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- end managed-bean-->
<!-- 問い合わせ検索 managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx03101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx03101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- end managed-bean-->
<!-- 問い合わせ一覧 managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrx03102</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrx.Xrx03102</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- end managed-bean-->
<!-- 事前指導未受講者出力 managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xre00301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xre.Xre00301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- end managed-bean-->
    
<!-- 配本 managed-bean-->
    <managed-bean>
    	<managed-bean-name>pc_Xrc00301</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00301</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00102</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00102</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00401</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00401</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00502</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00502</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00503</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00503</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00504</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00504</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00601</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00601</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrc00701</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00701</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc00901</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00901</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrc01001</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrc.Xrc01001</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_PXrc0101</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.PXrc0101</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_PXrc0201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.PXrc0201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrc00201</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00201</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
        <managed-bean>
    	<managed-bean-name>pc_Xrc00801</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.Xrc00801</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrc01101T01</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.Xrc01101T01</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrc01101T02</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.Xrc01101T02</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
	<managed-bean>
    	<managed-bean-name>pc_Xrc01101T03</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrc.Xrc01101T03</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

<!-- 履修 managed-bean-->
	<!-- 履修随時登録 -->
    <managed-bean>
        <managed-bean-name>pc_Xrb00101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00102T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00102T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00102T02</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00102T02</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!-- 履修一括登録 -->
    <managed-bean>
        <managed-bean-name>pc_Xrb00201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!-- 履修状況表 -->
    <managed-bean>
        <managed-bean-name>pc_Xrb00301</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00301</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!-- 認定科目条件登録 -->
    <managed-bean>
    	<managed-bean-name>pc_Xrb00401</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00401</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrb00402</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00402</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrb00403</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00403</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
    	<managed-bean-name>pc_Xrb00404</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00404</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!--  -->
    <managed-bean>
        <managed-bean-name>pc_Xrb01001</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb01001</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!-- 履修変更申請確認 -->
    <managed-bean>
        <managed-bean-name>pc_Xrb00501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00502</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00502</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00503</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00503</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00504</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00504</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00505</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00505</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00506</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00506</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00507</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00507</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!-- 履修科目届印刷 -->
    <managed-bean>
    	<managed-bean-name>pc_Xrb00801</managed-bean-name>
    	<managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00801</managed-bean-class>
    	<managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!-- 履修請求データ作成 -->
    <managed-bean>
        <managed-bean-name>pc_Xrb00601</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00601</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00602</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00602</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00603</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00603</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00604</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00604</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00605</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00605</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrb00606</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00606</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <!-- 履修変更履歴反映 -->
    <managed-bean>
        <managed-bean-name>pc_Xrb00701</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrb.Xrb00701</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>

<!-- 入学 managed-bean-->
<!--  
    <managed-bean>
        <managed-bean-name>pc_Xra00901T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00901T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
-->
    <managed-bean>
        <managed-bean-name>pc_Xra01001T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra01001T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra01101T01</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra01101T01</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00902</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00902</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00202</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00202</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00301</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00301</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00302</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00302</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00401</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00401</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00501</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00501</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00601</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00601</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00701</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00701</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xra00801</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xra.Xra00801</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- WEB申請 managed-bean-->
	<managed-bean>
        <managed-bean-name>pc_Xrx04101</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrx.Xrx04101</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrx04102</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrx.Xrx04102</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    <managed-bean>
        <managed-bean-name>pc_Xrx04201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrx.Xrx04201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
<!-- 教育実習受講料請求書データ作成 -->
    <managed-bean>
        <managed-bean-name>pc_Xrd00201</managed-bean-name>
        <managed-bean-class>com.jast.gakuen.rev.xrd.Xrd00201</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>  


    
<!-- start navigation-rule-->

<navigation-rule>   
<!-- 学籍異動 navigation-rule-->
	    <navigation-case>
	        <from-outcome>Xri00301</from-outcome>
	        <to-view-id>/rev/xri/Xri00301.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xri00501</from-outcome>
	        <to-view-id>/rev/xri/Xri00501.jsp</to-view-id>
	    </navigation-case>

<!-- 証明 navigation-rule-->
        <navigation-case>
            <from-outcome>Xrk00101</from-outcome>
            <to-view-id>/rev/xrk/Xrk00101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrk00201</from-outcome>
            <to-view-id>/rev/xrk/Xrk00201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrk00301</from-outcome>
            <to-view-id>/rev/xrk/Xrk00301.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrk00401</from-outcome>
            <to-view-id>/rev/xrk/Xrk00401.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrk00402</from-outcome>
            <to-view-id>/rev/xrk/Xrk00402.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrk00501</from-outcome>
            <to-view-id>/rev/xrk/Xrk00501.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrk00601</from-outcome>
            <to-view-id>/rev/xrk/Xrk00601.jsp</to-view-id>
        </navigation-case>

<!-- 手当・学務 navigation-rule-->
        <navigation-case>
            <from-outcome>Xrl00101</from-outcome>
            <to-view-id>/rev/xrl/Xrl00101.jsp</to-view-id>
        </navigation-case>
		<navigation-case>
            <from-outcome>Xrl00201</from-outcome>
            <to-view-id>/rev/xrl/Xrl00201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00301</from-outcome>
            <to-view-id>/rev/xrl/Xrl00301.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00401</from-outcome>
            <to-view-id>/rev/xrl/Xrl00401.jsp</to-view-id>
        </navigation-case>

		<navigation-case>
            <from-outcome>Xrl00501T01</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T01.jsp</to-view-id>
        </navigation-case>
         <navigation-case>
            <from-outcome>Xrl00501T02</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T02.jsp</to-view-id>
        </navigation-case>
         <navigation-case>
            <from-outcome>Xrl00501T03</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T03.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00501T04</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T04.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00501T05</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T05.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00501T06</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T06.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00501T07</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T07.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00501T08</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T08.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00501T09</from-outcome>
            <to-view-id>/rev/xrl/Xrl00501T09.jsp</to-view-id>
        </navigation-case>
        
		<navigation-case>
            <from-outcome>Xrl00601</from-outcome>
            <to-view-id>/rev/xrl/Xrl00601.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00701</from-outcome>
            <to-view-id>/rev/xrl/Xrl00701.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00801</from-outcome>
            <to-view-id>/rev/xrl/Xrl00801.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrl00802</from-outcome>
            <to-view-id>/rev/xrl/Xrl00802.jsp</to-view-id>
        </navigation-case>
       <navigation-case>
            <from-outcome>Xrl00901</from-outcome>
            <to-view-id>/rev/xrl/Xrl00901.jsp</to-view-id>
        </navigation-case>
<!-- 卒業 navigation-rule-->
        <navigation-case>
            <from-outcome>Xrj00101T01</from-outcome>
            <to-view-id>/rev/xrj/Xrj00101T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrj00101T02</from-outcome>
            <to-view-id>/rev/xrj/Xrj00101T02.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>PXrj0101</from-outcome>
            <to-view-id>/rev/xrj/pXrj0101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrj00201</from-outcome>
            <to-view-id>/rev/xrj/Xrj00201.jsp</to-view-id>
        </navigation-case>

<!-- 学費 navigation-rule-->	   
	    <navigation-case>
	        <from-outcome>Xrm00101T01</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00101T01.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xrm00101T02</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00101T02.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xrm00101T03</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00101T03.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xrm00201</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00201.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xrm00301T01</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00301T01.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xrm00301T02</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00301T02.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm00401</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00401.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm00501</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00501.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm00601</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00601.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm00701</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00701.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm00801</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00801.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm00901T01</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00901T01.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm00901T02</from-outcome>
	        <to-view-id>/rev/xrm/Xrm00901T02.jsp</to-view-id>
	    </navigation-case>	    
	    	    <navigation-case>
	        <from-outcome>Xrm01001</from-outcome>
	        <to-view-id>/rev/xrm/Xrm01001.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm01101</from-outcome>
	        <to-view-id>/rev/xrm/Xrm01101.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm01201</from-outcome>
	        <to-view-id>/rev/xrm/Xrm01201.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xrm01301</from-outcome>
	        <to-view-id>/rev/xrm/Xrm01301.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
	        <from-outcome>Xrm01302</from-outcome>
	        <to-view-id>/rev/xrm/Xrm01302.jsp</to-view-id>
	    </navigation-case>
        <navigation-case>
            <from-outcome>Xrm01401</from-outcome>
            <to-view-id>/rev/xrm/Xrm01401.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>Xrm01402</from-outcome>
            <to-view-id>/rev/xrm/Xrm01402.jsp</to-view-id>
        </navigation-case>

	    <navigation-case>
	        <from-outcome>Xrm01501</from-outcome>
	        <to-view-id>/rev/xrm/Xrm01501.jsp</to-view-id>
	    </navigation-case>
	    	    <navigation-case>
	        <from-outcome>Xrm01601</from-outcome>
	        <to-view-id>/rev/xrm/Xrm01601.jsp</to-view-id>
	    </navigation-case>
<!-- レポート navigation-rule-->

	    <navigation-case>
            <from-outcome>Xrf00401</from-outcome>
            <to-view-id>/rev/xrf/Xrf00401.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrf00402</from-outcome>
            <to-view-id>/rev/xrf/Xrf00402.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrf00501</from-outcome>
            <to-view-id>/rev/xrf/Xrf00501.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrf00502</from-outcome>
            <to-view-id>/rev/xrf/Xrf00502.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrf00601</from-outcome>
            <to-view-id>/rev/xrf/Xrf00601.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrf00701</from-outcome>
            <to-view-id>/rev/xrf/Xrf00701.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrf00801</from-outcome>
            <to-view-id>/rev/xrf/Xrf00801.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrf00802</from-outcome>
            <to-view-id>/rev/xrf/Xrf00802.jsp</to-view-id>
	    </navigation-case>

<!-- スクーリング navigation-rule-->	 	

       	<navigation-case>
            <from-outcome>Xrg00101</from-outcome>
            <to-view-id>/rev/xrg/Xrg00101.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xrg00102</from-outcome>
            <to-view-id>/rev/xrg/Xrg00102.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00201</from-outcome>
            <to-view-id>/rev/xrg/Xrg00201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00202</from-outcome>
            <to-view-id>/rev/xrg/Xrg00202.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00202T01</from-outcome>
            <to-view-id>/rev/xrg/Xrg00202T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00202T02</from-outcome>
            <to-view-id>/rev/xrg/Xrg00202T02.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00202T03</from-outcome>
            <to-view-id>/rev/xrg/Xrg00202T03.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00202T04</from-outcome>
            <to-view-id>/rev/xrg/Xrg00202T04.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00301</from-outcome>
            <to-view-id>/rev/xrg/Xrg00301.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00401</from-outcome>
            <to-view-id>/rev/xrg/Xrg00401.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00501</from-outcome>
            <to-view-id>/rev/xrg/Xrg00501.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00601</from-outcome>
            <to-view-id>/rev/xrg/Xrg00601.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00701</from-outcome>
            <to-view-id>/rev/xrg/Xrg00701.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00702</from-outcome>
            <to-view-id>/rev/xrg/Xrg00702.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00703</from-outcome>
            <to-view-id>/rev/xrg/Xrg00703.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00801</from-outcome>
            <to-view-id>/rev/xrg/Xrg00801.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg00901</from-outcome>
            <to-view-id>/rev/xrg/Xrg00901.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01001</from-outcome>
            <to-view-id>/rev/xrg/Xrg01001.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01101</from-outcome>
            <to-view-id>/rev/xrg/Xrg01101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01201</from-outcome>
            <to-view-id>/rev/xrg/Xrg01201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01301</from-outcome>
            <to-view-id>/rev/xrg/Xrg01301.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01401</from-outcome>
            <to-view-id>/rev/xrg/Xrg01401.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01501</from-outcome>
            <to-view-id>/rev/xrg/Xrg01501.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01601</from-outcome>
            <to-view-id>/rev/xrg/Xrg01601.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01701</from-outcome>
            <to-view-id>/rev/xrg/Xrg01701.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrg01801</from-outcome>
            <to-view-id>/rev/xrg/Xrg01801.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>PXrg0101</from-outcome>
            <to-view-id>/rev/xrg/pXrg0101.jsp</to-view-id>
        </navigation-case>
        
<!-- 科目試験 navigation-rule-->

	    <navigation-case>
            <from-outcome>Xrh00401</from-outcome>
            <to-view-id>/rev/xrh/Xrh00401.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00402</from-outcome>
            <to-view-id>/rev/xrh/Xrh00402.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00402T01</from-outcome>
            <to-view-id>/rev/xrh/Xrh00402T01.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00402T02</from-outcome>
            <to-view-id>/rev/xrh/Xrh00402T02.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00402T03</from-outcome>
            <to-view-id>/rev/xrh/Xrh00402T03.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00402T04</from-outcome>
            <to-view-id>/rev/xrh/Xrh00402T04.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00403</from-outcome>
            <to-view-id>/rev/xrh/Xrh00403.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00501</from-outcome>
            <to-view-id>/rev/xrh/Xrh00501.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh00502</from-outcome>
            <to-view-id>/rev/xrh/Xrh00502.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh01201</from-outcome>
            <to-view-id>/rev/xrh/Xrh01201.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh01301</from-outcome>
            <to-view-id>/rev/xrh/Xrh01301.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh01302</from-outcome>
            <to-view-id>/rev/xrh/Xrh01302.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh01302T01</from-outcome>
            <to-view-id>/rev/xrh/Xrh01302T01.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh01302T02</from-outcome>
            <to-view-id>/rev/xrh/Xrh01302T02.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh01601</from-outcome>
            <to-view-id>/rev/xrh/Xrh01601.jsp</to-view-id>
	    </navigation-case>
	    <navigation-case>
            <from-outcome>Xrh01602</from-outcome>
            <to-view-id>/rev/xrh/Xrh01602.jsp</to-view-id>
	    </navigation-case>
        <navigation-case>
            <from-outcome>PXrh0101</from-outcome>
            <to-view-id>/rev/xrh/pXrh0101.jsp</to-view-id>
        </navigation-case>

<!-- 教育実習登録 navigation-rule-->	
    	<navigation-case>
            <from-outcome>Xrd00101</from-outcome>
            <to-view-id>/rev/xrd/Xrd00101.jsp</to-view-id>
    	</navigation-case>
    	<navigation-case>
            <from-outcome>Xrd00102T01</from-outcome>
            <to-view-id>/rev/xrd/Xrd00102T01.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>Xrd00102T02</from-outcome>
            <to-view-id>/rev/xrd/Xrd00102T02.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>Xrd00102T03</from-outcome>
            <to-view-id>/rev/xrd/Xrd00102T03.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrd00301</from-outcome>
            <to-view-id>/rev/xrd/Xrd00301.jsp</to-view-id>
        </navigation-case>
        
<!-- 介護等体験登録 navigation-rule-->	
    	<navigation-case>
            <from-outcome>Xre00101</from-outcome>
            <to-view-id>/rev/xre/Xre00101.jsp</to-view-id>
    	</navigation-case>
    	<navigation-case>
            <from-outcome>Xre00102T01</from-outcome>
            <to-view-id>/rev/xre/Xre00102T01.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>Xre00102T02</from-outcome>
            <to-view-id>/rev/xre/Xre00102T02.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>Xre00102T03</from-outcome>
            <to-view-id>/rev/xre/Xre00102T03.jsp</to-view-id>
        </navigation-case>
        
        <navigation-case>
            <from-outcome>Xre00301</from-outcome>
            <to-view-id>/rev/xre/Xre00301.jsp</to-view-id>
        </navigation-case> 
         
        <navigation-case>
            <from-outcome>Xre00201</from-outcome>
            <to-view-id>/rev/xre/Xre00201.jsp</to-view-id>
        </navigation-case>  
<!-- 配本 navigation-rule-->	
        <navigation-case>
            <from-outcome>Xrc00301</from-outcome>
            <to-view-id>/rev/xrc/Xrc00301.jsp</to-view-id>
        </navigation-case> 
        <navigation-case>
            <from-outcome>Xrc00101</from-outcome>
            <to-view-id>/rev/xrc/Xrc00101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00102</from-outcome>
            <to-view-id>/rev/xrc/Xrc00102.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00401</from-outcome>
            <to-view-id>/rev/xrc/Xrc00401.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00501</from-outcome>
            <to-view-id>/rev/xrc/Xrc00501.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00502</from-outcome>
            <to-view-id>/rev/xrc/Xrc00502.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00503</from-outcome>
            <to-view-id>/rev/xrc/Xrc00503.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00504</from-outcome>
            <to-view-id>/rev/xrc/Xrc00504.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00601</from-outcome>
            <to-view-id>/rev/xrc/Xrc00601.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00701</from-outcome>
            <to-view-id>/rev/xrc/Xrc00701.jsp</to-view-id>
        </navigation-case> 
        <navigation-case>
            <from-outcome>Xrc00901</from-outcome>
            <to-view-id>/rev/xrc/Xrc00901.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc01001</from-outcome>
            <to-view-id>/rev/xrc/Xrc01001.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>PXrc0101</from-outcome>
            <to-view-id>/rev/xrc/pXrc0101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>PXrc0201</from-outcome>
            <to-view-id>/rev/xrc/pXrc0201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc00201</from-outcome>
            <to-view-id>/rev/xrc/Xrc00201.jsp</to-view-id>
        </navigation-case> 
                <navigation-case>
            <from-outcome>Xrc00801</from-outcome>
            <to-view-id>/rev/xrc/Xrc00801.jsp</to-view-id>
        </navigation-case>
                <navigation-case>
            <from-outcome>Xrc01101T01</from-outcome>
            <to-view-id>/rev/xrc/Xrc01101T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc01101T02</from-outcome>
            <to-view-id>/rev/xrc/Xrc01101T02.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrc01101T03</from-outcome>
            <to-view-id>/rev/xrc/Xrc01101T03.jsp</to-view-id>
        </navigation-case>   
 <!-- end navigation-rule-->

<!-- 履修 navigation-rule-->

        <navigation-case>
            <from-outcome>Xrb01001</from-outcome>
            <to-view-id>/rev/xrb/Xrb01001.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00101</from-outcome>
            <to-view-id>/rev/xrb/Xrb00101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00102T01</from-outcome>
            <to-view-id>/rev/xrb/Xrb00102T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00102T02</from-outcome>
            <to-view-id>/rev/xrb/Xrb00102T02.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00201</from-outcome>
            <to-view-id>/rev/xrb/Xrb00201.jsp</to-view-id>
        </navigation-case>
        <!-- 履修状況表 -->
        <navigation-case>
            <from-outcome>Xrb00301</from-outcome>
            <to-view-id>/rev/xrb/Xrb00301.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00401</from-outcome>
            <to-view-id>/rev/xrb/Xrb00401.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00402</from-outcome>
            <to-view-id>/rev/xrb/Xrb00402.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00403</from-outcome>
            <to-view-id>/rev/xrb/Xrb00403.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00404</from-outcome>
            <to-view-id>/rev/xrb/Xrb00404.jsp</to-view-id>
        </navigation-case>
        <!-- 履修変更申請確認 -->
        <navigation-case>
            <from-outcome>Xrb00501</from-outcome>
            <to-view-id>/rev/xrb/Xrb00501.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00502</from-outcome>
            <to-view-id>/rev/xrb/Xrb00502.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00503</from-outcome>
            <to-view-id>/rev/xrb/Xrb00503.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00504</from-outcome>
            <to-view-id>/rev/xrb/Xrb00504.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00505</from-outcome>
            <to-view-id>/rev/xrb/Xrb00505.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00506</from-outcome>
            <to-view-id>/rev/xrb/Xrb00506.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00507</from-outcome>
            <to-view-id>/rev/xrb/Xrb00507.jsp</to-view-id>
        </navigation-case>
        <!-- 履修科目届 -->
        <navigation-case>
            <from-outcome>Xrb00801</from-outcome>
            <to-view-id>/rev/xrb/Xrb00801.jsp</to-view-id>
        </navigation-case>
        <!-- 履修請求データ作成 -->
        <navigation-case>
            <from-outcome>Xrb00601</from-outcome>
            <to-view-id>/rev/xrb/Xrb00601.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00602</from-outcome>
            <to-view-id>/rev/xrb/Xrb00602.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00603</from-outcome>
            <to-view-id>/rev/xrb/Xrb00603.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00604</from-outcome>
            <to-view-id>/rev/xrb/Xrb00604.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00605</from-outcome>
            <to-view-id>/rev/xrb/Xrb00605.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrb00606</from-outcome>
            <to-view-id>/rev/xrb/Xrb00606.jsp</to-view-id>
        </navigation-case>
        <!-- 履修変更履歴反映 -->
        <navigation-case>
            <from-outcome>Xrb00701</from-outcome>
            <to-view-id>/rev/xrb/Xrb00701.jsp</to-view-id>
        </navigation-case>

<!-- 入学 navigation-rule-->
<!--  
        <navigation-case>
-->
            <!-- 画面表示上の機能IDをCob001とする-->
<!-- 
            <from-outcome>Cob00101T51</from-outcome>
            <to-view-id>/rev/xra/Xra00901T01.jsp</to-view-id>
        </navigation-case>
-->
        <navigation-case>
            <!-- 画面表示上の機能IDをCob001とする-->
            <from-outcome>Cob00151</from-outcome>
            <to-view-id>/rev/xra/Xra00902.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <!-- 画面表示上の機能IDをCob001とする-->
            <from-outcome>Cob00101T52</from-outcome>
            <to-view-id>/rev/xra/Xra01001T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <!-- 画面表示上の機能IDをCob001とする-->
            <from-outcome>Cob00101T53</from-outcome>
            <to-view-id>/rev/xra/Xra01101T01.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00101</from-outcome>
            <to-view-id>/rev/xra/Xra00101.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00201</from-outcome>
            <to-view-id>/rev/xra/Xra00201.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00202</from-outcome>
            <to-view-id>/rev/xra/Xra00202.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00301</from-outcome>
            <to-view-id>/rev/xra/Xra00301.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00302</from-outcome>
            <to-view-id>/rev/xra/Xra00302.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00401</from-outcome>
            <to-view-id>/rev/xra/Xra00401.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00501</from-outcome>
            <to-view-id>/rev/xra/Xra00501.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00601</from-outcome>
            <to-view-id>/rev/xra/Xra00601.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00701</from-outcome>
            <to-view-id>/rev/xra/Xra00701.jsp</to-view-id>
        </navigation-case>
       	<navigation-case>
            <from-outcome>Xra00801</from-outcome>
            <to-view-id>/rev/xra/Xra00801.jsp</to-view-id>
        </navigation-case>

<!-- 共通 navigation-rule-->
		<navigation-case>
            <from-outcome>Xrx00201</from-outcome>
            <to-view-id>/rev/xrx/Xrx00201.jsp</to-view-id>
        </navigation-case>
		<navigation-case>
            <from-outcome>Xrx00202</from-outcome>
            <to-view-id>/rev/xrx/Xrx00202.jsp</to-view-id>
        </navigation-case>
 		<navigation-case>
            <from-outcome>Xrx03101</from-outcome>
            <to-view-id>/rev/xrx/Xrx03101.jsp</to-view-id>
        </navigation-case>
        		<navigation-case>
            <from-outcome>Xrx03102</from-outcome>
            <to-view-id>/rev/xrx/Xrx03102.jsp</to-view-id>
        </navigation-case>       
<!-- WEB申請 navigation-rule-->
        <navigation-case>
            <from-outcome>Xrx04101</from-outcome>
            <to-view-id>/rev/xrx/Xrx04101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrx04102</from-outcome>
            <to-view-id>/rev/xrx/Xrx04102.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>Xrx04201</from-outcome>
            <to-view-id>/rev/xrx/Xrx04201.jsp</to-view-id>
        </navigation-case>
<!-- end navigation-rule-->
</navigation-rule>  

<!-- start navigation-rule　from-outcome-->	     
<!-- 学籍異動 navigation-rule　from-outcome-->	
	<navigation-rule>
		<from-view-id>/rev/xri/Xri00301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xri/Xri00301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xri/Xri00301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xri/Xri00501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xri/Xri00501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xri/Xri00501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>

<!-- 証明 navigation-rule　from-outcome-->
	<navigation-rule>
		<from-view-id>/rev/xrk/Xrk00101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrk/Xrk00101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrk/Xrk00101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrk/Xrk00201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrk/Xrk00201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrk/Xrk00201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrk/Xrk00301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrk/Xrk00301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrk/Xrk00301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrk/Xrk00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrk/Xrk00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrk/Xrk00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrk/Xrk00402.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrk/Xrk00402.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrk/Xrk00402.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrk/Xrk00601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrk/Xrk00601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrk/Xrk00601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>

<!-- 手当・学務 navigation-rule　from-outcome-->
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T03.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T03.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T03.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T04.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T04.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T04.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T05.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T05.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T05.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T06.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T06.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T06.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T07.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T07.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T07.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T08.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T08.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T08.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00501T09.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T09.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00501T09.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00701.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00701.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00701.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00801.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00801.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00801.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00802.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00802.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00802.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrl/Xrl00901.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrl/Xrl00901.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrl/Xrl00901.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
<!-- 卒業 navigation-rule　from-outcome-->
	<navigation-rule>
		<from-view-id>/rev/xrj/Xrj00101T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrj/Xrj00101T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrj/Xrj00101T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrj/Xrj00101T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrj/Xrj00101T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrj/Xrj00101T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrj/pXrj0101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrj/pXrj0101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrj/pXrj0101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrj/Xrj00201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrj/Xrj00201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrj/Xrj00201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
<!-- 学費 navigation-rule　from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00101T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00101T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00101T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00101T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00101T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00101T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00101T03.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00101T03.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00101T03.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>

    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrm/Xrm00301T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00301T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00301T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrm/Xrm00301T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00301T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00301T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>


	<navigation-rule>
		<from-view-id>/rev/xri/Xri00301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xri/Xri00301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xri/Xri00301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xri/Xri00501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xri/Xri00501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xri/Xri00501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>


	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00701.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00701.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00701.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00801.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00801.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00801.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00901T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00901T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00901T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm00901T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm00901T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm00901T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>	
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm01001.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01001.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01001.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm01101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm01201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm01301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrm/Xrm01302.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01302.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01302.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>	
	<navigation-rule>
		<from-view-id>/rev/xrm/Xrm01401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrm/Xrm01402.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01402.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01402.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm01501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	    <navigation-rule>
		<from-view-id>/rev/xrm/Xrm01601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrm/Xrm01601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrm/Xrm01601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>

<!-- レポート  navigation-rule from-outcome-->
	<navigation-rule>
		<from-view-id>/rev/xrf/Xrf00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrf/Xrf00402.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00402.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00402.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrf/Xrf00501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrf/Xrf00502.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00502.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00502.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrf/Xrf00601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrf/Xrf00701.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00701.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00701.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
    <from-view-id>/rev/xrf/Xrf00801.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00801.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00801.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrf/Xrf00802.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrf/Xrf00802.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrf/Xrf00802.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
<!-- ｽｸｰﾘﾝｸﾞ  navigation-rule　from-outcome-->		
	<navigation-rule>
		<from-view-id>/rev/xrg/Xrg00101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrg/Xrg00101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrg/Xrg00101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrg/Xrg00102.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrg/Xrg00102.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrg/Xrg00102.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrg/pXrg0101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrg/pXrg0101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrg/pXrg0101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xri/Xri00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xri/Xri00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xri/Xri00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
	
	
	
	
	
	
	
	
<!-- 科目試験  navigation-rule from-outcome-->
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh00402T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh00402T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh00402T03.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T03.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T03.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh00402T04.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T04.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh00402T04.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh00403.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh00403.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh00403.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh00501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh00501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh00501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh01201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh01201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh01201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh01301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh01301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh01301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh01301T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh01301T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh01301T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh01301T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh01301T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh01301T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh01601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh01601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh01601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/Xrh01602.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/Xrh01602.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/Xrh01602.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrh/pXrh0101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrh/pXrh0101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrh/pXrh0101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
<!-- 教育実習登録  navigation-rule　from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrd/Xrd00101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrd/Xrd00101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrd/Xrd00101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
		<navigation-rule>
		<from-view-id>/rev/xrd/Xrd00102T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrd/Xrd00102T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrd/Xrd00102T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrd/Xrd00102T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrd/Xrd00102T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrd/Xrd00102T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrd/Xrd00102T03.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrd/Xrd00102T03.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrd/Xrd00102T03.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrd/Xrd00301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrd/Xrd00301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrd/Xrd00301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
<!-- 介護等体験登録  navigation-rule　from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xre/Xre00101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xre/Xre00101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xre/Xre00101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
		<navigation-rule>
		<from-view-id>/rev/xre/Xre00102T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xre/Xre00102T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xre/Xre00102T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xre/Xre00102T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xre/Xre00102T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xre/Xre00102T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xre/Xre00102T03.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xre/Xre00102T03.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xre/Xre00102T03.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xre/Xre00301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xre/Xre00301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xre/Xre00301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xre/Xre00201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xre/Xre00201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xre/Xre00201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
<!-- 配本  navigation-rule　from-outcome-->
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00301.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00301.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00301.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00102.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00102.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00102.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00502.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00502.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00502.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00503.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00503.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00503.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00504.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00504.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00504.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>	
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00701.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00701.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00701.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00901.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00901.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00901.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>	
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc01001.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc01001.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc01001.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/pXrc0101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/pXrc0101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/pXrc0101.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/pXrc0201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/pXrc0201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/pXrc0201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00201.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
		<navigation-rule>
		<from-view-id>/rev/xrc/Xrc00801.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc00801.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc00801.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
		<navigation-rule>
		<from-view-id>/rev/xrc/Xrc01101T01.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc01101T01.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc01101T01.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc01101T02.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc01101T02.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc01101T02.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrc/Xrc01101T03.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrc/Xrc01101T03.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrc/Xrc01101T03.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
<!-- 学生関連情報照会 navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00100.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00100.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00100.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(学籍基本情報) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00101.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(身分異動情報) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00102.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00102.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00102.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(資格情報) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00103.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00103.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00103.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(履修登録) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00104.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00104.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00104.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(科目試験) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00105.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00105.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00105.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(スクーリング) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00106.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00106.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00106.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(スクーリング単位認定) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00107.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00107.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00107.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(教育実習情報) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00108.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00108.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00108.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(学費納入) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00109.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00109.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00109.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(学費記録) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00110.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00110.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00110.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(証明書発行履歴) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00111.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00111.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00111.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(介護等体験情報) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00112.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00112.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00112.jsp</to-view-id>
		</navigation-case>
				<navigation-case>
			<from-outcome>X</from-outcome>
			<to-view-id>/rev/xrx/Xrx00112.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(レポート・試験情報) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00113.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00113.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00113.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(自由設定) navigation-rule from-outcome-->    
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00114.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00114.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00114.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 学生関連情報照会(卒業関連) navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00115.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00115.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00115.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 名称種別設定 navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00201.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00201.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00201.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 名称種別項目設定 navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx00202.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx00202.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx00202.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 問い合わせ検索 navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx03101.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx03101.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx03101.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->
<!-- 問い合わせ一覧 navigation-rule from-outcome-->	
    <navigation-rule>
		<from-view-id>/rev/xrx/Xrx03102.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrx/Xrx03102.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrx/Xrx03102.jsp</to-view-id>
		</navigation-case>
    </navigation-rule>
<!-- end navigation-rule from-outcome-->

<!-- 履修  navigation-rule from-outcome-->

    <navigation-rule>
        <from-view-id>/rev/km/Kmb00502.jsp</from-view-id>
        <navigation-case>
            <!-- 画面表示上の機能IDをKmb005とする-->
            <from-outcome>Kmb00511</from-outcome>
            <to-view-id>/rev/xrb/Xrb01001.jsp</to-view-id>
        </navigation-case> 
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xrb/Xrb01001.jsp</from-view-id>
        <navigation-case>
            <from-outcome>Xrb01001</from-outcome>
            <to-view-id>/rev/xrb/Xrb01001.jsp</to-view-id>
        </navigation-case> 
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xrb/Xrb00101.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrb/Xrb00101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrb/Xrb00101.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xrb/Xrb00102T01.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrb/Xrb00102T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrb/Xrb00102T01.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xrb/Xrb00102T02.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrb/Xrb00102T02.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrb/Xrb00102T02.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xrb/Xrb00201.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrb/Xrb00201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrb/Xrb00201.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <!-- 履修状況表 -->
    <navigation-rule>
        <from-view-id>/rev/xrb/Xrb00301.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrb/Xrb00301.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrb/Xrb00301.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00401.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00401.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00401.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00402.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00402.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00402.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00403.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00403.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00403.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00404.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00404.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00404.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<!-- 履修変更申請 -->
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00501.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00501.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00501.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
    <navigation-rule>
		<from-view-id>/rev/xrb/Xrb00502.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00502.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00502.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00503.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00503.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00503.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00504.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00504.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00504.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00505.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00505.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00505.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00506.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00506.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00506.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00507.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00507.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00507.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
  <!-- 履修科目届 -->
  <navigation-rule>
      <from-view-id>/rev/xrb/Xrb00801.jsp</from-view-id>
      <navigation-case>
          <from-outcome>true</from-outcome>
          <to-view-id>/rev/xrb/Xrb00801.jsp</to-view-id>
      </navigation-case>
      <navigation-case>
          <from-outcome>false</from-outcome>
          <to-view-id>/rev/xrb/Xrb00801.jsp</to-view-id>
      </navigation-case>
  </navigation-rule>
    <!-- 履修請求データ作成 -->
    <navigation-rule>
		<from-view-id>/rev/xrb/Xrb00601.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00601.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00601.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
    <navigation-rule>
		<from-view-id>/rev/xrb/Xrb00602.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00602.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00602.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00603.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00603.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00603.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00604.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00604.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00604.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00605.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00605.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00605.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00606.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00606.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00606.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	<!-- 履修変更履歴反映 -->
	<navigation-rule>
		<from-view-id>/rev/xrb/Xrb00701.jsp</from-view-id>
		<navigation-case>
			<from-outcome>true</from-outcome>
			<to-view-id>/rev/xrb/Xrb00701.jsp</to-view-id>
		</navigation-case>
		<navigation-case>
			<from-outcome>false</from-outcome>
			<to-view-id>/rev/xrb/Xrb00701.jsp</to-view-id>
		</navigation-case>
	</navigation-rule>
	
<!-- 入学 navigation-rule　from-outcome-->

<!--  
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00901T01.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00901T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00901T01.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
-->
    <navigation-rule>
        <from-view-id>/rev/xra/Xra01001T01.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra01001T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra01001T01.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra01101T01.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra01101T01.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra01101T01.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00902.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00902.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00902.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00101.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00101.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00201.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00201.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00202.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00202.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00202.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00301.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00301.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00301.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00302.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00302.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00302.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00401.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00401.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00401.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00501.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00501.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00501.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00601.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00601.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00601.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00701.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00701.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00701.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xra/Xra00801.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xra/Xra00801.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xra/Xra00801.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <navigation-case>
            <from-outcome>Xri00101</from-outcome>
            <to-view-id>/rev/xri/Xri00101.jsp</to-view-id>
        </navigation-case>
	</navigation-rule>
	<navigation-rule>
        <navigation-case>
            <from-outcome>Xri00201</from-outcome>
            <to-view-id>/rev/xri/Xri00201.jsp</to-view-id>
        </navigation-case>
	</navigation-rule>
		<navigation-rule>
        <navigation-case>
            <from-outcome>Xri00401</from-outcome>
            <to-view-id>/rev/xri/Xri00401.jsp</to-view-id>
        </navigation-case>
	</navigation-rule>
	
	
<!-- WEB申請 navigation-rule　from-outcome-->
    <navigation-rule>
        <from-view-id>/rev/xrx/Xrx04101.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrx/Xrx04101.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrx/Xrx04101.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xrx/Xrx04102.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrx/Xrx04102.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrx/Xrx04102.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/rev/xrx/Xrx04201.jsp</from-view-id>
        <navigation-case>
            <from-outcome>true</from-outcome>
            <to-view-id>/rev/xrx/Xrx04201.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>false</from-outcome>
            <to-view-id>/rev/xrx/Xrx04201.jsp</to-view-id>
        </navigation-case>
	</navigation-rule>
        
<!-- 教育実習受講料請求書データ作成 -->
	<navigation-rule>
        <navigation-case>
            <from-outcome>Xrd00201</from-outcome>
            <to-view-id>/rev/xrd/Xrd00201.jsp</to-view-id>
        </navigation-case>
	</navigation-rule>
        
    
    <navigation-rule>
        <from-view-id>/rev/xrh/Xrh01601.jsp</from-view-id>
        <navigation-case>
            <from-outcome>Xrh01602</from-outcome>
            <to-view-id>/rev/xrh/Xrh01602.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    
<!-- end navigation-rule from-outcome-->
</faces-config>