<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz02101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz02101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz02101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz02101.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz02101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz02101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="9%"></TD>
						<TD width="743">
						<TABLE border="0" width="550" align="center" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TD align="left" height="25" nowrap><h:outputText
										styleClass="likeOutput" id="lblkl" value="大分類（上"
										style="font-size: 8pt"></h:outputText><h:inputText
										styleClass="inputText_dis" id="htmlkl" size="１" maxlength="1"
										value="#{pc_Ssz02101.propDBriChikiKetasu.stringValue}"
										style="font-size: 8pt; height: 12px; margin: auto; margin-bottom: auto; margin-left: auto; margin-right: auto; margin-top: auto; width: 10px"
										disabled="true"></h:inputText><h:outputText
										styleClass="likeOutput" id="lblklb" value="桁）"
										style="font-size: 8pt"></h:outputText></TD>
									<TD width="125" height="25" align="left" nowrap></TD>
									<TD align="left" height="25" nowrap><h:outputText
										styleClass="likeOutput" id="lblkm" value="中分類（上"
										style="font-size: 8pt"></h:outputText><h:inputText
										styleClass="inputText_dis" id="htmlkm" size="1" maxlength="1"
										value="#{pc_Ssz02101.propCBriChikiKetasu.stringValue}"
										disabled="true"
										style="font-size: 8pt; height: 12px; margin-bottom: auto; margin-left: auto; margin-right: auto; margin-top: auto; width: 10px"></h:inputText><h:outputText
										styleClass="likeOutput" id="lblkmb" value="桁）"
										style="font-size: 8pt"></h:outputText></TD>
									<TD width="125" height="25" align="left" nowrap></TD>
									<TD align="left" height="25" nowrap><h:outputText
										styleClass="likeOutput" id="lblks" value="小分類（上"
										style="font-size: 8pt"></h:outputText><h:inputText
										styleClass="inputText_dis" id="htmlks" size="1" maxlength="1"
										value="#{pc_Ssz02101.propSBriChikiKetasu.stringValue}"
										disabled="true"
										style="font-size: 8pt; height: 12px; margin: auto; margin-bottom: auto; margin-left: auto; margin-right: auto; margin-top: auto; width: 10px"></h:inputText><h:outputText
										styleClass="likeOutput" id="lblksb" value="桁）"
										style="font-size: 8pt"></h:outputText></TD>





								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="105"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD></TD>
						<TD width="514"></TD>
						<TD><h:outputText styleClass="outputText" id="text7"
							style="font-size: 8pt"
							value="#{pc_Ssz02101.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text8" value="件"
							style="font-size: 8pt"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="19%"></TD>
						<TD width="580">
						<div class="listScroll" style="height:296px;width:551px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							styleClass="meisai_scroll" id="table1"
							width="534" value="#{pc_Ssz02101.propSszTiki.list}" var="varlist"
							 rowClasses="#{pc_Ssz02101.propSszTiki.rowClasses}">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text1" styleClass="outputText" value="コード"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text5"
									value="#{varlist.chikiCd}"></h:outputText>
								<f:attribute value="74" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="区分" id="text2"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text4"
									value="#{varlist.chikiType}"></h:outputText>
								<f:attribute value="74" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text3"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text6"
									value="#{varlist.chikiName}"></h:outputText>
								<f:attribute value="326" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" styleClass="commandExButton"
									id="select" action="#{pc_Ssz02101.doSelectAction}" value="選択"></hx:commandExButton>
								<f:attribute value="24" name="width" />
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD width="22%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="19%"></TD>
						<TD width="562">
						<DIV align="left">
						<TABLE border="0" width="100%" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="34%"><h:outputText
										styleClass="outputText" id="lblCode"
										value="#{pc_Ssz02101.propChikiCd.labelName}"
										style="#{pc_Ssz02101.propChikiCd.labelStyle}"></h:outputText></TH>
									<TD align="left" width="66%"><h:inputText
										styleClass="inputText" id="htmlChikiCd"
										value="#{pc_Ssz02101.propChikiCd.stringValue}"
										style="#{pc_Ssz02101.propChikiCd.style}"
										maxlength="#{pc_Ssz02101.propChikiCd.maxLength}" size="6">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="34%"><h:outputText
										styleClass="outputText" id="lblName"
										value="#{pc_Ssz02101.propChikiName.labelName}"
										style="#{pc_Ssz02101.propChikiName.labelStyle}"></h:outputText></TH>
									<TD align="left" width="66%"><h:inputText
										styleClass="inputText" id="htmlChikiName"
										value="#{pc_Ssz02101.propChikiName.stringValue}"
										style="#{pc_Ssz02101.propChikiName.style}"
										maxlength="#{pc_Ssz02101.propChikiName.maxLength}" size="40">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="34%"><h:outputText
										styleClass="outputText" id="lblNameA"
										value="#{pc_Ssz02101.propChikiNameKana.labelName}"
										style="#{pc_Ssz02101.propChikiNameKana.labelStyle}"></h:outputText></TH>
									<TD align="left" width="66%"><h:inputText
										styleClass="inputText" id="htmlChikiNameKana"
										value="#{pc_Ssz02101.propChikiNameKana.stringValue}"
										style="#{pc_Ssz02101.propChikiNameKana.style}"
										maxlength="#{pc_Ssz02101.propChikiNameKana.maxLength}"
										size="40">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="34%"><h:outputText
										styleClass="outputText" id="lblNameB"
										value="#{pc_Ssz02101.propChikiNameRyak.labelName}"
										style="#{pc_Ssz02101.propChikiNameRyak.labelStyle}"></h:outputText></TH>
									<TD align="left" width="66%"><h:inputText
										styleClass="inputText" id="htmlChikiNameRyak"
										value="#{pc_Ssz02101.propChikiNameRyak.stringValue}"
										style="#{pc_Ssz02101.propChikiNameRyak.style}"
										maxlength="#{pc_Ssz02101.propChikiNameRyak.maxLength}"
										size="20">
									</h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</DIV>
						</TD>
						<TD width="22%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD align="center" valign="middle"><hx:commandExButton
							type="submit" value="確定" styleClass="commandExButton_dat"
							id="register" action="#{pc_Ssz02101.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Ssz02101.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz02101.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz02101.propSszTiki.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition("scroll","listScroll")
</SCRIPT>
</HTML>

