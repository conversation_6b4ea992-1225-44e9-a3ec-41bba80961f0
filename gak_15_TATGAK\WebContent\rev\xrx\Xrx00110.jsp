<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00110.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<f:subview id="Xrx00110">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrx00110.onPageLoadBegin}">
		<%-- ↓ コンテンツ部 ↓ --%>
		<hx:jspPanel>
			<DIV class="column" align="center" style="width:870px">
			<TABLE class="table" border="0" cellpadding="5" width="870">
				<TBODY>
					<!-- 学生氏名カナ -->
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblShimeiNameKana"
							value="#{pc_Xrx00110.propShimeiNameKana.labelName}"
							style="#{pc_Xrx00110.propShimeiNameKana.labelStyle}"></h:outputText></TH>
						<TD width="280"><h:outputText styleClass="outputText"
							id="htmlShimeiNameKana"
							value="#{pc_Xrx00110.propShimeiNameKana.stringValue}"
							style="#{pc_Xrx00110.propShimeiNameKana.style}"></h:outputText></TD>
						<!-- 性別 -->
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblSeibetu" value="#{pc_Xrx00110.propSeibetu.labelName}"
							style="#{pc_Xrx00110.propSeibetu.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlSeibetu"
							value="#{pc_Xrx00110.propSeibetu.stringValue}"
							style="#{pc_Xrx00110.propSeibetu.style}"></h:outputText></TD>
						<%-- 学年 --%>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblGakunen" value="#{pc_Xrx00110.propGakunen.labelName}"
							style="#{pc_Xrx00110.propGakunen.labelStyle}"></h:outputText></TH>
						<TD width="280"><h:outputText styleClass="outputText"
							id="htmlGakunen" value="#{pc_Xrx00110.propGakunen.stringValue}"
							style="#{pc_Xrx00110.propGakunen.style}"></h:outputText></TD>
						<!-- 在籍期限 -->
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblLimitzaiseki"
							value="#{pc_Xrx00110.propLimitzaiseki.labelName}"
							style="#{pc_Xrx00110.propLimitzaiseki.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlLimitzaiseki"
							value="#{pc_Xrx00110.propLimitzaiseki.stringValue}"
							style="#{pc_Xrx00110.propLimitzaiseki.style}"></h:outputText></TD>
						<%-- 学費期限 --%>
					<TR>
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblLimitkigen"
							value="#{pc_Xrx00110.propLimitkigen.labelName}"
							style="#{pc_Xrx00110.propLimitkigen.labelStyle}"></h:outputText></TH>
						<TD width="280"><h:outputText styleClass="outputText"
							id="htmlLimitkigen"
							value="#{pc_Xrx00110.propLimitkigen.stringValue}"
							style="#{pc_Xrx00110.propLimitkigen.style}"></h:outputText></TD>

						<!-- 異動出学状態 -->
						<TH class="v_a" width="150"><h:outputText styleClass="outputText"
							id="lblIdo" value="#{pc_Xrx00110.propIdo.labelName}"
							style="#{pc_Xrx00110.propIdo.labelStyle}"></h:outputText></TH>
						<TD><h:outputText styleClass="outputText" id="htmlIdo"
							value="#{pc_Xrx00110.propIdo.stringValue}"
							style="#{pc_Xrx00110.propIdo.style}"></h:outputText></TD>
					</TR>
				</TBODY>
			</TABLE>
			<DIV class="column" align="center" style="width:870px">
			<TABLE class="table" border="0" cellpadding="5" width="870">
				<TBODY>
					<!-- 学費年度 -->
					<TR>
						<TH nowrap class="v_a" width="150px"><h:outputText
							styleClass="outputText" id="lblGhYear"
							value="#{pc_Xrx00110.propGhYear.labelName}"
							style="#{pc_Xrx00110.propGhYear.labelStyle}">
						</h:outputText></TH>
						<TD width="*" colspan=2><h:inputText styleClass="inputText"
							id="htmlGhYear" size="4" value="#{pc_Xrx00110.propGhYear.value}"
							style="#{pc_Xrx00110.propGhYear.style}"
							disabled="#{pc_Xrx00110.propGhYear.disabled}" tabindex="1">
							<hx:inputHelperAssist imeMode="inactive"
								errorClass="inputText_Error" promptCharacter="_" />
							<f:convertDateTime pattern="yyyy" />
						</h:inputText></TD>
						<TD  width="300px" valign="middle">
						<hx:commandExButton
							type="submit" value="選択" 
							styleClass="cmdBtn_dat_s" 
							id="select"
							disabled="#{pc_Xrx00110.propSelect.disabled}"
							action="#{pc_Xrx00110.doSelectAction}">
						</hx:commandExButton>
						<hx:commandExButton 
							type="submit" value="解除"
							styleClass="cmdBtn_etc_s" 
							id="unselect"
							action="#{pc_Xrx00110.doUnselectAction}"
							disabled="#{pc_Xrx00110.propUnselect.disabled}">
						</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<DIV class="column" align="center" style="width:870px">
				<h:dataTable
				rowClasses="#{pc_Xrx00110.propPayList.rowClasses}" cellpadding="0"
				cellspacing="0" headerClass="headerClass" footerClass="footerClass"
				styleClass="meisai_scroll" id="htmlPayList"
				value="#{pc_Xrx00110.propPayList.list}" var="varlist" width="870">
				<h:column id="column1">
					<!-- 振込依頼人コード -->
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListFurihitocd" value="振込依頼人コード">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="150" name="width" />
					<f:attribute value="text-align: center" name="style" />
					<h:outputText styleClass="outputText" id="htmlListFurihitocd"
						value="#{varlist.furihitocd}">
					</h:outputText>
				</h:column>
				<!-- 内訳 -->
				<h:column id="column2">
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListUtiwake" value="内訳">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="180" name="width"/>
					<h:outputText styleClass="outputText" id="htmlListUtiwake"
							value="#{varlist.utiwakekamokuOut.displayValue}"
							title="#{varlist.utiwake}">
					</h:outputText>
				</h:column>
				<!-- 請求日 -->
				<h:column id="column3">
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListSeikyuDate" value="請求日">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="100" name="width" />
					<f:attribute value="text-align: right" name="style" />
					<h:outputText style="padding: 2px;" styleClass="outputText" id="htmlListSeikyuDate"
						value="#{varlist.seikyuDate}">
					</h:outputText>
				</h:column>
				<!-- 請求金額 -->
				<h:column id="column4">
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListSeikyuBilled" value="請求金額">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="120" name="width" />
					<f:attribute value="text-align: right" name="style" />
					<h:outputText style="padding: 2px;" styleClass="outputText" id="htmlListSeikyuBilled"
						value="#{varlist.seikyuBilled}">
					</h:outputText>
				</h:column>
				<!-- 入金日 -->
				<h:column id="column5">
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListNyukinDate" value="入金日">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="100" name="width" />
					<f:attribute value="text-align: right" name="style" />
					<h:outputText style="padding: 2px;" styleClass="outputText" id="htmlListNyukinDate"
						value="#{varlist.nyukinDate}">
					</h:outputText>
				</h:column>
				<!-- 入金金額 -->
				<h:column id="column6">
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListNyukinBilled" value="入金金額">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="120" name="width" />
					<f:attribute value="text-align: right" name="style" />
					<h:outputText style="padding: 2px;" styleClass="outputText" id="htmlListNyukinBilled"
						value="#{varlist.nyukinBilled}">
					</h:outputText>
				</h:column>
				<!-- 返金日 -->
				<h:column id="column7">
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListHenkinDate" value="返金日">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="100" name="width" />
					<f:attribute value="text-align: right" name="style" />
					<h:outputText style="padding: 2px;" styleClass="outputText" id="htmlListHenkinDate"
						value="#{varlist.henkinDate}">
					</h:outputText>
				</h:column>
				<!-- 返金金額 -->
				<h:column id="column8">
					<f:facet name="header">
						<hx:jspPanel>
							<center><h:outputText styleClass="outputText"
								id="lblListHenkinBilled" value="返金金額">
							</h:outputText>
							</center>
						</hx:jspPanel>
					</f:facet>
					<f:attribute value="120" name="width" />
					<f:attribute value="text-align: right" name="style" />
					<h:outputText style="padding: 2px;" styleClass="outputText" id="htmlListHenkinBilled"
						value="#{varlist.henkinBilled}">
					</h:outputText>
				</h:column>
			</h:dataTable>
			</DIV>
			</DIV>
			</DIV>
			<h:inputHidden value="#{pc_Xrx00110.propExecutableSearch.integerValue}" id="htmlExecutableSearch"></h:inputHidden>
			<h:inputHidden value="#{pc_Xrx00110.hidGhYear.value}" id="htmlHidGhYear"></h:inputHidden>
			
			
		</hx:jspPanel>
		<BR>
		<%-- ↑ コンテンツ部 ↑ --%>
	</hx:scriptCollector>
</f:subview>

