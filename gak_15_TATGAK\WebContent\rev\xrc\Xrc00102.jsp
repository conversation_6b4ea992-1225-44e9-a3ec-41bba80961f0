<%-- 
	物品登録（物品明細登録）
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00102.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc00102.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc00102.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc00102.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc00102.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc00102.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer" align="">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Xrc00102.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content" class="outer">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE width="900" class="table">
							<TBODY>
								<TR>
									<TH width="100" class="v_a"><h:outputText
										styleClass="outputText" id="text1" value="物品コード"
										style="#{pc_Xrc00102.propBuppinCd.labelName}"></h:outputText></TH>
									<TD width="100"><h:outputText styleClass="outputText"
										id="lblBuppinCd" 
										value="#{pc_Xrc00102.propBuppinCd.stringValue}"
										style="#{pc_Xrc00102.propBuppinCd.style}"></h:outputText></TD>
									<TH width="100" class="v_b"><h:outputText
										styleClass="outputText" id="text2" value="物品名称"></h:outputText></TH>
									<TD width="600"><h:outputText styleClass="outputText"
										id="lblBuppinName"
										value="#{pc_Xrc00102.propBuppinName.stringValue}"
										style="#{pc_Xrc00102.propBuppinName.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD></TD><TD></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" nowrap align="right"><h:outputText
							styleClass="outputText" id="lblCount" 
							value="#{pc_Xrc00102.propBpnEd.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text3" value="件"></h:outputText></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" align="center">                    		
                    	<div class="listScroll" style="height:125px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrc00102.propBpnEd.rowClasses}"
							styleClass="meisai_scroll" id="htmlBpnEd"
							value="#{pc_Xrc00102.propBpnEd.list}" var="varlist">                    							
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text4" styleClass="outputText" value="版"></h:outputText>
								</f:facet>
								<f:attribute value="35" name="width" />
								<h:outputText styleClass="outputText" id="text5"
									value="#{varlist.edition}"></h:outputText>
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column2">								
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="書名" id="text6"></h:outputText>
								</f:facet>
								<f:attribute value="172" name="width" />
								<h:outputText styleClass="outputText" id="text7"
									value="#{varlist.syosekiName.displayValue}"
									title="#{varlist.syosekiName.value}"></h:outputText>
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="著者名" id="text8"></h:outputText>
								</f:facet>
								<f:attribute value="172" name="width" />
								<h:outputText styleClass="outputText" id="text9"
									value="#{varlist.chosyaName.displayValue}"
									title="#{varlist.chosyaName.value}"></h:outputText>
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="出版社名" id="text10"></h:outputText>
								</f:facet>
								<f:attribute value="172" name="width" />
								<h:outputText styleClass="outputText" id="text11"
									value="#{varlist.syupansyaName.displayValue}"
									title="#{varlist.syupansyaName.value}"></h:outputText>
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="定価"
										id="text12"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" 
								    id="text13"
									value="#{varlist.teika}"></h:outputText>
								<f:attribute value="65" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="単価" id="text14"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text15"
									value="#{varlist.tanka}"></h:outputText>
								<f:attribute value="65" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column7">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="原価" id="text16"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text17" value="#{varlist.genka}" ></h:outputText>
								<f:attribute value="68" name="width" />
								<f:attribute value="true" name="nowrap" />  
								<f:attribute value="text-align: right" name="style" />                 			
							</h:column>
							<h:column id="column8">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="売価" id="text18"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text19" 
								value="#{varlist.hanbaiKakak}"></h:outputText>
								<f:attribute value="65" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column9">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="重量" id="text20"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text21" value="#{varlist.jyuryo}" >
                               </h:outputText>
								<f:attribute value="45" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column10">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="30" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Xrc00102.doSelectAction}"></hx:commandExButton>
								<f:attribute value="center" name="align" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
						</h:dataTable>
						<div>
						</TD>
					</TR>
					<TR>
						<TD><BR></TD><TD><BR></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE class="table" width="896">
							<TBODY>
								<TR>
									<TH class="v_c" width="150"><h:outputText
										styleClass="outputText" id="lblEdition"
										style="#{pc_Xrc00102.propEdition.labelStyle}"
										value="#{pc_Xrc00102.propEdition.labelName}"></h:outputText></TH>
									<TD colspan="5" width="746"><h:inputText styleClass="inputText"
										id="htmlEdition" 
										value="#{pc_Xrc00102.propEdition.integerValue}"
										style="#{pc_Xrc00102.propEdition.style}"
										maxlength="#{pc_Xrc00102.propEdition.maxLength}" 
										size="2"><f:convertNumber pattern="##0"/>
                            			<hx:inputHelperAssist errorClass="inputText_Error"
                              			promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_d"><h:outputText
										styleClass="outputText" id="lblSyosekiName"
										value="#{pc_Xrc00102.propSyosekiName.labelName}"
										style="#{pc_Xrc00102.propSyosekiName.labelStyle}"></h:outputText></TH>
									<TD colspan="5" width="746"><h:inputText styleClass="inputText"
										id="htmlSyosekiName"
										value="#{pc_Xrc00102.propSyosekiName.stringValue}"
										style="#{pc_Xrc00102.propSyosekiName.style}"
										maxlength="#{pc_Xrc00102.propSyosekiName.maxLength}" 
										size="70"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_e"><h:outputText
										styleClass="outputText" id="lblChosyaName"
										value="#{pc_Xrc00102.propChosyaName.labelName}"
										style="#{pc_Xrc00102.propChosyaName.labelStyle}"></h:outputText></TH>
									<TD colspan="5" width="746"><h:inputText styleClass="inputText"
										id="htmlChosyaName"
										value="#{pc_Xrc00102.propChosyaName.stringValue}"
										style="#{pc_Xrc00102.propChosyaName.style}"
										maxlength="#{pc_Xrc00102.propChosyaName.maxLength}"
										size="70"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_f"><h:outputText
										styleClass="outputText" id="lblSyupansyaName"
										value="#{pc_Xrc00102.propSyupansyaName.labelName}"
										style="#{pc_Xrc00102.propSyupansyaName.labelStyle}"></h:outputText></TH>
									<TD colspan="5" width="746"><h:inputText 
									    styleClass="inputText"
										id="htmlSyupansyaName"
										value="#{pc_Xrc00102.propSyupansyaName.stringValue}"
										style="#{pc_Xrc00102.propSyupansyaName.style}"
										maxlength="#{pc_Xrc00102.propSyupansyaName.maxLength}"
										size="70"></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_g"><h:outputText
										styleClass="outputText" id="lblTeika"
										value="#{pc_Xrc00102.propTeika.labelName}"></h:outputText></TH>
									<TD width="100"><h:inputText styleClass="inputText"
										id="htmlTeika"
										value="#{pc_Xrc00102.propTeika.integerValue}"
										style="#{pc_Xrc00102.propTeika.style}"
										maxlength="#{pc_Xrc00102.propTeika.maxLength}"
										size="10">
										<f:convertNumber type="number" pattern="########0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
										</h:inputText></TD>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblTanka"
										value="#{pc_Xrc00102.propTanka.labelName}"></h:outputText></TH>
									<TD width="200"><h:inputText styleClass="inputText"
										id="htmlTanka"
										value="#{pc_Xrc00102.propTanka.integerValue}"
										style="#{pc_Xrc00102.propTanka.style}"
										maxlength="#{pc_Xrc00102.propTanka.maxLength}"
										size="10">
										<f:convertNumber type="number" pattern="########0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" /></h:inputText></TD>
									<TH width="150" class="v_b"><h:outputText
										styleClass="outputText" id="lblGenka"
										value="#{pc_Xrc00102.propGenka.labelName}"></h:outputText></TH>
									<TD width="200"><h:inputText styleClass="inputText"
										id="htmlGenka" 
										value="#{pc_Xrc00102.propGenka.doubleValue}"
										style="#{pc_Xrc00102.propGenka.style}"
										maxlength="#{pc_Xrc00102.propGenka.maxLength}" size="8">
										<f:convertNumber type="number" pattern="#######0.0" />
		                                <hx:inputHelperAssist errorClass="inputText_Error"
		                                    promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_c"><h:outputText
										styleClass="outputText" 
										id="lblHanbaiKakak"
										value="#{pc_Xrc00102.propHanbaiKakak.labelName}"
										style="#{pc_Xrc00102.propHanbaiKakak.labelStyle}"></h:outputText></TH>
									<TD width="100"><h:inputText styleClass="inputText"
                            			id="htmlHanbaiKakak" size="7"
                            			disabled="#{pc_Xrc00102.propHanbaiKakak.disabled}"
                            			value="#{pc_Xrc00102.propHanbaiKakak.integerValue}"
                            			style="#{pc_Xrc00102.propHanbaiKakak.style}"
                            			maxlength="#{pc_Xrc00102.propHanbaiKakak.maxLength}">
                            			<f:convertNumber pattern="########0"/>
                            			<hx:inputHelperAssist errorClass="inputText_Error"
                              			promptCharacter="_" /></h:inputText></TD>
									<TH width="150" class="v_c"><h:outputText
										styleClass="outputText" id="lblJyuryo"
										value="#{pc_Xrc00102.propJyuryo.labelName}"
										style="#{pc_Xrc00102.propJyuryo.labelStyle}"></h:outputText></TH>
									<TD colspan="3" width="450"><h:inputText styleClass="inputText"
										id="htmlJyuryo" 
										value="#{pc_Xrc00102.propJyuryo.integerValue}"
										style="#{pc_Xrc00102.propJyuryo.style}"
										maxlength="#{pc_Xrc00102.propJyuryo.maxLength}" 
										size="4"><f:convertNumber pattern="#####0"/>
                            			<hx:inputHelperAssist errorClass="inputText_Error"
                              			promptCharacter="_" /></h:inputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_f" valign="top"><h:outputText
										styleClass="outputText" id="lblMemo"
										value="#{pc_Xrc00102.propMemo.labelName}"
										style="#{pc_Xrc00102.propMemo.labelStyle}"></h:outputText><hx:graphicImageEx
										styleClass="graphicImageEx" id="imageEx1" 
										url="../image/ja/hankakukana_Permission.gif"></hx:graphicImageEx></TH>
									<TD colspan="5" width="700"><h:inputTextarea styleClass="inputTextarea"
										id="htmlMemo" 
										value="#{pc_Xrc00102.propMemo.stringValue}"
										style="#{pc_Xrc00102.propMemo.style}" cols="90" rows="7"
										disabled="#{pc_Xrc00102.propMemo.disabled}"></h:inputTextarea></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR class="hr" noshade>
			<BR>
			<TABLE width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Xrc00102.doRegisterAction}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Xrc00102.doDeleteAction1}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrc00102.doClearAction}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrc00102.propBpnEd.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition("scroll","listScroll")
</SCRIPT>
</HTML>

