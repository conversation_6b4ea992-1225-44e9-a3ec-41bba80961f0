<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz00101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz00101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz00101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz00101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->

			<TABLE width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="700">
						<TABLE border="0" class="table" width="700" cellpadding="0"
							cellspacing="0">
							<TBODY>
								<TR>
									<TH width="180" class="v_a"><h:outputText
										styleClass="outputText" id="lblKjinhyuSturikHh"
										style="#{pc_Ssz00101.propKjinhyuSturikHh.labelStyle}"
										value=" 求人票出力方法"></h:outputText></TH>
									<TD width="520"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlKjinhyuSturikHh"
										layout="pageDirection"
										value="#{pc_Ssz00101.propKjinhyuSturikHh.stringValue}"
										disabled="#{pc_Ssz00101.propKjinhyuSturikHh.disabled}">
										<f:selectItem itemValue="0" itemLabel="各校単位で出力" />
										<f:selectItem itemValue="1" itemLabel="学園（法人）で一括出力" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH class="v_a" width="180"><h:outputText
										styleClass="outputText" id="lblGakko"
										value="#{pc_Ssz00101.propGakko.labelName}"
										style="#{pc_Ssz00101.propGakko.labelStyle}"></h:outputText></TH>
									<TD width="520"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlGakko" disabled="#{pc_Ssz00101.propGakko.disabled}"
										style="#{pc_Ssz00101.propGakko.style}"
										value="#{pc_Ssz00101.propGakko.value}">
										<f:selectItems value="#{pc_Ssz00101.propGakko.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH width="180" class="v_b"><h:outputText
										styleClass="outputText" id="lblDataHozonNensu"
										value="#{pc_Ssz00101.propDataHozonNensuLbl.labelName}"
										style="#{pc_Ssz00101.propDataHozonNensuLbl.labelStyle}"></h:outputText></TH>
									<TD width="520"><h:inputText styleClass="inputText"
										id="htmlDataHozonNensu"
										value="#{pc_Ssz00101.propDataHozonNensu.stringValue}" size="2"
										maxlength="#{pc_Ssz00101.propDataHozonNensu.maxLength}"
										style="#{pc_Ssz00101.propDataHozonNensu.style}"></h:inputText><h:outputText
										styleClass="outputText" id="lblDataHozonNensu1Text"
										value="年前までの求人情報等のデータを保存"></h:outputText><BR>
									<h:inputText styleClass="inputText" id="htmlKsdHozonNensu"
										value="#{pc_Ssz00101.propKsdHozonNensu.stringValue}" size="2"
										maxlength="#{pc_Ssz00101.propDataHozonNensu.maxLength}"
										style="#{pc_Ssz00101.propKsdHozonNensu.style}"></h:inputText><h:outputText
										styleClass="outputText" id="lblDataHozonNensu2Text" 
										value="年前までの就職活動等のデータを保存"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="180" class="v_c"><h:outputText
										styleClass="outputText" id="lblReijoHakkoTsyKbn"
										value="礼状発行対象"
										style="#{pc_Ssz00101.propReijoHakkoTsyKbn.labelStyle}"></h:outputText></TH>
									<TD width="520"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlReijoHakkoTsyKbn"
										layout="pageDirection"
										value="#{pc_Ssz00101.propReijoHakkoTsyKbn.stringValue}">
										<f:selectItem itemValue="0"
											itemLabel="学生の内定が決まった企業に対して礼状の発行対象とする。" />
										<f:selectItem itemValue="1"
											itemLabel="学生の就職先として決定した企業に対して礼状の発行対象とする。" />
									</h:selectOneRadio></TD>
								</TR>
																<TR>
									<TH width="180" class="v_c"><h:outputText
										styleClass="outputText" id="lblDaitaiKgyCd"
										value="#{pc_Ssz00101.propDaitaiKgyCd.labelName}"
										style="#{pc_Ssz00101.propDaitaiKgyCd.labelStyle}"></h:outputText></TH>
									<TD width="520"><h:inputText styleClass="inputText"
										id="htmlDaitaiKgyCd"
										disabled="#{pc_Ssz00101.propDaitaiKgyCd.disabled}"
										value="#{pc_Ssz00101.propDaitaiKgyCd.stringValue}" size="10"
										maxlength="#{pc_Ssz00101.propDaitaiKgyCd.maxLength}"
										style="#{pc_Ssz00101.propDaitaiKgyCd.style}"></h:inputText><BR>
									<h:outputText styleClass="note" id="lblDaitaiKgyCdText"
										value="※指定したマスタ未登録企業コードに対して就職活動データが存在すると変更不可となります。"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="180" class="v_c"><h:outputText
										styleClass="outputText" id="lblSinroKbn"
										value="#{pc_Ssz00101.propSinroKiboGakusei.labelName}"
										style="#{pc_Ssz00101.propSinroKiboGakusei.labelStyle}"></h:outputText></TH>
									<TD width="520"><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlSinroKiboGakusei"
										layout="pageDirection"
										value="#{pc_Ssz00101.propSinroKiboGakusei.stringValue}">
										<f:selectItem itemValue="0"
											itemLabel="進路希望（職種・業種・勤務地・企業）のいずれかを登録済み" />
										<f:selectItem itemValue="1"
											itemLabel="学生進路区分を登録済み、または進路希望（職種・業種・勤務地・企業）のいずれかを登録済み" />
									</h:selectOneRadio>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="20%"></TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確 定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Ssz00101.doRegisterAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />

		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

