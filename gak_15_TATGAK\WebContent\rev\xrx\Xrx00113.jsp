<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00113.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<style>
#content .column .table .test {
text-align:right;
vertical-align:middle;
border:0px;
border-right: 1px solid #CCCCCC;
border-top: 1px solid #CCCCCC;
background-color:#FFFFFF;
padding: 0px;
empty-cells: hide;
height:20px;
padding-left:2px;
}

#content .column .table .test1 {
text-align:left;
vertical-align:middle;
border:0px;
border-right: 0px solid #CCCCCC;
border-top: 1px solid #CCCCCC;
background-color:#FFFFFF;
padding: 0px;
empty-cells: hide;
height:20px;
padding-left:2px;
}

#content .column .table .dummy {
text-align:right;
vertical-align:middle;
border:0px;
background-color:#FFFFFF;
padding: 0px;
empty-cells: hide;
height:0px;
padding-left:2px;
}

</style>

<SCRIPT type="text/javascript">
	
 	function doKamokuAjax(thisObj, thisEvent, targetLabel, targetLabel2) {
	// 科目名称,単位数を取得する
		var servlet = "rev/xrf/XrfKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;

	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);


		// 単位数
		if ( targetLabel2 != "" ) {
		    var args2 = new Array();
		    args2['code'] = thisObj.value;
		    args2['tanisu'] = "GET";
		    args2['addString'] = " 単位";

			ajaxUtil.getCodeName(servlet, targetLabel2, args2);
		}
	}
	
	function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=" + "form1:Xrx00113:htmlKamokCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}
	
	function loadAction(event){
		doKamokuAjax(document.getElementById('form1:htmlKamokCd'), event, 'form1:lblKamokNm', '');
	}

</SCRIPT>

<f:subview id="Xrx00113">

	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrx00113.onPageLoadBegin}">

		<%-- ↓ コンテンツ部 ↓ --%>
		<hx:jspPanel>
			<DIV style="width:900px">

			<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
				<TBODY>
					<TR>
						<TD class="dummy" width="150"></TD>
						<TD class="dummy" width="200"></TD>
						<TD class="dummy" width="150"></TD>
						<TD class="dummy" width="100"></TD>
					</TR>
					<TR>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblKamokCd" value="#{pc_Xrx00113.propKamokCd.labelName}"
							style="#{pc_Xrx00113.propKamokCd.labelStyle}">
						</h:outputText></TH>
						<TD colspan="3" class="test1"><h:inputText styleClass="inputText"
							id="htmlKamokCd" style="#{pc_Xrx00113.propKamokCd.style}"
							maxlength="#{pc_Xrx00113.propKamokCd.maxLength}"
							onblur="return doKamokuAjax(this, event, 'form1:Xrx00113:lblKamokNm', '');"
							value="#{pc_Xrx00113.propKamokCd.stringValue}" tabindex="1">
							<hx:inputHelperAssist imeMode="disabled"
								errorClass="inputText_Error" />
						</h:inputText> <hx:commandExButton type="button"
							styleClass="commandExButton_search" id="searchKamok"
							onclick="return openKamokuSearchWindow(this, event);"
							disabled="#{pc_Xrx00113.propSearchKamok.disabled}" tabindex="2">
						</hx:commandExButton> <h:outputText styleClass="outputText"
							id="lblKamokNm" style="#{pc_Xrx00113.propKamokNm.labelStyle}"
							value="#{pc_Xrx00113.propKamokNm.stringValue}">
						</h:outputText></TD>
						<TD class="test"><hx:commandExButton type="submit"
							styleClass="commandExButton_dat" id="clear" value="検索"
							action="#{pc_Xrx00113.doSearchAction}"
							disabled="#{pc_Xrx00113.propSearch.disabled}"
							rendered="#{pc_Xrx00113.propSearch.rendered}" tabindex="3">
						</hx:commandExButton></TD>
					</TR>

					<TR>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblKamokSikenResult"
							value="#{pc_Xrx00113.propKamokSikenResult.labelName}"
							style="#{pc_Xrx00113.propKamokSikenResult.labelStyle}">
						</h:outputText></TH>
						<TD><h:outputText styleClass="outputText"
							id="htmlKamokSikenResult"
							value="#{pc_Xrx00113.propKamokSikenResult.stringValue}">
						</h:outputText></TD>
						<TH class="v_a"><h:outputText styleClass="outputText"
							id="lblKamokSikenDate"
							value="#{pc_Xrx00113.propKamokSikenDate.labelName}"
							style="#{pc_Xrx00113.propKamokSikenDate.labelStyle}">
						</h:outputText></TH>
						<TD colspan="2"><h:outputText styleClass="outputText"
							id="htmlKamokSikenDate"
							value="#{pc_Xrx00113.propKamokSikenDate.stringValue}">
						</h:outputText></TD>

					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" align="left"
				style="margin-left:10px;">
				<THEAD align="center">
					<TR>
						<TD>1分冊</TD>
					</TR>
				</THEAD>
				<TBODY>

					<TR>
						<TD>
						<DIV id="listScroll" class="listScroll" style="height: 174px;"><h:dataTable
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrx00113.propListBunsatu1.rowClasses}"
							styleClass="meisai_scroll"
							value="#{pc_Xrx00113.propListBunsatu1.list}" var="varlist1">

							<h:column id="column11">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="添削依頼教員"
										id="lblListtensakIraikyoin1">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListtensakIraikyoin1"
									style="#{varlist1.tensakIraikyoin.style}"
									value="#{varlist1.tensakIraikyoin.displayValue}"
									title="#{varlist1.tensakIraikyoin.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column12">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="受付日"
										id="lblListuketukeDate1">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListuketukeDate1"
									style="#{varlist1.uketukeDate.style}"
									value="#{varlist1.uketukeDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column13">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価"
										id="lblListhyoka1">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListhyoka1"
									style="#{varlist1.hyoka.style}"
									value="#{varlist1.hyoka.stringValue}">
								</h:outputText>
								<f:attribute value="40" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column14">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="無効理由"
										id="lblListmukoriyu1">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListmukoriyu1"
									style="#{varlist1.mukoriyu.style}"
									value="#{varlist1.mukoriyu.displayValue}"
									title="#{varlist1.mukoriyu.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column15">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価入力日"
										id="lblListhyokaNyuryokDate1">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListhyokaNyuryokDate1"
									style="#{varlist1.hyokaNyuryokDate.style}"
									value="#{varlist1.hyokaNyuryokDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>


						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" cellpadding="0" cellspacing="0"
				style="align-left:4px;">
				<THEAD align="center">
					<TR>
						<TD>2分冊</TD>
					</TR>
				</THEAD>
				<TBODY>

					<TR>
						<TD>
						<DIV id="listScroll" class="listScroll" style="height: 174px;"><h:dataTable
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrx00113.propListBunsatu2.rowClasses}"
							styleClass="meisai_scroll"
							value="#{pc_Xrx00113.propListBunsatu2.list}" var="varlist2">

							<h:column id="column21">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="添削依頼教員"
										id="lblListtensakIraikyoin2">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListtensakIraikyoin2"
									style="#{varlist2.tensakIraikyoin.style}"
									value="#{varlist2.tensakIraikyoin.displayValue}"
									title="#{varlist2.tensakIraikyoin.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column22">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="受付日"
										id="lblListuketukeDate2">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListuketukeDate2"
									style="#{varlist2.uketukeDate.style}"
									value="#{varlist2.uketukeDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column23">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価"
										id="lblListhyoka2">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListhyoka2"
									style="#{varlist2.hyoka.style}"
									value="#{varlist2.hyoka.stringValue}">
								</h:outputText>
								<f:attribute value="40" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column24">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="無効理由"
										id="lblListmukoriyu2">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListmukoriyu2"
									style="#{varlist2.mukoriyu.style}"
									value="#{varlist2.mukoriyu.displayValue}"
									title="#{varlist2.mukoriyu.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column25">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価入力日"
										id="lblListhyokaNyuryokDate2">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListhyokaNyuryokDate2"
									style="#{varlist2.hyokaNyuryokDate.style}"
									value="#{varlist2.hyokaNyuryokDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<BR>

			<TABLE border="0" cellpadding="0" cellspacing="0" align="left"
				style="margin-left:10px;">
				<THEAD align="center">
					<TR>
						<TD>3分冊</TD>
					</TR>
				</THEAD>
				<TBODY>

					<TR>
						<TD>
						<DIV id="listScroll" class="listScroll" style="height: 174px;"><h:dataTable
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrx00113.propListBunsatu3.rowClasses}"
							styleClass="meisai_scroll"
							value="#{pc_Xrx00113.propListBunsatu3.list}" var="varlist3">

							<h:column id="column31">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="添削依頼教員"
										id="lblListtensakIraikyoin3">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListtensakIraikyoin3"
									style="#{varlist3.tensakIraikyoin.style}"
									value="#{varlist3.tensakIraikyoin.displayValue}"
									title="#{varlist3.tensakIraikyoin.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column32">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="受付日"
										id="lblListuketukeDate3">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListuketukeDate3"
									style="#{varlist3.uketukeDate.style}"
									value="#{varlist3.uketukeDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column33">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価"
										id="lblListhyoka3">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListhyoka3"
									style="#{varlist3.hyoka.style}"
									value="#{varlist3.hyoka.stringValue}">
								</h:outputText>
								<f:attribute value="40" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column34">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="無効理由"
										id="lblListmukoriyu3">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListmukoriyu3"
									style="#{varlist3.mukoriyu.style}"
									value="#{varlist3.mukoriyu.displayValue}"
									title="#{varlist3.mukoriyu.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column35">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価入力日"
										id="lblListhyokaNyuryokDate3">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListhyokaNyuryokDate3"
									style="#{varlist3.hyokaNyuryokDate.style}"
									value="#{varlist3.hyokaNyuryokDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			<TABLE border="0" cellpadding="0" cellspacing="0"
				style="align-left:4px;">
				<THEAD align="center">
					<TR>
						<TD>4分冊</TD>
					</TR>
				</THEAD>
				<TBODY>

					<TR>
						<TD>
						<DIV id="listScroll" class="listScroll" style="height: 174px;"><h:dataTable
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrx00113.propListBunsatu4.rowClasses}"
							styleClass="meisai_scroll"
							value="#{pc_Xrx00113.propListBunsatu4.list}" var="varlist4">

							<h:column id="column41">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="添削依頼教員"
										id="lblListtensakIraikyoin4">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListtensakIraikyoin4"
									style="#{varlist4.tensakIraikyoin.style}"
									value="#{varlist4.tensakIraikyoin.displayValue}"
									title="#{varlist4.tensakIraikyoin.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column42">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="受付日"
										id="lblListuketukeDate4">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListuketukeDate4"
									style="#{varlist4.uketukeDate.style}"
									value="#{varlist4.uketukeDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column43">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価"
										id="lblListhyoka4">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListhyoka4"
									style="#{varlist4.hyoka.style}"
									value="#{varlist4.hyoka.stringValue}">
								</h:outputText>
								<f:attribute value="40" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

							<h:column id="column44">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="無効理由"
										id="lblListmukoriyu4">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListmukoriyu4"
									style="#{varlist4.mukoriyu.style}"
									value="#{varlist4.mukoriyu.displayValue}"
									title="#{varlist4.mukoriyu.stringValue}">
								</h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>

							<h:column id="column45">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="評価入力日"
										id="lblListhyokaNyuryokDate4">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText"
									id="htmlListhyokaNyuryokDate4"
									style="#{varlist4.hyokaNyuryokDate.style}"
									value="#{varlist4.hyokaNyuryokDate.stringValue}">
								</h:outputText>
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>

						</h:dataTable></DIV>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

			</DIV>
		</hx:jspPanel>

		<%-- ↑ コンテンツ部 ↑ --%>
	</hx:scriptCollector>
</f:subview>
