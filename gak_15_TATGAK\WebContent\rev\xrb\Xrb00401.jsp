<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
    title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
        function confirmOk() {  
        //count = count + 1;
            //if (document.getElementById('form1:max').value == "max") {
                indirectClick('search');
            //} else{   
            //  indirectClick('clear');
            //}
        }
        function confirmCancel() {
            // alert('実行を中断しました。');   
        }



function loadAction(event){

//    changeScrollPosition('scroll', 'listScroll');
}

function doAllSelect(thisObj, thisEvent) {
// チェックボックス一括チェック
	check('htmlNtkJknList','htmlListCheckBox');
}

function doAllUnSelect(thisObj, thisEvent) {
// チェックボックス一括解除
	uncheck('htmlNtkJknList','htmlListCheckBox');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
    <f:loadBundle basename="properties.message" var="msg" />
    <BODY onLoad="loadAction(event)">
    <hx:scriptCollector id="scriptCollector1"
        preRender="#{pc_Xrb00401.onPageLoadBegin}">
        <h:form styleClass="form" id="form1">

            <!-- ヘッダーインクルード -->
            <jsp:include page="../inc/header.jsp" />

            <!-- ヘッダーへのデータセット領域 -->
            <div style="display:none;"><hx:commandExButton type="submit"
                value="閉じる" styleClass="commandExButton" id="closeDisp"
                action="#{pc_Xrb00401.doCloseDispAction}"
                ></hx:commandExButton> <h:outputText
                styleClass="outputText" id="htmlFuncId"
                value="#{pc_Xrb00401.funcId}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlLoginId"
                value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
                styleClass="outputText" id="htmlScrnName"
                value="#{pc_Xrb00401.screenName}"></h:outputText></div>

            <!--↓outer↓-->
            <DIV class="outer">

            <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
                id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
                styleClass="outputText" escape="false">
            </h:outputText></FIELDSET>

            <!--↓content↓-->
            <DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
            </DIV>
            <DIV id="content">
            <DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
            <TABLE width="900">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
	                                	id="lblNyugakNendo"
										style="#{pc_Xrb00401.propNyugakNendo.labelStyle}"
	                                	value="#{pc_Xrb00401.propNyugakNendo.labelName}">
	                                </h:outputText>
	                            </TH>
	                            <TD width="260">
	                            	<h:inputText styleClass="inputText"
	                                	id="htmlNyugakNendo"
										disabled="#{pc_Xrb00401.propNyugakNendo.disabled}"
	                                	value="#{pc_Xrb00401.propNyugakNendo.dateValue}"
	                                	style="#{pc_Xrb00401.propNyugakNendo.style}"
	                                	size="10" tabindex="1">
										<f:convertDateTime pattern="yyyy" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_"/>
	                            	</h:inputText>
	                            </TD>
	                           	<TH class="v_b" width="140">
	                           		<h:outputText styleClass="outputText"
	                                	id="lblNyugakGakkiNo"
										style="#{pc_Xrb00401.propNyugakGakkiNo.labelStyle}"
	                                	value="#{pc_Xrb00401.propNyugakGakkiNo.labelName}">
	                                </h:outputText>
	                            </TH>
	                            <TD width="260">
	                            	<h:inputText styleClass="inputText"
	                                	id="htmlNyugakGakkiNo"
										disabled="#{pc_Xrb00401.propNyugakGakkiNo.disabled}"
	                                	value="#{pc_Xrb00401.propNyugakGakkiNo.integerValue}"
	                                	style="#{pc_Xrb00401.propNyugakGakkiNo.style}"
	                                	size="10" tabindex="2">
	                                	<f:convertNumber type="number" pattern="#0" />
	                                	<hx:inputHelperAssist errorClass="inputText_Error"
	                                	    promptCharacter="_"/>
	                            	</h:inputText>
	                            </TD>
	                        </TR>
	                    </TABLE>
                    </TD>
                    <TD align="left">
                    	<hx:commandExButton type="submit"
							value="選択" styleClass="commandExButton" id="select"
							action="#{pc_Xrb00401.doSelectAction}"
							disabled="#{pc_Xrb00401.propSelect.disabled}" tabindex="3">
						</hx:commandExButton>
						<hx:commandExButton type="submit"
							value="解除" styleClass="commandExButton" id="unselect"
							action="#{pc_Xrb00401.doUnselectAction}"
							disabled="#{pc_Xrb00401.propUnselect.disabled}" tabindex="4">
						</hx:commandExButton>
					</TD>
                </TR>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE width="900">
                <TR>
                    <TD width="810" align="left">
	                    <TABLE class="table">
	                        <TR>
	                            <TH class="v_a" width="140">
	                            	<h:outputText styleClass="outputText"
										id="lblNyugakNenji"
										style="#{pc_Xrb00401.propNyugakNenji.labelStyle}"
	                                	value="#{pc_Xrb00401.propNyugakNenji.labelName}">
									</h:outputText>
								</TH>
	                            <TD width="260">
		                        	<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNyugakNenji" tabindex="5"
										disabled="#{pc_Xrb00401.propNyugakNenji.disabled}"
										value="#{pc_Xrb00401.propNyugakNenji.value}">
										<f:selectItems value="#{pc_Xrb00401.propNyugakNenji.list}" />
									</h:selectOneMenu>
								</TD>
	                            <TH class="v_b" width="140">
	                            	<h:outputText styleClass="outputText"
										id="lblNyugakSbt"
										style="#{pc_Xrb00401.propNyugakSbt.labelStyle}"
	                                	value="#{pc_Xrb00401.propNyugakSbt.labelName}">
									</h:outputText>
								</TH>
	                            <TD width="260">
		                        	<h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNyugakSbt" tabindex="6"
										disabled="#{pc_Xrb00401.propNyugakSbt.disabled}"
										value="#{pc_Xrb00401.propNyugakSbt.value}">
										<f:selectItems value="#{pc_Xrb00401.propNyugakSbt.list}" />
									</h:selectOneMenu>
								</TD>
	                        </TR>
		                    <TR>
		                        <TH class="v_c">
		                        	<h:outputText styleClass="outputText"
		                        		id="lblCurGakkaCd"
										style="#{pc_Xrb00401.propCurGakkaCd.labelStyle}"
	                                	value="#{pc_Xrb00401.propCurGakkaCd.labelName}">
									</h:outputText>
								</TH>
		                        <TD colspan="3">
		                        	<h:selectOneMenu styleClass="selectOneMenu"
		                        		id="htmlCurGakkaCd" tabindex="7"
										disabled="#{pc_Xrb00401.propCurGakkaCd.disabled}"
										value="#{pc_Xrb00401.propCurGakkaCd.value}">
										<f:selectItems value="#{pc_Xrb00401.propCurGakkaCd.list}" />
									</h:selectOneMenu>
								</TD>
		                    </TR>
	                    </TABLE>
                    </TD>
                    <TD align="left">
                    	<hx:commandExButton type="submit"
							value="検索" styleClass="commandExButton" id="search"
							action="#{pc_Xrb00401.doSearchAction}"
							disabled="#{pc_Xrb00401.propSearch.disabled}" tabindex="8">
						</hx:commandExButton>
						<hx:commandExButton type="submit"
							value="解除" styleClass="commandExButton" id="cancel"
							action="#{pc_Xrb00401.doCancelAction}"
							disabled="#{pc_Xrb00401.propCancel.disabled}" tabindex="9">
						</hx:commandExButton>
					</TD>
                </TR>
            </TABLE>
            <HR class="hr" noshade>
            <TABLE width="900" class="button_bar" border="0" cellpadding="0"
                cellspacing="0">
                <TR>
                    <TD >
                    	<hx:commandExButton type="submit" value="新規追加"
							styleClass="commandExButton_dat" id="addNew"
							disabled="#{pc_Xrb00401.propAddNew.disabled}"
							style="#{pc_Xrb00401.propAddNew.style}"
							action="#{pc_Xrb00401.doAddNewAction}" tabindex="10">
						</hx:commandExButton>
                    </TD>
                </TR>
            </TABLE>
			<TABLE width="900">
				<TR>
					<TD width="810" align="left">
						<TABLE border="0" cellpadding="0">
							<TR>
								<TD align="right" nowrap class="outputText">
									<h:outputText styleClass="outputText"
										id="htmlNtkJknListCount"
										value="#{pc_Xrb00401.propNtkJknListCount.value}">
									</h:outputText>
								</TD>
							</TR>
							<tr>
								<td>
									<DIV style="height:180px" class="listScroll"
										onscroll="setScrollPosition('scroll', this);">
										<h:dataTable border="0" cellpadding="0" cellspacing="0"
											columnClasses="columnClass1" headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_Xrb00401.propNtkJknList.rowClasses}"
											styleClass="meisai_scroll" id="htmlNtkJknList"
											value="#{pc_Xrb00401.propNtkJknList.list}" var="varlist">
											<h:column id="column1">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value=""
														id="lblListTaisyoChk">
													</h:outputText>
												</f:facet>
												<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlListCheckBox" value="#{varlist.taisyoChk}">
												</h:selectBooleanCheckbox>
												<f:attribute value="text-align:center" name="style" />
												<f:attribute value="40" name="width" />
											</h:column>
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="科目振替コード"
														id="lblListKamokuFurikaeCd">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokuFurikaeCd"
													value="#{varlist.kamokuFurikaeCd}">
												</h:outputText>
												<f:attribute value="true" name="nowrap" />
												<f:attribute value="100" name="width" />
											</h:column>
											<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="振替タイトル"
														id="lblListTitle">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListTitle"
													value="#{varlist.title}">
												</h:outputText>
												<f:attribute value="true" name="nowrap" />
												<f:attribute value="300" name="width" />
											</h:column>
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="科目振替区分"
														id="lblListKamokuFurikaeKbn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokuFurikaeKbn"
													value="#{varlist.kamokuFurikaeKbnName}">
												</h:outputText>
												<f:attribute value="true" name="nowrap" />
												<f:attribute value="150" name="width" />
											</h:column>
											<h:column id="column5">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="認定対象"
														id="lblListKisyutokFlg">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKisyutokFlg"
													value="#{varlist.kisyutokFlgName}">
												</h:outputText>
												<f:attribute value="true" name="nowrap" />
												<f:attribute value="150" name="width" />
											</h:column>
											<h:column id="column6">
												<f:facet name="header">
												</f:facet>
												<hx:commandExButton type="submit" value="選択"
													styleClass="commandExButton" id="edit"
													action="#{pc_Xrb00401.doEditAction}" tabindex="11">
												</hx:commandExButton>
												<f:attribute value="40" name="width" />
											</h:column>
										</h:dataTable>
										<BR>
									</DIV>
								</td>
							</tr>
						</TABLE>
					</td>
				</tr>
			</TABLE>
			<TABLE width="900" align="center">
				<TR>
					<TD align="left">
						<hx:commandExButton type="submit" value="一括チェック"
							styleClass="check" id="btnAllSelect"  onclick="return doAllSelect(this, event);">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="一括解除"
							styleClass="uncheck" id="btnAllUnSelect" onclick="return doAllUnSelect(this, event);">
						</hx:commandExButton>
						<hx:commandExButton type="submit" value="削除"
							styleClass="commandExButton" id="delete"
							action="#{pc_Xrb00401.doDeleteAction}"
							disabled="#{pc_Xrb00401.propDelete.disabled}"
							confirm="#{msg.SY_MSG_0004W}" tabindex="12">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
            <!-- ↑ここにコンポーネントを配置 --></DIV>
            </DIV>
            <!--↑content↑--></DIV>
            <!--↑outer↑-->
            <!-- フッダーインクルード -->
            <jsp:include page="../inc/footer.jsp" />
        </h:form>
    </hx:scriptCollector>
    </BODY>
    <jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>

