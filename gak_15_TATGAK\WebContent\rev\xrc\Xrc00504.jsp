<%-- 
	入出庫登録(発注管理)
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00504.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc00504.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc00504.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc00504.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc00504.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc00504.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer" align="">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Xrc00504.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content" class="outer">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE width="900" class="table">

							<TBODY>
								<TR>
									<TH width="150"><h:outputText 
										styleClass="outputText" id="text1" 
										value="物品コード"
										style="#{pc_Xrc00504.propBuppinCd.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblBuppinCd" 
										value="#{pc_Xrc00504.propBuppinCd.stringValue}"
										style="#{pc_Xrc00504.propBuppinCd.style}"></h:outputText></TD>
									<TH width="130"><h:outputText
										styleClass="outputText" id="text2" 
										value="物品名称"></h:outputText></TH>
									<TD width="470" colspan="3"><h:outputText styleClass="outputText"
										id="lblBuppinName"
										value="#{pc_Xrc00504.propBuppinName.stringValue}"
										style="#{pc_Xrc00504.propBuppinName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="150" class="v_a"><h:outputText 
										styleClass="outputText" id="text3" 
										value="物品区分"
										style="#{pc_Xrc00504.propBuppinKbnName.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblBuppinKbnName" 
										value="#{pc_Xrc00504.propBuppinKbnName.stringValue}"
										style="#{pc_Xrc00504.propBuppinKbnName.style}"></h:outputText></TD>
									<TH width="150"><h:outputText 
										styleClass="outputText" id="text4" 
										value="廃止フラグ"
										style="#{pc_Xrc00504.propHaisiFlgName.labelName}"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblTaxFlgName" 
										value="#{pc_Xrc00504.propHaisiFlgName.stringValue}"
										style="#{pc_Xrc00504.propHaisiFlgName.style}"></h:outputText></TD>
									<TH width="150" ><h:outputText
										styleClass="outputText" id="text5" 
										value="配本版"></h:outputText></TH>
									<TD width="150"><h:outputText styleClass="outputText"
										id="lblHaihonEd"
										value="#{pc_Xrc00504.propHaihonEd.stringValue}"
										style="#{pc_Xrc00504.propHaihonEd.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH><h:outputText 
										styleClass="outputText" id="text6" 
										value="版"
										style="#{pc_Xrc00504.propEdition.labelName}"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="lblEdition" 
										value="#{pc_Xrc00504.propEdition.integerValue}"
										style="#{pc_Xrc00504.propEdition.style}"></h:outputText></TD>
									<TH width="100"><h:outputText
										styleClass="outputText" id="text8" 
										value="書名"></h:outputText></TH>
									<TD colspan="5"><h:outputText styleClass="outputText"
										id="lblSyosekiName"
										value="#{pc_Xrc00504.propSyosekiName.stringValue}"
										style="#{pc_Xrc00504.propSyosekiName.style}"></h:outputText></TD>		
								</TR>
								<TR>
									<TH><h:outputText 
										styleClass="outputText" id="text7" 
										value="著者名"
										style="#{pc_Xrc00504.propChosyaName.labelName}"></h:outputText></TH>
									<TD colspan="5"><h:outputText styleClass="outputText"
										id="lblChosyaName" 
										value="#{pc_Xrc00504.propChosyaName.stringValue}"
										style="#{pc_Xrc00504.propChosyaName.style}"></h:outputText></TD>								
								</TR>
								<TR>									
									<TH width="100"><h:outputText
										styleClass="outputText" id="text9"
										value="出版社名"></h:outputText></TH>
									<TD colspan="5"><h:outputText styleClass="outputText"
										id="lblSyupansyaName"
										value="#{pc_Xrc00504.propSyupansyaName.stringValue}"
										style="#{pc_Xrc00504.propSyupansyaName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="text10" 
										value="刷"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="lblImpression"
										value="#{pc_Xrc00504.propImpression.integerValue}"
										style="#{pc_Xrc00504.propImpression.style}"></h:outputText></TD>										
									<TH><h:outputText
										styleClass="outputText" id="text11" 
										value="発注日"></h:outputText></TH>
									<TD colspan="4"><h:outputText styleClass="outputText"
										id="lblPropHakkoDate"
										value="#{pc_Xrc00504.propHakkoDate.stringValue}"
										style="#{pc_Xrc00504.propHakkoDate.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="text12" 
										value="入出庫先種別＿発注"></h:outputText></TH>
									<TD colspan="3"><h:outputText styleClass="outputText"
										id="lblNsksakiSbtCdOrder"
										value="#{pc_Xrc00504.propNsksakiSbtCdOrder.stringValue}"
										style="#{pc_Xrc00504.propNsksakiSbtCdOrder.style}"></h:outputText></TD>		
									<TH><h:outputText
										styleClass="outputText" id="text13" 
										value="完了フラグ"></h:outputText></TH>
									<TD><h:outputText styleClass="outputText"
										id="lblPropKanryoFlg"
										value="#{pc_Xrc00504.propKanryoFlg.stringValue}"
										style="#{pc_Xrc00504.propKanryoFlg.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD></TD><TD></TD>
					</TR>
					<TR>
						<TD></TD><TD></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" nowrap align="right"><h:outputText
							styleClass="outputText" id="lblCountImp" 
							value="#{pc_Xrc00504.propBpntRrkm.listCount}"></h:outputText><h:outputText
							styleClass="outputText" id="text14" value="件"></h:outputText></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900" align="center">
                    	<div class="listScroll" style="height:150px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrc00504.propBpntRrkm.rowClasses}"
							styleClass="meisai_scroll" id="htmlBpntRrkm"
							value="#{pc_Xrc00504.propBpntRrkm.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text15" styleClass="outputText" value="枝番"></h:outputText>
								</f:facet>
								<f:attribute value="40" name="width" />
								<h:outputText styleClass="outputText" id="text16"
									value="#{varlist.edaban}"></h:outputText>
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column2">								
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="請求日" id="text17"></h:outputText>
								</f:facet>
								<f:attribute value="70" name="width" />
								<h:outputText styleClass="outputText" id="text18"
									value="#{varlist.billDate}"></h:outputText>
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="入出庫先種別＿請求" id="text19"></h:outputText>
								</f:facet>
								<f:attribute value="260" name="width" />
								<h:outputText styleClass="outputText" id="text20"
									value="#{varlist.nsksakiSbtBillCd}"></h:outputText>
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="金額内容" id="text21"></h:outputText>
								</f:facet>
								<f:attribute value="400" name="width" />
								<h:outputText styleClass="outputText" id="text22"
									value="#{varlist.kingakName}"></h:outputText>
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="請求金額" id="text23"></h:outputText>
								</f:facet>
								<f:attribute value="70" name="width" />
								<h:outputText styleClass="outputText" id="text24"
									value="#{varlist.kingak}"></h:outputText>
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="35" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Xrc00504.doSelectAction}"></hx:commandExButton>
								<f:attribute value="center" name="align" />
								<f:attribute value="middle" name="valign" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
						</h:dataTable>
						<div>
						</TD>
					</TR>
					<TR>
						<TD><BR></TD>
					</TR>
					<TR>
						<TD width="15%"></TD>
						<TD width="900">
						<TABLE class="table" width="896">
							<TBODY>
								<TR>
									<TH width="150"><h:outputText
										styleClass="outputText" id="lblEdaban"
										value="#{pc_Xrc00504.propEdaban.labelName}"></h:outputText></TH>
                              		<TD width="750"><h:inputText styleClass="inputText"
										id="htmlImpression"
										value="#{pc_Xrc00504.propEdaban.integerValue}"
										style="#{pc_Xrc00504.propEdaban.style}"
										maxlength="#{pc_Xrc00504.propEdaban.maxLength}"
										size="1" 
										disabled="#{pc_Xrc00504.propEdaban.disabled}">
										<f:convertNumber type="number" pattern="####0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText>(新規登録時は空欄)</TD>
								</TR>
								<TR>
									<TH><h:outputText 
									    styleClass="outputText" id="lblBillDate"
										value="#{pc_Xrc00504.propBillDate.labelName}"
										style="#{pc_Xrc00504.propBillDate.labelStyle}"></h:outputText>
									 </TH>
									<TD><h:inputText id="htmlBillDate"
										styleClass="inputText" size="13"
										value="#{pc_Xrc00504.propBillDate.dateValue}"
										disabled="#{pc_Xrc00504.propBillDate.disabled}">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
										promptCharacter="_" /></h:inputText>
									</TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblNsksakiSbtBillCd"
										value="#{pc_Xrc00504.propNsksakiSbtBillCd.labelName}"
										style="#{pc_Xrc00504.propNsksakiSbtBillCd.labelStyle}"></h:outputText>
									</TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlNsksakiSbtBillCd" 
										value="#{pc_Xrc00504.propNsksakiSbtBillCd.stringValue}"
										style="#{pc_Xrc00504.propNsksakiSbtBillCd.style};width:400px"
										disabled="#{pc_Xrc00504.propNsksakiSbtBillCd.disabled}">
										<f:selectItems value="#{pc_Xrc00504.propNsksakiSbtBillCd.list}" />
										</h:selectOneMenu>
									</TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblKingakName"
										value="#{pc_Xrc00504.propKingakName.labelName}"
										style="#{pc_Xrc00504.propKingakName.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlKingakName"
										value="#{pc_Xrc00504.propKingakName.stringValue}"
										style="#{pc_Xrc00504.propKingakName.style}"
										maxlength="#{pc_Xrc00504.propKingakName.maxLength}"
										size="75" 
										disabled="#{pc_Xrc00504.propKingakName.disabled}"></h:inputText></TD>
								</TR>
								<TR>
									<TH><h:outputText
										styleClass="outputText" id="lblKingak"
										value="#{pc_Xrc00504.propKingak.labelName}"
										style="#{pc_Xrc00504.propKingak.labelStyle}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlKingak"
										value="#{pc_Xrc00504.propKingak.integerValue}"
										style="#{pc_Xrc00504.propKingak.style}"
										maxlength="#{pc_Xrc00504.propKingak.maxLength}"
										size="6" 
										disabled="#{pc_Xrc00504.propKingak.disabled}">
										<f:convertNumber type="number" pattern="########0"/>
										<hx:inputHelperAssist errorClass="inputText_Error" 
										promptCharacter="_" /></h:inputText></TD>
								</TR>
						
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<HR class="hr" noshade>
			<TABLE width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0001W}"
							action="#{pc_Xrc00504.doRegisterAction}"
							disabled="#{pc_Xrc00504.propRegisterButton.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Xrc00504.doDeleteAction}"
							disabled="#{pc_Xrc00504.propDeleteButton.disabled}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xrc00504.doClearAction}"
							disabled="#{pc_Xrc00504.propClearButton.disabled}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrc00504.propBpntRrkm.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT LANGUAGE="JavaScript">
changeScrollPosition("scroll","listScroll")
</SCRIPT>
</HTML>

