<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz03501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz03501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz03501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz03501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz03501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz03501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			
			<TABLE width="100%" align="center">
				<TR>
					<TD>
						<TABLE width="780">
							<TR>
								<TD align="right"><hx:commandExButton
													type="submit" value="年度コピー" styleClass="commandExButton"
													id="copy" action="#{pc_Ssz03501.doCopyAction}"></hx:commandExButton>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE width="583">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText
										styleClass="outputText" id="htmlCount"
										value="#{pc_Ssz03501.propCount.stringValue}"></h:outputText><h:outputText
										styleClass="outputText" id="text8" value="件"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE width="560">
							<TR>
								<TD>
								<div id="listScroll" class="listScroll"
									style="height:146px;"
									onscroll="setScrollPosition('scroll',this);"><h:dataTable
									border="0" cellpadding="2" cellspacing="0"
									headerClass="headerClass" footerClass="footerClass"
									rowClasses="#{pc_Ssz03501.propMendan.rowClasses}"
									styleClass="meisai_scroll" id="table1" width="560"
									value="#{pc_Ssz03501.propMendan.list}" var="varlist">
									<h:column id="column1">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="面談年度" id="text2"></h:outputText>
										</f:facet>
										<f:attribute value="72" name="width" />
										<h:outputText styleClass="outputText" id="text6"
											value="#{varlist.mendanNendo}"></h:outputText>
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText id="text1" styleClass="outputText" value="コード"></h:outputText>
										</f:facet>
										<f:attribute value="62" name="width" />
										<h:outputText styleClass="outputText" id="text5"
											value="#{varlist.mendanCd}"></h:outputText>
										<f:attribute value="text-align: left; vertical-align: middle"
											name="style" />
									</h:column>
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="内容" id="text3"></h:outputText>
										</f:facet>
										<f:attribute value="396" name="width" />
										<h:outputText styleClass="outputText" id="text7"
											value="#{varlist.mokuteout.displayValue}"
											title="#{varlist.mokuteout.value}"></h:outputText>
									</h:column>
									<h:column id="column4">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit" value="選択"
											styleClass="commandExButton" id="select"
											action="#{pc_Ssz03501.doButton1Action}"></hx:commandExButton>
										<f:attribute value="30" name="width" />
									</h:column>
								</h:dataTable></div>
								</TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE width="583">
							<TBODY>
								<TR>
									<TD>
									<TABLE border="0" class="table" cellspacing="0" cellpadding="0" width="100%">
										<TBODY>
											<TR>
												<TH class="v_b" width="30%"><h:outputText
													styleClass="outputText" id="lblMendanNendo"
													value="#{pc_Ssz03501.propMendanNendo.labelName}"
													style="#{pc_Ssz03501.propMendanNendo.labelStyle}"></h:outputText></TH>
												<TD class="v_e" width="70%"><h:inputText
													id="htmlMendanNendo"
													value="#{pc_Ssz03501.propMendanNendo.dateValue}"
													style="#{pc_Ssz03501.propMendanNendo.style}"
													maxlength="#{pc_Ssz03501.propMendanNendo.maxLength}"
													styleClass="inputText" size="3">
													<hx:inputHelperAssist errorClass="inputText_Error"
													imeMode="inactive" promptCharacter="_" />
													<f:convertDateTime pattern="yyyy" />
													</h:inputText></TD>
											</TR>
											<TR>
												<TH class="v_a"><h:outputText
													styleClass="outputText" id="lblMendanCd"
													value="#{pc_Ssz03501.propMendanCd.labelName}"
													style="#{pc_Ssz03501.propMendanCd.labelStyle}"></h:outputText></TH>
												<TD class="v_d"><h:inputText
													styleClass="inputText" id="htmlMendanCd"
													value="#{pc_Ssz03501.propMendanCd.stringValue}"
													style="#{pc_Ssz03501.propMendanCd.style}"
													maxlength="#{pc_Ssz03501.propMendanCd.maxLength}" size="3">
												</h:inputText></TD>
											</TR>
											<TR>
												<TH class="v_c"><h:outputText
													styleClass="outputText" id="lblMendanTheme"
													value="#{pc_Ssz03501.propMendanTheme.labelName}"></h:outputText></TH>
												<TD class="v_f"><h:inputTextarea
													styleClass="inputTextarea" id="htmlMendanTheme"
													value="#{pc_Ssz03501.propMendanTheme.stringValue}" cols="50"
													rows="10" style="#{pc_Ssz03501.propMendanTheme.style}">
												</h:inputTextarea></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						<BR>
						<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD align="center" width="100%"><hx:commandExButton type="submit"
										value="確定" styleClass="commandExButton_dat" id="register"
										action="#{pc_Ssz03501.doRegisterAction}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Ssz03501.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Ssz03501.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz03501.propMendan.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

