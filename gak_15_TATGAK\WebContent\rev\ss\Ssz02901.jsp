<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz02901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz02901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz02901.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz02901.doCloseDispAction}"
></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz02901.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz02901.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD></TD>
						<TD width="55%" align="right"><h:outputText styleClass="outputText"
							id="htmlCount"
							value="#{pc_Ssz02901.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text2" value="件"></h:outputText></TD>
						<TD></TD>
					</TR>
					<TR>
						<TD width="20%"></TD>
						<TD align="center" width="55%"><div class="listScroll" style="height:296px;" id="listScroll"  onscroll="setScrollPosition('scroll',this);">
						<h:dataTable border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Ssz02901.propKigyoKeiretu.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz02901.propKigyoKeiretu.list}" var="varlist"
							width="500">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text3" styleClass="outputText" value="区分"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text6"
									value="#{varlist.kigyoKeiretuKbn}"></h:outputText>
								<f:attribute value="102" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text4"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text7"
									value="#{varlist.kigyoKeiretuKbnName}"></h:outputText>
								<f:attribute value="440" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz02901.doSelectAction}"></hx:commandExButton>
								<f:attribute value="24" name="width" />
							</h:column>
						</h:dataTable></div></TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="55%">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%" class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="164"><h:outputText
										styleClass="outputText" id="lblKigyoKeiretuKbn"
										value="#{pc_Ssz02901.propKigyoKeiretuKbn.labelName}"
										style="#{pc_Ssz02901.propKigyoKeiretuKbn.labelStyle}"></h:outputText></TH>
									<TD width="353"><h:inputText styleClass="inputText"
										id="htmlKigyoKeiretuKbn" size="5"
										value="#{pc_Ssz02901.propKigyoKeiretuKbn.stringValue}"
										style="#{pc_Ssz02901.propKigyoKeiretuKbn.style}"
										maxlength="#{pc_Ssz02901.propKigyoKeiretuKbn.maxLength}"></h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="164"><h:outputText
										styleClass="outputText" id="lblKigyoKeiretuKbnName"
										value="#{pc_Ssz02901.propKigyoKeiretuKbnName.labelName}"
										style="#{pc_Ssz02901.propKigyoKeiretuKbnName.labelStyle}"></h:outputText></TH>
									<TD width="353"><h:inputText styleClass="inputText"
										id="htmlKigyoKeiretuKbnName" size="50"
										value="#{pc_Ssz02901.propKigyoKeiretuKbnName.stringValue}"
										style="#{pc_Ssz02901.propKigyoKeiretuKbnName.style}"
										maxlength="#{pc_Ssz02901.propKigyoKeiretuKbnName.maxLength}"></h:inputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR><TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD width="100%"><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="register" action="#{pc_Ssz02901.doRegisterAction}" confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Ssz02901.doDeleteAction}" confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Ssz02901.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz02901.propKigyoKeiretu.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

