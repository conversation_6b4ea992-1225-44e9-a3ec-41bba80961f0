<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01502.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01502.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz01502.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz01502.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz01502.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz01502.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 --> <hx:commandExButton
				type="submit" value="戻る" styleClass="commandExButton"
				id="returnDisp" action="#{pc_Ssz01502.doReturnDispAction}"></hx:commandExButton>
			<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 --><BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="25%"></TD>
						<TD align="left">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="100%">
							<TBODY>
								<TR>
									<TH width="171" class="v_a"><h:outputText
										styleClass="outputText" id="lblFreTsyCd"
										value="#{pc_Ssz01502.propFreTsyCd.labelName}"></h:outputText></TH>
									<TD width="281"><h:outputText styleClass="outputText"
										id="htmlFreTsyCd"
										value="#{pc_Ssz01502.propFreTsyCd.stringValue}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="171" class="v_b"><h:outputText
										styleClass="outputText" id="lblFreTsyName"
										value="#{pc_Ssz01502.propFreTsyName.labelName}"></h:outputText></TH>
									<TD width="281"><h:outputText styleClass="outputText"
										id="htmlFreTsyName"
										value="#{pc_Ssz01502.propFreTsyName.stringValue}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="25%"></TD>
						<TD align="right" width="586"><h:outputText
							styleClass="outputText" id="htmlCount" style="font-size: 8pt"
							value="#{pc_Ssz01502.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text6" value="件"
							style="font-size: 8pt"></h:outputText></TD>
						<TD width="25%"></TD>
					</TR>
					<TR>
						<TD width="25%"></TD>
						<TD align="center">
						<div class="listScroll" style="height:191px;width: 470px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Ssz01502.propKomok.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Ssz01502.propKomok.list}" var="varlist" width="453px">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text7" styleClass="outputText" value="ＮＯ"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text3"
									value="#{varlist.freKomokNO}"></h:outputText>
								<f:attribute value="55" name="width" />
								<f:attribute value="text-align: right" name="style" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="代表" id="text8"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text4"
									value="#{varlist.titleFlg}"></h:outputText>
								<f:attribute value="55" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称" id="text9"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text5"
									value="#{varlist.freKomokName}"></h:outputText>
								<f:attribute value="360" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
								</f:facet>
								<f:attribute value="24" name="width" />
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz01502.doSelectAction}"></hx:commandExButton>
								<f:attribute value="right" name="align" />
							</h:column>
						</h:dataTable></div>
						</TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="25%"></TD>
						<TD align="left" width="50%">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="100%">
							<TBODY>
								<TR>
									<TH class="v_a" width="189"><h:outputText
										styleClass="outputText" id="lblKomokNo"
										style="#{pc_Ssz01502.propKomokNo.labelStyle}"
										value="#{pc_Ssz01502.propKomokNo.labelName}"></h:outputText></TH>
									<TD width="274"><h:inputText styleClass="inputText"
										id="htmlKomokNo" style="#{pc_Ssz01502.propKomokNo.style}"
										maxlength="#{pc_Ssz01502.propKomokNo.maxLength}" size="3"
										value="#{pc_Ssz01502.propKomokNo.stringValue}">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="189"><h:outputText
										styleClass="outputText" id="lblKomokName"
										style="#{pc_Ssz01502.propKomokName.labelStyle}"
										value="#{pc_Ssz01502.propKomokName.labelName}"></h:outputText></TH>
									<TD width="274"><h:inputText styleClass="inputText"
										id="htmlKomokName" style="#{pc_Ssz01502.propKomokName.style}"
										maxlength="#{pc_Ssz01502.propKomokName.maxLength}" size="20"
										value="#{pc_Ssz01502.propKomokName.stringValue}">
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="189"><h:outputText
										styleClass="outputText" id="lblTitle"
										style="#{pc_Ssz01502.propTitle.labelStyle}"value="#{pc_Ssz01502.propTitle.labelName}"></h:outputText></TH>
									<TD width="274"><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlTitle"
										value="#{pc_Ssz01502.propTitle.checked}"></h:selectBooleanCheckbox></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz01502.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Ssz01502.doDeleteAction}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz01502.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz01502.propKomok.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

