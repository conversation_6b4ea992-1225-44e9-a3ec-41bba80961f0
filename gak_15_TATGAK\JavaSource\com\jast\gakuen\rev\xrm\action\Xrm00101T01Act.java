package com.jast.gakuen.rev.xrm.action;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.jast.gakuen.framework.PageCodeBaseEx;
import com.jast.gakuen.framework.batch.BatchConst;
import com.jast.gakuen.framework.batch.BatchLogic;
import com.jast.gakuen.framework.constant.ActionConst;
import com.jast.gakuen.framework.constant.SyMsgConst;
import com.jast.gakuen.framework.db.DbException;
import com.jast.gakuen.framework.property.Checkable;
import com.jast.gakuen.framework.util.CheckComponent;
import com.jast.gakuen.framework.util.MessageList;
import com.jast.gakuen.framework.util.UtilDate;
import com.jast.gakuen.framework.util.UtilIni;
import com.jast.gakuen.framework.util.UtilProperty;
import com.jast.gakuen.framework.util.UtilStr;
import com.jast.gakuen.framework.util.UtilSystem;
import com.jast.gakuen.rev.RevActionBase;
import com.jast.gakuen.rev.co.Cos00401;
import com.jast.gakuen.rev.co.util.UtilCombBoxMaker;
import com.jast.gakuen.rev.co.util.UtilCosOpt;
import com.jast.gakuen.rev.gh.constant.GhConstant;
import com.jast.gakuen.rev.gh.constant.GhMsgConst;
import com.jast.gakuen.rev.gh.db.dao.GhgPayhBunDAO;
import com.jast.gakuen.rev.gh.db.dao.GhgPaywDAO;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhARComparator;
import com.jast.gakuen.rev.gh.db.entity.GhgPayhBunAR;
import com.jast.gakuen.rev.gh.db.entity.GhgPaywAR;
import com.jast.gakuen.rev.gh.util.UtilGhCombBoxMaker;
import com.jast.gakuen.rev.gh.util.UtilGhFormat;
import com.jast.gakuen.rev.gh.util.UtilGhGyomuNendo;
import com.jast.gakuen.rev.gh.util.UtilGhPdf;
import com.jast.gakuen.rev.xrm.Xrm00101T01;
import com.jast.gakuen.rev.xrm.Xrm00101T02;
import com.jast.gakuen.rev.xrm.Xrm00101T03;
import com.jast.gakuen.rev.xrm.action.bean.Xrm00101L01Bean;
import com.jast.gakuen.rev.xrm.constant.XrmConst;
import com.jast.gakuen.rev.xrm.constant.XrmIniConst;
import com.jast.gakuen.rev.xrm.db.dao.XrmGhgPayhDAO;
import com.jast.gakuen.rev.xrm.util.XrmUtilCombBoxMaker;
import com.jast.gakuen.rev.xrm.util.XrmUtilConvert;
import com.jast.gakuen.rev.xrx.db.dao.XrxMeiKanriKmkDAO;
import com.jast.gakuen.rev.xrx.db.entity.XrxMeiKanriKmkAR;

/**
 * �������o�̓A�N�V�����N���X
 * <br>
 * <AUTHOR>
 */
public class Xrm00101T01Act extends RevActionBase {
	
	/** �t�H�[�}�b�g */
	static public final String DATE_PATTERN ="yyyy";
	
	
	//�G���[���b�Z�[�W�p������
	private final String NOCHECK_ERR_MSG = "�[�t��";
	private final String ERR_MSG_TSUSHIN_TEXT_1 = "���l����1�s��32byte�ȓ���";
	private final String ERR_MSG_TSUSHIN_TEXT_2 = "���l����6�s�ȓ���";	
	private final String ERR_MSG_KIGEN_TEXT_3 = "�[�������𒼐ړ��͎��́A�[������/�L������";	
	//�[�������̃G���[���b�Z�[�W�ǉ�
	private final String ERR_MSG_PAYDATE_1 = "�[������";
	private final String ERR_MSG_YUKOULIMIT_1 = "�L������";
	
	/**
	 * ��ʏ���������<br>
	 * ��ʂ̏��������s���B<br>
	 * @param  pagecode 	(PageCodeBaseEx)�y�[�W�R�[�h
	 * @return String		���������������ꍇ"true"���A�ȊO�̏ꍇ��"false"��Ԃ�
	 */
	protected String init(PageCodeBaseEx pagecode) {
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		
		//��ʏ�����
		try {
			//�����l�ݒ�
			setInitItemValue(pc);
			
			if(pc.getPropgyoumList().getList() == null || pc.getPropgyoumList().getList().size()==0){
				pc.getPropgyoumList().addListItemAutoIndent("", "" , "", 1);
				UtilSystem.getDisplayInfo().setDisplayMessage(					
				"���̊Ǘ��}�X�^�ɋƖ��R�[�h�����݂��܂���B<br>�V�X�e���Ǘ��҂ɂ��₢���킹���������B");
				
				return  ActionConst.RET_FALSE;
			}
			
		} catch (Exception e) {
			
			throw new RuntimeException(e);
		}
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * �����l�Z�b�g<br>
	 * �e���ڂɏ����l��ݒ肷��B
	 * <br>
	 * @param  pc 		(Xrm00101T01)�y�[�W�R�[�h
	 * @throws DbException
	 */
	private void setInitItemValue(Xrm00101T01 pc) throws DbException {
		
		UtilCombBoxMaker utilCombBoxMaker = new UtilCombBoxMaker();
		UtilGhCombBoxMaker ghUtilCombBoxMaker = new UtilGhCombBoxMaker();
		
		//�w��N�x�ݒ�
		pc.getPropGhNendo().setDateValue(
				UtilDate.editDateYYYYMMDD(this.getGhNendo(), 1, 1));
		
		//�z���ϔ[�t���ꗗ
		pc.getPropPayhList().setListbean(new Xrm00101L01Bean());
		pc.getPropPayhList().setRows(0);
		pc.getPropPayhList().setList(new ArrayList());
		
		//�w�N�R���{�{�b�N�X�擾
		utilCombBoxMaker.getGakunenCombBox(getDbs(),pc.getPropGakunen());
		
		
		// ��ʁD�ʐM��(�؃C�W�[)��ݒ�
		if(pc.getPropTsushinText().getStringValue() == null){
			pc.getPropTsushinText().setStringValue("");
			
			//�ʐM��
			pc.getPropTsushinText().setDisabled(true);
		}
		
		//���s���t�i���^�u����̑J�ڎ��ɂ͐ݒ肵�Ȃ��j
		Date now = nowDate();
		if(pc.getpropHakkouDate().getDateValue() == null){
			pc.getpropHakkouDate().setDateValue(now);
		}
		
		//�[������
		pc.getPropNonyuDate().setDisabled(true);
		//�L������
		pc.getPropYukoDate().setDisabled(true);
		
		//�����w�ȑg�D�R���{�{�b�N�X�擾
		ghUtilCombBoxMaker.getSzksCombBox(getDbs(),pc.getPropSzkGakka());
		
		//�Ɩ��R�[�h
		pc.getPropgyoumList().setList(new ArrayList());
		pc.getPropgyoumList().setReadonly(true);
		XrmUtilCombBoxMaker box = new XrmUtilCombBoxMaker();		
		box.getGyoumListCombBox(getDbs(),pc.getPropgyoumList(),0);
		pc.getPropgyoumList().setValue("");
		
		
		//�[�t�����
		pc.getPropChkListSeika().setChecked(true);
		pc.getPropChkListToitu().setChecked(true);
		pc.getPropChkListKamokutori().setChecked(true);
		
		//�A�w���
		utilCombBoxMaker.getSyugakSbtCombBox(getDbs(),pc.getPropSyugakSbt());
		
		//�ٓ����  
		UtilCombBoxMaker comb = new UtilCombBoxMaker();
		comb.getIdoSyutgakSbt2CombBox(getDbs(), pc.getPropIdoSbtList());
		
		//�I�v�V�����e�[�u�����O����͒l���擾
		//(�f�[�^�����݂��Ȃ��ꍇ�͏����l��ݒ�)
		loadDefaultItemValueOption(pc);
		//�I�v�V�����e�[�u���Ƀf�[�^�����݂��Ȃ��ꍇ
		if(pc.getPropOutPutKbn().getValue()==null){
			//�o�͋敪
			pc.getPropOutPutKbn().setValue("1");		
		}
		
		//�t�H�[�J�X�Z�b�g
		UtilSystem.getDisplayInfo().setTargetFocusId(
				pc.getPropGhNendo().getId());
		
	}
	
	/**
	 * �A�N�V����(�I��)<br>
	 * ���͂��ꂽ�w��N�x�ɔz������Ă���[�t���̈ꗗ��\������B
	 * <br>
	 * @param   pagecode 	(PageCodeBaseEx)�y�[�W�R�[�h
	 * @return  String		���������������ꍇ"true"���A�ȊO�̏ꍇ��"false"��Ԃ�
	 * @throws  DbException
	 */
	protected String search(PageCodeBaseEx pagecode) throws DbException {
		
		try {
			
			Xrm00101T01 pc = (Xrm00101T01)pagecode;
			
			//���̓`�F�b�N
			MessageList message = checkNendoErr(pc);
			
			if (message.size() != 0) {
				//���̓`�F�b�N�ŃG���[���L��ꍇ
				UtilSystem.getDisplayInfo()
				.setDisplayMessage(message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			//�z���ϔ[�t���ꗗ�̐ݒ�
			if (!setPayhList(pc)) {
				return ActionConst.RET_FALSE;
			}
			
			
			//���b�N���ڐݒ�
			//�w��N�x
			pc.getPropGhNendo().setDisabled(true);
			pc.getPropChkListSeika().setDisabled(true);
			pc.getPropChkListKamokutori().setDisabled(true);
			pc.getPropChkListToitu().setDisabled(true);
			pc.getPropgyoumList().setDisabled(true);
			
			//�e�{�^��
			pc.getPropActiveControlSearch().setIntegerValue(
					new Integer(GhConstant.GH_CTR_DISABLED));
			
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * ���̓`�F�b�N(�w��N�x)<br>
	 * �w��N�x�̓��̓`�F�b�N���s���B
	 * <br>
	 * @param  pc		 (Xrm00101T01)�y�[�W�R�[�h
	 * @return chkMsg	 (MessageList)���b�Z�[�W���X�g
	 * @throws DbException
	 */
	private MessageList checkNendoErr(Xrm00101T01 pc) throws DbException {
		
		CheckComponent cc = new CheckComponent();
		//�w��N�x
		cc.addComponent(pc.getPropGhNendo());
		MessageList chkMsg =  cc.check();
		//�[�t�����
		if(!pc.getPropChkListSeika().isChecked()&&
				!pc.getPropChkListKamokutori().isChecked()&&
				!pc.getPropChkListToitu().isChecked()){
			chkMsg.add(UtilProperty.getMsgString(
					SyMsgConst.SY_MSG_0006E,
					pc.getPropChkListLbl().getName()));
			
		}
		return chkMsg;
	}
	
	/**
	 * �z���ϔ[�t���ꗗ���擾����<br>
	 * ���͂��ꂽ���Ԃɔz������Ă���[�t���z�������擾����B
	 * <br>
	 * @param  pc         (ghe00702T01)�y�[�W�R�[�h 
	 * @return            (boolean) true�F���� false�F�ُ� 
	 * @throws Exception
	 */
	private boolean setPayhList(Xrm00101T01 pc) throws DbException {
		
		try {
			
			SimpleDateFormat sdf = new SimpleDateFormat(GhConstant.GH_FORMAT_YYYY);
			//�����N�x
			int intGhYear = 0;
			if (pc.getPropGhNendo().getDateValue() != null) {
				intGhYear = Integer.parseInt(sdf.format(pc.getPropGhNendo().getDateValue()));
			}
			//�[�t����ʎ擾
			ArrayList paysyu = payKubunget(pc);
			//�w��N�x
			int ghNend =Integer.parseInt(sdf.format(pc.getPropGhNendo().getDateValue()));
			
			//�z���ϔ[�t���ꗗ�擾
			//�[�t���z���ꗗDAO�쐬
			XrmGhgPayhDAO xrmGhgPayhDAO = (XrmGhgPayhDAO)getDbs().getDao(XrmGhgPayhDAO.class);
			List payhList = xrmGhgPayhDAO.findByGyomuCdPayCdJoinGhgPayh(ghNend,pc.getPropgyoumList().getStringValue(),paysyu);	
			
			//�[�t���R�[�h���Ƀ\�[�g
			GhgPayhARComparator sort = new  GhgPayhARComparator();
			sort.asc(GhgPayhARComparator.PAY_CD);
			Collections.sort(payhList, sort);
			
			if (payhList.size() == 0) {
				//�Y���f�[�^�����݂��Ȃ��ꍇ�G���[
				MessageList message = new MessageList();
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0032E));
				//�w��N�x�Ƀt�H�[�J�X���Z�b�g
				UtilSystem.getDisplayInfo().setTargetFocusId(
						pc.getPropGhNendo().getId());
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return false;
			}
			
			if (payhList.size() > 0) {
				//�f�[�^�����̃G���[�`�F�b�N
				//ini�t�@�C�����Max�����擾
				int WarningCount =
					Integer.parseInt(UtilIni.getParameter(
							GhConstant.GH_SECTION_MAX, 
							GhConstant.GH_KEY_MAXSEARCH1));
				//ini�t�@�C�����Max�����擾
				int ErrorCount =
					Integer.parseInt(UtilIni.getParameter(
							GhConstant.GH_SECTION_MAX, 
							GhConstant.GH_KEY_MAXSEARCH2));
				
				if (payhList.size() > ErrorCount) {
					//�������ʂ��G���[�����ȏ�̏ꍇ
					UtilSystem.getDisplayInfo().setDisplayMessage(
							UtilProperty.getMsgString(
									SyMsgConst.SY_MSG_0034E,String.valueOf(
											ErrorCount)));
					return false;
					
				} else if (payhList.size() > WarningCount  &&
						pc.getPropExecutableSearch()
						.getIntegerValue().intValue() == 0) {
					//�������ʂ��G���[�����������x�������ȏ�̏ꍇ
					UtilSystem.getDisplayInfo().setConfirmMessage(
							UtilProperty.getMsgString(
									SyMsgConst.SY_MSG_0016W,
									String.valueOf(payhList.size())));
					//��x�����`�F�b�N�ׂ̈̃{�^���̏��������s�Ȃ�
					pc.getPropExecutableSearch()
					.setIntegerValue(new Integer(1));
					return false;
					
				} else {
					//��x�����`�F�b�N�ׂ̈̃{�^���̏��������s�Ȃ�
					pc.getPropExecutableSearch()
					.setIntegerValue(new Integer(1));
				}
				
				//���X�g�Ƀf�[�^���Z�b�g
				ArrayList payArrList = createPayhList(payhList);
				pc.getPropPayhList().setList(payArrList);
				UtilGhFormat.setRows(
						pc.getPropPayhList(),
						payArrList);
			}
			
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		return true;
	}
	
	/**
	 * �z���ϔ[�t���ꗗ���X�g�쐬<br>
	 * �z��������ʕ\���p�ɕҏW����B
	 * <br>
	 * @param  payhList			(List)�z���ϔ[�t���ꗗ
	 * @return payhArrayList	(ArrayList)�z���ϔ[�t�����
	 * @throws DbException
	 */
	private ArrayList createPayhList(List payhList) throws DbException {
		
		//�z���ϔ[�t���ꗗ���X�g
		ArrayList payhArrayList = new ArrayList();
		
		//��ʕ\���pBean�ɒl��ݒ肷��
		for (int i = 0; i < payhList.size(); i++) {
			GhgPayhAR ghgPayhAR =(GhgPayhAR)payhList.get(i);
			//�[�t���z�����[���擾
			GhgPayhBunDAO ghgPayhDAO = (GhgPayhBunDAO)getDbs().getDao(GhgPayhBunDAO.class);
			List ghgPayhBunARList = ghgPayhDAO.findByPayh(ghgPayhAR.getNendo(),
					ghgPayhAR.getPayCd(),
					ghgPayhAR.getPatternCd(),
					ghgPayhAR.getBunnoKbnCd());
			
			
			//���[�񐔌J��Ԃ�
			for(int f=0;f<ghgPayhBunARList.size();f++){
				GhgPayhBunAR ghgPayhBunAR =(GhgPayhBunAR)ghgPayhBunARList.get(f);
				
				String payCd = "";
				String patternCd = "";
				int bunnoKbnCd = 0;
				int bunkatsuNo = 0;
				String payLimit = "";
				String payName = "";
				if (ghgPayhAR != null) {
					payCd = ghgPayhAR.getPayCd();
					patternCd = ghgPayhAR.getPatternCd();
					bunnoKbnCd = ghgPayhAR.getBunnoKbnCd();
					bunkatsuNo = ghgPayhBunAR.getBunkatsuNo();
					Date payLimitDate = ghgPayhBunAR.getPayLimit();
					if (payLimitDate != null) {
						payLimit = UtilDate.editDate(payLimitDate, 2);
					}
					payName = ghgPayhAR.getPayName();
					
				}
				
				Xrm00101L01Bean bean = new Xrm00101L01Bean();
				
				//�N�x
				bean.setNendo("");
				//�[�t���R�[�h
				bean.setPayCd(payCd);
				//�p�^�[���R�[�h
				bean.setPatternCd(patternCd);
				//���[�敪�R�[�h
				bean.setBunnoKbnCd(String.valueOf(bunnoKbnCd));
				//�[�t������
				bean.setPayName(payName);
				//����NO
				bean.setBunkatsuNo(String.valueOf(bunkatsuNo));
				//�[�t����
				bean.setPayLimit(payLimit);
				//�o�͓�
				bean.setOutputDate("");
				
				payhArrayList.add(bean);
			}
		}
		
		return payhArrayList;
	}
	
	/**
	 * �A�N�V����(����)<br>
	 * ���b�N���ڂ̉������s���B
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)�y�[�W�R�[�h
	 * @return String		���������������ꍇ"true"���A�ȊO�̏ꍇ��"false"��Ԃ�
	 * @throws DbException
	 */
	protected String unselect(PageCodeBaseEx pagecode) throws DbException {
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		
		//���X�g������
		pc.getPropPayhList().setListbean(new Xrm00101L01Bean());
		pc.getPropPayhList().setList(new ArrayList());
		pc.getPropPayhList().setRows(0);
		
		//��x�����`�F�b�N
		pc.getPropExecutableSearch().setIntegerValue(new Integer(0));
		
		
		//�R���g���[������
		pc.getPropGhNendo().setDisabled(false);
		pc.getPropChkListSeika().setDisabled(false);
		pc.getPropChkListKamokutori().setDisabled(false);
		pc.getPropChkListToitu().setDisabled(false);
		pc.getPropgyoumList().setDisabled(false);
		//�t�H�[�J�X�Z�b�g
		UtilSystem.getDisplayInfo().setTargetFocusId(
				pc.getPropGhNendo().getId());
		
		//�e�{�^��
		pc.getPropActiveControlSearch().setIntegerValue(
				new Integer(GhConstant.GH_CTR_ENABLED));
		
		return ActionConst.RET_TRUE;
	}
	
	/**
	 * �A�N�V����(���s)<br>
	 * ���������쐬����B
	 * <br>
	 * @param  pagecode		(PageCodeBaseEx)�y�[�W�R�[�h
	 * @return String		���������������ꍇ"true"���A�ȊO�̏ꍇ��"false"��Ԃ�
	 */
	protected String exec(PageCodeBaseEx pagecode) {
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		return execXrm00101BAT01(pc, ActionConst.ACTION_EXEC);
	}
	
	
	/**
	 * �o�b�`���s<br>
	 * �o�b�`���������s����B
	 * <br>
	 * @param  pc	  		(Xrm00101T01)�y�[�W�R�[�h
	 * @param  action 		(String)�A�N�V�����敪
	 * @return String		���������������ꍇ"true"���A�ȊO�̏ꍇ��"false"��Ԃ�
	 */
	private String execXrm00101BAT01(Xrm00101T01 pc, String action) {
		
		try {
			
			ArrayList outPayhList = new ArrayList();
			MessageList message = new MessageList();
			Xrm00101BAT01 bat;
			CheckComponent cc = new CheckComponent();
			
			//���s���t
			cc.addComponent(pc.getpropHakkouDate());
			message =  cc.check();
			
			//�ʐM��(�؃C�W�[)�����s�R�[�h���L�[�ɕ���
			String[] aryTsushinTextChk =getpayeasyTexSubString( pc.getPropTsushinText().getStringValue());
			
			int lineByte = 0; // 1�s�̃o�C�g��
			
			for (int j = 0; j < aryTsushinTextChk.length; j++) {
				
				//1�s�̃o�C�g�����擾
				lineByte = aryTsushinTextChk[j].getBytes().length;
				
				//�ʐM��(�؃C�W�[) 1�s�̍ő啶����32byte�𒴂��Ă���ꍇ�G���[				
				if (lineByte > 32) {
					
					//�G���[���b�Z�[�W�ɒǉ�
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0015E, ERR_MSG_TSUSHIN_TEXT_1));
					
				}
				
			}
			
			//�ʐM��(�؃C�W�[) �ő�s����6�s�𒴂��Ă���ꍇ�G���[
			if (aryTsushinTextChk.length > 6) {
				
				//�G���[���b�Z�[�W�ɒǉ�
				message.add(UtilProperty.getMsgString(
						SyMsgConst.SY_MSG_0015E, ERR_MSG_TSUSHIN_TEXT_2));
				
			}
			//11/13�ǉ��F�������ړ��͎��A�w�肵�ĂȂ��ꍇ�G���[
			if(pc.getPropKigenkbn().isChecked()){
				//�L�������A�[�����������݂��Ă��Ȃ��ꍇ
				if(pc.getPropNonyuDate().getDateValue()==null||pc.getPropYukoDate().getDateValue()==null){
					//�G���[���b�Z�[�W�ɒǉ�
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0008E, ERR_MSG_KIGEN_TEXT_3));
				}else{
					//���t���փ`�F�b�N
					String addMonth = "";
					String[] errMsg = new String[2];
					//EUC���̎�ʍ��ڂ���w��̔[�������͈͂��擾
					XrxMeiKanriKmkDAO xrxMeiKanriKmkDAO = (XrxMeiKanriKmkDAO)getDbs().getDao(XrxMeiKanriKmkDAO.class);
					List list = xrxMeiKanriKmkDAO.findBySbtGyomu(XrmConst.M_SBT_PAY_LIMIT, XrmConst.M_GYOMU_CD);
					if (list.size() != 0) {
						XrxMeiKanriKmkAR xrxMeiKanriKmkAR = (XrxMeiKanriKmkAR) list.get(0);
						addMonth = xrxMeiKanriKmkAR.getKmkName1();
					}			
					//�G���[���b�Z�[�W�ɒǉ�
					errMsg[0] = ERR_MSG_PAYDATE_1 + "+" + addMonth + "����";
					errMsg[1] = ERR_MSG_YUKOULIMIT_1;
					
					Calendar cal = Calendar.getInstance();
					
					cal.setTime(pc.getPropNonyuDate().getDateValue());
					
					cal.add(Calendar.MONTH, Integer.parseInt(addMonth));
					
					cal.getTime();
					
					Date cmpDate1 = cal.getTime();
					Date cmpDate2 = pc.getPropYukoDate().getDateValue();
					
					//�[������ + �w��̔[�������͈� < �L�������ȊO�̏ꍇ�G���[
					if (cmpDate1.compareTo(cmpDate2) <= 0) {
						
						message.add(UtilProperty.getMsgString(
								GhMsgConst.GH_MSG_0002E, errMsg));
					}
					//�[���������L�������̏ꍇ�G���[
					Date cmpDatenonyu= pc.getPropNonyuDate().getDateValue();
					Date cmpDateyuko = pc.getPropYukoDate().getDateValue();
					if(cmpDateyuko.compareTo(cmpDatenonyu) < 0){
						//�G���[���b�Z�[�W�ɒǉ�
						errMsg = new String[2];
						errMsg[0] =ERR_MSG_YUKOULIMIT_1;
						errMsg[1] =  ERR_MSG_PAYDATE_1;
						message.add(UtilProperty.getMsgString(
								GhMsgConst.GH_MSG_0002E, errMsg));

					}

				}
			}
			
			
			
			//�ٓ���ʂ����I���ŁA�ٓ����t�ɓ��͂�����ꍇ�̓G���[
			if (Checkable.NO_SELECT_VALUE.equals(pc.getPropIdoSbtList().getStringValue())) {
				if(pc.getPropIdoDateStart().getDateValue() != null 
						|| pc.getPropIdoDateEnd().getDateValue() != null){
					//�t�H�[�J�X���ٓ���ʂɃZ�b�g
					UtilSystem.getDisplayInfo().setTargetFocusId(
							pc.getPropIdoSbtList().getId());
					//���b�Z�[�W�ǉ�
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0006E
							, "�ٓ����t���w�肷��ꍇ�́A�ٓ��o�w���"));
				}
			}
			
			//���t���󔒂Ŗ����ꍇ�͑召�`�F�b�N
			if ((pc.getPropIdoDateStart().getDateValue() != null)
					&& pc.getPropIdoDateEnd().getDateValue() != null) {
				//�ٓ��J�n��<=�ٓ��I�����`�F�b�N
				int chkDate = UtilDate.compDateYYYYMMDD(
						UtilDate.editDate(pc.getPropIdoDateStart().getDateValue(), 1), 
						UtilDate.editDate(pc.getPropIdoDateEnd().getDateValue(), 1));
				
				if (chkDate == 2) {
					//�ٓ��J�n�����ٓ��I�����ꍇ�̓G���[
					String[] msgDate = new String[2];
					msgDate[0] = pc.getPropIdoDateStart().getName();
					msgDate[1] = pc.getPropIdoDateEnd().getName();
					//�t�H�[�J�X���ٓ��J�n���ɃZ�b�g
					UtilSystem.getDisplayInfo().setTargetFocusId(
							pc.getPropIdoDateStart().getId());
					//���b�Z�[�W�ǉ�
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0023E
							, msgDate));
				}
			}
			if (message.size() != 0) {
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			
			//�[�t���I���`�F�b�N
			List payList = createPayList(pc.getPropPayhList().getList());
			if ((payList == null || payList.size() == 0) && pc.getPropPayOutType().getValue().toString().equals("0")) {
				//���X�g����ŁA�[�t���o�͏����w�肪�u�ꗗ���I���v�̏ꍇ
				//1�����I������Ă��Ȃ��ꍇ�G���[
				message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0006E,NOCHECK_ERR_MSG));
				UtilSystem.getDisplayInfo().setDisplayMessage(message.getErrMsg());
				return ActionConst.RET_FALSE;
			}else{
				int cnt = 0;
				if (payList != null){
					//�[�t���̑I�𐔃`�F�b�N(1000���ȏ�̑I���̓G���[�Ƃ���)
					for (Iterator ite = payList.iterator(); ite.hasNext();) {
						Xrm00101L01Bean paywBean = (Xrm00101L01Bean) ite.next();
						if(paywBean.isPayChecked()){
							cnt++;
						}
						if(cnt > 999){
							//1000���ȏ�Ȃ̂ŃG���[
							message.add(UtilProperty.getMsgString(SyMsgConst.SY_MSG_0013E, "�[�t����1000���ȏ�"));
							break;
						}
					}
				}
			}
			
			//�ٓ���ʂ����I���ŁA�ٓ����t�ɓ��͂�����ꍇ�̓G���[
			if (Checkable.NO_SELECT_VALUE.equals(pc.getPropIdoSbtList().getStringValue())) {
				if(pc.getPropIdoDateStart().getDateValue() != null 
						|| pc.getPropIdoDateEnd().getDateValue() != null){
					//�t�H�[�J�X���ٓ���ʂɃZ�b�g
					UtilSystem.getDisplayInfo().setTargetFocusId(
							pc.getPropIdoSbtList().getId());
					//���b�Z�[�W�ǉ�
					message.add(UtilProperty.getMsgString(
							SyMsgConst.SY_MSG_0006E
							, "�ٓ����t���w�肷��ꍇ�́A�ٓ��o�w���"));
				}
			}
			
			if (message.size() > 0) {
				//�G���[�����݂���ꍇ
				//�G���[���b�Z�[�W�o��
				UtilSystem.getDisplayInfo().setDisplayMessage(
						message.getErrMsg());
				return ActionConst.RET_FALSE;
			}
			
			
			//�o�b�`�̃R���X�g���Ăяo��
			bat = new Xrm00101BAT01(BatchConst.BATKBN_P, true);
			
			//�I��[�t���Ώۂ̊Ǘ�NO�擾
			Map kanriList = getGakuseiInfo(pc,payList);
			
			//�敪
			String kbn = "PAY";
			
			//���[�^�C�g��
			//���[�^�C�g���e�[�u�����f�[�^�擾
			UtilGhPdf ghUtilPdf = new UtilGhPdf();
			String title = ghUtilPdf.loadDefaultItemValuePdfTitle(
					this.getDbs(), Xrm00101BAT01.PDF_FILEID_CHKLIST);
			
			//�o�͏������擾
			//���s���t(java.sql.date)
			java.sql.Date hakkoDate = UtilDate.cnvSqlDate(pc.getpropHakkouDate().getDateValue());
			
			//�ʐM��
			//�ʐM�敪�`�F�b�N
			boolean tsusinKbn = pc.getPropTushinKbn().isChecked();
			String tsusinTxt = "";
			
			
			//�ʐM�����ړ��͎�
			String[] aryTsushinText=null;
			if(pc.getPropTushinKbn().isChecked()){
				//�ʐM��(�؃C�W�[)���擾
				aryTsushinText =getpayeasyTexSubString(pc.getPropTsushinText().getStringValue());
			}else{
				//ini�t�@�C���̊��蕶�����g�p����ꍇ
				//OS�ˑ��̉��s�R�[�h���擾
				String crlf = System.getProperty("line.separator");
				String strTsushinText = XrmUtilConvert.toNoNullStr(UtilIni.getProductParameter(XrmIniConst.XRM
						, XrmIniConst.SEIKYUUOUT[0]
												 , XrmIniConst.SEIKYUUOUT[1]));
				
				// INI�t�@�C���̉��s�R�[�h(\n)��OS�ˑ��̉��s�R�[�h�ɂĒu��
				strTsushinText = strTsushinText.replaceAll("\\\\n", crlf);
				
				aryTsushinText = strTsushinText.split(crlf);
			}
			//�o�͋敪
			String syutKbn = pc.getPropOutPutKbn().getStringValue();
			
			
			//�w��N�x
			SimpleDateFormat sdf = new SimpleDateFormat(GhConstant.GH_FORMAT_YYYY);
			int ghNendo = Integer.parseInt(sdf.format(pc.getPropGhNendo().getDateValue()));
			
			//�w�N
			String strGakunen =	UtilStr.cnvNull(pc.getPropGakunen().getStringValue());
			
			if ((strGakunen.equals("")) ||
					(strGakunen.equals(Checkable.NO_SELECT_VALUE))) {
				strGakunen = "0";
			} 
			
			//�A�w���
			String syugaksyubetu = 
				UtilStr.cnvNull(pc.getPropSyugakSbt().getStringValue());
			if (syugaksyubetu.equals(Checkable.NO_SELECT_VALUE)) {
				syugaksyubetu = "";
			}
			
			//�����w�ȑg�D
			String szkGakka =
				UtilStr.cnvNull((pc.getPropSzkGakka().getStringValue()));
			if (szkGakka.equals(Checkable.NO_SELECT_VALUE)) {
				szkGakka = "";
			}
			
			//�Ɩ��R�[�h
			String gyomcd = pc.getPropgyoumList().getStringValue();
			if(gyomcd.equals(Checkable.NO_SELECT_VALUE)){
				gyomcd="";
			}
			
			//�ٓ����
			String idoSbt = pc.getPropIdoSbtList().getStringValue();
			if (idoSbt.equals(Checkable.NO_SELECT_VALUE)) {
				idoSbt = "";
			}
			//�ٓ��J�n���̓��͒l���擾
			java.sql.Date idoDateFrom = UtilDate.cnvSqlDate(pc.getPropIdoDateStart().getDateValue());
			java.sql.Date idoDateTo =UtilDate.cnvSqlDate(pc.getPropIdoDateEnd().getDateValue());
			
			//11/13�ǉ��F���ړ��͑I�����A�[�������ƗL���������擾(��I������null�Ńo�b�`��)
			java.sql.Date nonyuKigen =null;
			java.sql.Date yukoKigen = null;
			if(pc.getPropKigenkbn().isChecked()){
				nonyuKigen= UtilDate.cnvSqlDate(pc.getPropNonyuDate().getDateValue());
				yukoKigen= UtilDate.cnvSqlDate(pc.getPropYukoDate().getDateValue());
			}
			//�o�b�`�ɏo�͏�����ݒ�
			bat.setOutputConditions(kbn,
					title,
					hakkoDate,
					tsusinKbn,
					aryTsushinText,
					ghNendo,
					syutKbn,
					strGakunen,
					syugaksyubetu,
					szkGakka,
					idoSbt,
					idoDateFrom,
					idoDateTo,
					kanriList,
					payList,
					gyomcd,
					nonyuKigen,
					yukoKigen);
			
			//�I�v�V�����e�[�u���Ƀf�[�^�ǉ�
			saveDefaultItemValue(pc);
			
			if (BatchLogic.startBatch(bat)) {
				//�o�b�`���s�m�F��ʌĂяo�� (��ʃI�����[�h���ɐV�K�E�B���h�E�ŃI�[�v��)
				Cos00401.open(bat);
				
			} else {
				//�u�������s���ł��B�ڍׂ̓o�b�`�ڍ׏����Q�Ƃ��ĉ������B�v
				UtilSystem.getDisplayInfo().setPopupMessage(
						UtilProperty.getMsgString(SyMsgConst.SY_MSG_0010I));
			}
			
			return ActionConst.RET_TRUE;
			
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
	
	/**
	 * �o�b�`�����Ώ۔z�����X�g�쐬<br>
	 * �����Ώۃ`�F�b�N�����Ă���z�������o�b�`�����̃��X�g�ɐݒ肷��B
	 * <br>
	 * @param  payList			(ArrayList)�ꗗ�ɕ\�����ꂢ�Ă���z�����
	 * @return list			(ArrayList)�����Ώۂ̔z�����
	 * @throws DbException
	 */
	private ArrayList createPayList(ArrayList payList) {
		ArrayList list = new ArrayList();
		
		for (Iterator it = payList.iterator(); it.hasNext();) {
			Xrm00101L01Bean payBean = (Xrm00101L01Bean) it.next();
			if (payBean.isPayChecked()) {
				list.add(payBean);
			}
		}
		
		if (list.size() == 0) {
			list = null;
		}
		
		return list;
	}
	
	
	/**
	 * �w��N�x�擾����<br>
	 * �w��Ɩ��N�x��int�^�Ŏ擾����B
	 * <br>
	 * @return gyomunendo		(int)�Ɩ��N�x
	 * @throws DbException
	 */
	protected int getGhNendo() throws DbException {
		
		UtilGhGyomuNendo ghUtilGyomuNendo = new UtilGhGyomuNendo();
		int gyomunendo = ghUtilGyomuNendo.getGyomuNendoInt(this.getDbs());
		
		return gyomunendo;
	}
	
	
	/**
	 * �O����͒l�����[�h<br>
	 * �O����͂����l���I�v�V�����e�[�u������擾����ʂɔ��f����B<br>
	 * �I�v�V�����e�[�u���Ƀf�[�^�����݂��Ȃ��ꍇ�́A�����l��ݒ肷��B<br>
	 * (���[�^�C�g���ɂ��Ă͒��[�^�C�g����薼�̂��擾���ݒ肷��)
	 * <br>
	 * @param  pc 	(Xrm00101T01)�y�[�W�R�[�h
	 * @throws DbException
	 */
	private void loadDefaultItemValueOption(Xrm00101T01 pc)
	throws DbException {
		
		UtilCosOpt utilOpt = new UtilCosOpt(
				getDbs(),
				UtilSystem.getMySystemData().getLoginID(),
				UtilSystem.getFuncIdFromFormId(pc.getFormId()),
				Integer.parseInt(
						pc.getFormId().substring(pc.getFormId().length() - 1))
		);
		//�f�[�^���ǂ݂���DAO�̃��R�[�h�L���b�V���Ɋi�[
		utilOpt.preLoad();
		
		
		//�o�͋敪
		pc.getPropOutPutKbn().setValue(utilOpt.getValue(pc.getPropOutPutKbn().getId()));
		
		
	}
	
	/**
	 * ������͒l���Z�[�u<br>
	 * ������͒l���I�v�V�����e�[�u���ɕۑ�����B
	 * <br>
	 * @param  pc 		(Xrm00101T01)�y�[�W�R�[�h
	 * @throws DbException
	 */
	private void saveDefaultItemValue(Xrm00101T01 pc)
	throws DbException {
		
		UtilCosOpt utilOpt = new UtilCosOpt(
				getDbs(),
				UtilSystem.getMySystemData().getLoginID(),
				UtilSystem.getFuncIdFromFormId(pc.getFormId()),
				Integer.parseInt(
						pc.getFormId().substring(pc.getFormId().length() - 1))
		);
		//�f�[�^���ǂ݂���DAO�̃��R�[�h�L���b�V���Ɋi�[
		utilOpt.preLoad();
		
		//@@@@@ (F-UK-345-00) JAST s.hase 2006/09/13 START
		
		//�w�N
		utilOpt.setValue(pc.getPropGakunen().getId(),
				pc.getPropGakunen().getStringValue());
		//�����w�ȑg�D
		utilOpt.setValue(pc.getPropSzkGakka().getId(),
				pc.getPropSzkGakka().getStringValue());
		//���s���t
		String hakko ="";
		Date hakkoDate = pc.getpropHakkouDate().getDateValue();
		if(hakkoDate!=null){
			hakko=UtilDate.editDate(hakkoDate,1);
		}
		utilOpt.setValue(pc.getpropHakkouDate().getId(),hakko);
		//�o�͋敪
		utilOpt.setValue(pc.getPropOutPutKbn().getId(),pc.getPropOutPutKbn().getStringValue());
		
		//�ٓ��o�w��� 
		utilOpt.setValue(pc.getPropIdoSbtList().getId(),
				pc.getPropIdoSbtList().getStringValue());
		//�ٓ����t�E�J�n
		String idoDateFrom = "";
		Date dateIdoDateSta = pc.getPropIdoDateStart().getDateValue();
		if (dateIdoDateSta != null) {
			idoDateFrom = UtilDate.editDate(dateIdoDateSta,1);
		}
		utilOpt.setValue(pc.getPropIdoDateStart().getId(), idoDateFrom);
		//�ٓ����t�E�I��
		String idoDateTo = "";
		Date dateIdoDateEnd = pc.getPropIdoDateEnd().getDateValue();
		if (dateIdoDateEnd != null) {
			idoDateTo = UtilDate.editDate(dateIdoDateEnd,1);
		}
		utilOpt.setValue(pc.getPropIdoDateEnd().getId(), idoDateTo);
		
		//�[�t���o�͏����w��
		utilOpt.setValue(pc.getPropPayOutType().getId(),
				pc.getPropPayOutType().getStringValue());
	}
	/**
	 * �������o�͂��s���w���̊Ǘ�NO���X�g�擾
	 * @param pc
	 * @return
	 * @throws DbException
	 */
	protected Map getGakuseiInfo(Xrm00101T01 pc,List payList) throws DbException{
		long[] kanriNo =  new long[]{};
		ArrayList kanList = new ArrayList();
		
		List list=new ArrayList();
		//�n�b�V���}�b�v�쐬
		Map kanriMap = new HashMap();
		
		for(int i=0;i<payList.size();i++){
			Xrm00101L01Bean bean = new Xrm00101L01Bean();
			//�`�F�b�N���s�����z�������擾
			bean = (Xrm00101L01Bean)payList.get(i);
			java.sql.Date gnen = UtilDate.cnvSqlDate(pc.getPropGhNendo().getDateValue());
			String ne =convertDatetoString(gnen);
			int nend = Integer.parseInt(ne);
			
			
			//�z����񂩂犄�����[���擾
			GhgPaywDAO ghgPaywDAO = (GhgPaywDAO)getDbs().getDao(GhgPaywDAO.class);
			List ghgPaywARList = ghgPaywDAO.findByGhgPayh(nend,
					bean.getPayCd(),
					bean.getPatternCd(),
					Integer.parseInt(bean.getBunnoKbnCd()));
			
			for(int f=0;f<ghgPaywARList.size();f++){
				GhgPaywAR ghgPaywAR = (GhgPaywAR)ghgPaywARList.get(f);
				//���[�ς݂͏��O
				if(ghgPaywAR.isPayEnd()){
					continue;
				}
				
				//�Ǘ�NO���擾
				long kanri= ghgPaywAR.getKanriNo();
				String kanriN=Long.toString(kanri);
				
				//����Ǘ�NO�͎擾���Ȃ�
				if(kanriMap.containsKey(kanriN)){
					continue;
				}
				//�n�b�V���}�b�v�ɊǗ�NO���L�[�Ƃ��Ċi�[
				kanriMap.put(kanriN,kanriN);
				
				list.add(kanriN);
				
			}
		}			
		return kanriMap;
	}
	
	
	/**
	 * �^�u�J��(�[�t���w���w����) <br>
	 * �[�t���w���w��^�u��ʂ֑J�ڂ���
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)�y�[�W�R�[�h
	 * @return String		���������������ꍇ"true"���A�ȊO�̏ꍇ��"false"��Ԃ�
	 * @throws DbException
	 * @throws Exception
	 */
	protected String payGakTab(PageCodeBaseEx pagecode) throws DbException,Exception {
		
		//���s���t�A�ʐM�敪�A�o�͋敪���R��ʂŋ��L����B
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		
		Xrm00101T02 nextPage = (Xrm00101T02)UtilSystem.getManagedBean(Xrm00101T02.class);
		
		nextPage.setPropHakkouDate(pc.getpropHakkouDate());
		nextPage.setPropTsushinText(pc.getPropTsushinText());
		nextPage.setPropTushinKbn(pc.getPropTushinKbn());
		nextPage.setPropOutPutKbn(pc.getPropOutPutKbn());
		nextPage.setPropNonyuDate(pc.getPropNonyuDate());
		nextPage.setPropYukoDate(pc.getPropYukoDate());
		nextPage.setPropKigenkbn(pc.getPropKigenkbn());
		
		return Xrm00101T02.FORMID;
	}
	
	/**
	 * �^�u�J��(�w���w����) <br>
	 * �w���w��^�u��ʂ֑J�ڂ���
	 * <br>
	 * @param  pagecode 	(PageCodeBaseEx)�y�[�W�R�[�h
	 * @return String		���������������ꍇ"true"���A�ȊO�̏ꍇ��"false"��Ԃ�
	 * @throws DbException
	 * @throws Exception
	 */
	protected String gakTab(PageCodeBaseEx pagecode) throws DbException,Exception {
		
		//���s���t�A�ʐM�敪�A�o�͋敪���R��ʂŋ��L����B
		Xrm00101T01 pc = (Xrm00101T01)pagecode;
		
		Xrm00101T03 nextPage = (Xrm00101T03)UtilSystem.getManagedBean(Xrm00101T03.class);
		
		nextPage.setPropHakkouDate(pc.getpropHakkouDate());
		nextPage.setPropTsushinText(pc.getPropTsushinText());
		nextPage.setPropTushinKbn(pc.getPropTushinKbn());
		nextPage.setPropOutPutKbn(pc.getPropOutPutKbn());
		nextPage.setPropNonyuDate(pc.getPropNonyuDate());
		nextPage.setPropYukoDate(pc.getPropYukoDate());
		nextPage.setPropKigenkbn(pc.getPropKigenkbn());
		
		return Xrm00101T03.FORMID;
	}
	
	/**
	 *���ݓ��t�̎擾
	 *@return java.sql.Date 
	 */
	public java.sql.Date nowDate(){
		//	���ݓ�����sqlDate�Ŏ擾
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		// java.sql.Date�ɕϊ�
		java.sql.Date now = new java.sql.Date(cal.getTimeInMillis());	
		
		return now;
	}
	
	/**
	 *�ʐM���̊����A�񊈐��𑀍삷��<br>
	 *@param pagecode
	 */
	protected String clickcheakBox(PageCodeBaseEx pagecode){
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		if(pc.getPropTushinKbn().isChecked()){
			pc.getPropTsushinText().setDisabled(false);
			return ActionConst.RET_TRUE;
		}else{
			pc.getPropTsushinText().setDisabled(true);
			return ActionConst.RET_TRUE;
		}
	}
	/**
	 *�����̊����A�񊈐��𑀍삷��<br>
	 *@param pagecode
	 */
	protected String clickKigencheakBox(PageCodeBaseEx pagecode){
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		if(pc.getPropKigenkbn().isChecked()){
			pc.getPropNonyuDate().setDisabled(false);
			//			pc.getPropNonyuDate().setReadonly(false);
			pc.getPropYukoDate().setDisabled(false);
			//			pc.getPropYukoDate().setReadonly(false);
			return ActionConst.RET_TRUE;
		}else{
			pc.getPropNonyuDate().setDisabled(true);
			//			pc.getPropNonyuDate().setReadonly(true);
			pc.getPropYukoDate().setDisabled(true);
			//			pc.getPropYukoDate().setReadonly(true);
			return ActionConst.RET_TRUE;
		}
	}
	
	/**
	 * �`�F�b�N�ς݂̔[�t���敪���擾<br>
	 * @param pagecode
	 * @return ArrayList
	 */
	protected ArrayList payKubunget(PageCodeBaseEx pagecode){
		ArrayList list = new ArrayList();
		Xrm00101T01 pc = (Xrm00101T01) pagecode;
		if(pc.getPropChkListSeika().isChecked()){
			list.add("A");
		}
		if(pc.getPropChkListKamokutori().isChecked()){
			list.add("B");
		}
		if(pc.getPropChkListToitu().isChecked()){
			list.add("C");
		}
		return list;
		
	}
	/**
	 * Date�^�̃I�u�W�F�N�g��String�^�ɕϊ����܂�.
	 */
	public String convertDatetoString(java.sql.Date date) {
		return (new SimpleDateFormat(DATE_PATTERN)).format(date);
	}
	/**
	 * �y�C�W�[�ʐM�����A�s�ŕ�������B<br>
	 * @param strTsushinTextchk
	 * @return String[] null�̏ꍇ�̓J������
	 */
	public String[] getpayeasyTexSubString(String strTsushinTextchk){
		//null�`�F�b�N
		if(strTsushinTextchk==null){
			strTsushinTextchk="";
		}	
		//OS�ˑ��̉��s�R�[�h���擾
		String crlf = System.getProperty("line.separator");
		//�ʐM��(�؃C�W�[)�����s�R�[�h���L�[�ɕ���
		String[] aryTsushinTextChk = strTsushinTextchk.split(crlf);
		
		return aryTsushinTextChk;
	}
	
}
