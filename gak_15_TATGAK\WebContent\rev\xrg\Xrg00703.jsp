<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg00703.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg00703.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード	'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード	'event' の代わりに 'thisEvent' を使用します
var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlGakuseiCd&kyoShokuin=3";
 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
}

// 戻るボタン押下時処理
function checkChangeData(id) {
	var changeDataFlg = document.getElementById("form1:htmlHidChangeDataFlg").value;
	if(changeDataFlg == "1"){
		return doPopupMsg(id);
	}else{
		return true;
	}
	return true;
}

// ポップアップメッセージを表示
function doPopupMsg(id, msg) {
  var args = new Array();
  args[0] = msg;
  return confirm(messageCreate(id, args));
}

// データチェンジ時
function onChangeData() {
	document.getElementById("form1:htmlHidChangeDataFlg").value = "1";
}

// 結果欄に結果を設定
function setKekka(thisObj) {

	var row = thisObj.name.split(':');
	if (thisObj.value == 1){
		document.getElementsByName('form1:gakuseiTable:' + row[2] + ':htmlResult')[0].innerHTML = "許可"
	} else {
		document.getElementsByName('form1:gakuseiTable:' + row[2] + ':htmlResult')[0].innerHTML = "不許可"
	}
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />

	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg00703.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg00703.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg00703.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg00703.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!-- ↓ここに戻るボタンを配置 -->
			<DIV class="head_button_area">
				<TABLE>
					<TR>
						<TD nowrap align="right">
							<hx:commandExButton type="submit" tabindex="7"
							value="戻　る" styleClass="commandExButton" id="returnDisp"
							action="#{pc_Xrg00703.doReturnDispAction}"
							onclick="return checkChangeData('#{msg.SY_MSG_0014W}');">
							</hx:commandExButton>
						</TD>
					</TR>
				</TABLE>
			</DIV>
			<!-- ↑ここに戻るボタンを配置 -->

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			<TABLE width="720" class="table" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblNendo"
								value="#{pc_Xrg00703.propNendo.labelName}"
								style="#{pc_Xrg00703.propNendo.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="3">
							<h:outputText styleClass="outputText" id="htmlNendo"
								value="#{pc_Xrg00703.propNendo.stringValue}"
								style="#{pc_Xrg00703.propNendo.style}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblSchoolingSbtCd"
								value="#{pc_Xrg00703.propSchSbtNm.labelName}"
								style="#{pc_Xrg00703.propSchSbtNm.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="350">
							<h:outputText styleClass="outputText" id="htmlSchoolingSbtCd"
								value="#{pc_Xrg00703.propSchSbtNm.displayValue}"
								title="#{pc_Xrg00703.propSchSbtNm.stringValue}"
								style="#{pc_Xrg00703.propSchSbtNm.style}">
							</h:outputText>
						</TD>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblKaikokiJigen"
								value="#{pc_Xrg00703.propKaisaikiJigen.labelName}"
								style="#{pc_Xrg00703.propKaisaikiJigen.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="150">
							<h:outputText styleClass="outputText" id="htmlKaikokiJigen"
								value="#{pc_Xrg00703.propKaisaikiJigen.displayValue}"
								title="#{pc_Xrg00703.propKaisaikiJigen.stringValue}"
								style="#{pc_Xrg00703.propKaisaikiJigen.style}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblJugyoName"
								value="#{pc_Xrg00703.propJugyoName.labelName}"
								style="#{pc_Xrg00703.propJugyoName.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="3">
							<h:outputText styleClass="outputText" id="htmlJugyoName"
								value="#{pc_Xrg00703.propJugyoName.displayValue}"
								title="#{pc_Xrg00703.propJugyoName.stringValue}"
								style="#{pc_Xrg00703.propJugyoName.style}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH width="150">
							<h:outputText styleClass="outputText"
								id="lblDisplayTarget"
								value="#{pc_Xrg00703.propDisplayTarget.labelName}"
								style="#{pc_Xrg00703.propDisplayTarget.labelStyle}">
							</h:outputText>
						</TH>
						<TD colspan="3">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox"
								id="htmlKyokaFlg" tabindex="1"
								value="#{pc_Xrg00703.propKyokaFlg.checked}"
								disabled="#{pc_Xrg00703.propKyokaFlg.disabled}"
								readonly="#{pc_Xrg00703.propKyokaFlg.readonly}"
								style="#{pc_Xrg00703.propKyokaFlg.style}">
							</h:selectBooleanCheckbox>
							許可
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox"
								id="htmlFukyokaFlg" tabindex="2"
								value="#{pc_Xrg00703.propFukyokaFlg.checked}"
								disabled="#{pc_Xrg00703.propFukyokaFlg.disabled}"
								readonly="#{pc_Xrg00703.propFukyokaFlg.readonly}"
								style="#{pc_Xrg00703.propFukyokaFlg.style}">
							</h:selectBooleanCheckbox>
							不許可
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE width="810">
				<TBODY>
					<TR>
						<TD width="810"
							style="background-color: transparent; text-align: center" nowrap
							class="clear_border">
							<hx:commandExButton type="submit" value="再表示"
								styleClass="commandExButton_dat" id="redisplay"
								action="#{pc_Xrg00703.doRedisplayAction}"
								tabindex="3"
								disabled="#{pc_Xrg00703.propRedisplay.disabled}"
								style="#{pc_Xrg00703.propRedisplay.style}"
								onclick="return checkChangeData('#{msg.SY_MSG_0014W}');">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="810">
				<TBODY>
					<TR>
						<TD align="right">
							<h:outputText
								styleClass="outputText" id="lblCount"
								value="#{pc_Xrg00703.propCount.value}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="810">
				<TBODY>
					<TR>
						<TD>
							<div id="listScroll" class="listScroll"
								onscroll="setScrollPosition('scroll',this);" 
							style="height:350px;width:810px;">
								<h:dataTable
									border="0" cellpadding="2" cellspacing="0"
									columnClasses="columnClass1" headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Xrg00703.propGakuseiList.rowClasses}"
									styleClass="meisai_scroll" id="gakuseiTable"
									value="#{pc_Xrg00703.propGakuseiList.list}" var="varlist">
									<h:column id="column1">
										<f:facet name="header">
											<h:outputText id="lblListGakuseiCd" styleClass="outputText"
												value="学籍番号"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlGakusekiNo"
											value="#{varlist.gakusekiCd}"></h:outputText>
										<f:attribute value="80" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle"
											name="style" />
										<f:attribute value="left" name="align" />
									</h:column>
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="学生氏名"
												id="lblGakuseiName"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlGakuseiName"
											value="#{varlist.propGakuseiNm.displayValue}"
											title="#{varlist.propGakuseiNm.stringValue}"></h:outputText>
										<f:attribute value="150" name="width" />
										<f:attribute value="text-align: left; vertical-align: middle"
											name="style" />
										<f:attribute value="left" name="align" />
									</h:column>
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="履修方法"
												id="lblRishuHoho"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlRishuHoho"
											value="#{varlist.propRisyuHoho.displayValue}"
											title="#{varlist.propRisyuHoho.stringValue}"></h:outputText>
										<f:attribute value="90" name="width" />
										<f:attribute value="text-align: left; vertical-align: middle"
											name="style" />
										<f:attribute value="left" name="align" />
									</h:column>
									<h:column id="column4">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="学生身分"
												id="lblGakuseiMbn"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlGakuseiMbn"
											value="#{varlist.propGakMibun.displayValue}"
											title="#{varlist.propGakMibun.stringValue}"></h:outputText>
										<f:attribute value="120" name="width" />
										<f:attribute value="text-align: left; vertical-align: middle"
											name="style" />
										<f:attribute value="left" name="align" />
									</h:column>
									<h:column id="column5">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="希望順位"
												id="lblKibojun"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlKibojun"
											value="#{varlist.kiboJyuni}"></h:outputText>
										<f:attribute value="60" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle"
											name="style" />
										<f:attribute value="right" name="align" />
									</h:column>
									<h:column id="column6">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="申込日時"
												id="lblMskDate"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlMskDate"
											value="#{varlist.propMskNitiji.stringValue}"></h:outputText>
										<f:attribute value="110" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle"
											name="style" />
										<f:attribute value="left" name="align" />
									</h:column>
									<h:column id="column7">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="結果"
												id="lblResult"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlResult"
											value="#{varlist.result}"></h:outputText>
										<f:attribute value="50" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle"
											name="style" />
										<f:attribute value="left" name="align" />
									</h:column>
									<h:column id="column8">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="許可状態"
												id="lblKyokaStatus"></h:outputText>
										</f:facet>
										<h:selectOneRadio
												disabledClass="selectOneRadio_Disabled"
												styleClass="selectOneRadio" id="htmlKyokaStatus"
												value="#{varlist.propKyokaStatus.stringValue}"
												disabled="#{varlist.propKyokaStatus.disabled}"
												style="#{varlist.propKyokaStatus.style}"
												onchange="onChangeData();" tabindex="4"
												onclick="return setKekka(this);">
												<f:selectItem itemValue="1" itemLabel="許可" />
												<f:selectItem itemValue="2" itemLabel="不許可" />
										</h:selectOneRadio>
										<f:attribute value="120" name="width" />
										<f:attribute value="text-align: left; vertical-align: middle"
											name="style" />
										<f:attribute value="left" name="align" />
									</h:column>
									<h:column id="column9">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit" value="エラー"
											styleClass="commandExButton" id="errorSearch"
											action="#{pc_Xrg00703.doSearchErrorAction}" tabindex="4"
											disabled="#{varlist.propErrorBtn.disabled}"
											rendered="#{varlist.propErrorBtn.rendered}"
											style="#{varlist.propErrorBtn.style}"></hx:commandExButton>
										<f:attribute value="40" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle"
											name="style" />
									</h:column>
								</h:dataTable>
							</div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="800">
				<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit"
								styleClass="commandExButton_dat" id="update" value="更新"
								action="#{pc_Xrg00703.doUpdateAction}" 
								disabled="#{pc_Xrg00703.propUpdate.disabled}"
								confirm="#{msg.SY_MSG_0003W}" tabindex="5"
								style="#{pc_Xrg00703.propUpdate.style}">
							</hx:commandExButton>
							<hx:commandExButton type="submit"
								styleClass="commandExButton_dat" id="makeCSV" value="CSV作成"
								action="#{pc_Xrg00703.doMakeCSVAction}" tabindex="6"
								disabled="#{pc_Xrg00703.propMakeCSV.disabled}"
								style="#{pc_Xrg00703.propMakeCSV.style}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrg00703.propGakuseiList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
			<h:inputHidden id="htmlHidChangeDataFlg" value="#{pc_Xrg00703.changeDataFlg}" ></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

