<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrj/PXrj0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pXrj0101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >

<SCRIPT type="text/javascript">


function confirmOk() {
	document.getElementById('form1:htmlExecutableSearch').value = "1";
	indirectClick('search');
}

function confirmCancel() {
	document.getElementById('form1:htmlExecutableSearch').value = "0";
}

//親画面に学籍番号を返却
function doSelect(thisObj, thisEvent) {

	var buttonid = thisObj.id;
	// 正規表現にて選択行のindex値を取得
	var point_start = buttonid.search(":[0-9]*:");
	var point_end = buttonid.search(":button_select");
	var index = buttonid.substring(point_start+1,point_end);
	
	// 学籍番号
	var retValue = document.getElementById("form1:htmlGakuseiList:"+ index +":lblGakusekino_list").innerHTML;
	// 学籍番号返却フィールドID
	var retFieldName = document.getElementById("form1:htmlGakusekiNoIdHidden").value;

	if (window.opener) {
		window.opener.document.getElementById(retFieldName).value = retValue;
		window.opener.document.getElementById(retFieldName).onblur(); // 親ウィンドウのAjaxを実行して名称をセット
	}
	// 自画面クローズ処理
	self.close();
	return true;
}
	
</SCRIPT>

</HEAD>
	<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg"/>
		<BODY>
		<hx:scriptCollector id="scriptCollector1" preRender="#{pc_PXrj0101.onPageLoadBegin}">
			<h:form styleClass="form" id="form1">	

				<!-- ヘッダーインクルード -->
				<jsp:include page ="../inc/childHeader.jsp" />

				<!-- ヘッダーへのデータセット領域 -->
				<DIV style="display:none;">
					<hx:commandExButton
						type="submit"
						value="閉じる"
						styleClass="commandExButton"
						id="closeDisp"
						action="#{pc_PXrj0101.doCloseDispAction}">
					</hx:commandExButton>
					<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_PXrj0101.funcId}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
					<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_PXrj0101.screenName}"></h:outputText>
				</DIV>

				<!--↓outer↓-->
				<DIV class="outer">

					<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
						<h:outputText id="message" 
							value="#{requestScope.DISPLAY_INFO.displayMessage}"
							styleClass="outputText"
							escape="false">
						</h:outputText>
					</FIELDSET>

					<!--↓content↓-->
					<DIV class="head_button_area" >　
					</DIV>
					
					<DIV id="content">
					<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" width="750px">
					<TBODY>
					<TR>
					<TD>
					<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
					<TBODY>
					<TR>
					<TD>
						<TABLE class="table" width="100%" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TH nowrap class="v_a" width="150px" >
										<h:outputText styleClass="outputText" 
											id="lblSyutgakDate"
											value="#{pc_PXrj0101.propSyutgakDate.name}"
											style="#{pc_PXrj0101.propSyutgakDate.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="2" width="*">
										<h:inputText styleClass="inputText"
											id="htmlSyutgakDate"
											value="#{pc_PXrj0101.propSyutgakDate.dateValue}"
											size="5" 
											style="#{pc_PXrj0101.propSyutgakDate.style}">
											<hx:inputHelperAssist errorClass="inputText_Error"
												imeMode="inactive" promptCharacter="_" />
											<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>																	
								</TR>
								<TR>
									<TH nowrap class="v_a" width="150px">
										<h:outputText styleClass="outputText" 
											id="lblSyutgakno"
											value="#{pc_PXrj0101.propSyutgakno.labelName}"
											style="#{pc_PXrj0101.propSyutgakno.labelStyle}">
										</h:outputText>
									</TH>
									<TD colspan="2" width="*">
										<h:inputText styleClass="inputText"
											id="htmlSyutgakno"
											value="#{pc_PXrj0101.propSyutgakno.integerValue}"
											size="3" 
											style="#{pc_PXrj0101.propSyutgakno.style}">
											<f:convertNumber type="number" pattern="#0"/>
											<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />	
										</h:inputText>
									</TD>																	
								</TR>
								<TR>
									<TH nowrap class="v_b" width="150px">
										<h:outputText styleClass="outputText" 
											id="lblName"
											value="#{pc_PXrj0101.propName.labelName}"
											style="#{pc_PXrj0101.propName.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="*">
										<h:inputText styleClass="inputText"
											id="htmlName" 
											value="#{pc_PXrj0101.propName.value}"
											maxlength="#{pc_PXrj0101.propName.maxLength}" 
											size="40"
											style="#{pc_PXrj0101.propName.style}">
											<hx:inputHelperAssist errorClass="inputText_Error" />
										</h:inputText>
									</TD>
									<TD width="240px">
										<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" id="htmlNameFindType"
											value="#{pc_PXrj0101.propNameFindType.value}">
											<f:selectItem itemValue="0" itemLabel="部分一致" />
											<f:selectItem itemValue="1" itemLabel="前方一致" />																				
										</h:selectOneRadio>
									</TD>
								</TR>
								<TR>
									<TH nowrap class="v_c" width="150px">
										<h:outputText styleClass="outputText" 
											id="lblGakusekino"
											value="#{pc_PXrj0101.propGakusekino.labelName}"
											style="#{pc_PXrj0101.propGakusekino.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="*">
										<h:inputText styleClass="inputText"
											id="htmlGakusekino"
											value="#{pc_PXrj0101.propGakusekino.value}"
											maxlength="#{pc_PXrj0101.propGakusekino.maxLength}"
											size="15" 
											style="#{pc_PXrj0101.propGakusekino.style}">
											<hx:inputHelperAssist imeMode="disabled"
												errorClass="inputText_Error" />
										</h:inputText>
									</TD>
									<TD width="240px">
										<h:selectOneRadio disabledClass="selectOneRadio_Disabled"
											styleClass="selectOneRadio" 
											id="htmlGakusekinoFindType"
											value="#{pc_PXrj0101.propGakusekinoFindType.value}">
											<f:selectItem itemValue="0" itemLabel="部分一致" />
											<f:selectItem itemValue="1" itemLabel="前方一致" />																				
										</h:selectOneRadio>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						</TR>
						<TR>
						<TD>
						<TABLE cellspacing="1" cellpadding="1" class="button_bar" width="100%">
							<TBODY>
								<TR align="right">
									<TD align="center" width="724">
										<hx:commandExButton type="submit" 
											value="検索" 
											styleClass="commandExButton_dat"
											id="search" 
											action="#{pc_PXrj0101.doSearchAction}">
										</hx:commandExButton>&nbsp;
										<hx:commandExButton type="submit"
											value="クリア" 
											styleClass="commandExButton_etc" 
											id="clear"
											action="#{pc_PXrj0101.doClearAction}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD width="754">
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
							<TBODY>
								<TR>
									<TD align="right">
										<h:outputText styleClass="outputText"
											id="lblCount"
											value="#{pc_PXrj0101.propGakuseiList.listCount}">
										</h:outputText>
										<h:outputText styleClass="outputText"
											id="text4" value="件">
										</h:outputText>
									</TD>
								</TR>
								<TR>
									<TD>
										<DIV style="height: 314px; width=100%;" id="listScroll" class="listScroll">
											<h:dataTable
												rows="#{pc_PXrj0101.propGakuseiList.rows}"
												rowClasses="#{pc_PXrj0101.propGakuseiList.rowClasses}"
												headerClass="headerClass"
												footerClass="footerClass"
												styleClass="meisai_scroll" id="htmlGakuseiList"
												value="#{pc_PXrj0101.propGakuseiList.list}" var="varlist"
												width="731px">
												<h:column id="column1">
													<f:facet name="header">
														<h:outputText id="txtLabel1"
															styleClass="outputText"
															value="出学年度">
														</h:outputText>
													</f:facet>
													<f:attribute value="95px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblSyutgakDate_list"
														value="#{varlist.propSyutgakDate}">
													</h:outputText>
												</h:column>
												<h:column id="column2">
													<f:facet name="header">
														<h:outputText id="txtLabel2"
															styleClass="outputText"
															value="出学学期名称">
														</h:outputText>
													</f:facet>
													<f:attribute value="150px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblSyutgakno_list"
														value="#{varlist.propSyutgakName}">
													</h:outputText>
												</h:column>
												<h:column id="column3">
													<f:facet name="header">
														<h:outputText id="txtLabel3"
															styleClass="outputText"
															value="学籍番号">
														</h:outputText>
													</f:facet>
													<f:attribute value="200px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblGakusekino_list"
														value="#{varlist.propGakusekino}">
													</h:outputText>
												</h:column>
												<h:column id="column4">
													<f:facet name="header">
														<h:outputText id="txtLabel4"
															styleClass="outputText"
															value="氏名">
														</h:outputText>
													</f:facet>
													<f:attribute value="250px" name="width" />
													<h:outputText styleClass="outputText" 
														id="lblGakuseinm_list"
														value="#{varlist.propName}">
													</h:outputText>
												</h:column>
												<h:column id="column5">
													<f:facet name="header">
													</f:facet>
													<f:attribute value="35px" name="width" />
													<hx:commandExButton type="submit" value="選択"
														styleClass="commandExButton" id="button_select"
														onclick="return doSelect(this, event);"></hx:commandExButton>
												</h:column>
											</h:dataTable>
										</DIV>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
					</TR>
					</TBODY>
					</TABLE>
				</TD>
				</TR>
				</TBODY>
				</TABLE>
				<!-- ↑ここにコンポーネントを配置 -->
				</DIV>
				</DIV>
				<!--↑content↑-->
				</DIV>
				<!--↑outer↑-->
				
				<!-- フッダーインクルード -->
				<jsp:include page ="../inc/childFooter.jsp" />

				<h:inputHidden id="htmlGakusekiNoIdHidden" value="#{pc_PXrj0101.propGakusekiNoIdHidden.stringValue}"></h:inputHidden>
				<h:inputHidden value="#{pc_PXrj0101.propExecutableSearch.integerValue}" id="htmlExecutableSearch">
					<f:convertNumber />
				</h:inputHidden>
				</h:form>
			</hx:scriptCollector>
		</BODY>
	<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>
