<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@page import="com.jast.gakuen.rev.xrb.constant.code.SinseiKbn"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00101.jsp</TITLE>

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	
<SCRIPT type="text/javascript">

	// start
	function openKamokuSubWindow(field1) {
		// 科目検索画面
		var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp"
			+ "?retFieldName=" + field1;

		var reOpenSubWinFlg = "0";
		var ajaxServlet = "rev/xrx/XrxRemoveFromSessionAJAX";
		var args = new Array();
			args['pcClass'] = 'com.jast.gakuen.rev.km.PKmz0101';
			args['motoFuncId'] = '';
		var engine      = new AjaxEngine();
		engine.setCallbackMethod(
			function(value) {
				var windowPointer = openModalWindow(url, "pKmz0101", "<%=com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
				if(reOpenSubWinFlg=="0"){
					focus();
				}
			}
		);
		engine.send(ajaxServlet,null,args);
	}

	function doKamokuAjax(thisObj, thisEvent, targetLabel) {
		// 科目名称を取得する
		var servlet = "rev/xrb/XrbKmkAJAX";
	    var args = new Array();
	        args['code'] = thisObj.value;
	        args['nyugakNendo'] = document.getElementById('form1:nyugakNendo').innerText;
	        args['nyugakGakkiNo'] = document.getElementById('form1:nyugakGakkiNo').innerText;
	        args['curGakkaCd'] = document.getElementById('form1:curGakkaCd').innerText;
	    
	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);
	}
	// end

	function func_1(thisObj, thisEvent) {
		changeScrollPosition('scroll','listScroll');
		changeScrollPosition('scroll2','listScroll2');
		doGakuseiAjax(document.getElementById('form1:htmlGakusekiCd'),thisEvent, 'form1:htmlSearchName');
		doKamokuAjax(document.getElementById('form1:htmlKamokCd'),thisEvent, 'form1:lblKamokName');
		doKamokuAjax(document.getElementById('form1:htmlKamokCdNew'),thisEvent,'form1:lblKamokNameNew');
		
		onChangeHenkoKbn();
	}
	function func_2(thisObj, thisEvent) {
		check('htmlRsyuTorokList','htmlSelected');
	}
	function func_3(thisObj, thisEvent) {
		uncheck('htmlRsyuTorokList','htmlSelected');
	}

	function openSubWindow5() {
		// 学生検索画面（引数：なし）

		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0101.jsp?retFieldName=form1:htmlGakusekiCd";
		openModalWindow(url, "pCob0101", "<%=com.jast.gakuen.rev.co.PCob0101.getWindowOpenOption() %>");
		return true;
	}
	
	function doGakuseiAjax(thisObj, thisEvent, targetId) {
	   if (thisObj.value == '') {
	       return;
	   }
		 // 学生名称を取得する
		 var servlet = "rev/co/CobGakseiAJAX";
		 var args = new Array();
		 args['code1'] = thisObj.value;
		 var ajaxUtil = new AjaxUtil();
		 ajaxUtil.getCodeName(servlet, targetId, args);
	}

	function confirmOk() {	
	   	if(document.getElementById('form1:isSelect').value == 'select'){
	   		var vCount = document.getElementById("form1:htmlConfirmValue").value ;
	    	vCount = parseInt(vCount) + parseInt(1);
	    	document.getElementById("form1:htmlConfirmValue").value = vCount;
	    	indirectClick('select');
	    }else{
        	var str = document.getElementById('form1:htmlSelectButtonFlg').value;
			var id = str.substring(6, str.length + 1); 
			indirectClick(id);
		}
	}
	function confirmCancel() {

		//以下のボタンが押された場合はアクションメソッドを呼び出す

		// 2 :削除ボタン押下

		// 3 :取消ボタン押下

		//それ以外の場合は値を初期化して終了する

		document.getElementById("form1:htmlConfirmValue").value = '0';
		var flg = document.getElementById('form1:htmlSelectButtonFlg').value;

		if (flg == "form1:delete") {

			document.getElementById('form1:htmlExecutableWarning').value = "2";
			document.getElementById('form1:htmlExecutableWarning2').value = "2";
			indirectClick('delete');
		} else if (flg == "form1:cancel") {

			document.getElementById('form1:htmlExecutableWarning').value = "2";
			document.getElementById('form1:htmlExecutableWarning2').value = "2";
			indirectClick('cancel');
		} else {

			document.getElementById('form1:htmlExecutableWarning').value = "0";
			document.getElementById('form1:htmlExecutableWarning2').value = "0";
			document.getElementById('form1:htmlWarningCount').value = "0";
		}
		// alert('実行を中断しました。');	
	}


	function setSelectButtonFlg(thisObj, thisEvent) {
		document.getElementById('form1:htmlSelectButtonFlg').value = thisObj.id;
	}
	
	function setButtonFlgClear(thisObj, thisEvent) {
		document.getElementById('form1:htmlSelectButtonFlg').value = "form1:clear";
	}

	function onChangeHenkoKbn() {
		if(document.getElementById('form1:htmlHenkoKbn').value == <%=SinseiKbn.KAMOKU_HENKO.getCode()%>){
			document.getElementById('form1:htmlKamokCdNew').disabled = false;
		} else {
			document.getElementById('form1:htmlKamokCdNew').value = "";
			document.getElementById('form1:htmlKamokCdNew').disabled = true;
		}
	}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="return func_1(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00101.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
styleClass="commandExButton" id="closeDisp"
action="#{pc_Xrb00101.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00101.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00101.screenName}"></h:outputText>
</div>			
			
<div style="display:none;">
<h:outputText styleClass="outputText" id="nyugakNendo" value="#{pc_Xrb00101.nyugakNendo}"></h:outputText>
<h:outputText styleClass="outputText" id="nyugakGakkiNo" value="#{pc_Xrb00101.gakkiNo}"></h:outputText>
<h:outputText styleClass="outputText" id="curGakkaCd" value="#{pc_Xrb00101.curGakkaCd}"></h:outputText>
</div>

<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD>
									<TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
										class="table">
										<TBODY>
											<TR>
												<TH width="200" class="v_e">
													<h:outputText
													styleClass="outputText" id="lblGakusekiCd"
													value="#{pc_Xrb00101.propGakusekiCd.labelName}"
													style="#{pc_Xrb00101.propGakusekiCd.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="*" style="border-right-style:none;">
													<h:inputText
													styleClass="inputText" id="htmlGakusekiCd"
													value="#{pc_Xrb00101.propGakusekiCd.stringValue}"
													style="#{pc_Xrb00101.propGakusekiCd.style}"
													disabled="#{pc_Xrb00101.propGakusekiCd.disabled}"
													readonly="#{pc_Xrb00101.propGakusekiCd.readonly}"
													onblur="return doGakuseiAjax(this, event, 'form1:htmlSearchName')"
													maxlength="#{pc_Xrb00101.propGakusekiCd.maxLength}" size="10">
													</h:inputText>
													<hx:commandExButton
													type="button" styleClass="commandExButton_search"
													id="gakusekiSearch"
													action="#{pc_Xrb00101.doGakusekiSearchAction}"
													disabled="#{pc_Xrb00101.propGakusekiCd.disabled}"
													onclick="return openSubWindow5(this, event);">
													</hx:commandExButton>
													<h:outputText
													styleClass="outputText" id="htmlSearchName"
													value="#{pc_Xrb00101.propSearchName.stringValue}" style="#{pc_Xrb00101.propSearchName.style}">
													</h:outputText>
												</TD>
												<TD class="buttonArea"width="148" style="border-left-style:none;">
													<hx:commandExButton type="submit"
													value="選択" id="select" styleClass="commandExButton"
													action="#{pc_Xrb00101.doSelectAction}"
													disabled="#{pc_Xrb00101.propGakusekiCd.disabled}"
													onclick="return setSelectButtonFlg(this, event);">
													</hx:commandExButton>
													<hx:commandExButton type="submit"
													value="解除"  id="unselect" styleClass="commandExButton"
													action="#{pc_Xrb00101.doUnselectAction}"
													disabled="#{!pc_Xrb00101.propGakusekiCd.disabled}"
													onclick="return setSelectButtonFlg(this, event);">
													</hx:commandExButton>
													<hx:commandExButton type="submit"
													value="完了解除"  id="completeCancell" styleClass="commandExButton"
													action="#{pc_Xrb00101.doCompleteCancell}"
													disabled="#{!pc_Xrb00101.propGakusekiCd.disabled}"
													onclick="return setSelectButtonFlg(this, event);">
													</hx:commandExButton>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE></TD>
					</TR>
					<TR>
						<TD align="center" height="5px">
						</TD>
					</TR>
					<TR>
						<TD align="center"><TABLE width="900" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH width="200" class="v_g">
										<h:outputText styleClass="outputText"
										id="lblNyugakNendo"
										value="#{pc_Xrb00101.propNyugakNendo.labelName}"
										style="#{pc_Xrb00101.propNyugakNendo.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="200">
										<h:outputText styleClass="outputText"
										id="htmlNyugakNendo"
										value="#{pc_Xrb00101.propNyugakNendo.stringValue}"
										style="#{pc_Xrb00101.propNyugakNendo.style}">
										</h:outputText>
									</TD>
									<TH width="200" class="v_g">
										<h:outputText styleClass="outputText"
										id="lblGakkiNo"
										value="#{pc_Xrb00101.propGakkiNo.labelName}"
										style="#{pc_Xrb00101.propGakkiNo.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="200">
										<h:outputText styleClass="outputText"
										id="htmlGakkiNo"
										value="#{pc_Xrb00101.propGakkiNo.stringValue}"
										style="#{pc_Xrb00101.propGakkiNo.style}">
										</h:outputText>
									</TD>
								</TR>
								<TR>
									<TH width="200" class="v_b">
										<h:outputText styleClass="outputText"
										id="lblCurGakkaName"
										value="#{pc_Xrb00101.propCurGakkaName.labelName}"
										style="#{pc_Xrb00101.propCurGakkaName.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="700" colspan="3">
										<h:outputText styleClass="outputText"
										id="htmlCurGakkaName"
										value="#{pc_Xrb00101.propCurGakkaName.displayValue}" 
										title="#{pc_Xrb00101.propCurGakkaName.value}"
										style="#{pc_Xrb00101.propCurGakkaName.style}">
										</h:outputText>
									</TD>
								</TR>
								<TR>
									<TH width="200" class="v_a">
										<h:outputText styleClass="outputText"
										id="lblNyugakNenji"
										value="#{pc_Xrb00101.propNyugakNenji.labelName}"
										style="#{pc_Xrb00101.propNyugakNenji.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="300">
										<h:outputText styleClass="outputText"
										id="htmlNyugakNenji"
										value="#{pc_Xrb00101.propNyugakNenji.stringValue}"
										style="#{pc_Xrb00101.propNyugakNenji.style}">
										</h:outputText>
									</TD>
									<TH width="200" class="v_g">
										<h:outputText styleClass="outputText"
										id="lblGakunen"
										value="#{pc_Xrb00101.propGakunen.labelName}"
										style="#{pc_Xrb00101.propGakunen.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="200">
										<h:outputText styleClass="outputText"
										id="htmlGakunen"
										value="#{pc_Xrb00101.propGakunen.stringValue}"
										style="#{pc_Xrb00101.propGakunen.style}">
										</h:outputText>
									</TD>
								</TR>
							</TBODY>
						</TABLE></TD>
					</TR>
					<TR>
						<TD align="center" height="5px">
						</TD>
					</TR>
					<TR>
						<TD align="center">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="96%">
							<TBODY>
								<TR>
									<TD align="left" width="450">
										<h:outputText styleClass="outputText" id="text10" value="履修登録情報一覧"></h:outputText>
									</TD>
									<TD align="right" width="450">
										<h:outputFormat styleClass="outputFormat" id="htmlCount" value=" {0}件">
										    <f:param id="param1" name="count" value="#{pc_Xrb00101.propRisyuTorokList.listCount}"></f:param>
										</h:outputFormat>
									</TD>
								</TR>
								<TR>
									<TD colspan="2">
										<DIV id="listScroll" class="listScroll" style="height:210px;" onscroll="setScrollPosition('scroll',this)" >
										<h:dataTable id="htmlRsyuTorokList" value="#{pc_Xrb00101.propRisyuTorokList.list}" rowClasses="#{pc_Xrb00101.propRisyuTorokList.rowClasses}" 
										    headerClass="headerClass" footerClass="footerClass" styleClass="meisai_scroll"  border="0" cellpadding="0" cellspacing="0" width="880" var="varlist">
										
                                        <%-- 削除チェックボックス --%>
										<h:column id="column8">
											<f:facet name="header" />
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox" id="htmlSelected" value="#{varlist.selected}" rendered="#{varlist.renderDeleteCheckBox}" />
											<f:attribute value="30" name="width" />
											<f:attribute name="style" value="text-align:center;"/>
										</h:column>

                                        <%-- 科目コード --%>
										<h:column id="column3">
											<f:facet name="header">
												<h:outputText id="lblKamokCdList" value="科目<br>コード" styleClass="outputText" escape="false"/>
											</f:facet>
											<h:outputText id="htmlKamokCdList" value="#{varlist.kamokCd}" styleClass="outputText" />
											<f:attribute name="width" value="55" />
											<f:attribute name="style" value="text-align:center;"/>
										</h:column>

                                        <%-- 科目名称 --%>
										<h:column id="column4">
											<f:facet name="header">
												<h:outputText id="lblKamokNameList" value="科目名称" styleClass="outputText" />
											</f:facet>
											<h:outputText id="htmlKamokNameList" value="#{varlist.kamokNameShryk}" title="#{varlist.kamokName}" styleClass="outputText" />
											<f:attribute name="width" value="310" />
											<f:attribute name="style" value="text-align:left;"/>
										</h:column>
										
                                        <%-- 履修年次 --%>
										<h:column id="column5">
											<f:facet name="header">
												<h:outputText id="lblRisyuNenjiDisp" value="履修年次" styleClass="outputText" />
											</f:facet>
											<h:outputText id="htmlRisyuNenjiDisp" value="#{varlist.risyuNenjiDisp}" styleClass="outputText" />
											<f:attribute name="width" value="80" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>

                                        <%-- 履修方法 --%>
										<h:column id="column6">
											<f:facet name="header">
												<h:outputText id="lblRisyuHohoDisp" value="履修方法" styleClass="outputText" />
											</f:facet>
											<h:outputText id="htmlRisyuHohoDisp" value="#{varlist.risyuHohoDisp}" styleClass="outputText" />
											<f:attribute name="width" value="80" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>

                                        <%-- 状態 --%>
										<h:column id="column7">
											<f:facet name="header">
												<h:outputText id="lblStateList" value="状態" styleClass="outputText" />
											</f:facet>
											<h:outputText id="htmlStateList" value="#{varlist.state}" styleClass="outputText" />
											<f:attribute name="width" value="60" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>
										
										<%-- 除外 --%>
										<h:column id="column9">
											<f:facet name="header">
												<h:outputText id="lblSetJgaiList" value="除外" styleClass="outputText" />
											</f:facet>
											<h:outputText id="htmlSetJgaiList" value="#{varlist.setJgai}" styleClass="outputText" />
											<f:attribute name="width" value="60" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>

										<%-- 単位 --%>
										<h:column id="column13">
											<f:facet name="header">
												<h:outputText id="lblTaniList" value="単位" styleClass="outputText" />
											</f:facet>
											<h:outputText id="htmlTaniList" value="#{varlist.tani}" styleClass="outputText" />
											<f:attribute name="width" value="70" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>
										
										<%-- 登録状態 --%>
										<h:column id="column14">
											<f:facet name="header">
												<h:outputText id="lblHenkoFlgList" value="登録<br>状態" styleClass="outputText" escape="false"/>
											</f:facet>
											<h:outputText id="htmlHenkoFlgList" value="#{varlist.henkoStr}" styleClass="outputText" />
											<f:attribute name="width" value="75" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>

                                        <%-- 選択ボタン --%>
										<h:column id="column17">
											<f:facet name="header" />
											<hx:commandExButton type="submit" id="risyuSelect" value="選択" styleClass="commandExButton" disabled="#{varlist.selectSentakuButton.disabled}"
											    rendered="#{varlist.renderSelectButton}" action="#{pc_Xrb00101.doRisyuSelectAction}" onclick="return setSelectButtonFlg(this, event);" />
											<f:attribute name="width" value="30" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>

                                        <%-- 除外ボタン --%>
										<h:column id="column18">
											<f:facet name="header" />
											<hx:commandExButton type="submit" id="edit" value="除外" styleClass="commandExButton" disabled="#{varlist.selectSakujoButton.disabled}"
										        rendered="#{varlist.renderExcludeButton}" action="#{pc_Xrb00101.doEditAction}" onclick="return setSelectButtonFlg(this, event);" />
											<f:attribute name="width" value="30" />
											<f:attribute name="style" value="text-align:center;" />
										</h:column>

										</h:dataTable>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TD align="left" colspan="2">
										<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD width="60">
													<hx:commandExButton type="button" styleClass="check"
													id="check" onclick="return func_2(this, event);">
													</hx:commandExButton>
													<hx:commandExButton type="button" styleClass="uncheck"
													id="uncheck" onclick="return func_3(this, event);">
													</hx:commandExButton>
												</TD>
												<TD width="840">
													<hx:commandExButton type="submit" value="削除"
													styleClass="commandExButton" id="delete"
													action="#{pc_Xrb00101.doDeleteAction}"
													onclick="return setSelectButtonFlg(this, event);"
													disabled="#{pc_Xrb00101.propKamokCd.disabled}">
													</hx:commandExButton>
												</TD>
											</TR>
										</TBODY>
										</TABLE>
									</TD>
								</TR>
							</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center" height="5px">
						</TD>
					</TR>
					<TR>
						<TD align="center">
							<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD width="800"><TABLE width="800" border="0" cellpadding="0"
										cellspacing="0" class="table">
										<TBODY>
											<TR>
												<TH class="v_a" width="150">
													<h:outputText styleClass="outputText"
													id="lblKamokCd"
													value="#{pc_Xrb00101.propKamokCd.labelName}"
													style="#{pc_Xrb00101.propKamokCd.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="550" colspan="3">
													<h:inputText styleClass="inputText"
													id="htmlKamokCd"
													value="#{pc_Xrb00101.propKamokCd.stringValue}"
													style="#{pc_Xrb00101.propKamokCd.style}"
													maxlength="#{pc_Xrb00101.propKamokCd.maxLength}" size="10"
													onblur="return doKamokuAjax(this, event, 'form1:lblKamokName');"
													disabled="#{pc_Xrb00101.propKamokCd.disabled}" >
							                        <hx:inputHelperAssist errorClass="inputText_Error"
								                    imeMode="inactive" promptCharacter="_" />
							                        </h:inputText>
													<hx:commandExButton type="button" value=""
													styleClass="commandExButton_search" id="search"
													onclick="return openKamokuSubWindow('form1:htmlKamokCd');"
													disabled="#{pc_Xrb00101.propSearchButton.disabled}">
													</hx:commandExButton>
													<h:outputText styleClass="outputText" id="lblKamokName" value="#{pc_Xrb00101.propKamokName.stringValue}"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TH class="v_a" width="150">
													<h:outputText styleClass="outputText"
													value="変更前科目コード"
													style="#{pc_Xrb00101.propKamokNameOld.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="550" colspan="3">
													<h:outputText styleClass="outputText" value="#{pc_Xrb00101.propKamokNameOld.stringValue}"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TH width="150" class="v_c">
													<h:outputText styleClass="outputText"
													id="lblRisyuNenji"
													value="#{pc_Xrb00101.propRisyuNenji.labelName}"
													style="#{pc_Xrb00101.propRisyuNenji.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="200">
													<h:selectOneMenu styleClass="selectOneMenu"
													id="htmlRisyuNenji"
													value="#{pc_Xrb00101.propRisyuNenji.stringValue}"
													style="#{pc_Xrb00101.propRisyuNenji.style};width:140px"
													disabled="#{pc_Xrb00101.propRisyuNenji.disabled}">
 													<f:selectItems value="#{pc_Xrb00101.propRisyuNenji.list}" />
													</h:selectOneMenu>
												</TD>
												<TH width="150" class="v_c">
													<h:outputText styleClass="outputText"
													id="lblRisyuHoho"
													value="#{pc_Xrb00101.propRisyuHoho.labelName}"
													style="#{pc_Xrb00101.propRisyuHoho.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="200">
													<h:selectOneMenu styleClass="selectOneMenu"
													id="htmlRisyuHoho"
													value="#{pc_Xrb00101.propRisyuHoho.stringValue}"
													style="#{pc_Xrb00101.propRisyuHoho.style};width:140px"
													disabled="#{pc_Xrb00101.propRisyuHoho.disabled}">
 													<f:selectItems value="#{pc_Xrb00101.propRisyuHoho.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
											<TR>
												<TH align="left" valign="middle" class="v_f" nowrap width="150">
													<h:outputText styleClass="outputText"
													id="lblTorokDate"
													style="#{pc_Xrb00101.propTorokDate.labelStyle}"
													value="#{pc_Xrb00101.propTorokDate.labelName}">
													</h:outputText>
												</TH>
												<TD align="left" valign="middle" width="200">
													<h:inputText styleClass="inputText"
													id="htmlTorokDate" size="12"
													disabled="#{pc_Xrb00101.propTorokDate.disabled}"
													value="#{pc_Xrb00101.propTorokDate.dateValue}">
													<f:convertDateTime />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													</h:inputText>
												</TD>
												<TH align="left" valign="middle" class="v_e" nowrap width="150">
													<h:outputText styleClass="outputText"
													id="lblHenkoKbn"
													style="#{pc_Xrb00101.propHenkoKbn.labelStyle}"
													value="#{pc_Xrb00101.propHenkoKbn.labelName}">
													</h:outputText>
												</TH>
												<TD width="200">
													<h:selectOneMenu styleClass="selectOneMenu"
													id="htmlHenkoKbn"
													value="#{pc_Xrb00101.propHenkoKbn.stringValue}"
													style="#{pc_Xrb00101.propHenkoKbn.style};width:140px"
													disabled="#{pc_Xrb00101.propHenkoKbn.disabled}"
													onchange="return onChangeHenkoKbn();">
 													<f:selectItems value="#{pc_Xrb00101.propHenkoKbn.list}" />
													</h:selectOneMenu>
												</TD>
											</TR>
											<TR>
												<TH class="v_a" width="150">
													<h:outputText
													styleClass="outputText"
													id="lblKamokCdNew"
													value="#{pc_Xrb00101.propKamokCdNew.labelName}"
													style="#{pc_Xrb00101.propKamokCdNew.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="550" colspan="3">
													<h:inputText styleClass="inputText"
													id="htmlKamokCdNew"
													value="#{pc_Xrb00101.propKamokCdNew.stringValue}"
													style="#{pc_Xrb00101.propKamokCdNew.style}"
													maxlength="#{pc_Xrb00101.propKamokCdNew.maxLength}" size="10"
													onblur="return doKamokuAjax(this, event, 'form1:lblKamokNameNew');"
													disabled="#{pc_Xrb00101.propKamokCdNew.disabled}">
							                        <hx:inputHelperAssist errorClass="inputText_Error"
								                    imeMode="inactive" promptCharacter="_" />
													</h:inputText>
													<hx:commandExButton type="button" value=""
													styleClass="commandExButton_search" id="searchNew"
													onclick="return openKamokuSubWindow('form1:htmlKamokCdNew');"
													disabled="#{pc_Xrb00101.propSearchButtonNew.disabled}">
													</hx:commandExButton>
													<h:outputText styleClass="outputText" id="lblKamokNameNew" value="#{pc_Xrb00101.propKamokNameNew.stringValue}"></h:outputText>
												</TD>
											</TR>
											<TR>
												<TH align="left" valign="middle" class="v_f" nowrap width="150">
													<h:outputText styleClass="outputText"
													id="lblHenkoDate"
													style="#{pc_Xrb00101.propHenkoDate.labelStyle}"
													value="#{pc_Xrb00101.propHenkoDate.labelName}">
													</h:outputText>
												</TH>
												<TD align="left" valign="middle" width="200">
													<h:inputText styleClass="inputText"
													id="htmlHenkoDate" size="12"
													disabled="#{pc_Xrb00101.propHenkoDate.disabled}"
													value="#{pc_Xrb00101.propHenkoDate.dateValue}">
													<f:convertDateTime />
													<hx:inputHelperDatePicker />
													<hx:inputHelperAssist errorClass="inputText_Error"
														promptCharacter="_" />
													</h:inputText>
												</TD>
												<TH width="150" class="v_d">
													<h:outputText styleClass="outputText"
													id="lblHaihonTyusyutuFlg"
													value="#{pc_Xrb00101.propHaihonTyusyutuFlg.labelName}"
													style="#{pc_Xrb00101.propHaihonTyusyutuFlg.labelStyle}">
													</h:outputText>
												</TH>
												<TD width="300">
													<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
													id="htmlHaihonTyusyutuFlg"
													value="#{pc_Xrb00101.propHaihonTyusyutuFlg.checked}"
													style="#{pc_Xrb00101.propHaihonTyusyutuFlg.style}"
													disabled="#{pc_Xrb00101.propHaihonTyusyutuFlg.disabled}">
													</h:selectBooleanCheckbox>
												</TD>
											</TR>
											<TR>
												<TH align="left" valign="middle" class="v_f" nowrap width="150">
													<h:outputText styleClass="outputText"
													id="lblFurikomiIraiCd" style="#{pc_Xrb00101.propFurikomiIraiCd.labelStyle}" value="#{pc_Xrb00101.propFurikomiIraiCd.name}"/>
												</TH>
												<TD width="550" colspan="3">
													<h:outputText styleClass="outputText" id="htmlFurikomiIraiCd" value="#{pc_Xrb00101.propFurikomiIraiCd.stringValue}"/>
												</TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
									<TD width="100" align="left">
										<hx:commandExButton type="submit"
										id="register"
										value="登録" styleClass="commandExButton"
										action="#{pc_Xrb00101.doRegisterAction}"
										onclick="return setSelectButtonFlg(this, event);"
										disabled="#{pc_Xrb00101.propKamokCd.disabled}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center" height="5px">
						</TD>
					</TR>
					<TR>
						<TD align="center">
							<TABLE width="900" border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD align="left">
										<h:outputText styleClass="outputText" id="text9" value="履修登録エラー情報一覧"></h:outputText>
									</TD>
									<TD align="right">
										<h:outputFormat styleClass="outputFormat" id="format1" value=" {0}件">
										<f:param id="param2" name="msg1" value="#{pc_Xrb00101.propRsyuErrorList.listCount}"></f:param>
										</h:outputFormat>
									</TD>
								</TR>
								<TR>
									<TD colspan="2">
										<DIV class="listScroll"
										onscroll="setScrollPosition('scroll2',this)"
										style="height:80px;width:900px;"
										id="listScroll2">
										<h:dataTable border="0" cellpadding="0" cellspacing="0"
										headerClass="headerClass" 
										footerClass="footerClass" styleClass="meisai_scroll" 
										id="htmlRsyuErrorList" var="varlist"
										value="#{pc_Xrb00101.propRsyuErrorList.list}"
										rowClasses="#{pc_Xrb00101.propRsyuErrorList.rowClasses}"
										width="880"
										columnClasses="columnClass1">
										<h:column id="column16">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="エラー"
												id="lblErrorList2"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlErrorList2"
												value="#{varlist.error}"></h:outputText>
											<f:attribute value="60" name="width" />
										</h:column>
										<h:column id="column20">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="科目コード"
												id="lblKamokCdList2"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlKamokCdList2"
												value="#{varlist.kamokCd}"></h:outputText>
											<f:attribute value="100" name="width" />
										</h:column>
										<h:column id="column24">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="エラーコード"
												id="lblErrorCdList"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText" id="htmlErrorCdList"
												value="#{varlist.errorCd}"></h:outputText>
											<f:attribute value="100" name="width" />
										</h:column>
										<h:column id="column25">
											<f:facet name="header">
												<h:outputText styleClass="outputText" value="エラーメッセージ" 
												id="lblErrorMessageList"></h:outputText>
											</f:facet>
											<h:outputText styleClass="outputText"
												id="htmlErrorMessageList" value="#{varlist.errorMessage.displayValue}"
												title="#{varlist.errorMessage.value}"></h:outputText>
											<f:attribute value="620" name="width" />
										</h:column>
										</h:dataTable>
										</DIV>
									</TD>
								</TR>
							</TBODY>
							</TABLE>
						</TD>
					</TR>
					<TR>
						<TD height="5px"></TD>
					</TR>
					<TR>
						<TD align="center">
							<TABLE width="900" border="0" cellpadding="0" cellspacing="0" class="table">
							<TBODY>
								<TR>
									<TH class="v_d" width="150">
										<h:outputText styleClass="outputText"
										id="lblRisyuChk"
										value="#{pc_Xrb00101.propRisyuChk.labelName}"
										style="#{pc_Xrb00101.propRisyuChk.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="750">
										<h:selectOneRadio styleClass="selectOneRadio"
										id="htmlRisyuChk"
										disabledClass="selectOneRadio_Disabled"
										value="#{pc_Xrb00101.propRisyuChk.stringValue}"
										style="#{pc_Xrb00101.propRisyuChk.style}"
										disabled="#{pc_Xrb00101.propRisyuChk.disabled}">
										<f:selectItem itemValue="0" itemLabel="行う" />
										<f:selectItem itemValue="1" itemLabel="行わない" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center">
							<TABLE border="0" cellpadding="0" cellspacing="0" width="900" class="button_bar">
							<TBODY>
								<TR>
									<TD>
										<hx:commandExButton type="submit"
										id="fix" value="確定"
										styleClass="commandExButton_dat"
										action="#{pc_Xrb00101.doFixAction}"
										confirm="#{msg.SY_MSG_0003W}"
										disabled="#{!pc_Xrb00101.propGakusekiCd.disabled}">
										</hx:commandExButton>
										<hx:commandExButton type="submit"
										id="clear" value="クリア"
										styleClass="commandExButton_etc"
										onclick="return setButtonFlgClear(this, event);"
										action="#{pc_Xrb00101.doClear2Action}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
							</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrb00101.propRisyuTorokList.scrollPosition}" id="scroll"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrb00101.propRsyuErrorList.scrollPosition}" id="scroll2"></h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrb00101.propExecutableWarning.integerValue}"
				id="htmlExecutableWarning">
				<f:convertNumber type="number" />
			</h:inputHidden>
			<h:inputHidden
				value="#{pc_Xrb00101.propExecutableWarning2.integerValue}"
				id="htmlExecutableWarning2">
				<f:convertNumber type="number" />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00101.propSelectButtonFlg.stringValue}"
				id="htmlSelectButtonFlg">
				
			</h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00101.propWarningCount.integerValue}"
				id="htmlWarningCount">
				<f:convertNumber type="number" />
			</h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00101.propIsSelect.stringValue}"
				id="isSelect">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Xrb00101.propConfirmValue.integerValue}"
				id="htmlConfirmValue">
				<f:convertNumber type="number" />
			</h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

