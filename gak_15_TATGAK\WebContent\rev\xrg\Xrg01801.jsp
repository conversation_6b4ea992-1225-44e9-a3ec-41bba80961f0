<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg01801.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg01801.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">


<SCRIPT type="text/javascript">
// 教員検索アイコン押下時処理
function openKyoinCdSearchWindow(thisObj, thisEvent) {
	
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlKyoinCd";
	openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return true;
}

// 教員氏名を取得する
function getJinjiName(thisObj, thisEvent) {
	var servlet = "rev/co/CobJinjAJAX";
	var target = "form1:htmlKyoinName";
	var code = thisObj.value;
	getCodeName(servlet, target, code );
}

var schSbtCd = "";
function getSchoolingSbtCb() {
	// スクーリング種別コンボボックス取得AJAX
	var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
	var args = new Array();
	args['nendo'] = document.getElementById('form1:htmlNendo').value;
	args['bunrui'] = "1";
	args['upperKakuteiKbn'] = "30";
	var target = "";
	
	comb = document.getElementById('form1:htmlSchooling');
	schSbtCd = comb.options[comb.selectedIndex].value;
	
	var ajaxUtil = new AjaxUtil();
	ajaxUtil.getPluralValue(servlet, target, args);
}

function callBackMethod(value){
	var comb = document.getElementById('form1:htmlSchooling');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
}

function func_1(thisObj, thisEvent) {
	check('htmlTargetOutputList','htmlSelected');
}
function func_2(thisObj, thisEvent) {
	uncheck('htmlTargetOutputList','htmlSelected');
}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg01801.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg01801.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg01801.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg01801.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 --> <!-- ↑ここに戻るボタンを配置 -->
			</DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="780">
				<TBODY>
					<TR>
						<TD>
						<TABLE class="table" width="100%">
							<TBODY>
								<TR>
									<TH class="v_b" width="200px"><h:outputText
										styleClass="outputText" id="lblShoriKbn"
										value="#{pc_Xrg01801.propShoriKbn.labelName}"
										style="#{pc_Xrg01801.propShoriKbn.labelStyle}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlShoriKbn" value="#{pc_Xrg01801.propShoriKbn.value}"
										tabindex="1" style="width: 200px; #{pc_Xrg01801.propShoriKbn.style}"
										readonly="#{pc_Xrg01801.propShoriKbn.readonly}"
										disabled="#{pc_Xrg01801.propShoriKbn.disabled}">
										<f:selectItems value="#{pc_Xrg01801.propShoriKbn.list}" />
									</h:selectOneMenu></TD>
									<TD rowspan="5" width="120"
										style="background-color: transparent;border-bottom-style: none; border-top-style: none; border-right-style: none; text-align: right">
									<hx:commandExButton type="submit" value="選択"
										styleClass="commandExButton" id="select" tabindex="8"
										action="#{pc_Xrg01801.doSelectAction}"
										disabled="#{pc_Xrg01801.propSelect.disabled}"
										style="#{pc_Xrg01801.propSelect.style}">
									</hx:commandExButton> <hx:commandExButton type="submit"
										value="解除" tabindex="9" styleClass="commandExButton"
										id="unselect" action="#{pc_Xrg01801.doUnselectAction}"
										disabled="#{pc_Xrg01801.propUnselect.disabled}"
										style="#{pc_Xrg01801.propUnselect.style}">
									</hx:commandExButton></TD>
								</TR>
								<TR>
									<TH class="v_b" width="200px"><h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Xrg01801.propNendo.labelName}"
										style="#{pc_Xrg01801.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlNendo" tabindex="2"
										value="#{pc_Xrg01801.propNendo.dateValue}"
										disabled="#{pc_Xrg01801.propNendo.disabled}"
										style="#{pc_Xrg01801.propNendo.style}" size="10"
										onblur="getSchoolingSbtCb();">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="200px"><h:outputText
										styleClass="outputText" id="lblSchooling"
										value="#{pc_Xrg01801.propSchooling.labelName}"
										style="#{pc_Xrg01801.propSchooling.labelStyle}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSchooling"
										value="#{pc_Xrg01801.propSchooling.value}" tabindex="3"
										style="width: 200px; #{pc_Xrg01801.propSchooling.style}"
										disabled="#{pc_Xrg01801.propSchooling.disabled}">
										<f:selectItems value="#{pc_Xrg01801.propSchooling.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_b" width="200"><h:outputText
										styleClass="outputText" id="lblKaisaiki"
										value="#{pc_Xrg01801.propKaisaiki.labelName}"
										style="#{pc_Xrg01801.propKaisaiki.labelStyle}">
									</h:outputText></TH>
									<TD width="400"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlKaisaiki" value="#{pc_Xrg01801.propKaisaiki.value}"
										tabindex="4" style="width: 200px; #{pc_Xrg01801.propKaisaiki.style}"
										disabled="#{pc_Xrg01801.propKaisaiki.disabled}">
										<f:selectItems value="#{pc_Xrg01801.propKaisaiki.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_a" width="200px"><h:outputText
										styleClass="outputText" id="lblTargetOutput"
										value="表示対象（レポート評価）">
									</h:outputText></TH>
									<TD><h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox" id="htmlTargetOutputGokaku"
										tabindex="5"
										value="#{pc_Xrg01801.propTargetOutputGokaku.checked}"
										disabled="#{pc_Xrg01801.propTargetOutputGokaku.disabled}"
										style="#{pc_Xrg01801.propTargetOutputGokaku.style}">
									</h:selectBooleanCheckbox> 合格 <h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox"
										id="htmlTargetOutputFugokaku" tabindex="6"
										value="#{pc_Xrg01801.propTargetOutputFugokaku.checked}"
										disabled="#{pc_Xrg01801.propTargetOutputFugokaku.disabled}"
										style="#{pc_Xrg01801.propTargetOutputFugokaku.style}">
									</h:selectBooleanCheckbox> 不合格 <h:selectBooleanCheckbox
										styleClass="selectBooleanCheckbox"
										id="htmlTargetOutputMitoroku" tabindex="7"
										value="#{pc_Xrg01801.propTargetOutputMitoroku.checked}"
										disabled="#{pc_Xrg01801.propTargetOutputMitoroku.disabled}"
										style="#{pc_Xrg01801.propTargetOutputMitoroku.style}">
									</h:selectBooleanCheckbox> 未登録</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="right"><h:outputText styleClass="outputText"
							id="htmlCount"
							value="#{pc_Xrg01801.propTargetOutputList.listCount}">
						</h:outputText> <h:outputText styleClass="outputText" id="text1"
							value="件" >
						</h:outputText></TD>
					</TR>
					<TR>
						<TD>
						<DIV style="height:220px" class="listScroll" id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><h:dataTable
							border="0" cellpadding="0" cellspacing="0"
							columnClasses="columnClass" headerClass="headerClass"
							footerClass="footerClass"
							rowClasses="#{pc_Xrg01801.propTargetOutputList.rowClasses}"
							styleClass="meisai_scroll" id="htmlTargetOutputList" width="760px"
							value="#{pc_Xrg01801.propTargetOutputList.list}" var="varlist">
							<h:column id="column01">
								<f:facet name="header">
								</f:facet>
								<hx:jspPanel rendered="#{varlist.selected.disabled == false}">
									<h:selectBooleanCheckbox tabindex="10"
										styleClass="selectBooleanCheckbox" id="htmlSelected"
										value="#{varlist.selected.checked}">
									</h:selectBooleanCheckbox>
								</hx:jspPanel>
								<f:attribute value="20" name="width" />
								<f:attribute value="center" name="align" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column02">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学籍番号"
										id="lblGakusekiNoColumn">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlGakusekiNoColumn"
									value="#{varlist.gakusekiNo}" styleClass="outputText">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column03">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblNameColumn"
										value="学生氏名" styleClass="outputText">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlGakuseiNameColumn"
									value="#{varlist.gakuseiName.displayValue}"
									title="#{varlist.gakuseiName.value}" styleClass="outputText">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column04">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblKamokuCdColumn"
										value="科目コード">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlKamokuCdColumn"
									value="#{varlist.kamokuCd}" styleClass="outputText">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="70" name="width" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column05">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblKamokuNameColumn"
										value="科目名">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlKamokuNameColumn"
									value="#{varlist.kamokuName.displayValue}"
									title="#{varlist.kamokuName.value}" styleClass="outputText">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="text-align: left" name="style" />
							</h:column>
							<h:column id="column06">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblUketsukebiColumn"
										value="受付日">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlHakkobiColumn"
									value="#{varlist.uketsukebi}" styleClass="outputText">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="80" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column07">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblReportHyokaColumn"
										value="レポート評価">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlReportHyokaColumn"
									value="#{varlist.reportHyoka}" styleClass="outputText">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
							<h:column id="column08">
								<f:facet name="header">
									<h:outputText styleClass="outputText" id="lblSchoolingHyokaColumn"
										value="スクーリング評価">
									</h:outputText>
								</f:facet>
								<h:outputText id="htmlSchoolingHyokaColumn"
									value="#{varlist.schoolingHyoka}" styleClass="outputText">
								</h:outputText>
								<f:attribute value="true" name="nowrap" />
								<f:attribute value="100" name="width" />
								<f:attribute value="text-align: center" name="style" />
							</h:column>
						</h:dataTable></DIV>
						</TD>
					</TR>
					<TR>
						<TD align="left"><hx:commandExButton type="button"
							styleClass="check" id="check"
							onclick="return func_1(this, event);" tabindex="11">
						</hx:commandExButton> <hx:commandExButton type="button"
							styleClass="uncheck" id="uncheck"
							onclick="return func_2(this, event);" tabindex="12">
						</hx:commandExButton></TD>
					</TR>
					<TR>
						<TD valign="top">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="100%">
							<TBODY>
								<TR>
									<TH class="v_a" width="200px"><h:outputText
										styleClass="outputText" id="lblReportUketsukebi"
										value="#{pc_Xrg01801.propReportUketsukebi.labelName}"
										style="#{pc_Xrg01801.propReportUketsukebi.labelStyle}">
									</h:outputText></TH>
									<TD><h:inputText id="htmlReportUketsukebi"
										styleClass="inputText" tabindex="13"
										value="#{pc_Xrg01801.propReportUketsukebi.dateValue}"
										disabled="#{pc_Xrg01801.propReportUketsukebi.disabled}"
										size="12">
										<f:convertDateTime />
										<hx:inputHelperDatePicker styleClass="width:100px;" />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="200px"><h:outputText
										styleClass="outputText" id="lblReportHyoka"
										value="#{pc_Xrg01801.propReportHyoka.labelName}"
										style="#{pc_Xrg01801.propReportHyoka.labelStyle}">
									</h:outputText></TH>
									<TD><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlReportHyoka"
										value="#{pc_Xrg01801.propReportHyoka.value}" tabindex="14"
										style="width: 200px; #{pc_Xrg01801.propReportHyoka.style}"
										disabled="#{pc_Xrg01801.propReportHyoka.disabled}">
										<f:selectItems value="#{pc_Xrg01801.propReportHyoka.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_c" width="200px"><h:outputText
										styleClass="outputText" id="lblKyoinCd"
										value="#{pc_Xrg01801.propKyoinCd.labelName}"
										style="#{pc_Xrg01801.propKyoinCd.labelStyle}">
									</h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlKyoinCd" tabindex="15"
										value="#{pc_Xrg01801.propKyoinCd.stringValue}"
										style="#{pc_Xrg01801.propKyoinCd.style}"
										disabled="#{pc_Xrg01801.propKyoinCd.disabled}"
										maxlength="#{pc_Xrg01801.propKyoinCd.maxLength}" size="20"
										onblur="return getJinjiName(this, event);">
									</h:inputText> <hx:commandExButton type="button"
										styleClass="commandExButton_search" id="searchKyoinCd"
										onclick="openKyoinCdSearchWindow(this, event);" tabindex="16"
										disabled="#{pc_Xrg01801.propSearchKyoinCd.disabled}"
										style="#{pc_Xrg01801.propSearchKyoinCd.style}">
									</hx:commandExButton> <h:outputText styleClass="outputText"
										id="htmlKyoinName" value="#{pc_Xrg01801.propKyoinName.value}"
										style="#{pc_Xrg01801.propKyoinName.style}">
									</h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register" tabindex="17"
							action="#{pc_Xrg01801.doRegisterAction}"
							disabled="#{pc_Xrg01801.propRegister.disabled}"
							confirm="#{msg.SY_MSG_0002W}" 
							style="#{pc_Xrg01801.propRegister.style}">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			</DIV>
			</DIV>
			</DIV>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
