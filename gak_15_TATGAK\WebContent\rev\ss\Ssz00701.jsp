<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function confirmOk() {

	document.getElementById('form1:htmlConfirmVal').value = "1";

	 indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlConfirmVal').value = "0";
	return false;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz00701.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz00701.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz00701.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz00701.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --><TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="50%">
						<TABLE class="table" width="100%">
							<TBODY>
								<TR>
									<TH class="v_a" width="170"><h:outputText
										styleClass="outputText" id="lblNensyuKaisoNo1"
										value="#{pc_Ssz00701.propNensyuKaisoNo1.labelName}"
										style="#{pc_Ssz00701.propNensyuKaisoNo1.labelStyle}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo1"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo1.maxLength}"
										size="20"
										value="#{pc_Ssz00701.propNensyuKaisoNo1.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo1.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text3"
										value=" 千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo2.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo2"
										value="#{pc_Ssz00701.propNensyuKaisoNo2.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"										
										id="htmlNensyuKaisoNo2"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo2.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo2.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo2.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text6" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo3.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo3"
										value="#{pc_Ssz00701.propNensyuKaisoNo3.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo3"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo3.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo3.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo3.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text9" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo4.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo4"
										value="#{pc_Ssz00701.propNensyuKaisoNo4.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo4"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo4.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo4.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo4.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text12" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_e" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo5.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo5"
										value="#{pc_Ssz00701.propNensyuKaisoNo5.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo5"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo5.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo5.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo5.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text15" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_f" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo6.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo6"
										value="#{pc_Ssz00701.propNensyuKaisoNo6.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo6"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo6.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo6.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo6.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text18" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_g" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo7.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo7"
										value="#{pc_Ssz00701.propNensyuKaisoNo7.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo7"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo7.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo7.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo7.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text21" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo8.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo8"
										value="#{pc_Ssz00701.propNensyuKaisoNo8.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo8"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo8.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo8.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo8.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text24" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_b" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo9.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo9"
										value="#{pc_Ssz00701.propNensyuKaisoNo9.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo9"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo9.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo9.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo9.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text27" value="千円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH class="v_c" width="170"><h:outputText
										style="#{pc_Ssz00701.propNensyuKaisoNo10.labelStyle}"styleClass="outputText" id="lblNensyuKaisoNo10"
										value="#{pc_Ssz00701.propNensyuKaisoNo10.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlNensyuKaisoNo10"
										maxlength="#{pc_Ssz00701.propNensyuKaisoNo10.maxLength}"
										size="20" value="#{pc_Ssz00701.propNensyuKaisoNo10.stringValue}"
										style="#{pc_Ssz00701.propNensyuKaisoNo10.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text30" value="千円未満"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD width="50%">
						<TABLE class="table" width="100%">
							<TBODY>
								<TR>
									<TH class="v_d" width="170"><h:outputText
										styleClass="outputText" id="lblDefValuetext31"
										value="#{pc_Ssz00701.propDefValue.labelName}"></h:outputText></TH>
									<TD width="320"><h:inputText styleClass="inputText"
										id="htmlDefValue"
										value="#{pc_Ssz00701.propDefValue.stringValue}"
										maxlength="#{pc_Ssz00701.propDefValue.maxLength}" size="21"
										style="#{pc_Ssz00701.propDefValue.style}"></h:inputText>
										<h:outputText styleClass="outputText"
										id="text33" value="(標準は「３０歳時」です）"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="25%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD align="center"><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz00701.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			
			<!-- ↑ここにコンポーネントを配置 --><!--↑content↑--></DIV>
			</DIV>
			</DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ssz00701.propConfirmVal.stringValue}" id="htmlConfirmVal"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

