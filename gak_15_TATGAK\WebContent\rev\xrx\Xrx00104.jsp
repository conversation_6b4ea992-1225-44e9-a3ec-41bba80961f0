<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrx/Xrx00104.java" --%><%-- /jsf:pagecode --%>
<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%-- タグライブラリ --%>
<%@taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>

<%-- スタイルシート --%>
<style type="text/css">
<!--

.tableStyle {
    border-collapse: collapse !important;
    border:0px !important;
}

.courseYearBigHeaderStyle {
    width: 80px !important;  
    border-top-style:none !important; 
    border-bottom-style: solid !important; 
    border-right-style: none !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.courseYearBigHeaderReportStyle {
    width: 72px !important;  
    border-top-style:none !important; 
    border-bottom-style: solid !important; 
    border-right-style: none !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.courseYearSmallHeaderStyle, .courseYearDataStyle {
    width: 20px !important;  
    border-top-style:none !important; 
    border-bottom-style: none !important; 
    border-right-style: solid !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.courseYearSmallHeaderReportStyle, .courseYearDataReportStyle {
    width: 18px !important;  
    border-top-style:none !important; 
    border-bottom-style: none !important; 
    border-right-style: solid !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.courseYearLastCulomnSmallHeaderStyle, .courseYearLastCulomnDataStyle {
    width: 20px !important;  
    border-top-style:none !important; 
    border-bottom-style: none !important; 
    border-right-style: none !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.courseYearLastCulomnSmallHeaderReportStyle, .courseYearLastCulomnDataReportStyle {
    width: 18px !important;  
    border-top-style:none !important; 
    border-bottom-style: none !important; 
    border-right-style: none !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.memo1DataStyle {
    width: 53px !important;
    border-top-style:none !important; 
    border-bottom-style: none !important; 
    border-right-style: solid !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.memo2DataStyle {
    width: 45px !important;
    border-top-style:none !important; 
    border-bottom-style: none !important; 
    border-right-style: none !important; 
    border-left-style: none !important;
    text-align: center !important; 
}

.total_label {
	width: 252px;
	border-left-style: none !important;
	border-top-style: none !important;
	border-bottom-style: none !important;
	text-align: center !important;
}
.total_top_group_a {
	width:31px;
	border-top-style: none !important;
	border-bottom-style: none !important;
	text-align: center !important;
}
.total_top_group_b {
    width: 20px !important;  
    border-top-style:none !important; 
    border-bottom-style: none !important; 
    text-align: center !important; 

}
.total_top_group_c {
	width:75px;
	border-top-style: none !important;
	border-bottom-style: none !important;
	text-align: center !important;
}
.total_bottom {
    width: 20px !important;  
	border-bottom-style: none !important;
	text-align: center !important;
}
.total_brank {
	width:294px;
	border-top-style: none !important;
  border-right-style: none !important; 
	border-bottom-style: none !important;
}

-->
</style>

<f:subview id="Xrx00104">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrx00104.onPageLoadBegin}">
    <%-- ↓ コンテンツ部 ↓ --%>
    <hx:jspPanel>
    <DIV style="width:940px">

    <%-- 履修状況表 --%>
    <TABLE width="100%" class="tableStyle">	
    <TR><TD>
    <DIV id="listScroll" class="listScroll" style="height:338px;" onscroll="setScrollPosition('scroll', this)">

        <h:dataTable id="risyuList" value="#{pc_Xrx00104.propRisyuList.list}"  var="varlist"
            border="0" cellpadding="2" cellspacing="0" 
            columnClasses="columnClass1" headerClass="headerClass"  footerClass="footerClass" styleClass="meisai_scroll" 
            rowClasses="#{pc_Xrx00104.propRisyuList.rowClasses}">

            <%-- 科目コード --%>
            <h:column id="colKmkCd">
                <f:facet name="header">
                    <h:outputText  id="headKmkCd" value="科目<br>&nbsp;コード" styleClass="outputText" escape="false"/>
                </f:facet>
                <h:commandLink id="linkKmkCd" styleClass="commandLink" action="#{pc_Xrx00104.doLinkAction}">
                    <h:outputText id="bodyKmkCd" styleClass="outputText" value="#{varlist.kamokCd}"/>
                </h:commandLink>
                <f:attribute name="width" value="47px"/>
                <f:attribute name="style" value="text-align:center;"/>
            </h:column>

            <%-- 科目名 --%>
            <h:column id="colKmkName">
                <f:facet name="header">
                    <h:outputText  id="headKmkName" value="科目名" styleClass="outputText"/>
                </f:facet>
                <h:outputText id="bodyKmkName" styleClass="outputText" value="#{varlist.kamokNameShryk}" title="#{varlist.kamokName}" escape="false"/>
                <f:attribute name="width" value="204px"/>
                <f:attribute name="style" value="text-align: left;"/>
            </h:column>

            <%-- 他大認定 --%>
            <h:column id="colOtherCertification">
                <f:facet name="header">
                    <h:outputText  id="headOtherCertification" value="他大<br>&nbsp;認定" styleClass="outputText" escape="false"/>
                </f:facet>
                <h:outputText id="bodyOtherCertification" styleClass="outputText" value="#{varlist.otherCertification}"/>
                <f:attribute name="width" value="31px"/>
                <f:attribute name="style" value="text-align: center;"/>
            </h:column>

            <%-- 本学認定 --%>
            <h:column id="colMainCertification">
                <f:facet name="header">
                    <h:outputText  id="headMainCertification" value="本学<br>&nbsp;認定" styleClass="outputText" escape="false"/>
                    <f:attribute name="style" value="text-align: center;"/>
                </f:facet>
                <h:outputText id="bodyMainCertification" styleClass="outputText" value="#{varlist.mainCertification}"/>
                <f:attribute name="width" value="31px"/>
                <f:attribute name="style" value="text-align: center;"/>
            </h:column>

            <%-- 登録単位 --%>
            <h:column id="colEntryUtni">
                <f:facet name="header">
                    <h:outputText  id="headEntryUtni" value="登録<br>&nbsp;単位 " styleClass="outputText" escape="false"/>
                </f:facet>
                <h:outputText id="bodyEntryUtni" styleClass="outputText" value="#{varlist.entryUnit}"/>
                <f:attribute name="width" value="31px"/>
                <f:attribute name="style" value="text-align: center;"/>
            </h:column>

            <%-- 修得単位 --%>
            <h:column id="colAcquisition">
                <f:facet name="header">
                    <h:outputText  id="headAcquisition" value="修得<br>&nbsp;単位" styleClass="outputText" escape="false"/>
                </f:facet>
                <h:outputText id="bodyAcquisition" styleClass="outputText" value="#{varlist.acquisition}"/>
                <f:attribute name="width" value="31px"/>
                <f:attribute name="style" value="text-align: center; border-right-style: none;"/>
            </h:column>

            <%-- テキスト履修年次 --%>
            <h:column id="colTextCourseYear">
                <f:facet name="header">
                    <hx:jspPanel id="panelTextCourseYer">
                    <TABLE class="tableStyle">
                        <TBODY>
                            <TR>
                                <TH colspan="4" class="courseYearBigHeaderStyle">
                                    <h:outputText  id="headTextCourseYear" value="テキスト<br>履修年次"  styleClass="outputText"  escape="false"/>
                                </TH>
                            </TR>
                            <TR>
                                <TH class="courseYearSmallHeaderStyle">
                                    <h:outputText id="headTextCourseYear1" value="1" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearSmallHeaderStyle">
                                    <h:outputText id="headTextCourseYear2" value="2" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearSmallHeaderStyle">
                                    <h:outputText id="headTextCourseYear3" value="3" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearLastCulomnSmallHeaderStyle">
                                    <h:outputText id="headTextCourseYear4" value="4" styleClass="outputText"/>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE>
                    </hx:jspPanel>
                </f:facet>
                <f:attribute name="style" value="text-align: center;"/>
                
                <hx:jspPanel id="panelTextCourseYer2">
                <TABLE class="tableStyle">
                    <TBODY>
                        <TR>
	                        <TD class="courseYearDataStyle">
	                            <h:outputText id="bocyTextCourseYear1" value="#{varlist.textCourseYear1}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearDataStyle">
	                            <h:outputText id="bodyTextCourseYear2" value="#{varlist.textCourseYear2}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearDataStyle">
	                            <h:outputText id="bodyTextCourseYear3" value="#{varlist.textCourseYear3}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearLastCulomnDataStyle">
	                            <h:outputText id="bodyTextCourseYear4" value="#{varlist.textCourseYear4}" styleClass="outputText"/>
	                        </TD>
                            </TR>
                        </TBODY>
                    </TABLE>
                </hx:jspPanel>
            </h:column>

            <%-- スクーリング履修年次 --%>
            <h:column id="colSchlngCourseYear">
                <f:facet name="header">
                    <hx:jspPanel id="panelSchlngCourseYear">
                    <TABLE class="tableStyle">
                        <TBODY>
                            <TR>
                                <TH colspan="4" class="courseYearBigHeaderStyle">
                                    <h:outputText  id="headSchlngCourseYear" value="スクーリング<br>履修年次"  styleClass="outputText" escape="false"/>
                                    <f:attribute name="style" value="text-align: center;"/>
                                </TH>
                            </TR>
                            <TR>
                                <TH class="courseYearSmallHeaderStyle">
                                    <h:outputText id="headSchlngCourseYear1" value="1" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearSmallHeaderStyle">
                                    <h:outputText id="headSchlngCourseYear2" value="2" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearSmallHeaderStyle">
                                    <h:outputText id="headSchlngCourseYear3" value="3" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearLastCulomnSmallHeaderStyle">
                                    <h:outputText id="headSchlngCourseYear4" value="4" styleClass="outputText"/>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE>
                    </hx:jspPanel>
                </f:facet>
                <f:attribute name="style" value="text-align: center;"/>
                
                <hx:jspPanel id="panelSchlngCourseYear2">
                <TABLE class="tableStyle">
	                <TBODY>
	                    <TR>
	                        <TD class="courseYearDataStyle">
	                            <h:outputText id="bodySchlngCourseYear1" value="#{varlist.schlngCourseYear1}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearDataStyle">
	                            <h:outputText id="bodySchlngCourseYear2" value="#{varlist.schlngCourseYear2}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearDataStyle">
	                            <h:outputText id="bodySchlngCourseYear3" value="#{varlist.schlngCourseYear3}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearLastCulomnDataStyle">
	                            <h:outputText id="bodySchlngCourseYear4" value="#{varlist.schlngCourseYear4}" styleClass="outputText"/>
	                        </TD>
	                    </TR>
	                </TBODY>
                </TABLE>
                </hx:jspPanel>
            </h:column>

            <%-- レポート分冊 --%>
            <h:column id="colReportSeparateVolume">
                <f:facet name="header">
                    <hx:jspPanel id="panelReportSeparateVolume">
                    <TABLE class="tableStyle">
                        <TBODY>
                            <TR>
                                <TH colspan="4" class="courseYearBigHeaderReportStyle">
                                    <h:outputText  id="headReportSeparateVolume" value="レポート<br>分冊"  styleClass="outputText" escape="false"/>
                                    <f:attribute name="style" value="text-align: center"/>
                                </TH>
                            </TR>
                            <TR>
                                <TH class="courseYearSmallHeaderReportStyle">
                                    <h:outputText id="headReportSeparateVolume1" value="1" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearSmallHeaderReportStyle">
                                    <h:outputText id="headReportSeparateVolume2" value="2" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearSmallHeaderReportStyle">
                                    <h:outputText id="headReportSeparateVolume3" value="3" styleClass="outputText"/>
                                </TH>
                                <TH class="courseYearLastCulomnSmallHeaderReportStyle">
                                    <h:outputText id="headReportSeparateVolume4" value="4" styleClass="outputText"/>
                                </TH>
                            </TR>
                        </TBODY>
                    </TABLE>
                    </hx:jspPanel>
                </f:facet>
                <f:attribute name="style" value="text-align: center;"/>               

                <hx:jspPanel id="panelSchlngCourseYear3">
                <TABLE class="tableStyle">
	                <TBODY>
	                    <TR>
	                        <TD class="courseYearDataReportStyle">
	                            <h:outputText id="outputText" value="#{varlist.reportSeparateVolume1}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearDataReportStyle">
	                            <h:outputText id="bodyReportSeparateVolume2" value="#{varlist.reportSeparateVolume2}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearDataReportStyle">
	                            <h:outputText id="bodyReportSeparateVolume3" value="#{varlist.reportSeparateVolume3}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="courseYearLastCulomnDataReportStyle">
	                           <h:outputText id="bodyReportSeparateVolume4" value="#{varlist.reportSeparateVolume4}" styleClass="outputText"/>
	                        </TD>
	                    </TR>
	                </TBODY>
                </TABLE>
                </hx:jspPanel>
            </h:column>

            <%-- 評価 --%>
            <h:column id="colEvaluation">
                <f:facet name="header">
                    <h:outputText  id="headEvaluation" value="評価" styleClass="outputText"/>
                </f:facet>
                <h:outputText id="bodyEvaluation" value="#{varlist.evaluation}" style="#{varlist.evaluationColor}" styleClass="outputText"/>
                <f:attribute name="width" value="32px"/>
                <f:attribute name="style" value="text-align: center;"/>
            </h:column>

            <%-- 最終試験日 --%>
            <h:column id="colLastExaminationDate">
                <f:facet name="header">
                     <h:outputText id="headLastExaminationDate" value="最終<br>試験日" styleClass="outputText" escape="false"/>
                </f:facet>
                <h:outputText id="bodyLastExaminationDate" styleClass="outputText" value="#{varlist.lastExaminationDate}"/>
                <f:attribute name="width" value="53px"/>
                <f:attribute name="style" value="text-align: center;"/>
            </h:column>

            <%-- 登録日 --%>
            <h:column id="colEntryDate">
                <f:facet name="header">
                    <h:outputText  id="headEntryDate" value="登録日" styleClass="outputText"/>
                </f:facet>
                <h:outputText id="bodyEntryDate" styleClass="outputText" value="#{varlist.entryDate}"/>
                <f:attribute name="width" value="53px"/>
                <f:attribute name="style" value="text-align: center;"/>
            </h:column>

            <%-- 配本日 --%>
            <h:column id="colHaihonDate">
                <f:facet name="header">
                    <h:outputText  id="headHaihonDate" value="配本日" styleClass="outputText"/>
                </f:facet>
                <h:outputText id="bodyHaihonDate" styleClass="outputText" value="#{varlist.haihonDate}"/>
                <f:attribute name="width" value="53px"/>
                <f:attribute name="style" value="text-align: center;"/>           
            </h:column>

            <%-- 備考 --%>
            <h:column id="colNote">
                <f:facet name="header">
                    <hx:jspPanel id="panelNote1">
                        <h:outputText  id="headNote" value="備考"  styleClass="outputText"/>
                    </hx:jspPanel>
                </f:facet>
                <f:attribute name="style" value="text-align: center;"/>
                <f:attribute name="width" value="98px"/>
                 
                 <hx:jspPanel id="panelNote2">
                 <TABLE class="tableStyle">
	                <TBODY>
	                    <TR>
	                        <TD class="memo1DataStyle">
	                            <h:outputText id="bodyNote1" value="#{varlist.note1}" styleClass="outputText"/>
	                        </TD>
	                        <TD class="memo2DataStyle">
	                            <h:outputText id="bodyNote2" value="#{varlist.note2}" styleClass="outputText"/>
	                        </TD>
	                    </TR>
	                <TBODY>
                 </TABLE>
                 </hx:jspPanel>
            </h:column>

            <%-- 単位数合計欄 --%>
            <f:facet name="footer">
              <hx:jspPanel id="footerArea">
                <table class="table">
                  <tr>
                    <%-- 合計ラベル --%>
                    <td rowspan="2" class="total_label"><h:outputText id="total" value="合計"/></td>
                    <%-- 他大認定合計 --%>
                    <td rowspan="2" class="total_top_group_a"><h:outputText id="otherCertificationSum" value="#{pc_Xrx00104.otherCertificationSum}"/></td>
                    <%-- 本学認定合計 --%>
                    <td rowspan="2" class="total_top_group_a"><h:outputText id="mainCertificationSum" value="#{pc_Xrx00104.mainCertificationSum}"/></td>
                    <%-- 登録単位合計 --%>
                    <td rowspan="2" class="total_top_group_a"><h:outputText id="entryUnitSum" value="#{pc_Xrx00104.entryUnitSum}"/></td>
                    <%-- 修得単位合計 --%>
                    <td rowspan="2" class="total_top_group_a"><h:outputText id="acquisitionSum" value="#{pc_Xrx00104.acquisitionSum}"/></td>
                    <%-- テキスト登録単位合計 --%>
                    <td class="total_top_group_b"><h:outputText id="textEntryUnitSum1" value="#{pc_Xrx00104.textEntryUnitSum1}"/></td>
                    <td class="total_top_group_b"><h:outputText id="textEntryUnitSum2" value="#{pc_Xrx00104.textEntryUnitSum2}"/></td>
                    <td class="total_top_group_b"><h:outputText id="textEntryUnitSum3" value="#{pc_Xrx00104.textEntryUnitSum3}"/></td>
                    <td class="total_top_group_b"><h:outputText id="textEntryUnitSum4" value="#{pc_Xrx00104.textEntryUnitSum4}"/></td>
                    <%-- スクーリング登録単位合計 --%>
                    <td class="total_top_group_b"><h:outputText id="schEntryUnitSum1" value="#{pc_Xrx00104.schEntryUnitSum1}"/></td>
                    <td class="total_top_group_b"><h:outputText id="schEntryUnitSum2" value="#{pc_Xrx00104.schEntryUnitSum2}"/></td>
                    <td class="total_top_group_b"><h:outputText id="schEntryUnitSum3" value="#{pc_Xrx00104.schEntryUnitSum3}"/></td>
                    <td class="total_top_group_b"><h:outputText id="schEntryUnitSum4" value="#{pc_Xrx00104.schEntryUnitSum4}"/></td>
                    <%-- 登録単位(ラベル) --%>
                    <td class="total_top_group_c"><h:outputText id="entryUnitFooterLabel" value="登録単位"/></td>
                    <td class="total_brank" rowspan="2">&nbsp;</td>
                  </tr>
                  <tr>
                    <%-- テキスト修得単位合計 --%>
                    <td class="total_bottom"><h:outputText id="textSyutokUnitSu1" value="#{pc_Xrx00104.textSyutokUnitSum1}"/></td>
                    <td class="total_bottom"><h:outputText id="textSyutokUnitSu2" value="#{pc_Xrx00104.textSyutokUnitSum2}"/></td>
                    <td class="total_bottom"><h:outputText id="textSyutokUnitSu3" value="#{pc_Xrx00104.textSyutokUnitSum3}"/></td>
                    <td class="total_bottom"><h:outputText id="textSyutokUnitSu4" value="#{pc_Xrx00104.textSyutokUnitSum4}"/></td>
                    <%-- スクーリング修得単位合計 --%>
                    <td class="total_bottom"><h:outputText id="schSyutokUnitSum1" value="#{pc_Xrx00104.schSyutokUnitSum1}"/></td>
                    <td class="total_bottom"><h:outputText id="schSyutokUnitSum2" value="#{pc_Xrx00104.schSyutokUnitSum2}"/></td>
                    <td class="total_bottom"><h:outputText id="schSyutokUnitSum3" value="#{pc_Xrx00104.schSyutokUnitSum3}"/></td>
                    <td class="total_bottom"><h:outputText id="schSyutokUnitSum4" value="#{pc_Xrx00104.schSyutokUnitSum4}"/></td>
                    <%-- 修得単位(ラベル) --%>
                    <td class="total_bottom"><h:outputText id="syutokUnit" value="修得単位"/></td>
                  </tr>
                </table>
              </hx:jspPanel>
            </f:facet>

        </h:dataTable>
    </DIV>
    </TD></TR>
    <TR>
	    <TD align="left">
		    <h:outputText id="htmlSikkoYoteiMessage" value="#{pc_Xrx00104.propSikkoYoteiMessage.stringValue}"styleClass="outputText" />
		</TD>
	</TR>	
    </TABLE>
    </DIV>
    </hx:jspPanel>
    <BR>
    <%-- ↑ コンテンツ部 ↑ --%>
</hx:scriptCollector>
</f:subview>