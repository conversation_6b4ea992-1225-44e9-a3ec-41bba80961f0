<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00901.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@page import="com.jast.gakuen.rev.xrl.Xrl00901"; %>
<%@page import="com.jast.gakuen.framework.util.UtilSystem";%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrl00901.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
	return true;
}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00901.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00901.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrl00901.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00901.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 --></DIV>
			<DIV id="content">

			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="600" cellpadding="0" cellspacing="0"
				class="table" style="margin-top:40px;">
				<TBODY>
					<TR>
					   <TH width="80" nowrap class="v_a">
					   		<h:outputText
								styleClass="outputText" id="lblTeateNendo"
								value="#{pc_Xrl00901.propTeateNendo.labelName}"
								style="#{pc_Xrl00901.propTeateNendo.labelStyle}">
							</h:outputText></TH>
						<TD  width="100px">
							<h:inputText styleClass="inputText"
								id="txtTeateNendo"
								value="#{pc_Xrl00901.propTeateNendo.dateValue}"
								disabled="#{pc_Xrl00901.outputTargetKbn != 1}"
								style="#{pc_Xrl00901.propTeateNendo.style}" size="6">
							 	<hx:inputHelperAssist errorClass="inputText_Error"
								    imeMode="inactive" promptCharacter="_" />
							 	<f:convertDateTime pattern="yyyy" />
							 </h:inputText>					
						</TD>
						<TD style="background-color: transparent; text-align: left" class="clear_border">
							<hx:commandExButton type="submit"
								style="margin-left:10px;"
								value="選択" styleClass="commandExButton" id="selectXrlTeat"
								disabled="#{pc_Xrl00901.outputTargetKbn != 1}"
								action="#{pc_Xrl00901.doSelectXrlTeatAction}">
							</hx:commandExButton>
							<hx:commandExButton type="submit"
								value="解除" styleClass="commandExButton" id="releaseXrlTeat"
								disabled="#{pc_Xrl00901.outputTargetKbn != 2}"
								action="#{pc_Xrl00901.doReleaseXrlTeatAction}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="600" height="380" class="tab_body">
			<TBODY>
				<TR height="100">
				<TD valign="top"  align="center">
				<BR>
				<% 
				Xrl00901 pc = (Xrl00901)UtilSystem.getManagedBean(Xrl00901.class);		
				int outputTarget= pc. getOutputTargetKbn();
				if(outputTarget == 2){
             	%>
					<TABLE width="550" border="0" cellpadding="0" cellspacing="0" class="table">
						<TBODY>				
								<TR>
									<TH class="v_b" width="180"><h:outputText styleClass="outputText"
										id="lblSikyuTaisyoTuk" value="#{pc_Xrl00901.propSikyuTaisyoTuk.labelName}"
										style="#{pc_Xrl00901.propSikyuTaisyoTuk.labelStyle}"
										></h:outputText></TH>
									<TD align="left"><h:selectOneMenu styleClass="selectOneMenu"
										id="htmlSikyuTaisyoTuk" value="#{pc_Xrl00901.propSikyuTaisyoTuk.value}"
										disabled="#{pc_Xrl00901.outputTargetKbn != 2}"
										style="width:150px">
										<f:selectItems value="#{pc_Xrl00901.propSikyuTaisyoTuk.list}" />
									</h:selectOneMenu></TD>
								</TR>
								<TR>
									<TH class="v_c" width="120"><h:outputText styleClass="outputText"
										id="lblTeateSakuseiDate"
										value="#{pc_Xrl00901.propTeateSakuseiDate.labelName}"
										style="#{pc_Xrl00901.propTeateSakuseiDate.labelStyle}"></h:outputText></TH>
									<TD align="left"><h:inputText styleClass="inputText"
										id="htmlTeateSakuseiDate"
										value="#{pc_Xrl00901.propTeateSakuseiDate.dateValue}"
										disabled="#{pc_Xrl00901.outputTargetKbn != 2}"
										style="#{pc_Xrl00901.propTeateSakuseiDate.style}" size="14">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_d" width="120"><h:outputText styleClass="outputText"
										id="lblSiharaiDate"
										value="#{pc_Xrl00901.propSiharaiDate.labelName}"
										style="#{pc_Xrl00901.propSiharaiDate.labelStyle}"
										></h:outputText></TH>
									<TD align="left"><h:inputText styleClass="inputText"
										id="htmlSiharaiDate"
										value="#{pc_Xrl00901.propSiharaiDate.dateValue}"
										disabled="#{pc_Xrl00901.outputTargetKbn != 2}"
										style="#{pc_Xrl00901.propSiharaiDate.style}" size="14">
										<f:convertDateTime />
										<hx:inputHelperDatePicker />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
									</h:inputText></TD>
								</TR>
							</TBODY>
					</TABLE>
					<% }%>
				<TD/>
				<TR/>
				<TR height="100">
				<TD>&nbsp;</TD>
				</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="600" style="margin-top:20px;" cellpadding="0" cellspacing="0"
				class="table">
				<TR>
					<TD valign="middle" style="background-color: transparent; text-align: center" class="clear_border">
						<hx:commandExButton
								disabled="#{pc_Xrl00901.outputTargetKbn != 2}"
								type="submit" value="実行" styleClass="commandExButton_out"
								id="csvout" action="#{pc_Xrl00901.doCsvoutAction}"
								confirm="#{msg.SY_MSG_0001W}">
						</hx:commandExButton>
					</TD>
				</TR>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

