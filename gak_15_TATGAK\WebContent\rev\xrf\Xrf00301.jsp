<%-- 
	レポート課題返却登録
	
	<AUTHOR>
--%>

<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
	function func_1(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
		var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlKyouinCd";
		 openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	}
	
	function getJinjiName(thisObj, thisEvent) {
		// 教員名称を取得する
  		var servlet = "rev/co/CobJinjAJAX";
  		var target = "form1:lblKyouinName";
  		getCodeName(servlet, target, thisObj.value);
 	}
 	
	 function openKamokuSearchWindow(thisObj, thisEvent) {
	    var url="${pageContext.request.contextPath}/faces/rev/km/pKmz0101.jsp?retFieldName=form1:htmlKamokuCd";
	    openModalWindow(url, "pKmz0101", "<%= com.jast.gakuen.rev.km.PKmz0101.getWindowOpenOption() %>");
	    return true;
	
	}

	function doKamokuAjax(thisObj, thisEvent, targetLabel, targetLabel2) {
	// 科目名称,単位数を取得する
		var servlet = "rev/xrf/XrfKmkAJAX";
	    var args = new Array();
	    args['code'] = thisObj.value;

	    var ajaxUtil = new AjaxUtil();
		ajaxUtil.getCodeName(servlet, targetLabel, args);


		// 単位数
		if ( targetLabel2 != "" ) {
		    var args2 = new Array();
		    args2['code'] = thisObj.value;
		    args2['tanisu'] = "GET";
		    args2['addString'] = " 単位";

			ajaxUtil.getCodeName(servlet, targetLabel2, args2);
		}
	}

	
	function func_2(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

		// 時間割リスト一括チェック

		check("htmlKadaiTantouList","htmlListCheckBox");
	}
	
	function func_3(thisObj, thisEvent) {
		//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
		//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します

		// 時間割リスト一括チェック解除

		uncheck("htmlKadaiTantouList","htmlListCheckBox");
	}

	function loadAction(event) {
		// 画面ロード時の教員名・科目名の再取得
		getJinjiName(document.getElementById('form1:htmlKyouinCd'), event);
		doKamokuAjax(document.getElementById('form1:htmlKamokuCd'), event, 'form1:lblKamokuName', 'form1:htmlTani');
	}

	function confirmOk() {
		var phase;
		phase = parseInt(document.getElementById('form1:htmlBackPhase').value);
		if (phase == 0) {
			document.getElementById('form1:htmlBackPhase').value = 1;
			indirectClick('select');
		}
	}

	function confirmCancel() {
		document.getElementById('form1:htmlBackPhase').value = 9;
		indirectClick('select');
	}

</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00301.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00301.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここに戻るボタンを配置 -->
				<!-- ↑ここに戻るボタンを配置 -->
			</DIV>
			<DIV id="content">
				<DIV class="column" align="center">
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
						<TBODY>
							<TR>
								<TH class="v_a" width="240">
									<h:outputText styleClass="outputText"
										id="lblNendo"
										value="#{pc_Xrf00301.propNendo.labelName}" 
										style="#{pc_Xrf00301.propNendo.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:inputText id="htmlNendo" styleClass="inputText" 
										readonly="#{pc_Xrf00301.propNendo.readonly}" 
										style="#{pc_Xrf00301.propNendo.style}" 
										value="#{pc_Xrf00301.propNendo.dateValue}" 
										maxlength="#{pc_Xrf00301.propNendo.maxLength}" 
										disabled="#{pc_Xrf00301.propNendo.disabled}" size="4" tabindex="1"
										>
										<hx:inputHelperAssist errorClass="inputText_Error"
						    			imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
								<TD rowspan="3" width="100" align="right"
									style="background-color: transparent; text-align: right"
									class="clear_border"><hx:commandExButton type="submit"
									value="#{pc_Xrf00301.propSelect.caption}"
									styleClass="commandExButton" id="select"
									disabled="#{pc_Xrf00301.propSelect.disabled}"
									rendered="#{pc_Xrf00301.propSelect.rendered}"
									style="#{pc_Xrf00301.propSelect.style}"
									action="#{pc_Xrf00301.doSelectAction}" tabindex="2">
								</hx:commandExButton> <hx:commandExButton type="submit"
									value="#{pc_Xrf00301.propRelease.caption}"
									styleClass="commandExButton" id="release"
									disabled="#{pc_Xrf00301.propRelease.disabled}"
									rendered="#{pc_Xrf00301.propRelease.rendered}"
									style="#{pc_Xrf00301.propRelease.style}"
									action="#{pc_Xrf00301.doReleaseAction}" tabindex="3">
								</hx:commandExButton></TD>
							</TR>
							
							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblKyouinCd"
										value="#{pc_Xrf00301.propKyouinCd.labelName}" 
										style="#{pc_Xrf00301.propKyouinCd.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:inputText id="htmlKyouinCd" styleClass="inputText" 
										readonly="#{pc_Xrf00301.propKyouinCd.readonly}" 
										style="#{pc_Xrf00301.propKyouinCd.style}" 
										value="#{pc_Xrf00301.propKyouinCd.stringValue}" 
										maxlength="#{pc_Xrf00301.propKyouinCd.maxLength}" 
										disabled="#{pc_Xrf00301.propKyouinCd.disabled}"
										size="10" tabindex="4"
										onblur="return getJinjiName(this, event);">
									</h:inputText>
									<hx:commandExButton type="button" value=""
										styleClass="commandExButton_search" id="searchKyouin"
										disabled="#{pc_Xrf00301.propSearchKyouin.disabled}"
										rendered="#{pc_Xrf00301.propSearchKyouin.rendered}"
										style="#{pc_Xrf00301.propSearchKyouin.style}" 
										tabindex="5" 
										onclick="return func_1(this, event);">
									</hx:commandExButton><h:outputText
              							styleClass="outputText"
              							id="lblKyouinName"
              							value="#{pc_Xrf00301.propKyouinName.displayValue}"
              							title="#{pc_Xrf00301.propKyouinName.stringValue}">
              						</h:outputText></TD>				
							</TR>
							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblKamokuCd"
										value="#{pc_Xrf00301.propKamokuCd.labelName}" 
										style="#{pc_Xrf00301.propKamokuCd.labelStyle}">
									</h:outputText>
								</TH>
								<TD width="360">
									<h:inputText id="htmlKamokuCd" styleClass="inputText" 
                                        tabindex="6" size="10" 
                                        value="#{pc_Xrf00301.propKamokuCd.stringValue}"
                                        readonly="#{pc_Xrf00301.propKamokuCd.readonly}"
                                        disabled="#{pc_Xrf00301.propKamokuCd.disabled}"
                                        style="#{pc_Xrf00301.propKamokuCd.style}"
                                        maxlength="#{pc_Xrf00301.propKamokuCd.maxLength}"
                                        onblur="return doKamokuAjax(this, event, 'form1:lblKamokuName', 'form1:htmlTani');">
									</h:inputText>
									<hx:commandExButton type="button" value=""
										styleClass="commandExButton_search" id="searchKamoku"
										disabled="#{pc_Xrf00301.propSearchKamoku.disabled}"
										rendered="#{pc_Xrf00301.propSearchKamoku.rendered}"
										style="#{pc_Xrf00301.propSearchKamoku.style}" 
										tabindex="7" 
										onclick="return openKamokuSearchWindow(this, event);">
									</hx:commandExButton><h:outputText
              							styleClass="outputText" id="lblKamokuName"
                                        title ="#{pc_Xrf00301.propKamokuName.stringValue}"
                                        value="#{pc_Xrf00301.propKamokuName.displayValue}">
              						</h:outputText></TD>
              					<TD width="60">
              						<h:outputText styleClass="outputText"
										id="htmlTani"
										value="#{pc_Xrf00301.propTani.stringValue}" >
									</h:outputText></TD>	
							</TR>

							<TR>
								<TH class="v_a" width="150">
									<h:outputText styleClass="outputText"
										id="lblSyuturyoku"
										value="#{pc_Xrf00301.propSyuturyoku.labelName}" 
										style="#{pc_Xrf00301.propSyuturyoku.labelStyle}">
									</h:outputText>
								</TH>
								<TD colspan="2">
									<h:selectOneRadio
						              	disabledClass="selectOneRadio_Disabled"
						              	styleClass="selectOneRadio" id="htmlSyuturyoku"
						              	disabled="#{pc_Xrf00301.propSyuturyoku.disabled}"
						              	value="#{pc_Xrf00301.propSyuturyoku.stringValue}"
						              	style="#{pc_Xrf00301.propSyuturyoku.style}"
										tabindex="8"> 
						              	<f:selectItem itemValue="0" itemLabel="全て"/>
						              	<f:selectItem itemValue="1" itemLabel="未返却" />
						              	<f:selectItem itemValue="2" itemLabel="返却済" />
							        </h:selectOneRadio></TD>	
							</TR>
						</TBODY>
					</TABLE>
					
					<BR>

					<TABLE border="0" cellpadding="5">
						<TBODY>
					        <TR>
						        <TD width="750" align="right">
							        <h:outputText styleClass="outputText" id="htmlCount"
								        value="#{pc_Xrf00301.propKadaiTantouList.listCount}">
							        </h:outputText>
							        <h:outputText
								        styleClass="outputText" id="lblCount" value="件">
							        </h:outputText>
						        </TD>
					        </TR>
							
							<TR>
							<!-- ↓データテーブル部↓ -->
							<TD>
								<div id="listScroll" class="listScroll"
								onscroll="setScrollPosition('scroll',this);"
								style="height:330px;">
								<h:dataTable
										columnClasses="columnClass" headerClass="headerClass"
										footerClass="footerClass"
										rowClasses="#{pc_Xrf00301.propKadaiTantouList.rowClasses}"
										styleClass="meisai_scroll" id="htmlKadaiTantouList" width="750"
										value="#{pc_Xrf00301.propKadaiTantouList.list}" var="varlist">
										<h:column id="column1">
											<f:facet name="header">
											</f:facet>
											<h:selectBooleanCheckbox styleClass="selectBooleanCheckbox"
												id="htmlListCheckBox"
												disabled="#{pc_Xrf00301.propListCheckBox.disabled}"
												readonly="#{pc_Xrf00301.propListCheckBox.readonly}"
												rendered="#{pc_Xrf00301.propListCheckBox.rendered}"
												required="#{pc_Xrf00301.propListCheckBox.required}"
												style="#{pc_Xrf00301.propListCheckBox.style}"
												tabindex="9" 
												value="#{varlist.selected}">
											</h:selectBooleanCheckbox>
											<f:attribute value="30" name="width" />
											<f:attribute value="text-align: center" name="style" />
										</h:column>
										
										<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Xrf00301.propListKyouinName.labelName}"
														id="lblListKyouinNameColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKyouinName"
													style="#{pc_Xrf00301.propListKyouinName.style}"
													value="#{varlist.listKyoinName.displayValue}"
													title="#{varlist.listKyoinName.stringValue}">
												</h:outputText>
												<f:attribute value="100" name="width" />
										</h:column>
	
										<h:column id="column3">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Xrf00301.propListKamokuCd.labelName}"
														id="lblListKamokuCdColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokuCd"
													style="#{pc_Xrf00301.propListKamokuCd.style}"
													value="#{varlist.listKamokuCd}">
													<f:convertDateTime />
												</h:outputText>
												<f:attribute value="65" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>

										<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Xrf00301.propListKamokuName.labelName}"
														id="lblListKamokuNameColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKamokuName"
													style="#{pc_Xrf00301.propListKamokuName.style}"
													value="#{varlist.listKamokuName.displayValue}"
													title="#{varlist.listKamokuName.stringValue}">
													<f:convertDateTime />
												</h:outputText>
												<f:attribute value="256" name="width" />
										</h:column>
										
										<h:column id="column5">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Xrf00301.propListBunsatu.labelName}"
														id="lblListBunsatuColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListBunsatu"
													style="#{pc_Xrf00301.propListBunsatu.style}"
													value="#{varlist.listBunsatu}">
													<f:convertDateTime />
												</h:outputText>
												<f:attribute value="40" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>
												
										<h:column id="column6">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Xrf00301.propListSinkyu.labelName}"
														id="lblListSinkyuColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListSinkyu"
													style="#{pc_Xrf00301.propListSinkyu.style}"
													value="#{varlist.listSinkyukan}">
													<f:convertDateTime />
												</h:outputText>
												<f:attribute value="60" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>
														
										<h:column id="column7">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														value="#{pc_Xrf00301.propListKadaiIraibi.labelName}"
														id="lblListKadaiIraibiColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKadaiIraibi"
													style="#{pc_Xrf00301.propListKadaiIraibi.style}"
													value="#{varlist.listKadaiSakuseiIraibi}">
													<f:convertDateTime />
												</h:outputText>
												<f:attribute value="100" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>
										
										<h:column id="column8">
												<f:facet name="header" nowrap>
													<h:outputText styleClass="outputText"
														value="#{pc_Xrf00301.propListKadaiHenkyakubi.labelName}"
														id="lblListKadaiHenkyakubiColumn">
													</h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="htmlListKadaiHenkyakubi"
													style="#{pc_Xrf00301.propListKadaiHenkyakubi.style}"
													value="#{varlist.listKadaiHenkyakubi}">
													<f:convertDateTime />
												</h:outputText>
												<f:attribute value="80" name="width" />
												<f:attribute value="text-align: center" name="style" />
										</h:column>
																								
								</h:dataTable>
								</DIV>
							</TD>
							<!-- ↑データテーブル部↑ -->
							</TR>
							
							<TR>
								<TD align="left">
									
													<hx:commandExButton type="button" styleClass="check" 
														id="check" 
														disabled="#{pc_Xrf00301.propCheck.disabled}"
														
														onclick="return func_2(this, event);" tabindex="10">
													</hx:commandExButton>
													<hx:commandExButton type="button" styleClass="uncheck" 
														id="uncheck" 
														disabled="#{pc_Xrf00301.propUncheck.disabled}"
														
														onclick="return func_3(this, event);" tabindex="11">
													</hx:commandExButton>
												
								</TD>
							</TR>
						</TBODY>
					</TABLE>
					
					<BR>
					<TABLE border="0" cellpadding="0" cellspacing="0" class="table">
						<TBODY>
							<TR>
								<TH class="v_a" width="200">
									<h:outputText styleClass="outputText"
										id="lblKadaiHenkyakubi"
 										value="課題返却日"  style="#{pc_Xrf00301.propKadaiHenkyakubi.labelStyle}">
									</h:outputText>
								</TH>
								<TD width="560">
									<h:inputText id="htmlKadaiHenkyakubi"
										styleClass="inputText" size="12" tabindex="12"
										value="#{pc_Xrf00301.propKadaiHenkyakubi.dateValue}"
										readonly="#{pc_Xrf00301.propKadaiHenkyakubi.readonly}"
										disabled="#{pc_Xrf00301.propKadaiHenkyakubi.disabled}"
										style="#{pc_Xrf00301.propKadaiHenkyakubi.style}">
										<f:convertDateTime />
										<hx:inputHelperAssist errorClass="inputText_Error"
											promptCharacter="_" />
										<hx:inputHelperDatePicker />
									</h:inputText>
								</TD>	
							</TR>
						</TBODY>
					</TABLE>

					<BR>
					<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
						<TBODY>
							<TR>
						
								<TD>
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="kousin"
										value="#{pc_Xrf00301.propKousin.caption}"
										action="#{pc_Xrf00301.doKousinAction}"
										disabled="#{pc_Xrf00301.propKousin.disabled}"
										rendered="#{pc_Xrf00301.propKousin.rendered}"
										style="#{pc_Xrf00301.propKousin.style}" tabindex="13"
								        confirm="#{msg.SY_MSG_0002W}">
									</hx:commandExButton>
							
									<hx:commandExButton type="submit"
										styleClass="commandExButton_dat" id="clear"
										value="#{pc_Xrf00301.propClear.caption}"
										action="#{pc_Xrf00301.doClearAction}"
										disabled="#{pc_Xrf00301.propClear.disabled}"
										rendered="#{pc_Xrf00301.propClear.rendered}"
										style="#{pc_Xrf00301.propClear.style}" tabindex="14"
								        confirm="#{msg.SY_MSG_0014W}">
									</hx:commandExButton>
								</TD>	
								
							</TR>
						</TBODY>
					</TABLE>								
					</DIV>
												
				</DIV>
			
			</DIV>
		

		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
		<h:inputHidden value="#{pc_Xrf00301.propKadaiTantouList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
		<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrf00301.propBackPhase.integerValue}">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>
