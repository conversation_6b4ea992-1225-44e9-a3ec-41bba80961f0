<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz00501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function confirmOk() {

	document.getElementById('form1:htmlConfirmVal').value = "1";

	 indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlConfirmVal').value = "0";
	return false;
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz00501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz00501.doCloseDispAction}"
			></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz00501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz00501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 --><BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="100%">
				<TBODY>
					<TR>
						<TD width="20%"></TD>
						<TD align="center" width="554">
						<TABLE border="0" cellpadding="0" cellspacing="0" class="table"
							width="100%">
							<TBODY>
								<TR>
									<TH width="140" class="v_a"><h:outputText
										styleClass="outputText" id="text1"
										value="#{pc_Ssz00501.propKaisoNoOne.labelName}"
										style="#{pc_Ssz00501.propKaisoNoOne.labelStyle}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnoone"
										value="#{pc_Ssz00501.propKaisoNoOne.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoOne.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoOne.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text12"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_b"><h:outputText
										styleClass="outputText" id="text2"style="#{pc_Ssz00501.propKaisoNoTwo.labelStyle}"
										value="#{pc_Ssz00501.propKaisoNoTwo.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnotwo"
										value="#{pc_Ssz00501.propKaisoNoTwo.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoTwo.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoTwo.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text14"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_c"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoThree.labelStyle}"styleClass="outputText" id="text3"
										value="#{pc_Ssz00501.propKaisoNoThree.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnothree"
										value="#{pc_Ssz00501.propKaisoNoThree.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoThree.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoThree.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text16"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_d"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoFour.labelStyle}"styleClass="outputText" id="text4"
										value="#{pc_Ssz00501.propKaisoNoFour.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnofour"
										value="#{pc_Ssz00501.propKaisoNoFour.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoFour.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoFour.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text18"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_e"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoFive.labelStyle}"styleClass="outputText" id="text5"
										value="#{pc_Ssz00501.propKaisoNoFive.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnofive"
										value="#{pc_Ssz00501.propKaisoNoFive.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoFive.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoFive.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text20"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_f"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoSix.labelStyle}"styleClass="outputText" id="text6"
										value="#{pc_Ssz00501.propKaisoNoSix.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnosix"
										value="#{pc_Ssz00501.propKaisoNoSix.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoSix.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoSix.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text22"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_g"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoSeven.labelStyle}"styleClass="outputText" id="text7"
										value="#{pc_Ssz00501.propKaisoNoSeven.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnoseven"
										value="#{pc_Ssz00501.propKaisoNoSeven.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoSeven.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoSeven.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text24"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_a"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoEight.labelStyle}"styleClass="outputText" id="text8"
										value="#{pc_Ssz00501.propKaisoNoEight.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnoeight"
										value="#{pc_Ssz00501.propKaisoNoEight.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoEight.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoEight.style}"></h:inputText><h:outputText
										styleClass="outputText" id="text26" value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_b"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoNine.labelStyle}"styleClass="outputText" id="text9"
										value="#{pc_Ssz00501.propKaisoNoNine.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnonine"
										value="#{pc_Ssz00501.propKaisoNoNine.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoNine.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoNine.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text28"
										value="百万円未満"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="140" class="v_c"><h:outputText
										style="#{pc_Ssz00501.propKaisoNoTen.labelStyle}"styleClass="outputText" id="text10"
										value="#{pc_Ssz00501.propKaisoNoTen.labelName}"></h:outputText></TH>
									<TD width="379"><h:inputText styleClass="inputText"
										id="htmlnoten"
										value="#{pc_Ssz00501.propKaisoNoTen.stringValue}"
										maxlength="#{pc_Ssz00501.propKaisoNoTen.maxLength}"
										style="#{pc_Ssz00501.propKaisoNoTen.style}">
									</h:inputText><h:outputText styleClass="outputText" id="text30"
										value="百万円未満"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
						<TD width="242"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" class="button_bar"
				width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit"
							value="確定" styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz00501.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Ssz00501.propConfirmVal.stringValue}" id="htmlConfirmVal"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>

