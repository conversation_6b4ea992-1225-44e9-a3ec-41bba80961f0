<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh00201.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh00201.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh00201.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh00201.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh00201.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">

				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
					<TBODY>
			           <TR>
			              <TH nowrap class="v_a" width="190">
			              		<!--年度 -->
			                	<h:outputText styleClass="outputText" id="lblNendo"
			                		value="#{pc_Xrh00201.propNendo.labelName}"
			                		style="#{pc_Xrh00201.propNendo.labelStyle}">
			                	</h:outputText>
			              </TH>
			              <TD>
			              		<h:inputText styleClass="inputText"
			                		id="htmlNendo" size="8"
			                		value="#{pc_Xrh00201.propNendo.dateValue}"
			                		readonly="#{pc_Xrh00201.propNendo.readonly}"
			                		style="#{pc_Xrh00201.propNendo.style}" tabindex="1">
			                		<hx:inputHelperAssist errorClass="inputText_Error"
						    		imeMode="inactive" promptCharacter="_" />
									<f:convertDateTime pattern="yyyy" />
			                	</h:inputText>
			              </TD>
			          </TR>
			          
			          <TR>
			          	 <TH class="v_a" width="190">
								<h:outputText styleClass="outputText" id="lblFileType"
									value="#{pc_Xrh00201.propFileType.labelName}" 
									style="#{pc_Xrh00201.propFileType.labelStyle}">
								</h:outputText>
						 </TH>
						 <TD width="500">
								<h:selectOneRadio
			              			disabledClass="selectOneRadio_Disabled"
			              			styleClass="selectOneRadio" id="htmlFileType"
			              			value="#{pc_Xrh00201.propFileType.stringValue}"
			              			style="#{pc_Xrh00201.propFileType.style}"
			              			layout="pageDirection" tabindex="2">
									<f:selectItems
										value="#{pc_Xrh00201.propFileType.list}" />
					             </h:selectOneRadio>
					     </TD>
					   </TR>      	
					   
					   <TR>
			              <TH width="150" nowrap bgcolor="silver" class="v_a">
			              	<h:outputText
								styleClass="outputText" id="lblInputFile"
								value="#{pc_Xrh00201.propInputFile.labelName}"
								style="#{pc_Xrh00201.propInputFile.labelStyle}">
							</h:outputText>
							<BR>
							(前回ファイル)
						  </TH>
						  <TD nowrap width="600">
						  	<hx:fileupload styleClass="fileupload"
								id="htmlInputFile" size="50"
								value="#{pc_Xrh00201.propInputFile.value}" 
								style="width:585px" tabindex="3">
								<hx:fileProp name="fileName" 
									value="#{pc_Xrh00201.propInputFile.fileName}"/>
								<hx:fileProp name="contentType"	
									value="#{pc_Xrh00201.propInputFile.contentType}" />
							</hx:fileupload>
							<BR>
							<h:outputText styleClass="outputText" id="htmlInputFileOld"
								value="#{pc_Xrh00201.propInputFileOld.stringValue}">
							</h:outputText>
							<BR>
						  </TD>
			          </TR>
			          
			          <TR>
			          	 <TH class="v_a" width="190">
								<h:outputText styleClass="outputText" id="lblRegKbn"
									value="#{pc_Xrh00201.propRegKbn.labelName}" 
									style="#{pc_Xrh00201.propRegKbn.labelStyle}">
								</h:outputText>
						 </TH>
						 <TD width="500">
								<h:selectOneRadio
			              			disabledClass="selectOneRadio_Disabled"
			              			styleClass="selectOneRadio" id="htmlRegKbn"
			              			value="#{pc_Xrh00201.propRegKbn.stringValue}"
			              			style="#{pc_Xrh00201.propRegKbn.style}"
			              			layout="pageDirection" tabindex="4">
									<f:selectItems
										value="#{pc_Xrh00201.propRegKbn.list}" />
					             </h:selectOneRadio>
					     </TD>
					  </TR>
					  
					  <TR>
						 <TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblSyoriKbn"
								value="#{pc_Xrh00201.propSyoriKbn.labelName}">
							</h:outputText>
						 </TH>
						 <TD>
							<h:selectBooleanCheckbox 
								styleClass="selectBooleanCheckbox"
                                id="htmlSyoriKbn" 
                                value="#{pc_Xrh00201.propSyoriKbn.checked}" tabindex="5">
                            </h:selectBooleanCheckbox>
                            チェックのみ(データの登録／更新は行いません)
						 </TD>	
					  </TR>
					  
					  <TR>
							<TH class="v_a">
								<h:outputText styleClass="outputText"
									value="チェックリスト出力指定">
								</h:outputText>
							</TH>
							<TD>
								<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlNorData" 
                                	value="#{pc_Xrh00201.propNorData.checked}" tabindex="6">
                            	</h:selectBooleanCheckbox>正常データ
                            	<BR>
								<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlErrData" 
                                	value="#{pc_Xrh00201.propErrData.checked}" tabindex="7">
                            	</h:selectBooleanCheckbox>エラーデータ
                            	<BR>
                            	<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlWarData" 
                                	value="#{pc_Xrh00201.propWarData.checked}" tabindex="8">
                            	</h:selectBooleanCheckbox>ワーニングデータ
							</TD>
					 </TR>
				  </TBODY>
				</TABLE>
				
				<BR>
				
				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
					
							<TD>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="exec"
									value="実行" tabindex="9"
									action="#{pc_Xrh00201.doExecAction}"
									confirm="#{msg.SY_MSG_0001W}"
									disabled="#{pc_Xrh00201.propExec.disabled}"
									style="#{pc_Xrh00201.propExec.style}">
								</hx:commandExButton>
		
							</TD>	
								
						</TR>
					</TBODY>
				</TABLE>
				
				</DIV>
			</DIV>
			
		</DIV>
		</h:form>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
