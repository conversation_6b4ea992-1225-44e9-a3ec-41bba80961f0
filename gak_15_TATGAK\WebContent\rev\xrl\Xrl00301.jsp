<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00301.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrl00301.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrl00301.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrl00301.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrl00301.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrl00301.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->
			<BR><BR><BR>
			<TABLE width="100%" border="0">
				<TBODY>
					<TR>
						<TD width="100%">
						<CENTER><TABLE class="table" width="700">
							<TBODY>
								<TR>
									<TH class="v_a" width="160">
										<h:outputText styleClass="outputText" id="lblTeateNendo"
											 value="#{pc_Xrl00301.propTeateNendo.labelName}" 
											 style="#{pc_Xrl00301.propTeateNendo.labelStyle}">
										</h:outputText>
									</TH>
									<TD width="540"><h:inputText styleClass="inputText"
										id="txtTeateNendo"
										value="#{pc_Xrl00301.propTeateNendo.dateValue}"
										style="#{pc_Xrl00301.propTeateNendo.style}"
										maxlength="#{pc_Xrl00301.propTeateNendo.maxLength}" size="10">
										<hx:inputHelperAssist errorClass="inputText_Error"
						    			imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
										</h:inputText>
									</TD>
								</TR>
								<TR>
									<TH class="v_e" width="160"><h:outputText styleClass="outputText" id="lblPdfTitle"
										 	value="#{pc_Xrl00301.propPdfTitle.labelName}"
										  	style="#{pc_Xrl00301.propPdfTitle.style}"></h:outputText>
									</TH>
									<TD width="540"><h:inputText styleClass="inputText" id="htmlPdfTitle"
										size="80" maxlength="#{pc_Xrl00301.propPdfTitle.maxLength}"
										style="#{pc_Xrl00301.propPdfTitle.style}"
										value="#{pc_Xrl00301.propPdfTitle.stringValue}"></h:inputText>
									</TD>
								</TR>
							</TBODY>
						</TABLE></CENTER>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			<TABLE width="700" class="button_bar" cellspacing="0" cellpadding="0">
				<TBODY>
					<TR>
						<TD width="700"><hx:commandExButton type="submit" value="PDF作成"
							styleClass="commandExButton_out" id="pdfout"
							confirm="#{msg.SY_MSG_0019W}"
							action="#{pc_Xrl00301.doPdfoutAction}"></hx:commandExButton>&nbsp;<hx:commandExButton
							type="submit" value="CSV作成" styleClass="commandExButton_out"
							id="csvout" confirm="#{msg.SY_MSG_0020W}"
							action="#{pc_Xrl00301.doCsvoutAction}"></hx:commandExButton>&nbsp;<hx:commandExButton
							type="submit" value="出力項目指定" styleClass="commandExButton_out"
							id="setoutput" action="#{pc_Xrl00301.doSetoutputAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>

			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

