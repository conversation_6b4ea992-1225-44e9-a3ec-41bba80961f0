<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrb/Xrb00102T01.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrb00102T01.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css"  >	
<LINK rel="stylesheet" type="text/css" href="../km/inc/gakuenKM.css"  >	

<SCRIPT type="text/javascript">

function func_1(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
check('htmlRisyuKihonChkList','htmlSelected');

}
function func_2(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
uncheck('htmlRisyuKihonChkList','htmlSelected');
}

function func_3(thisObj, thisEvent) {
//このコンポーネントを直接参照するには、キーワード  'this' の代わりに 'thisObj' を使用します
//生成済みイベントを参照するには、キーワード  'event' の代わりに 'thisEvent' を使用します
changeScrollPosition('scroll','listScroll');

}</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY onload="return func_3(this, event);">
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrb00102T01.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrb00102T01.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrb00102T01.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrb00102T01.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >
<!-- ↓ここに戻る／閉じるボタンを配置 --><hx:commandExButton type="submit" value="戻る"
				styleClass="commandExButton" id="returnDisp" action="#{pc_Xrb00102T01.doReturnDispAction}"></hx:commandExButton>
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->
			<TABLE width="100%" border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD align="center">
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH width="200" class="v_e"><h:outputText
										styleClass="outputText" id="lblName"
										value="#{pc_Xrb00102T01.xrb00102.propName.labelName}" style="#{pc_Xrb00102T01.xrb00102.propName.labelStyle}"></h:outputText></TH>
									<TD width="600" colspan="3"><h:outputText styleClass="outputText"
										id="htmlName"
										value="#{pc_Xrb00102T01.xrb00102.propName.stringValue}" style="#{pc_Xrb00102T01.xrb00102.propName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="200" class="v_f"><h:outputText
										styleClass="outputText" id="lblGakunen"
										value="#{pc_Xrb00102T01.xrb00102.propGakunen.labelName}" style="#{pc_Xrb00102T01.xrb00102.propGakunen.labelStyle}"></h:outputText></TH>
									<TD colspan="3" width="600"><h:outputText styleClass="outputText"
										id="htmlGakunen"
										value="#{pc_Xrb00102T01.xrb00102.propGakunen.stringValue}" style="#{pc_Xrb00102T01.xrb00102.propGakunen.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="200" class="v_a"><h:outputText
										styleClass="outputText" id="lblCurGakkaCd"
										value="#{pc_Xrb00102T01.xrb00102.propCurGakkaCd.labelName}" style="#{pc_Xrb00102T01.xrb00102.propCurGakkaCd.labelStyle}"></h:outputText></TH>
									<TD colspan="3" width="600"><h:outputText
										styleClass="outputText" id="htmlCurGakkaCd"
										title="#{pc_Xrb00102T01.xrb00102.propCurGakkaCd.value}"
										value="#{pc_Xrb00102T01.xrb00102.propCurGakkaCd.displayValue}"
										style="#{pc_Xrb00102T01.xrb00102.propCurGakkaCd.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center"><BR>
						</TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
							class="table">
							<TBODY>
								<TR>
									<TH width="200" class="v_c"><h:outputText
										styleClass="outputText" id="lblKamokCd"
										value="#{pc_Xrb00102T01.xrb00102.propKamokCd.labelName}" style="#{pc_Xrb00102T01.xrb00102.propKamokCd.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText
										styleClass="outputText" id="htmlKamokCd"
										value="#{pc_Xrb00102T01.xrb00102.propKamokCd.stringValue}" style="#{pc_Xrb00102T01.xrb00102.propKamokCd.style}"></h:outputText></TD>
									<TH width="200" class="v_c"><h:outputText
										styleClass="outputText" id="lblKamokName"
										value="#{pc_Xrb00102T01.xrb00102.propKamokName.labelName}" style="#{pc_Xrb00102T01.xrb00102.propKamokName.labelStyle}"></h:outputText></TH>
									<TD width="200"><h:outputText
										styleClass="outputText" id="htmlKamokName"
										value="#{pc_Xrb00102T01.xrb00102.propKamokName.stringValue}" style="#{pc_Xrb00102T01.xrb00102.propKamokName.style}"></h:outputText></TD>
								</TR>
								<TR>
									<TH width="200" class="v_d"><h:outputText
										styleClass="outputText" id="lblRisyuHoho"
										value="#{pc_Xrb00102T01.xrb00102.propRisyuHoho.labelName}"
										style="#{pc_Xrb00102T01.xrb00102.propRisyuHoho.labelStyle}"></h:outputText></TH>
									<TD colspan="3" width="600"><h:outputText
										styleClass="outputText" id="htmlRisyuHoho"
										title="#{pc_Xrb00102T01.xrb00102.propRisyuHoho.value}"
										value="#{pc_Xrb00102T01.xrb00102.propRisyuHoho.displayValue}"
										style="#{pc_Xrb00102T01.xrb00102.propRisyuHoho.style}"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD align="center"><BR>
						</TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE border="0" cellpadding="0" cellspacing="0">
							<TBODY>
								<TR>
									<TD align="left"><TABLE border="0" cellpadding="0" cellspacing="0">
										<TBODY>
											<TR>
												<TD><hx:commandExButton type="submit" value="履修基本チェック"
													styleClass="tab_head_on" id="risyuKihonChk" action="#{pc_Xrb00102T01.doRisyuKihonChkAction}"></hx:commandExButton></TD>
												<TD><hx:commandExButton type="submit" value="履修条件チェック"
													styleClass="tab_head_off" id="risyuJokenChk"
													action="#{pc_Xrb00102T01.doRisyuJokenChkAction}"></hx:commandExButton></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
								<TR>
									<TD>
									<TABLE border="0" cellpadding="0" cellspacing="0" width="600" class="tab_body">
										<TBODY>
											<TR>
												<TD valign="top">
												<TABLE border="0" cellpadding="0" cellspacing="0">
													<TBODY>
														<TR>
															<TD align="right"><h:outputFormat styleClass="outputFormat" id="htmlCount"
																value=" {0}件">
																<f:param id="param1" name="count"
																	value="#{pc_Xrb00102T01.propRisyuKihonChkList.listCount}"></f:param>
															</h:outputFormat></TD>
														</TR>
														<TR>
															<TD>
															<DIV style="height:273px;" class="listScroll"
																onscroll="setScrollPosition('scroll',this);"><h:dataTable
																border="0" cellpadding="0" cellspacing="0"
																columnClasses="columnClass1" headerClass="headerClass"
																footerClass="footerClass"
																rowClasses="#{pc_Xrb00102T01.propRisyuKihonChkList.rowClasses}"
																styleClass="meisai_scroll" id="htmlRisyuKihonChkList"
																value="#{pc_Xrb00102T01.propRisyuKihonChkList.list}"
																var="varlist" width="780">
																<h:column id="column1">
																	<f:facet name="header">

																	</f:facet>
																	<h:selectBooleanCheckbox
																		styleClass="selectBooleanCheckbox" id="htmlSelected"
																		value="#{varlist.selected}"
																		disabled="#{varlist.disabled}"
																		rendered="#{varlist.rendered}"></h:selectBooleanCheckbox>
																	<f:attribute value="30" name="width" />
																	<f:attribute value="center" name="align" />
																</h:column>
																<h:column id="column2">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText"
																			value="履修基本エラーコード" id="lblRisyuKihonChkCdList"></h:outputText>

																	</f:facet>
																	<h:outputText styleClass="outputText"
																		id="htmlRisyuKihonChkCdList"
																		value="#{varlist.risyuKihonChkCd}"></h:outputText>
																	<f:attribute value="150" name="width" />
																</h:column>
																<h:column id="column3">
																	<f:facet name="header">
																		<h:outputText styleClass="outputText"
																			value="履修基本エラーメッセージ" id="lblErrMsgList"></h:outputText>

																	</f:facet>
																	<h:outputText styleClass="outputText"
																		id="htmlErrMsgList" value="#{varlist.errMsg}"></h:outputText>
																	<f:attribute value="600" name="width" />
																</h:column>
															</h:dataTable></DIV>
															</TD>
														</TR>
														<TR>
															<TD align="left"><hx:commandExButton type="button" styleClass="check"
																id="check"onclick="return func_1(this, event);"></hx:commandExButton><hx:commandExButton
																type="button" styleClass="uncheck" id="uncheck" onclick="return func_2(this, event);"></hx:commandExButton></TD>
														</TR>
													</TBODY>
												</TABLE></TD>
											</TR>
										</TBODY>
									</TABLE>
									</TD>
								</TR>
							</TBODY>
						</TABLE></TD>
					</TR>
					<TR>
						<TD align="center"><BR>
						</TD>
					</TR>
					<TR>
						<TD align="center">
						<TABLE width="800" border="0" cellpadding="0" cellspacing="0"
							class="button_bar">
							<TBODY>
								<TR>
									<TD width="100%" class=""><hx:commandExButton type="submit"
										value="確定" styleClass="commandExButton_dat" id="fix"
										action="#{pc_Xrb00102T01.doFixAction}" confirm="#{msg.SY_MSG_0003W}"></hx:commandExButton> <hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_dat"
										id="unselect" action="#{pc_Xrb00102T01.doUnselectAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><!-- ↑ここにコンポーネントを配置 -->
</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../inc/footer.jsp" />
			<h:inputHidden
				value="#{pc_Xrb00102T01.propRisyuKihonChkList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../inc/common.jsp" />
</f:view>

</HTML>

