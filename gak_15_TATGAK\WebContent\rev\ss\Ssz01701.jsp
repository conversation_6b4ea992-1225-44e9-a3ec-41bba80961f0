<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz01701.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz01701.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Ssz01701.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Ssz01701.doCloseDispAction}"></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Ssz01701.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Ssz01701.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>
			
<!--↓content↓-->
<DIV class="head_button_area" >　
<!-- ↓ここに戻る／閉じるボタンを配置 -->
<!-- ↑ここに戻る／閉じるボタンを配置 -->
</DIV>
<DIV id="content">			
<DIV class="column" align="center">
<!-- ↓ここにコンポーネントを配置 -->


<!-- ↑ここにコンポーネントを配置 --><TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="74%"></TD>
						<TD align="left" valign="middle" width="239"><h:outputText styleClass="outputText"
							id="text9" value="#{pc_Ssz01701.propCount.stringValue}"
							style="font-size: 8pt; text-align: right; vertical-align: middle"></h:outputText><h:outputText
							styleClass="outputText" id="text1" style="text-align: center; vertical-align: middle" value="件"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD width="559">
			<div class="listScroll" style="height:296px; " id="listScroll"  onscroll="setScrollPosition('scroll',this);" >
			<h:dataTable border="0" cellpadding="2" cellspacing="0"
				headerClass="headerClass" footerClass="footerClass"
				rowClasses="#{pc_Ssz01701.propSscKifrTsy.rowClasses}"
				styleClass="meisai_scroll" id="table1"
				value="#{pc_Ssz01701.propSscKifrTsy.list}" var="varlist"
				 width="540">
				<h:column id="column1">
					<f:facet name="header">
									<h:outputText id="lblListTsyCd" styleClass="outputText"
										value="コード"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListTsyCd"
									value="#{varlist.freTsyCd}"></h:outputText>
								<f:attribute value="110" name="width" />
							</h:column>
				<h:column id="column2">
					<f:facet name="header">
									<h:outputText styleClass="outputText" value="名称"
										id="lblListFreTsyName"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="htmlListFreTsyName"
									value="#{varlist.freTsyName}"></h:outputText>
								<f:attribute value="380" name="width" />
							</h:column>
				<h:column id="column3">
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Ssz01701.doSelectAction}"></hx:commandExButton>
								<f:facet name="header">
					</f:facet>
								<f:attribute value="24" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
						name="style" />
							</h:column>
				<h:column id="column4">
					<f:facet name="header">
					</f:facet>
								<hx:commandExButton type="submit" value="編集"
									styleClass="commandExButton" id="edit"
									action="#{pc_Ssz01701.doEditAction}"></hx:commandExButton>
								<f:attribute value="24" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
			</h:dataTable></div></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
						<TABLE border="0" width="100%">
				<TBODY>
					<TR>
						<TD width="18%"></TD>
						<TD align="left" width="598">
			<DIV align="left"><TABLE border="0" class="table">
				<TBODY>
					<TR>
						<TH class="v_a" width="200"><h:outputText styleClass="outputText"
										id="lblFreTsyCd" value="#{pc_Ssz01701.propFreTsyCd.labelName}"
										style="#{pc_Ssz01701.propFreTsyCd.labelStyle}"></h:outputText></TH>
						<TD width="332"><h:inputText styleClass="inputText"
										id="htmlFreTsyCd"
										value="#{pc_Ssz01701.propFreTsyCd.stringValue}"
										style="#{pc_Ssz01701.propFreTsyCd.style}" size="5"
										maxlength="#{pc_Ssz01701.propFreTsyCd.maxLength}"></h:inputText></TD>
					</TR>
					<TR>
						<TH class="v_b" width="200"><h:outputText styleClass="outputText"
										id="lblFreTsyName"
										value="#{pc_Ssz01701.propFreTsyName.labelName}"
										style="#{pc_Ssz01701.propFreTsyName.labelStyle}"></h:outputText></TH>
						<TD width="332"><h:inputText styleClass="inputText"
										id="htmlFreTsyName"
										value="#{pc_Ssz01701.propFreTsyName.stringValue}"
										style="#{pc_Ssz01701.propFreTsyName.style}" size="30"
										maxlength="#{pc_Ssz01701.propFreTsyName.maxLength}"></h:inputText></TD>
					</TR>
				</TBODY>
			</TABLE></DIV>
			</TD>
						<TD width="19%"></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Ssz01701.doRegisterAction}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
							type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" confirm="#{msg.SY_MSG_0004W}"
							action="#{pc_Ssz01701.doDeleteAction}"></hx:commandExButton><hx:commandExButton
							type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Ssz01701.doClearAction}"></hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			</DIV>
</DIV>
<!--↑content↑--> 
</DIV>
<!--↑outer↑-->
<!-- フッダーインクルード -->
<jsp:include page ="../../rev/inc/footer.jsp" />
			<h:inputHidden value="#{pc_Ssz01701.propSscKifrTsy.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
</hx:scriptCollector>
</BODY>
<jsp:include page ="../../rev/inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

