<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xre/Xre00301.java" --%><%-- /jsf:pagecode --%>

<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>

<HTML>
<HEAD>

<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">

<LINK href="../../rev/inc/gakuen.css" rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">

<TITLE>Xre00301.jsp</TITLE>

<SCRIPT type="text/javascript">

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
 <hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xre00301.onPageLoadBegin}">
  <h:form styleClass="form" id="form1">
   
   <!-- ヘッダーインクルード -->
   <jsp:include page ="../inc/header.jsp" />

   <!--↓OUTER↓-->
   <DIV class="outer">
    
    <FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
     <h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	 	styleClass="outputText" escape="false"></h:outputText>
	</FIELDSET>

    <DIV id="content">
	 <DIV class="column">
      <TABLE width="600px" border="0">
       <TBODY>
        <TR>
         <TD align="left">
          <TABLE width="500px" cellspacing="0" cellpadding="0" border="0" class="table">
           <TBODY>
            <TR>
             <TH class="v_a" width="135px">
              <h:outputText
                  id="lblOutputTarget"
                  styleClass="outputText"
                  value="#{pc_Xre00301.propUntrained.labelName}"
                  style="#{pc_Xre00301.propUntrained.labelStyle}">
              </h:outputText>
             </TH>
             <TD>
              <h:selectOneRadio
                  id="htmlUntrained"
                  styleClass="selectOneRadio"
                  disabledClass="selectOneRadio_Disabled"
                  layout="pageDirection"
                  value="#{pc_Xre00301.propUntrained.value}"
                  disabled="#{pc_Xre00301.propUntrained.disabled}">
               <f:selectItem itemValue="0" itemLabel="事前指導未修得者" />
               <f:selectItem itemValue="1" itemLabel="事前指導未修得者かつ未申込者" />
              </h:selectOneRadio>
             </TD>
            </TR>
           </TBODY>
          </TABLE>
         </TD>
         <TD valign="bottom" align="left">
          <hx:commandExButton
              id="htmlOutSelect"
              type="submit"
              value="選択" styleClass="commandExButton" 
              action="#{pc_Xre00301.doOutSelectAction}"
              disabled="#{pc_Xre00301.propOutSelect.disabled}">
          </hx:commandExButton>
          <hx:commandExButton
              id="htmlOutCancel"
              type="submit"
              value="解除" styleClass="commandExButton" 
              action="#{pc_Xre00301.doOutCancelAction}"
              disabled="#{pc_Xre00301.propOutCancel.disabled}">
          </hx:commandExButton>
         </TD>
        </TR>
       </TBODY>
      </TABLE>
      <BR/>
      <TABLE width="600px" border="0" class="table">
       <TBODY>
        <TR>
         <TH class="v_a" width="135px">
          <h:outputText
              id="lblExperienceYear"
              styleClass="outputText"
              value="#{pc_Xre00301.propExperienceYear.labelName}"
              style="#{pc_Xre00301.propExperienceYear.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:inputText styleClass="inputText"
              id="htmlExperienceYear"
              value="#{pc_Xre00301.propExperienceYear.stringValue}"
              style="#{pc_Xre00301.propExperienceYear.style}"
              maxlength="#{pc_Xre00301.propExperienceYear.maxLength}"
              disabled="#{pc_Xre00301.propExperienceYear.disabled}"
              size="10">
           <hx:inputHelperAssist errorClass="inputText_Error" />
          </h:inputText>
         </TD>
        </TR>
        <TR>
         <TH class="v_b" width="135px">
          <h:outputText
              id="lblSchoolingYear"
              styleClass="outputText"
              value="#{pc_Xre00301.propSchoolingYear.labelName}"
              style="#{pc_Xre00301.propSchoolingYear.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <h:selectOneMenu
              id="htmlSchoolingYear"
              styleClass="selectOneMenu"
              value="#{pc_Xre00301.propSchoolingYear.value}"
		      disabled="#{pc_Xre00301.propSchoolingYear.disabled}"
              style="width:200px">
           <f:selectItems value="#{pc_Xre00301.propSchoolingYear.list}" />
          </h:selectOneMenu>
          <hx:commandExButton
              id="htmlYearSelect"
              type="submit"
              value="選択" styleClass="commandExButton" 
              action="#{pc_Xre00301.doYearSelectAction}"
              disabled="#{pc_Xre00301.propYearSelect.disabled}">
          </hx:commandExButton>
          <hx:commandExButton
              id="htmlYearCancel"
              type="submit"
              value="解除" styleClass="commandExButton" 
              action="#{pc_Xre00301.doYearCancelAction}"
              disabled="#{pc_Xre00301.propYearCancel.disabled}">
          </hx:commandExButton>
         </TD>
        </TR>
        <TR>
         <TH class="v_c" width="135px">
          <h:outputText
              id="lblSchoolingType"
              styleClass="outputText"
              value="#{pc_Xre00301.propSchoolingType.labelName}"
              style="#{pc_Xre00301.propSchoolingType.labelStyle}">
          </h:outputText>
         </TH>
         <TD>
          <TABLE>
           <TBODY>
            <TR height="30px">
             <TD style="border: 0px; vertical-align: bottom">
              <SPAN style="margin-left: 6px">
               <h:outputText
                   id="lblTypeCode"
                   styleClass="outputText"
                   value="#{pc_Xre00301.propTypeCode.labelName}"
                   style="#{pc_Xre00301.propTypeCode.labelStyle}">
               </h:outputText>
              </SPAN>
              <SPAN style="margin-left: 30px">
               <h:outputText
                   id="lblTypeName"
                   styleClass="outputText"
                   value="#{pc_Xre00301.propTypeName.labelName}"
                   style="#{pc_Xre00301.propTypeName.labelStyle}">
               </h:outputText>
              </SPAN>
             </TD>
            </TR>
            <TR>
             <TD style="border: 0px">
              <h:selectManyListbox
                  id="htmlSchoolingType"
                  styleClass="selectManyListbox"
                  value="#{pc_Xre00301.propSchoolingType.value}"
		          disabled="#{pc_Xre00301.propSchoolingType.disabled}"
                  style="width:430px; height:200px">
               <f:selectItems value="#{pc_Xre00301.propSchoolingType.list}" />
              </h:selectManyListbox>
             </TD>
            </TR>
           </TBODY>
          </TABLE>
         </TD>
        </TR>
       </TBODY>
      </TABLE>
      <BR/>
      <hx:commandExButton
          id="htmlCsvOut"
          type="submit"
          value="CSV作成"
          styleClass="commandExButton_out"
          action="#{pc_Xre00301.doCsvOutAction}"
          disabled="#{pc_Xre00301.propCsvOut.disabled}"
          confirm="#{msg.SY_MSG_0020W}">
      </hx:commandExButton>
     </DIV>
    </DIV>
   </DIV>
   <!--↑OUTER↑-->
		
   <!-- フッダーインクルード -->
   <jsp:include page ="../inc/footer.jsp" />

  </h:form>
 </hx:scriptCollector>
</BODY>

<jsp:include page ="../inc/common.jsp" />

</f:view>
</HTML>
