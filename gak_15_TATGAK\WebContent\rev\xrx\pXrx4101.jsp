<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/PXrc0101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>pXrc0101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">

		function init() {
			window.name = window.opener.kamokWindowName;
		}
		function confirmOk() {	
			document.getElementById('form1:clearCount').value = "1";
			indirectClick('search');
		}
		
		function confirmCancel() {	
		}
//親画面に物品コードを返却
function doSelect(thisObj, thisEvent) {
	
	var buttonid = thisObj.id;
	// 正規表現にて選択行のindex値を取得
	var point_start = buttonid.search(":[0-9]*:");
	var point_end = buttonid.search(":btnSelect");
	var index = buttonid.substring(point_start+1,point_end);
	var buppinCd = document.getElementById("form1:htmlBuppinList:"+ index +":lblBuppinCdList").innerHTML;
	// 物品コード返却フィールドID
	var retFieldName = document.getElementById("form1:htmlRetFieldName").value;
	if (window.opener) {
		window.opener.document.getElementById(retFieldName).value = buppinCd;
		window.opener.document.getElementById(retFieldName).onblur(); // 親ウィンドウのAjaxを実行して名称をセット
	}
	window.close();
	return true;
}

// リストダブルクリック処理
function dblClick(id) {
	indirectClick(id);
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	
	<BODY onload="init();">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_PXrc0101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/childHeader.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_PXrc0101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_PXrc0101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_PXrc0101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">
			<!-- ↓ここに戻る／閉じるボタンを配置 --> 
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 -->

			<TABLE width="100%" border="0" cellpadding="3" cellspacing="0">
				<TBODY>
					<TR>
						<TD>
						<TABLE class="table" width="620">
							<TBODY>
								<TR>
									<TH nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblBuppinCd"
										value="#{pc_PXrc0101.propBuppinCd.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlBuppinCd" size="18"
										value="#{pc_PXrc0101.propBuppinCd.stringValue}"
										style="#{pc_PXrc0101.propBuppinCd.style}"
										maxlength="#{pc_PXrc0101.propBuppinCd.maxLength}">
									</h:inputText></TD>
									<TD><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlBuppinCdFindType"
										value="#{pc_PXrc0101.propBuppinCdFindType.value}">
										<f:selectItem itemValue="1" itemLabel="部分一致" />
										<f:selectItem itemValue="0" itemLabel="前方一致" />
									</h:selectOneRadio></TD>
								</TR>
								<TR>
									<TH nowrap class="v_a" width="110"><h:outputText
										styleClass="outputText" id="lblBuppinNm"
										value="#{pc_PXrc0101.propBuppinNm.labelName}"></h:outputText></TH>
									<TD><h:inputText styleClass="inputText"
										id="htmlBuppinNm" size="50"
										value="#{pc_PXrc0101.propBuppinNm.stringValue}"
										style="#{pc_PXrc0101.propBuppinNm.style}"
										maxlength="#{pc_PXrc0101.propBuppinNm.maxLength}">
									</h:inputText></TD>
									<TD><h:selectOneRadio
										disabledClass="selectOneRadio_Disabled"
										styleClass="selectOneRadio" id="htmlBuppinFindType"
										value="#{pc_PXrc0101.propBuppinNmFindType.value}">
										<f:selectItem itemValue="1" itemLabel="部分一致" />
										<f:selectItem itemValue="0" itemLabel="前方一致" />
									</h:selectOneRadio></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE cellspacing="1" cellpadding="1" class="button_bar" width="620">
							<TBODY>
								<TR align="right">
									<TD align="center"><hx:commandExButton
										type="submit" value="検　索" styleClass="commandExButton_dat"
										id="search"
										action="#{pc_PXrc0101.doSearchAction}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear"
										action="#{pc_PXrc0101.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE cellpadding="0" cellspacing="0" width="620">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText styleClass="outputText"
										id="lblCount" value="#{pc_PXrc0101.propBuppinlist.listCount}件"></h:outputText>
									</TD>
								</TR>
								<TR>
									<TD colspan="3">
										<div class="listScroll" style="width: 621px; height: 300px"><h:dataTable
											width="604px" border="1" cellpadding="2" cellspacing="0"
											columnClasses="columnClass1" headerClass="headerClass"
											footerClass="footerClass"
											rowClasses="#{pc_PXrc0101.propBuppinlist.rowClasses}"
											styleClass="meisai_scroll" id="htmlBuppinList"
											value="#{pc_PXrc0101.propBuppinlist.list}" var="varlist">
											<h:column id="column1">
												<f:facet name="header">
													<h:outputText id="lblBuppinCdHead" styleClass="outputText"
														value="物品コード"></h:outputText>
												</f:facet>
												<f:attribute value="75" name="width" />
												<h:outputText styleClass="outputText" id="lblBuppinCdList"
													value="#{varlist.buppinCd}"></h:outputText>
											</h:column>
											<h:column id="column2">
												<f:facet name="header">
													<h:outputText styleClass="outputText" value="物品名称"
														id="lblBuppinNameHead"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="lblBuppinNameList"
													value="#{varlist.buppinName}"></h:outputText>
												<f:attribute value="464" name="width" />
											</h:column>
											<h:column id="column3">
												<f:facet name="header">
												  <h:outputText styleClass="outputText" value="物品区分" id="text5"></h:outputText>
												</f:facet>
												<h:outputText styleClass="outputText" id="lblBuppinKbnName"
													value="#{varlist.buppinKbnName}"></h:outputText>
												<f:attribute value="100" name="width" />
											</h:column>
											<h:column id="column4">
												<f:facet name="header">
													<h:outputText styleClass="outputText"
														id="lblButton_select_head"></h:outputText>
												</f:facet>
												<f:attribute value="40" name="width" />
												<hx:commandExButton type="button"
												value="選択" styleClass="commandExButton" id="btnSelect"
												onclick="return doSelect(this, event);"></hx:commandExButton>
											</h:column>
										</h:dataTable>
										</div>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden id="htmlRetFieldName"
				value="#{pc_PXrc0101.propRetFieldName.value}"></h:inputHidden>
			<!-- ↑ここにコンポーネントを配置 -->
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/childFooter.jsp" />
			<h:inputHidden value="#{pc_PXrc0101.propClearButton.integerValue}"
				id="clearCount">
				<f:convertNumber type="number" />
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>

</HTML>
