<%-- 
	最長在学年数登録
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xri/Xri00101.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xri00101.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">

<SCRIPT type="text/javascript">
	function confirmOk() {
		document.getElementById('form1:htmlExecutable').value = "1";
		indirectClick('delete');
		return true;
	}
	function confirmCancel() {
		return false;
	}
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xri00101.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xri00101.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xri00101.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xri00101.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" width="500" class="table" align="center">
				<TBODY>
					<TR>
						<TH class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblNyugakuNenji"
							value="#{pc_Xri00101.propNyugakuNenji.labelName}"
							style="#{pc_Xri00101.propNyugakuNenji.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlNyugakuNenji"
							value="#{pc_Xri00101.propNyugakuNenji.integerValue}"
							maxlength="#{pc_Xri00101.propNyugakuNenji.maxLength}" size="4"
							style="#{pc_Xri00101.propNyugakuNenji.style}"
							disabled="#{pc_Xri00101.propNyugakuNenji.disabled}">
							<f:convertNumber type="number" pattern="###0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText></TD>
						<TD width="100">
							<hx:commandExButton type="submit" value="選択"
								styleClass="commandExButton" id="topSelect"
								action="#{pc_Xri00101.doTopSelectAction}"
								disabled="#{pc_Xri00101.propSelectNyugakuNenji.disabled}">
							</hx:commandExButton>
							<hx:commandExButton type="submit" value="解除"
								styleClass="commandExButton" id="topUnSelect"
								action="#{pc_Xri00101.doTopUnSelectAction}"
								disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<BR>
			<TABLE border="0" align="center" width="500">
				<TBODY>
					<TR>
						<TD align="right"><h:outputText styleClass="outputText" id="text1"
							style="font-size: 8pt"
							value="#{pc_Xri00101.propCount.stringValue}"></h:outputText><h:outputText
							styleClass="outputText" id="text2" value="件"
							style="font-size: 8pt"></h:outputText></TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<TABLE border="0" align="center" width="500">
				<TBODY>
					<TR>
						<TD>
						<div class="listScroll" style="height:229px;"
							id="listScroll" onscroll="setScrollPosition('scroll',this);"><B>
						<h:dataTable border="0" cellpadding="2" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xri00101.propNenGkki.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xri00101.propNenGkki.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText id="text3" styleClass="outputText" value="入学年次"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text8"
									value="#{varlist.nyugakuNenjiName}"></h:outputText>
								<f:attribute value="text-align: right; vertical-align: middle"
									name="style" />
								<f:attribute value="100" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="就学種別" id="text4"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text9"
									value="#{varlist.syugakuSbtName}"></h:outputText>
								<f:attribute value="200" name="width" />
							</h:column>
							<h:column id="column3">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="最長在学年数" id="text5"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text10"
									value="#{varlist.maxZaigaku}"></h:outputText>
								<f:attribute value="text-align:right; vertical-align: middle"
									name="style" />
								<f:attribute value="150" name="width" />
							</h:column>
							<h:column id="column4">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="休学可能年数" id="text6"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text11"
									value="#{varlist.maxKyugaku}"></h:outputText>
								<f:attribute value="text-align:right; vertical-align: middle"
									name="style" />
								<f:attribute value="150" name="width" />
							</h:column>
							<h:column id="column5">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="修業年限" id="text7"></h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="text12"
									value="#{varlist.syugyoNengen}"></h:outputText>
								<f:attribute value="120" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle"
									name="style" />
							</h:column>
							<h:column id="column6">
								<f:facet name="header">
								</f:facet>
								<hx:commandExButton type="submit" value="選択"
									styleClass="commandExButton" id="select"
									action="#{pc_Xri00101.doSelectAction}"
									style="text-align: center; vertical-align: middle"></hx:commandExButton>
								<f:attribute value="26" name="width" />
								<f:attribute value="text-align: center; vertical-align: middle" name="style" />
							</h:column>
						</h:dataTable></B></div>
						</TD>
						<TD></TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR class="hr">
			<BR>
			<TABLE border="0" width="500" class="table" align="center">
				<TBODY>
					<TR>
						<TH class="v_c" width="150"><h:outputText
							styleClass="outputText" id="lblEditNyugakuNenji" value="入学年次"
							style="#{pc_Xri00101.propEditNyugakuNenji.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlEditNyugakuNenji"
							value="#{pc_Xri00101.propEditNyugakuNenji.value}"
							style="#{pc_Xri00101.propEditNyugakuNenji.style};width:150px"
							disabled="#{pc_Xri00101.propEditNyugakuNenji.disabled}">
							<f:selectItems value="#{pc_Xri00101.propEditNyugakuNenji.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH class="v_c" width="150"><h:outputText
							styleClass="outputText" id="lblEditSyugakuSbt" value="就学種別"
							style="#{pc_Xri00101.propEditSyugakuSbt.labelStyle}"></h:outputText></TH>
						<TD><h:selectOneMenu styleClass="selectOneMenu"
							id="htmlEditSyugakuSbt"
							value="#{pc_Xri00101.propEditSyugakuSbt.value}"
							style="#{pc_Xri00101.propEditSyugakuSbt.style};width:150px"
							disabled="#{pc_Xri00101.propEditNyugakuNenji.disabled}">
							<f:selectItems value="#{pc_Xri00101.propEditSyugakuSbt.list}" />
						</h:selectOneMenu></TD>
					</TR>
					<TR>
						<TH class="v_a" width="150"><h:outputText
 							styleClass="outputText" id="lblEditZaigakuNensu" value="最長在学年数"
 							style="#{pc_Xri00101.propEditZaigakuNensu.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlEditZaigakuNensu"
							value="#{pc_Xri00101.propEditZaigakuNensu.integerValue}"
							maxlength="#{pc_Xri00101.propEditZaigakuNensu.maxLength}" size="2"
							style="#{pc_Xri00101.propEditZaigakuNensu.style}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText>年　
						<h:selectOneMenu styleClass="selectOneMenu"
							id="htmlEditZaigakuTuki"
							value="#{pc_Xri00101.propEditZaigakuTuki.value}"
							style="#{pc_Xri00101.propEditZaigakuTuki.style}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}">
							<f:selectItems value="#{pc_Xri00101.propEditZaigakuTuki.list}" />
						</h:selectOneMenu>ヶ月</TD>
					</TR>
					<TR>
						<TH class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblKyugakuNensu" value="休学可能年数"
							style="#{pc_Xri00101.propEditKyugakuNensu.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlEditKyugakuNensu"
							value="#{pc_Xri00101.propEditKyugakuNensu.integerValue}"
							maxlength="#{pc_Xri00101.propEditKyugakuNensu.maxLength}" size="2"
							style="#{pc_Xri00101.propEditKyugakuNensu.style}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText>年　
						<h:selectOneMenu styleClass="selectOneMenu"
							id="htmlEditKyugakuTuki"
							value="#{pc_Xri00101.propEditKyugakuTuki.value}"
							style="#{pc_Xri00101.propEditKyugakuTuki.style}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}">
							<f:selectItems value="#{pc_Xri00101.propEditKyugakuTuki.list}" />
						</h:selectOneMenu>ヶ月</TD>
					</TR>
					<TR>
						<TH class="v_a" width="150"><h:outputText
							styleClass="outputText" id="lblEditSyugyoNengen" value="修業年限"
							style="#{pc_Xri00101.propNyugakuNenji.labelStyle}"></h:outputText></TH>
						<TD><h:inputText styleClass="inputText"
							id="htmlEditSyugyoNengen"
							value="#{pc_Xri00101.propEditSyugyoNengen.integerValue}"
							maxlength="#{pc_Xri00101.propEditSyugyoNengen.maxLength}" size="2"
							style="#{pc_Xri00101.propEditSyugyoNengen.style}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}">
							<f:convertNumber type="number" pattern="#0"/>
							<hx:inputHelperAssist errorClass="inputText_Error" promptCharacter="_" />
						</h:inputText>年　
						<h:selectOneMenu styleClass="selectOneMenu"
							id="htmlEditSyugyoNengenTuki"
							value="#{pc_Xri00101.propEditSyugyoNengenTuki.value}"
							style="#{pc_Xri00101.propEditSyugyoNengenTuki.style}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}">
							<f:selectItems value="#{pc_Xri00101.propEditSyugyoNengenTuki.list}" />
						</h:selectOneMenu>ヶ月</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<HR class="hr">
			<BR>
			<TABLE border="0" width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Xri00101.doRegisterAction}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}"
							confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton>
							<hx:commandExButton type="submit" value="削除" styleClass="commandExButton_dat"
							id="delete" action="#{pc_Xri00101.doDeleteAction}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}"
							confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
							id="clear" action="#{pc_Xri00101.doClearAction}"
							disabled="#{pc_Xri00101.propUnSelectNyugakuNenji.disabled}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 -->
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xri00101.propNenGkki.scrollPosition}"
				id="scroll"></h:inputHidden>			
			<h:inputHidden value="#{pc_Xri00101.propExecutable.integerValue}"
				id="htmlExecutable">
				<f:convertNumber />
			</h:inputHidden>				
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

