<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/ss/Ssz03401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Ssz03401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">
</SCRIPT>

<LINK rel="stylesheet" type="text/css" href="../../theme/tabpanel.css"
	title="Style">
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Ssz03401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Ssz03401.doCloseDispAction}"></hx:commandExButton>
			<h:outputText styleClass="outputText" id="htmlFuncId"
				value="#{pc_Ssz03401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Ssz03401.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area"><!-- ↓ここに戻る／閉じるボタンを配置 -->　<!-- ↑ここに戻る／閉じるボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center"><!-- ↓ここにコンポーネントを配置 --> <!-- ↑ここにコンポーネントを配置 -->
			
			<TABLE width="100%" align="center">
				<TR>
					<TD>
						<TABLE width="780">
							<TR>
								<TD align="right"><hx:commandExButton
													type="submit" value="年度コピー" styleClass="commandExButton"
													id="copy" action="#{pc_Ssz03401.doCopyAction}"></hx:commandExButton>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE width="583">
							<TBODY>
								<TR>
									<TD align="right"><h:outputText
										styleClass="outputText" id="htmlCount"
										value="#{pc_Ssz03401.propCount.stringValue}"></h:outputText><h:outputText
										styleClass="outputText" id="text3" value="件"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						<TABLE width="560">
							<TR>
								<TD>
								<div id="listScroll" class="listScroll"
									style="height:146px;"
									onscroll="setScrollPosition('scroll',this);"><h:dataTable
									border="0" cellpadding="2" cellspacing="0"
									headerClass="headerClass" footerClass="footerClass"
									rowClasses="#{pc_Ssz03401.propMokutekiList.rowClasses}"
									styleClass="meisai_scroll" id="table1" width="560"
									value="#{pc_Ssz03401.propMokutekiList.list}" var="varlist">
									<h:column id="column1">
										<f:facet name="header">
											<h:outputText styleClass="outputText"
												value="#{pc_Ssz03401.propNendo.name}" id="text6"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="text9"
											value="#{varlist.visitNendo}"></h:outputText>
										<f:attribute value="72" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>
									<h:column id="column2">
										<f:facet name="header">
											<h:outputText id="text1" styleClass="outputText" value="コード"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="text4"
											value="#{varlist.visitMokutekiCd}"></h:outputText>
										<f:attribute value="62" name="width" />
									</h:column>
									<h:column id="column3">
										<f:facet name="header">
											<h:outputText styleClass="outputText" value="内容" id="text2"></h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="text5"
											value="#{varlist.mokutekiOut.displayValue}"
											title="#{varlist.mokutekiOut.value}"></h:outputText>
										<f:attribute value="396" name="width" />
									</h:column>
									<h:column id="column4">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit" value="選択"
											styleClass="commandExButton" id="select"
											action="#{pc_Ssz03401.doSelectAction}"></hx:commandExButton>
										<f:attribute value="30" name="width" />
										<f:attribute value="text-align: center" name="style" />
									</h:column>
								</h:dataTable></div>
								</TD>
							</TR>
						</TABLE>
						<br>
						<TABLE width="583">
							<TR>
								<TD>
								<TABLE border="0" width="100%" class="table">
									<TBODY>
										<TR>
											<TH class="v_b" width="30%"><h:outputText styleClass="outputText"
												style="#{pc_Ssz03401.propNendo.labelStyle}" id="lblNendo"
												value="#{pc_Ssz03401.propNendo.labelName}"></h:outputText></TH>
											<TD width="70%"><h:inputText styleClass="inputText" id="htmlNendo"
												value="#{pc_Ssz03401.propNendo.dateValue}"
												style="#{pc_Ssz03401.propNendo.style}" size="3">
												<hx:inputHelperAssist errorClass="inputText_Error"
												imeMode="inactive" promptCharacter="_" />
												<f:convertDateTime pattern="yyyy" /></h:inputText></TD>
										</TR>
										<TR>
											<TH class="v_a"><h:outputText styleClass="outputText"
												id="lblMokutekiCd"
												value="#{pc_Ssz03401.propMokutekiCd.labelName}"
												style="#{pc_Ssz03401.propMokutekiCd.labelStyle}"></h:outputText></TH>
											<TD><h:inputText styleClass="inputText" id="htmlMokutekiCd"
												value="#{pc_Ssz03401.propMokutekiCd.stringValue}"
												style="#{pc_Ssz03401.propMokutekiCd.style}"
												maxlength="#{pc_Ssz03401.propMokutekiCd.maxLength}" size="3"></h:inputText></TD>
										</TR>
										<TR>
											<TH class="v_c"><h:outputText styleClass="outputText"
												id="lblMokuteki"
												value="#{pc_Ssz03401.propMokuteki.labelName}"></h:outputText></TH>
											<TD><h:inputTextarea styleClass="inputTextarea"
												id="htmlMokuteki"
												value="#{pc_Ssz03401.propMokuteki.stringValue}"
												style="#{pc_Ssz03401.propMokuteki.style}" cols="50" rows="10"></h:inputTextarea></TD>
										</TR>
									</TBODY>
								</TABLE>
								</TD>
							</TR>
						</TABLE>
						<br>
						<TABLE border="0" cellpadding="0" cellspacing="0" width="100%"
							class="button_bar">
							<TBODY>
								<TR>
									<TD><hx:commandExButton type="submit" value="確定"
										styleClass="commandExButton_dat" id="register"
										action="#{pc_Ssz03401.doRegisterAction}"
										confirm="#{msg.SY_MSG_0001W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="削除" styleClass="commandExButton_dat"
										id="delete" action="#{pc_Ssz03401.doDeleteAction}"
										confirm="#{msg.SY_MSG_0004W}"></hx:commandExButton><hx:commandExButton
										type="submit" value="クリア" styleClass="commandExButton_etc"
										id="clear" action="#{pc_Ssz03401.doClearAction}"></hx:commandExButton></TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TABLE>
			</DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<h:inputHidden value="#{pc_Ssz03401.propMokutekiList.scrollPosition}"
				id="scroll"></h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	<jsp:include page="../inc/footer.jsp" />
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT language="javaScript">

changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

