<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrl/Xrl00501T05.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrl00501T05.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	

<SCRIPT type="text/javascript">
</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
<BODY>
<hx:scriptCollector id="scriptCollector1" preRender="#{pc_Xrl00501T05.onPageLoadBegin}">
<h:form styleClass="form" id="form1">

<!-- ヘッダーインクルード -->
<jsp:include page ="../../rev/inc/header.jsp" />		

<!-- ヘッダーへのデータセット領域 -->
<div style="display:none;">
<hx:commandExButton type="submit" value="閉じる"
	styleClass="commandExButton" id="closeDisp"
	action="#{pc_Xrl00501T05.doCloseDispAction}"
	></hx:commandExButton>
<h:outputText styleClass="outputText" id="htmlFuncId" value="#{pc_Xrl00501T05.funcId}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlLoginId" value="#{SYSTEM_DATA.loginID}"></h:outputText>
<h:outputText styleClass="outputText" id="htmlScrnName" value="#{pc_Xrl00501T05.screenName}"></h:outputText>
</div>			
			
<!--↓outer↓-->
<DIV class="outer">

<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND>
<h:outputText id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
	styleClass="outputText" escape="false">
</h:outputText>
</FIELDSET>			
<DIV class="head_button_area">　<!-- ↓ここに戻る／閉じるボタンを配置 --></DIV>
<!--↓content↓-->
<DIV id="content"><!-- ↓ここにコンポーネントを配置 -->
<DIV class="column">
			<TABLE border="0" cellpadding="0" width="650"">
				<TBODY>
					<TR>
						<TD width="650">
						  <TABLE width="100%" border="0" cellpadding="0" cellspacing="0"
								class="table">
							<TBODY>
								<TR>
									<TH nowrap class="v_a">
					           		<!-- 手当データ作成対象 -->
					                	<h:outputText styleClass="outputText" id="lblTeateDataKbn"
					            		value="#{pc_Xrl00501T01.xrl00501.propTeateDataKbn.labelName}"
					            		style="#{pc_Xrl00501T01.xrl00501.propTeateDataKbn.labelStyle}"></h:outputText>
					            	</TH>
					            	<TD><h:selectOneMenu styleClass="selectOneMenu"
						                    id="htmlTeateDataKbn"
						                    value="#{pc_Xrl00501T01.xrl00501.propTeateDataKbn.stringValue}"
						                    disabled="#{pc_Xrl00501T01.xrl00501.propTeateDataKbn.disabled}"
						                    style="width:230px;">
						                  	<f:selectItems value="#{pc_Xrl00501T01.xrl00501.propTeateDataKbn.list}" />
						                </h:selectOneMenu>
						            </TD>
					            	<TD style = "border:none;background:none">
					            		<hx:commandExButton type="submit" value="選択"
						           			styleClass="commandExButton" id="selectSelOne"
						                 	disabled="#{pc_Xrl00501T01.xrl00501.propSelect.disabled}"
						                 	action="#{pc_Xrl00501T01.doSelectTeateDataAction}"></hx:commandExButton>
						                <hx:commandExButton
						                 	type="submit" value="解除" styleClass="commandExButton"
						                 	id="unselectSelOne"
						                  	disabled="#{pc_Xrl00501T01.xrl00501.propUnselect.disabled}"
						                  	action="#{pc_Xrl00501T01.doUnselectTeateDataAction}"></hx:commandExButton>
					            	</TD>
								</TR>
								<TR>
									<TH width="180" nowrap class="v_b"><h:outputText
										styleClass="outputText" id="lblTeateSakuseiDate"
										value="#{pc_Xrl00501T01.xrl00501.propTeateSakuseiDate.labelName}"
										style="#{pc_Xrl00501T01.xrl00501.propTeateSakuseiDate.labelStyle}"></h:outputText></TH>
									<TD width="250">
										<h:inputText styleClass="inputText" id="htmlTeateSakuseiDate"
											size="12"
											value="#{pc_Xrl00501T01.xrl00501.propTeateSakuseiDate.dateValue}"
											readonly="#{pc_Xrl00501T01.xrl00501.propTeateSakuseiDate.readonly}"
											disabled="#{pc_Xrl00501T01.xrl00501.propTeateSakuseiDate.disabled}"
											style="#{pc_Xrl00501T01.xrl00501.propTeateSakuseiDate.style}">
											<f:convertDateTime />
											<hx:inputHelperDatePicker />
											<hx:inputHelperAssist errorClass="inputText_Error"
												promptCharacter="_" />
										</h:inputText>
									</TD>
									<TD style = "border:none;background:none" width="220">
					            	</TD>
								</TR>
							</TBODY>	
						</TABLE>
						<BR>
						</TD>
					</TR>
					
					<TR>
						<TD width="650" align="left">
						<TABLE  border="0" cellpadding="0" cellspacing="0" width="650" class="tab_body">
							<TBODY>
								<TR>
									<TD width="100%" valign="top" height="400">
										<TABLE border="0" cellpadding="0" cellspacing="0" width="600" class="table" style="margin: 23px">
											<TBODY>
												<TR>
												<!-- 作問返却日 -->
								                  	<TH width="180" class="v_c"><h:outputText
								                    	styleClass="outputText" id="lblSakmonHenkyakDate"
								                    	value="#{pc_Xrl00501T05.propSakmonHenkyakDateFrom.labelName}" 
								                    	style="#{pc_Xrl00501T05.propSakmonHenkyakDateFrom.labelStyle}"></h:outputText></TH>
								                  	<TD width="400">
									                  	<h:inputText id="htmlSakmonHenkyakDateFrom"
															styleClass="inputText" size="12"
															value="#{pc_Xrl00501T05.propSakmonHenkyakDateFrom.dateValue}"
															readonly="#{pc_Xrl00501T05.propSakmonHenkyakDateFrom.readonly}"
															disabled="#{pc_Xrl00501T05.propSakmonHenkyakDateFrom.disabled}"
															style="#{pc_Xrl00501T05.propSakmonHenkyakDateFrom.style}">
															<f:convertDateTime />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
															<hx:inputHelperDatePicker />
														</h:inputText>
																～
														<h:inputText id="htmlSakmonHenkyakDateTo"
															styleClass="inputText" size="12"
															value="#{pc_Xrl00501T05.propSakmonHenkyakDateTo.dateValue}"
															readonly="#{pc_Xrl00501T05.propSakmonHenkyakDateTo.readonly}"
															disabled="#{pc_Xrl00501T05.propSakmonHenkyakDateTo.disabled}"
															style="#{pc_Xrl00501T05.propSakmonHenkyakDateTo.style}">
															<f:convertDateTime />
															<hx:inputHelperDatePicker />
															<hx:inputHelperAssist errorClass="inputText_Error"
																promptCharacter="_" />
														</h:inputText>
										            </TD>
								                </TR>
												<TR>
												<!-- 手当業務名称 -->
									           		<TH width="150px" nowrap class="v_d" height="2"><h:outputText
														styleClass="outputText" id="lblTeateGyomuNmMeisai"
														value="#{pc_Xrl00501T05.propTeateGyomuNm.labelName}"
														style="#{pc_Xrl00501T05.propTeateGyomuNm.labelStyle}">
													</h:outputText></TH>
													<TD nowrap width="*" height="2"><h:selectOneMenu styleClass="selectOneMenu"
														id="htmlTeateGyomuNm"
														value="#{pc_Xrl00501T05.propTeateGyomuNm.stringValue}"
														disabled="#{pc_Xrl00501T05.propTeateGyomuNm.disabled}"
														style="width:400px;">
														<f:selectItems value="#{pc_Xrl00501T05.propTeateGyomuNm.list}" />
													</h:selectOneMenu></TD>
												</TR>
											</TBODY>
										</TABLE>

									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD>
						<TABLE border="0" cellspacing="0" class="button_bar" width="100%">
							<TBODY>
								<TR>
									<TD width="" nowrap>
										<hx:commandExButton type="submit"
											value="実行" styleClass="commandExButton_dat"
											id="exec"
											action="#{pc_Xrl00501T05.doExecAction}"
											disabled="#{pc_Xrl00501T01.xrl00501.propExec.disabled}"
											confirm="#{msg.SY_MSG_0001W}">
										</hx:commandExButton>
									</TD>
								</TR>
							</TBODY>
						</TABLE>
					</TD>
				</TR>
			</TBODY>
		</TABLE>		
		<!-- ↑ここにコンポーネントを配置 --></DIV>
		<!--↑content↑--></DIV>
		<!--↑outer↑--></DIV>
		<!-- フッダーインクルード -->
		<jsp:include page="../inc/footer.jsp" />
		</h:form>
		</hx:scriptCollector>
		</BODY>
		<jsp:include page="../inc/common.jsp" />
	</f:view>

</HTML>
