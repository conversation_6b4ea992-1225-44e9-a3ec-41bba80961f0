<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrg/Xrg01401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrg01401.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
	
<SCRIPT type="text/javascript">
	function confirmOk() {
		document.getElementById('form1:htmlExecutableSearch').value = "0";
		indirectClick('search');
	}
	function confirmCancel() {
		document.getElementById('form1:htmlExecutableSearch').value = "1";
	}
	
	var schSbtCd = "";
	function getSchoolingSbtCb() {
		// スクーリング種別コンボボックス取得AJAX
		var servlet = "rev/xrg/XrgSchoolingSbtCbAJAX";
		var args = new Array();
		args['nendo'] = document.getElementById('form1:htmlNendo').value;
		args['jukoSekyuFlg'] = "true";
		args['bunrui'] = "1"
		var target = "";
	
		comb = document.getElementById('form1:htmlSchooling');
		schSbtCd = comb.options[comb.selectedIndex].value;
	
		var ajaxUtil = new AjaxUtil();
		ajaxUtil.getPluralValue(servlet, target, args);
	}
	
	function callBackMethod(value){
	var comb = document.getElementById('form1:htmlSchooling');
	var length = value['length'];
	comb.length = length;
	for(i = 0; i < length; i++){
		comb.options[i].value = value['key' + i];
		comb.options[i].text = value['value' + i];
		if(i == 0){
			comb.options[i].selected = true;
		}
		if(schSbtCd == comb.options[i].value){
			comb.options[i].selected = true;
		}
	}
	}
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrg01401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrg01401.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrg01401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrg01401.screenName}"></h:outputText></div>
			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area"><!-- ↓ここに戻るボタンを配置 --> <!-- ↑ここに戻るボタンを配置 -->
			</DIV>

			<!--↓content↓-->
			<DIV id="content">
			<DIV class="column" align="center">
			<TABLE width="520px">
				<TBODY>
					<TR>
						<TD>
						<TABLE class="table">
							<TBODY>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblNendo"
										value="#{pc_Xrg01401.propNendo.labelName}"
										style="#{pc_Xrg01401.propNendo.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="350"><h:inputText id="htmlNendo"
										styleClass="inputText"
										value="#{pc_Xrg01401.propNendo.dateValue}"
										onblur="getSchoolingSbtCb();" size="4">
										<hx:inputHelperAssist errorClass="inputText_Error"
											imeMode="inactive" promptCharacter="_" />
										<f:convertDateTime pattern="yyyy" />
									</h:inputText></TD>
								</TR>
								<TR>
									<TH class="v_a" width="150"><h:outputText
										styleClass="outputText" id="lblSchooling"
										value="#{pc_Xrg01401.propSchooling.labelName}"
										style="#{pc_Xrg01401.propSchooling.labelStyle}">
									</h:outputText></TH>
									<TD nowrap width="350"><h:selectOneMenu
										styleClass="selectOneMenu" id="htmlSchooling"
										value="#{pc_Xrg01401.propSchooling.value}"
										readonly="#{pc_Xrg01401.propSchooling.readonly}"
										style="#{pc_Xrg01401.propSchooling.style}; width:330px">
										<f:selectItems value="#{pc_Xrg01401.propSchooling.list}" />
									</h:selectOneMenu></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
					<TR>
						<TD><BR />
						<hx:commandExButton type="submit" value="検索"
							styleClass="commandExButton_dat" id="search"
							action="#{pc_Xrg01401.doSearchAction}"
							disabled="#{pc_Xrg01401.propSearch.disabled}"
							style="#{pc_Xrg01401.propSearch.style}">
						</hx:commandExButton> <BR />
						</TD>
					</TR>
					<TR>
						<TD align="right"><h:outputText styleClass="outputText"
							id="htmlCount" style="#{pc_Xrg01401.propCount.style}"
							value="#{pc_Xrg01401.propCount.stringValue}">
						</h:outputText> <h:outputText styleClass="outputText" id="text1"
							value="件">
						</h:outputText></TD>
					</TR>
					<TR>
						<TD>
						<div class="listScroll" style="height:350px;" id="listScroll"
							onscroll="setScrollPosition('scroll',this);"><B> <h:dataTable
							border="0" cellpadding="0" cellspacing="0"
							headerClass="headerClass" footerClass="footerClass"
							rowClasses="#{pc_Xrg01401.propMinousyaList.rowClasses}"
							styleClass="meisai_scroll" id="table1"
							value="#{pc_Xrg01401.propMinousyaList.list}" var="varlist">
							<h:column id="column1">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学籍番号"
										id="lblGakusekiNum">
									</h:outputText>
								</f:facet>
								<h:outputText styleClass="outputText" id="GakusekiNum"
									value="#{varlist.gakusekiNo}" style="width: 150px">
								</h:outputText>
								<f:attribute value="150" name="width" />
							</h:column>
							<h:column id="column2">
								<f:facet name="header">
									<h:outputText styleClass="outputText" value="学生氏名"
										id="lblGakuseiName">
									</h:outputText>
								</f:facet>
								<h:outputText id="GakuseiName" styleClass="outputText"
									value="#{varlist.gakuseiName.displayValue}"
									title="#{varlist.gakuseiName.stringValue}"
									style="width: 350px">
								</h:outputText>
								<f:attribute value="400" name="width" />
							</h:column>
						</h:dataTable> </B></div>
						</TD>
					</TR>
					<TR>
						<TD><BR />
						</TD>
					</TR>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							confirm="#{msg.SY_MSG_0002W}"
							action="#{pc_Xrg01401.doDeterminateAction}"
							disabled="#{pc_Xrg01401.propRegister.disabled}"
							style="#{pc_Xrg01401.propRegister.style}">
						</hx:commandExButton> <hx:commandExButton type="submit"
							value="CSV作成" styleClass="commandExButton_dat" id="csv"
							confirm="#{msg.SY_MSG_0020W}" 
							action="#{pc_Xrg01401.doCsvoutAction}"
							disabled="#{pc_Xrg01401.propCsv.disabled}"
							style="#{pc_Xrg01401.propCsv.style}">
						</hx:commandExButton></TD>
					</TR>
				</TBODY>
			</TABLE>
			<h:inputHidden
				value="#{pc_Xrg01401.propExecutableSearch.stringValue}"
				id="htmlExecutableSearch">
			</h:inputHidden>
			</DIV>
			</DIV>
			</DIV>
		</h:form>
		<!-- フッターインクルード -->
		<jsp:include page="../inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
