<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrh/Xrh01401.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css" title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css"  >	
<LINK href="${pageContext.request.contextPath}/theme/Master.css"
	rel="stylesheet" type="text/css">
<LINK rel="stylesheet" type="text/css"
	href="${pageContext.request.contextPath}/theme/stylesheet.css"
	title="Style">

<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<STYLE>

.note {
font-size: 10pt;
color: #000000;
}
</STYLE>
<SCRIPT type="text/javascript">
</SCRIPT>
</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
<f:loadBundle basename="properties.message" var="msg"/>
	<BODY>
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrh01401.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">
		<!-- ヘッダーインクルード -->
		<jsp:include page ="../../rev/inc/header.jsp" />
			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrh01401.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrh01401.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrh01401.screenName}"></h:outputText></div>
		<!--↓outer↓-->
		<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			
			<DIV id="content">
				<DIV class="column" align="center">

				<TABLE border="0" cellpadding="0" cellspacing="0" class="table" width="700" >
					<TBODY>

					   <TR>
			              <TH width="150" nowrap bgcolor="silver" class="v_a">
			              	<h:outputText
								styleClass="outputText" id="lblInputFile"
								value="#{pc_Xrh01401.propInputFile.labelName}"
								style="#{pc_Xrh01401.propInputFile.labelStyle}">
							</h:outputText>
							<BR>
							(前回ファイル)
						  </TH>
						  <TD nowrap width="600">
						  	<hx:fileupload styleClass="fileupload"
								id="htmlInputFile" size="50"
								value="#{pc_Xrh01401.propInputFile.value}" 
								style="width:585px" tabindex="1">
								<hx:fileProp name="fileName" 
									value="#{pc_Xrh01401.propInputFile.fileName}"/>
								<hx:fileProp name="contentType"	
									value="#{pc_Xrh01401.propInputFile.contentType}" />
							</hx:fileupload>
							<BR>
							<h:outputText styleClass="outputText" id="htmlInputFileOld"
								value="#{pc_Xrh01401.propInputFileOld.stringValue}">
							</h:outputText>
							<BR>
						  </TD>
			          </TR>
			          
			          <TR>
			          	 <TH class="v_a" width="190">
								<h:outputText styleClass="outputText" id="lblBunruiSelect"
									value="#{pc_Xrh01401.propBunruiSelect.labelName}" 
									style="#{pc_Xrh01401.propBunruiSelect.labelStyle}">
								</h:outputText>
						 </TH>
						 <TD width="500">
								<h:selectOneRadio
			              			disabledClass="selectOneRadio_Disabled"
			              			styleClass="selectOneRadio" id="htmlBunruiSelect"
			              			value="#{pc_Xrh01401.propBunruiSelect.stringValue}"
			              			style="#{pc_Xrh01401.propBunruiSelect.style}"
			              			tabindex="2">
									<f:selectItems
										value="#{pc_Xrh01401.propBunruiSelect.list}" />
					             </h:selectOneRadio>
					     </TD>
					  </TR>
					  <TR>
					  	<TH class="v_d" width="150">
					  		<h:outputText
					  			styleClass="outputText" id="lblRegKbn" value="データ登録区分指定">
					  		</h:outputText>
					  	</TH>
					  	<TD nowrap class="" width="500">
					  		<h:selectOneRadio 
					  			disabledClass="selectOneRadio_Disabled"
					  			styleClass="selectOneRadio" id="htmlRegKbn"
					  			layout="pageDirection"  tabindex="3" 
					  			value="#{pc_Xrh01401.propRegKbn.stringValue}"
					  			readonly="#{pc_Xrh01401.propRegKbn.readonly}"
					  			disabled="#{pc_Xrh01401.propRegKbn.disabled}"
					  			style="#{pc_Xrh01401.propRegKbn.style}">
					  			<f:selectItem itemValue="1"
					  				itemLabel="データを登録します。同一データが存在すればエラーとなります。" />
					  			<f:selectItem itemValue="2"
					  				itemLabel="データを登録または更新します。同一データが存在すれば上書き更新します。" />
					  		</h:selectOneRadio>
					  	</TD>
					  </TR>
					  <TR>
						 <TH class="v_a" width="150">
							<h:outputText styleClass="outputText"
								id="lblSyoriKbn"
								value="#{pc_Xrh01401.propSyoriKbn.labelName}">
							</h:outputText>
						 </TH>
						 <TD>
							<h:selectBooleanCheckbox 
								styleClass="selectBooleanCheckbox"
                                id="htmlSyoriKbn" 
                                value="#{pc_Xrh01401.propSyoriKbn.checked}" tabindex="4">
                            </h:selectBooleanCheckbox>
                            チェックのみ(データの登録／更新は行いません)
						 </TD>	
					  </TR>
					  
					  <TR>
							<TH class="v_a">
								<h:outputText styleClass="outputText"
									value="チェックリスト出力指定">
								</h:outputText>
							</TH>
							<TD>
								<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlNorData" 
                                	value="#{pc_Xrh01401.propNorData.checked}" tabindex="5">
                            	</h:selectBooleanCheckbox>正常データ
                            	<BR>
								<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
                                	id="htmlErrData" 
                                	value="#{pc_Xrh01401.propErrData.checked}" tabindex="6">
                            	</h:selectBooleanCheckbox>エラーデータ
                            	<BR>
                            	<h:selectBooleanCheckbox 
									styleClass="selectBooleanCheckbox"
									id="htmlWarData" 
									value="#{pc_Xrh01401.propWarData.checked}" tabindex="7">
								</h:selectBooleanCheckbox>ワーニングデータ
							</TD>
					 </TR>
				  </TBODY>
				</TABLE>
				
				
				<TABLE border="0" cellpadding="0" cellspacing="0">
				<TBODY>
					<TR>
						<TD width="110"></TD>
						<TD width="360" align="left">
							<h:outputText 
								value='※以下のパターンでOCRデータを優先する。' 
								styleClass="note">
							</h:outputText>
							<BR>
							<h:outputText 
								value='　・OCR-OCR 又は WEB-WEB：上書き更新する。' 
								styleClass="note">
							</h:outputText>
							<BR>
							<h:outputText 
								value='　・WEB-OCR：OCRデータを上書き更新する。' 
								styleClass="note">
							</h:outputText>
							<BR>
							<h:outputText 
								value='　・OCR-WEB：エラーとする。' 
								styleClass="note">
							</h:outputText>
						</TD>
						<TD width="200"></TD>
					</TR>
				</TBODY>
			  </TABLE>
				
				<BR>
				
				<TABLE width="700" border="0" cellpadding="0" cellspacing="0" class="button_bar">
					<TBODY>
						<TR>
					
							<TD>
								<hx:commandExButton type="submit"
									styleClass="commandExButton_dat" id="exec"
									value="実行" tabindex="8"
									action="#{pc_Xrh01401.doExecAction}"
									confirm="#{msg.SY_MSG_0001W}"
									disabled="#{pc_Xrh01401.propExec.disabled}"
									style="#{pc_Xrh01401.propExec.style}">
								</hx:commandExButton>
		
							</TD>	
								
						</TR>
					</TBODY>
				</TABLE>
				
				</DIV>
			</DIV>
			
		</DIV>
		</h:form>
		
		<!-- フッターインクルード -->
		<jsp:include page ="../../rev/inc/footer.jsp" />
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
</HTML>
