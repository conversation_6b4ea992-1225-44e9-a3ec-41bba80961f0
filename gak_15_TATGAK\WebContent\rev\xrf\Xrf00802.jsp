<%-- 
	レポート科目分冊担当教員登録
	
	<AUTHOR>
--%>



<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrf/Xrf00802.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrf00802.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../inc/gakuen.css">
<SCRIPT type="text/javascript">

function confirmOk() {
	var phase;
	phase = parseInt(document.getElementById('form1:htmlBackPhase').value);
	if (phase == 0) {
		document.getElementById('form1:htmlBackPhase').value = 1;
	}
	
	indirectClick('register');
}

function confirmCancel() {
	document.getElementById('form1:htmlBackPhase').value = 0;
}

function openKamokuSubWindow(thisObj, thisEvent) {
	//教員検索画面
	var url="${pageContext.request.contextPath}/faces/rev/co/pCob0301.jsp?retFieldName=form1:htmlKyoinCd";
	openModalWindow(url, "pCob0301", "<%=com.jast.gakuen.rev.co.PCob0301.getWindowOpenOption() %>");
	return false;
}

function doKyoinAjax(thisObj, thisEvent, target) {
  	//教員名を取得
  	var servlet = "rev/co/CobJinjAJAX";
  	getCodeName(servlet, target, thisObj.value);
  	return false;
}

function func_kadai_click(thisObj, thisEvent) {
	//課題フラグチェックボックスの処理
	var selObj1 = document.getElementById('form1:htmlKadaiFlag');
	var selObj2 = document.getElementById('form1:htmlTeisyutuKbn').value;
	
	if(!selObj1.checked) {
		document.getElementById('form1:htmlTegakiFlag').disabled = true;
		document.getElementById('form1:htmlKadaiIraibi').disabled = true;
	} else {
		if (selObj2 == '1') {
			document.getElementById('form1:htmlTegakiFlag').disabled = true;
		} else {
			document.getElementById('form1:htmlTegakiFlag').disabled = false;
		}
		document.getElementById('form1:htmlKadaiIraibi').disabled = false;
	}	
}	

//戻るボタン押下時処理
function onClickReturnDisp(id) {
	return true;
}

function loadAction(event) {
//画面ロード時の学生名称・教員名称の再取得
	doKyoinAjax(document.getElementById('form1:htmlKyoinCd'), event, 'form1:lblPreKyoinName');
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrf00802.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrf00802.doCloseDispAction}"></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrf00802.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrf00802.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">
			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>
			<!--↓content↓-->
			<DIV class="head_button_area">　
				<!-- ↓ここにボタンを配置 -->
				<hx:commandExButton type="submit"
					value="戻　る" styleClass="commandExButton" id="back"
					action="#{pc_Xrf00802.doReturnDispAction}"
					onclick="return onClickReturnDisp('#{msg.SY_MSG_0014W}');">
				</hx:commandExButton>
				<!-- ↑ここにボタンを配置 -->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->

			<TABLE border="0" cellpadding="0" cellspacing="0" width="800" class="table">
				<TBODY>
					<TR>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblKamokCd"
								value="科目コード">
							</h:outputText>
						</TH>
						<TD width="150">
							<h:outputText
								styleClass="outputText" id="htmlKamokCd"
								value="#{pc_Xrf00802.propKamokuCd.stringValue}">
							</h:outputText>
						</TD>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblKamokName"
								value="科目名称">
							</h:outputText>
						</TH>
						<TD width="410">
							<h:outputText
								styleClass="outputText" id="htmlKamokName"
								value="#{pc_Xrf00802.propKamokuName.stringValue}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblBunsatu"
								value="分冊">
							</h:outputText>
						</TH>
						<TD colspan="3" width="680">
							<h:outputText
								styleClass="outputText" id="htmlBunsatu"
								value="#{pc_Xrf00802.propBunsatu.stringValue}">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblSinKyuKan"
								value="新刊旧刊区分">
							</h:outputText>
						</TH>
						<TD width="150">
							<h:outputText
								styleClass="outputText" id="htmlSinKyuKan"
								value="#{pc_Xrf00802.propSinkyukan.stringValue}">
							</h:outputText>
						</TD>
						<TH class="v_b" width="120">
							<h:outputText
								styleClass="outputText" id="lblTeisyutu"
								value="提出区分">
							</h:outputText>
						</TH>
						<TD width="410">
							<h:outputText
								styleClass="outputText" id="htmlTeisyutu"
								value="#{pc_Xrf00802.propTeisyutu.stringValue}">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="800px">
				<TBODY>
					<TR>
						<TD align="right">
							<h:outputText styleClass="outputText" id="htmlCount"
								value="#{pc_Xrf00802.propGakkiList.listCount}">
							</h:outputText>
							<h:outputText
								styleClass="outputText" id="lblCount" value="件">
							</h:outputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>				

			<TABLE border="0" cellpadding="0" cellspacing="0"  width="800px">
				<TBODY>					
					<TR>
						<TD>
							<div id="listScroll" class="listScroll"
								onscroll="setScrollPosition('scroll',this);"
								style="height:168px;">
								<h:dataTable border="0"
									cellpadding="2" cellspacing="0" headerClass="headerClass"
									footerClass="footerClass"
									rowClasses="#{pc_Xrf00802.propGakkiList.rowClasses}"
									styleClass="meisai_scroll" id="tableGakki"
									value="#{pc_Xrf00802.propGakkiList.list}" var="varlist">

									<h:column id="column1">
										<f:facet name="header">
											<h:outputText id="ListText01" styleClass="outputText" value="教員コード">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKyoinCd"
											value="#{varlist.listKyoinCd}">
										</h:outputText>
										<f:attribute value="80" name="width" />
									</h:column>

									<h:column id="column2">
										<f:facet name="header">
											<h:outputText id="ListText02" styleClass="outputText" value="教員名">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKyoinName"
											value="#{varlist.listKyoinName}">
										</h:outputText>
										<f:attribute value="245" name="width" />
									</h:column>

									<h:column id="column3">
										<f:facet name="header">
											<h:outputText id="ListText03" styleClass="outputText" value="課題フラグ">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListKadaiFlg"
											value="#{varlist.listKadai}">
										</h:outputText>
										<f:attribute value="80" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column4">
										<f:facet name="header">
											<h:outputText id="ListText04" styleClass="outputText" value="課題作成依頼日">
											</h:outputText>
										</f:facet>
											<h:outputText styleClass="outputText" id="htmlListKadaiSakuseiIraibi"
												value="#{varlist.listKadaiSakuseiIraibiOutput}">
											</h:outputText>
										<f:attribute value="100" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column5">
										<f:facet name="header">
											<h:outputText id="ListText05" styleClass="outputText" value="手書フラグ">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListTegakiFlg"
											value="#{varlist.listTegaki}">
										</h:outputText>
										<f:attribute value="80" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column6">
										<f:facet name="header">
											<h:outputText id="ListText06" styleClass="outputText" value="添削フラグ">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListTensakuFlg"
											value="#{varlist.listTensaku}">
										</h:outputText>
										<f:attribute value="80" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>

									<h:column id="column7">
										<f:facet name="header">
											<h:outputText id="ListText07" styleClass="outputText" value="添削上限数">
											</h:outputText>
										</f:facet>
										<h:outputText styleClass="outputText" id="htmlListTensakuMax"
											value="#{varlist.listTensakuMax}">
										</h:outputText>
										<f:attribute value="80" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>
									
									<h:column id="column8">
										<f:facet name="header">
										</f:facet>
										<hx:commandExButton type="submit" value="選択"
											styleClass="commandExButton" id="selectKamoku"
											action="#{pc_Xrf00802.doReferAction}" tabindex="1">
										</hx:commandExButton>
										<f:attribute value="40" name="width" />
										<f:attribute value="text-align: center; vertical-align: middle" name="style" />
									</h:column>
								</h:dataTable>
							</div>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<BR>
			<TABLE border="0" cellpadding="0" cellspacing="0" width="800" class="table">
				<TBODY>
					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblKyoinCd"
								style="#{pc_Xrf00802.propKyoinCd.labelStyle}"
								value="#{pc_Xrf00802.propKyoinCd.labelName}">
							</h:outputText>
						</TH>
						<TD width="130">
							<h:inputText styleClass="inputText" id="htmlKyoinCd"
								value="#{pc_Xrf00802.propKyoinCd.stringValue}"
								maxlength="#{pc_Xrf00802.propKyoinCd.maxLength}" size="10"
								style="#{pc_Xrf00802.propKyoinCd.style}"
								onblur="return doKyoinAjax(this, event, 'form1:lblPreKyoinName');"
								tabindex="2">
								<hx:inputHelperAssist imeMode="disabled"
									errorClass="inputText_Error" />
							</h:inputText>
							<hx:commandExButton type="button" value=""
								styleClass="commandExButton_search" id="search"
								onclick="return openKamokuSubWindow(this, event);"
								tabindex="3">
							</hx:commandExButton>
						</TD>
						<TD colspan="4" width="490">
							<h:outputText styleClass="outputText" id="lblPreKyoinName">
							</h:outputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblKadaiFlag"
								style="#{pc_Xrf00802.propKadaiFlag.labelStyle}"
								value="課題フラグ">
							</h:outputText>
						</TH>
						<TD width="130">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlKadaiFlag"
								value="#{pc_Xrf00802.propKadaiFlag.checked}"
								style="#{pc_Xrf00802.propKadaiFlag.style}"
								onclick="return func_kadai_click(this, event);" tabindex="4">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" id="lblKadaiTanto" value="課題担当"
								style="#{pc_Xrf00802.propKadaiFlag.labelStyle}">
							</h:outputText>
						</TD>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblTegakiFlag"
								style="#{pc_Xrf00802.propTegakiFlag.labelStyle}"
								value="手書きフラグ">
							</h:outputText>
						</TH>
						<TD width="150">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlTegakiFlag"
								value="#{pc_Xrf00802.propTegakiFlag.checked}"
								disabled="#{pc_Xrf00802.propTegakiFlag.disabled}"
								style="#{pc_Xrf00802.propTegakiFlag.style}" tabindex="8">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" id="lblTegakiSitei" value="手書き指定"
								style="#{pc_Xrf00802.propTegakiFlag.labelStyle}">
							</h:outputText>
						</TD>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblKadaiIraibi"
								value="#{pc_Xrf00802.propKadaiIraibi.labelName}"
								style="#{pc_Xrf00802.propKadaiIraibi.labelStyle}">
							</h:outputText>
						</TH>
						<TD width="180">
							<h:inputText
								styleClass="inputText" id="htmlKadaiIraibi"
								value="#{pc_Xrf00802.propKadaiIraibi.dateValue}"
								disabled="#{pc_Xrf00802.propKadaiIraibi.disabled}"
								size="12" tabindex="5">
								<f:convertDateTime />
								<hx:inputHelperAssist errorClass="inputText_Error"
									promptCharacter="_" />
								<hx:inputHelperDatePicker />
							</h:inputText>
						</TD>
					</TR>
					<TR>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblTensakuFlag"
								style="#{pc_Xrf00802.propTensakuFlag.labelStyle}"
								value="添削フラグ">
							</h:outputText>
						</TH>
						<TD width="130">
							<h:selectBooleanCheckbox
								styleClass="selectBooleanCheckbox" id="htmlTensakuFlag"
								value="#{pc_Xrf00802.propTensakuFlag.checked}"
								style="#{pc_Xrf00802.propTensakuFlag.style}" tabindex="6">
							</h:selectBooleanCheckbox>
							<h:outputText
								styleClass="outputText" id="lblTensakuTanto" value="添削担当"
								style="#{pc_Xrf00802.propTensakuFlag.labelStyle}">
							</h:outputText>
						</TD>
						<TH class="v_b" width="180">
							<h:outputText
								styleClass="outputText" id="lblTensakuMax"
								value="添削上限数">
							</h:outputText>
						</TH>
						<TD colspan="3" width="360">
							<h:inputText styleClass="inputText"
								id="htmlTensakuMax"
								value="#{pc_Xrf00802.propTensakuMaxSu.stringValue}" size="4"
								maxlength="#{pc_Xrf00802.propTensakuMaxSu.maxLength}"
								tabindex="7">
							</h:inputText>
						</TD>
					</TR>
				</TBODY>
			</TABLE>

		
			<BR>
			<TABLE class="button_bar" width="100%">
				<TBODY>
					<TR>
						<TD>
							<hx:commandExButton type="submit" value="確定"
								styleClass="commandExButton_dat" id="register"
								action="#{pc_Xrf00802.doRegisterAction}"
								confirm="#{msg.SY_MSG_0002W}" tabindex="9">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="削除" styleClass="commandExButton_dat"
								id="delete" action="#{pc_Xrf00802.doDeleteAction}"
								disabled="#{pc_Xrf00802.propDelete.disabled}"
								confirm="#{msg.SY_MSG_0004W}" tabindex="10">
							</hx:commandExButton>
							<hx:commandExButton	type="submit" value="クリア" styleClass="commandExButton_etc"
								id="clear" action="#{pc_Xrf00802.doClearAction}"
								confirm="#{msg.SY_MSG_0014W}" tabindex="11">
							</hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV>
			</DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../inc/footer.jsp" />
			<h:inputHidden value="#{pc_Xrf00802.propGakkiList.scrollPosition}"
				id="scroll">
			</h:inputHidden>
			<h:inputHidden value="#{pc_Xrf00802.propTeisyutuKbn.stringValue}"
				id="htmlTeisyutuKbn">
			</h:inputHidden>
			<h:inputHidden
				id="htmlBackPhase"
				value="#{pc_Xrf00802.propBackPhase.integerValue}">
			</h:inputHidden>
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../inc/common.jsp" />
</f:view>
<SCRIPT type="text/javascript" LANGUAGE="JavaScript">
changeScrollPosition('scroll','listScroll');
</SCRIPT>
</HTML>

