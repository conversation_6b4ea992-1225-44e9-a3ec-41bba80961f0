<%-- 
	入出庫登録(条件指定)
	
	<AUTHOR>
--%>
<%-- <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"> IEを標準互換モードとして動作させる為に変更 --%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<%-- jsf:pagecode language="java" location="/JavaSource/com/jast/gakuen/rev/xrc/Xrc00501.java" --%><%-- /jsf:pagecode --%>
<%@taglib uri="http://java.sun.com/jsf/html" prefix="h"%>
<%@taglib uri="http://www.ibm.com/jsf/html_extended" prefix="hx"%>
<%@taglib uri="http://www.ibm.com/jsf/BrowserFramework" prefix="odc"%>
<HTML>
<HEAD>
<%@ taglib uri="http://java.sun.com/jsf/core" prefix="f"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<META name="GENERATOR" content="IBM Software Development Platform">
<META http-equiv="Content-Style-Type" content="text/css">
<TITLE>Xrc00501.jsp</TITLE>
<LINK rel="stylesheet" type="text/css" href="../../theme/stylesheet.css"
	title="Style">
<LINK rel="stylesheet" type="text/css" href="../../rev/inc/gakuen.css">

<SCRIPT type="text/javascript">

function loadAction(event) {
// 画面ロード時の物品コード、物品名称の再取得
	doBpnAjax(document.getElementById('form1:htmlBuppinCd'), event, 'form1:lblPreBuppinName');
}

function openBpnuSubWindow(field1) {
// 物品検索画面
    var url="${pageContext.request.contextPath}/faces/rev/xrc/pXrc0101.jsp"
		+ "?retFieldName=" + field1;
	openModalWindow(url, "pXrc0101", "<%=com.jast.gakuen.rev.xrc.PXrc0101.getWindowOpenOption() %>");
	return false;
}
function doBpnAjax(thisObj, thisEvent, targetLabel) {
// 物品名称を取得する
	var servlet = "rev/xrc/XrcBpnAJAX";
    var args = new Array();
    args['code'] = thisObj.value;
    
    var ajaxUtil = new AjaxUtil();
	ajaxUtil.getCodeName(servlet, targetLabel, args);
}

</SCRIPT>

</HEAD>
<f:view locale=#{SYSTEM_DATA.locale}>
	<f:loadBundle basename="properties.message" var="msg" />
	<BODY onLoad="loadAction(event)">
	<hx:scriptCollector id="scriptCollector1"
		preRender="#{pc_Xrc00501.onPageLoadBegin}">
		<h:form styleClass="form" id="form1">

			<!-- ヘッダーインクルード -->
			<jsp:include page="../../rev/inc/header.jsp" />

			<!-- ヘッダーへのデータセット領域 -->
			<div style="display:none;"><hx:commandExButton type="submit"
				value="閉じる" styleClass="commandExButton" id="closeDisp"
				action="#{pc_Xrc00501.doCloseDispAction}"
				></hx:commandExButton> <h:outputText
				styleClass="outputText" id="htmlFuncId"
				value="#{pc_Xrc00501.funcId}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlLoginId"
				value="#{SYSTEM_DATA.loginID}"></h:outputText> <h:outputText
				styleClass="outputText" id="htmlScrnName"
				value="#{pc_Xrc00501.screenName}"></h:outputText></div>

			<!--↓outer↓-->
			<DIV class="outer">

			<FIELDSET class="fieldset_err"><LEGEND>エラーメッセージ</LEGEND> <h:outputText
				id="message" value="#{requestScope.DISPLAY_INFO.displayMessage}"
				styleClass="outputText" escape="false">
			</h:outputText></FIELDSET>

			<!--↓content↓-->
			<DIV class="head_button_area">　
			<!-- ↓ここに戻る／閉じるボタンを配置 -->
			<!-- ↑ここに戻る／閉じるボタンを配置 -->
			　<!-- ← レイアウトの問題の為に、、全角スペースを配置-->
			</DIV>
			<DIV id="content">
			<DIV class="column" align="center">
			<!-- ↓ここにコンポーネントを配置 -->
			<TABLE border="0" cellspacing="0" cellpadding="0" width="675">
				<TBODY>
					<TR>
						<TD>
						<TABLE width="100%" border="0" cellspacing="0" cellpadding="0" class="table" >
							<TBODY>
								<TR>											
									<TH nowrap class="v_a"><h:outputText
									     styleClass="outputText" id="lblSyoriKbn"
									     value="#{pc_Xrc00501.propSyoriKbnCondition.labelName}"
									     style="#{pc_Xrc00501.propSyoriKbnCondition.labelStyle}"></h:outputText>
									</TH>
									<TD nowrap width="500"><h:selectOneRadio
                             		      id="htmlSyoriKbn"
                            		       styleClass="selectOneRadio"
                             		       disabledClass="selectOneRadio_Disabled"
                            		       value="#{pc_Xrc00501.propSyoriKbnCondition.value}"
                            		       disabled="#{pc_Xrc00501.propSyoriKbnCondition.disabled}">
                            		      <f:selectItem itemValue="#{pc_Xrc00501.syoriKbnZaiko}" itemLabel="在庫管理" />
                           		       <f:selectItem itemValue="#{pc_Xrc00501.syoriKbnHatyu}" itemLabel="発注管理" />
                           		      </h:selectOneRadio>
									</TD>
								</TR>
								<TR>
									<TH class="v_b" width="150"><h:outputText
										styleClass="outputText" id="lblBuppinCd"
										style="#{pc_Xrc00501.propBuppinCd.labelStyle}"
										value="#{pc_Xrc00501.propBuppinCd.labelName}"></h:outputText></TH>
									<TD width="525"><h:inputText styleClass="inputText"
										id="htmlBuppinCd"
										value="#{pc_Xrc00501.propBuppinCd.stringValue}"
										style="#{pc_Xrc00501.propBuppinCd.style}"
										maxlength="#{pc_Xrc00501.propBuppinCd.maxLength}" size="10"
										onblur="return doBpnAjax(this, event, 'form1:lblPreBuppinName');"
										disabled="#{pc_Xrc00501.propBuppinCd.disabled}">
										</h:inputText><hx:commandExButton
										type="button" value="" styleClass="commandExButton_search"
										id="search"
										onclick="return openBpnuSubWindow('form1:htmlBuppinCd');"
										disabled="#{pc_Xrc00501.propSearchButton.disabled}"></hx:commandExButton>
										<h:outputText styleClass="outputText" id="lblPreBuppinName"></h:outputText></TD>
								</TR>
							</TBODY>
						</TABLE>
						</TD>
					</TR>
				</TBODY>
			</TABLE><CENTER>
			<TABLE width="100%" class="button_bar">
				<TBODY>
					<TR>
						<TD><hx:commandExButton type="submit" value="確定"
							styleClass="commandExButton_dat" id="register"
							action="#{pc_Xrc00501.doRegisterAction}"
							disabled="#{pc_Xrc00501.propRegisterButton.disabled}"></hx:commandExButton>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
			<!-- ↑ここにコンポーネントを配置 --></DIV></DIV>
			<!--↑content↑--></DIV>
			<!--↑outer↑-->
			<!-- フッダーインクルード -->
			<jsp:include page="../../rev/inc/footer.jsp" />
		</h:form>
	</hx:scriptCollector>
	</BODY>
	<jsp:include page="../../rev/inc/common.jsp" />
</f:view>

</HTML>

